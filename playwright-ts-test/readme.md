# Playwright End-to-End Testing Framework Documentation

## 1. Introduction

### Overview

This project is a powerful testing framework using Playwright for GraphQL and REST APIs with support for reporting, BDD, and more.

### Purpose, Scope, and Key Objectives

- Automate end-to-end testing for web applications.
- Support for multiple browsers and platforms.
- Integration with CI/CD pipelines for continuous testing.

### Supported Browsers and Platforms

- **Chromium**
- **Firefox**
- **WebKit**
- **Windows, macOS, Linux**

---

## 2. Installation & Setup

### Prerequisites

- **Node.js** (v22 or higher)
- **npm** or **yarn**

### Step-by-Step Guide

#### Clone the repository

```sh
git clone <repository-url>
cd playwright-ts-test
```

#### Install dependencies

```sh
pnpm install
```


#### Install browsers

```sh
npx playwright install
```

#### Configure environment variables

Create a `.env` file in the root directory and add the necessary environment variables.

###  Example
# ONE_UI_BASE_URL=https://beta.console.zscaler.com/
# ONEUI_USERNAME=<EMAIL>
# ONEUI_PASSWORD=Admin@123

### Key Configuration Files

- **playwright.config.ts**: Main configuration file for Playwright.
- **global-setup.ts**: Script for global setup before tests run.

---

## 3. Project Structure

```ts
playwright-test/
├── .env                        # Environment variables file
├── global-setup.ts             # Global setup script for initializing tests
├── package.json                # Project dependencies and scripts
├── playwright.config.ts        # Playwright configuration file
├── readme.md                   # Project documentation
├── tsconfig.json               # TypeScript configuration
│
├── .features-gen/              # Auto-generated feature files (if applicable)
│   └── src/                    # Placeholder for generated test data
│
├── allure-results/             # Allure test results directory
│
├── playwright-report/          # Playwright HTML reports directory
│
├── resources/                  # Utility functions and helpers
│   ├── utils/
│   │   ├── BrowserStorageHelper.ts  # Helper for handling browser storage
│   │   ├── PlaywrightActions.ts     # Common Playwright actions
│   │   ├── ApiHelper.ts             # Helper for API requests
│   │   ├── Logger.ts                # Custom logger utility
│   ├── data/
│   │   ├── testData.ts              # Test data
│   ├── config/
│   │   ├── testConfig.ts            # Test configuration settings
│   ├── downloads/
│   │   ├── sampleFile.txt           # Sample file for download tests
│
├── src/                        # Main source directory
│   ├── tests/                  # Test cases directory
│   │   ├── ui/                 # UI tests
│   │   │   ├── features/       # Feature files for UI tests
│   │   │   │   ├── login.feature
│   │   │   │   ├── Cybersecurity.feature
│   │   │   │   ├── RBAC.feature
│   │   │   │   ├── ...
│   │   │   ├── pages/          # Page Object Model (POM) files
│   │   │   │   ├── LoginPage.ts
│   │   │   │   ├── Cybersecurity/
│   │   │   │   ├── RBAC/
│   │   │   │   ├── ...
│   │   │   ├── step_definitions/ # Step definitions for BDD tests
│   │   │   │   ├── Login.step.ts
│   │   │   │   ├── Cybersecurity/
│   │   │   │   ├── RBAC/
│   │   │   │   ├── ...
│   │   ├── api/                # API tests here
│   │   ├── gql/                # GQL tests here
```

---

## 4. Running Tests

### CLI Commands

#### Run all tests

```sh
npx bddgen && npx playwright test
```

#### Run specific test suite

Pick the project names from PLaywright.config.ts

### example
```sh
npx bddgen && npx playwright test --project=Login
```

#### Run specific test cases

Provide the feature file path to run testcases for particular page

### example

```sh
npx bddgen && npx playwright test src/tests/ui/features/Cybersecurity/advancedThreats.feature
```

#### Run specific test case

Add tags at the top of scenario which has to be executed

### example

```
@tag
Scenario: Verify the page
    Given User Logs in
    When User click okay
```

```sh
npx bddgen && npx playwright test src/tests/ui/features/Cybersecurity/advancedThreats.feature --grep=@tag
```

#### Run tests in headless mode

```sh
npx bddgen && npx playwright test --headless
```

#### Parallel execution

```sh
npx bddgen && npx playwright test --workers=4
```

---

## 5. Writing Tests

### Standard Test Structure and Syntax

Using Playwright Test Runner:

```ts

test('Login test', async ({ page }) => {
  await page.goto('https://example.com');
  await page.fill('#username', 'testuser');
  await page.fill('#password', 'password123');
  await page.click('button[type="submit"]');
  await expect(page).toHaveURL('/dashboard');
});
```

### Working with Selectors, Assertions, and Fixtures

- **Selectors:**

  ```ts
  await page.click('text=Submit');

  ```

- **Assertions:**

  ```ts
  await expect(page).toHaveText('Welcome');
  ```

- **Fixtures:**

  ```ts

test.use({ viewport: { width: 1280, height: 720 } });

```ts

---

## 6. Reports & Logs

### Generating Test Reports
#### Allure Report:
```sh
pnpm run test:report-generate
pnpm run test:report-open
```

### Accessing Logs and Screenshots

- **Logs:** Available in the `test-results` directory.
- **Screenshots:** Automatically captured for failed tests.


### Open Playwright Default reports

```
sh 
npx playwright show-report
```

---

## 7. Configuration & Environment Setup

### Managing Multiple Environments

Use `.env` files to manage different environments.

### Configuring `playwright.config.ts`

Example Configuration:

```ts
import { defineConfig } from '@playwright/test';

export default defineConfig({
  timeout: 60000,
  use: {
    headless: true,
    viewport: { width: 1280, height: 720 },
    actionTimeout: 10000,
    navigationTimeout: 30000,
  },
});
```

---

## 8. CI/CD Integration

### Running Playwright Tests in CI/CD

#### GitHub Actions

```yaml
name: Playwright Tests

on: [push]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '14'
      - run: pnpm install
      - run: npx playwright install
      - run: npx playwright test
```

---

## 9. Troubleshooting & Debugging

### Common Issues and Resolutions

- **Timeout Errors:** Increase timeout values in `playwright.config.ts`.
- **Element Not Found:** Ensure the selector is correct and the element is visible.

### Debugging Tips

#### Debug Mode

```sh
npx bddgen && npx playwright test --debug
```

#### Screenshot and Video Recording

Enabled in `playwright.config.ts`.

---

## 10. Extending the Framework

### Creating Custom Helpers and Utilities

Example Helper:

```ts

export async function login(page, username, password) {
  await page.fill('#username', username);
  await page.fill('#password', password);
  await page.click('button[type="submit"]');
}
```

### Implementing Reusable Functions

Example Function:

```ts

export async function navigateTo(page, url) {
  await page.goto(url);
}
```

---

## 11. Best Practices

### Writing Maintainable and Scalable Tests

- Use **Page Object Model (POM)** for structured automation.
- Avoid static waits; use Playwright's built-in waiting mechanisms.

### Optimizing Test Performance

- Run tests in parallel.
- Use **headless mode** for faster execution.

---

## 12. FAQs & Additional Resources

### Frequently Asked Questions

#### How do I run a specific test?

```sh
npx bddgen && npx playwright test path/to/test.spec.ts
```

#### How do I debug a test?

```sh
npx bddgen && npx playwright test --debug
```

### Additional Resources

- [Playwright Documentation](https://playwright.dev)
- [Playwright GitHub Repository](https://github.com/microsoft/playwright)
- [Playwright Community](https://playwright.dev/community)


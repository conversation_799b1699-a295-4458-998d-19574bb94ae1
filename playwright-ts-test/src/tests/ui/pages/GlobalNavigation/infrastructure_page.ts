import { Page } from '@playwright/test';
import PlaywrightActions from '../../../../../resources/utils/PlaywrightActions';
class infrastructure {
    menuIds: { [key: string]: string };
    subMenuIds: { [key: string]: string };
    verticalMenuIds: { [key: string]: string };

    constructor() {
        this.menuIds = { 
            Administration: "nav-pills-tab-1",
            Policies: "nav-pills-tab-2", 
            Infrastructure: "nav-pills-tab-3", 
            Logs: "nav-pills-tab-4",
        };

        this.subMenuIds = { 
            "Internet & SaaS": "mm-tabs-tab-0", 
            "Private Access": "mm-tabs-tab-1", 
            Locations: "mm-tabs-tab-2", 
            Connectors: "mm-tabs-tab-3", 
            "Common Resources": "mm-tabs-tab-4", 
        };

        this.verticalMenuIds = { 
            Component: "mega-menu-tabs-vertical-tab-0", 
            "Business Continuity": "mega-menu-tabs-vertical-tab-1",
            "B2B Exchange": "mega-menu-tabs-vertical-tab-2",
            "Client Connector Policies": "mega-menu-tabs-vertical-tab-3",
            "Traffic Forwarding": "mega-menu-tabs-vertical-tab-0",
            "Network Policies":"mega-menu-tabs-vertical-tab-1",
            "Client": "mega-menu-tabs-vertical-tab-0",
            "Edge": "mega-menu-tabs-vertical-tab-1",
            "Cloud": "mega-menu-tabs-vertical-tab-2",
            "Gateways": "mega-menu-tabs-vertical-tab-0",
            "Application": "mega-menu-tabs-vertical-tab-1",
            "Deployment": "mega-menu-tabs-vertical-tab-2",
        };
    }

    async internetAndSaas(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["Internet & SaaS"], "Internet & SaaS");
    }

    async trafficForwarding(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds["Traffic Forwarding"], "Traffic Forwarding");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Virtual Service Edges", "mega-menu-columns-group-0-link-0", "(//span[@class='-js-title-text'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Partner Integrations: SDWAN", "mega-menu-columns-group-1-link-0", "(//span[@class='-js-title-text'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Partner Integrations: Azure Virtual WAN", "mega-menu-columns-group-1-link-1", "(//span[@class='-js-title-text'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Hosted PAC Files", "mega-menu-columns-group-2-link-0", "(//span[@class='-js-title-text'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Subclouds", "mega-menu-columns-group-2-link-1", "(//span[@class='-js-title-text'])"); 

        // await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        // await PlaywrightActions.checkPageRenderWithTheme(page, "DC Exclusion", "mega-menu-columns-group-2-link-2", "(//span[@class='-js-title-text'])"); 
    }

    async networkPolicies(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds["Network Policies"], "Network Policies");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Forwarding Control Policy", "mega-menu-columns-group-0-link-0", "(//span[@class='-js-title-text'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Proxies and Gateways", "mega-menu-columns-group-0-link-1", "(//span[@class='-js-title-text'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Dedicated IP", "mega-menu-columns-group-0-link-2", "(//span[@class='-js-title-text'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Zscaler Private Access", "mega-menu-columns-group-0-link-3", "(//span[@class='-js-title-text'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Root Certificates", "mega-menu-columns-group-0-link-4", "(//span[@class='-js-title-text'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Bandwidth Control", "mega-menu-columns-group-1-link-0", "(//span[@class='-js-title-text'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Bandwidth Classes", "mega-menu-columns-group-1-link-1", "(//span[@class='-js-title-text'])"); 
    }

    async privateAccess(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["Private Access"], "Private Access");
    }

    async componentLinks(page: Page): Promise<void> {
        await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds["Component"], "Component");
        await PlaywrightActions.checkPageRenderWithTheme(page, "App Connectors", "mega-menu-columns-group-0-link-0", "(//span[@data-testid='refresh-view-icon'])");

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "App Connector Groups", "mega-menu-columns-group-0-link-1", "(//span[@data-testid='refresh-view-icon'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "App Connector Keys", "mega-menu-columns-group-0-link-2", "(//span[@data-testid='refresh-view-icon'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Private Service Edges", "mega-menu-columns-group-1-link-0", "(//span[@data-testid='refresh-view-icon'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Service Edge Groups", "mega-menu-columns-group-1-link-1", "(//span[@data-testid='refresh-view-icon'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Service Edge Keys", "mega-menu-columns-group-1-link-2", "(//span[@data-testid='refresh-view-icon'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Cloud Connector", "mega-menu-columns-group-2-link-0", "(//span[@data-testid='refresh-view-icon'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Cloud Connector Groups", "mega-menu-columns-group-2-link-1", "(//span[@data-testid='refresh-view-icon'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Branch Connector", "mega-menu-columns-group-2-link-2", "(//span[@data-testid='refresh-view-icon'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Branch Connector Groups", "mega-menu-columns-group-2-link-3", "(//span[@data-testid='refresh-view-icon'])"); 
        
        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Enrollment Certificates", "mega-menu-columns-group-3-link-0", `(//span[@data-testid="refresh-view-icon"])`); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Dashboard", "mega-menu-columns-group-4-link-0", "(//span[@data-testid='tab-list-item-networkPresence/dashboard-active'])[2]"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Connectors", "mega-menu-columns-group-4-link-1", "(//span[text()='Hide Filters'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Connectors Groups", "mega-menu-columns-group-4-link-2", "(//span[text()='Hide Filters'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Connector Provisioning Keys", "mega-menu-columns-group-4-link-3", "(//span[text()='Hide Filters'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Service Edges", "mega-menu-columns-group-4-link-4", "(//span[text()='Hide Filters'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Connected Users", "mega-menu-columns-group-4-link-5", "(//span[text()='Hide Filters'])"); 

        await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
        await PlaywrightActions.checkPageRenderWithTheme(page, "Segment", "mega-menu-columns-group-4-link-6", "(//span[text()='Hide Filters'])"); 
    }

    async businessContinuityLinks(page: Page): Promise<void> {
      await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds["Business Continuity"], "Business Continuity");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Private Cloud Controllers", "mega-menu-columns-group-0-link-0", "(//span[@data-testid='refresh-view-icon'])");

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Private Cloud Controller Groups", "mega-menu-columns-group-0-link-1", "(//span[@data-testid='refresh-view-icon'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Private Cloud Controller Provisioning Keys", "mega-menu-columns-group-0-link-2", "(//span[@data-testid='refresh-view-icon'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Private Clouds", "mega-menu-columns-group-0-link-3", "(//span[@data-testid='refresh-view-icon'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Settings", "mega-menu-columns-group-0-link-4", "(//span[@data-testid='tab-list-item-privateCloud/BusinessContinuitySettings-active'])[2]"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Disaster Recovery", "mega-menu-columns-group-1-link-0", "(//span[@data-testid='refresh-view-icon'])"); 
  }

  async clientConnectorPoliciesLinks(page: Page): Promise<void> {
      await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds["Client Connector Policies"], "Client Connector Policies");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Client Forwarding Policies", "mega-menu-columns-group-0-link-0", "(//span[@data-testid='refresh-view-icon'])");

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Redirection Policy", "mega-menu-columns-group-0-link-1", "(//span[@data-testid='refresh-view-icon'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "IP Assignment", "mega-menu-columns-group-1-link-0", "(//span[@data-testid='refresh-view-icon'])"); 
  }

  async locations(page: Page): Promise<void> {
      await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["Locations"], "Locations");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Locations", "mega-menu-columns-group-0-link-0", `(//div[@data-testid="locations-title"])`);

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Legacy Locations", "mega-menu-columns-group-0-link-1", "(//span[@class='-js-title-text'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Locations Groups", "mega-menu-columns-group-0-link-2", "(//span[@class='-js-title-text'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Azure Virtual WAN Locations", "mega-menu-columns-group-0-link-3", "(//span[@class='-js-title-text'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Trusted Networks", "mega-menu-columns-group-0-link-4", "(//h1[text()='Trusted Networks'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Location Templates", "mega-menu-columns-group-1-link-0", "(//div[text()='Location Templates'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Static IPs & GRE Tunnel", "mega-menu-columns-group-1-link-1", "(//span[@class='-js-title-text'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "VPN Credentials", "mega-menu-columns-group-1-link-2", "(//span[@class='-js-title-text'])"); 

    //   await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
    //   await PlaywrightActions.checkPageRenderWithTheme(page, "Trusted IP Locations", "mega-menu-columns-group-2-link-0", "(//section[text()='IP Locations'])"); 

    //   await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
    //   await PlaywrightActions.checkPageRenderWithTheme(page, "Trusted IP Location Groups", "mega-menu-columns-group-2-link-1", "(//section[text()='IP Location Groups'])"); 
  }
  
  async connectors(page: Page): Promise<void> {
      await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["Connectors"], "Connectors");
  }

  async client(page: Page): Promise<void> {
      await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds["Client"], "Client");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Device Overview", "mega-menu-columns-group-0-link-0", "(//h1[text()='Device Management'])");

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Partner Devices", "mega-menu-columns-group-0-link-1", "(//h1[text()='Partner Devices'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Machine Tunnel", "mega-menu-columns-group-0-link-2", "(//h1[text()='Machine Tunnel'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Device Groups", "mega-menu-columns-group-0-link-3", "(//h1[text()='Device Groups'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "VDI Device Management", "mega-menu-columns-group-0-link-4", "(//div[text()='VDI Device Management'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Windows", "mega-menu-columns-group-1-link-0", "(//h1[text()='Windows'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "MacOS", "mega-menu-columns-group-1-link-1", "(//h1[text()='macOS'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Linux", "mega-menu-columns-group-1-link-2", "(//h1[text()='Linux'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "iOS", "mega-menu-columns-group-1-link-3", "(//h1[text()='iOS'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Android", "mega-menu-columns-group-1-link-4", "(//h1[text()='Android'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Forwarding Profile for Platforms", "mega-menu-columns-group-2-link-0", "(//h1[text()='Forwarding Profile'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "VDI Profile", "mega-menu-columns-group-2-link-1", "(//div[text()='VDI Forwarding Profile'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "User Agent", "mega-menu-columns-group-3-link-0", "(//h1[text()='User Agent'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Dedicated Proxy Port", "mega-menu-columns-group-3-link-1", "(//h1[text()='Dedicated Proxy Port'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Endpoint Configuration", "mega-menu-columns-group-3-link-2", "(//h1[text()='Advanced Settings'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Directory Sync & Custom Root Cert", "mega-menu-columns-group-3-link-3", "(//h1[text()='Advanced Settings'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "App Supportability", "mega-menu-columns-group-4-link-0", "(//h1[text()='Advanced Settings'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Network Performance", "mega-menu-columns-group-4-link-1", "(//h1[text()='Advanced Settings'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "User Privacy", "mega-menu-columns-group-4-link-2", "(//h1[text()='Advanced Settings'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "App Fail Open", "mega-menu-columns-group-4-link-3", "(//h1[text()='Advanced Settings'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Client Connector Device Management", "mega-menu-columns-group-4-link-4", "(//h1[text()='Advanced Settings'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Business Continuity", "mega-menu-columns-group-4-link-5", "(//h1[text()='Business Continuity'])"); 

  }

  async edge(page: Page): Promise<void> {
      await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds["Edge"], "Edge");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Appliances", "mega-menu-columns-group-0-link-0", "//div[@data-testid='appliance-list-button-group']");

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Branch Connector Monitoring", "mega-menu-columns-group-0-link-1", "(//h1[text()='Dashboard'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Branch Provisioning", "mega-menu-columns-group-0-link-2", "(//h1[text()='Provisioning & Configuration'])");

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Branch Devices", "mega-menu-columns-group-0-link-3", "(//div[text()='Branch Devices'])");

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "ZT Devices", "mega-menu-columns-group-0-link-4", "(//div[text()='ZT Devices'])");

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Forwarding Policy", "mega-menu-columns-group-1-link-0", "(//div[text()='Traffic Forwarding'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Log and Streaming", "mega-menu-columns-group-1-link-1", "(//div[text()='Log and Control Forwarding'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "DNS", "mega-menu-columns-group-1-link-2", "(//div[text()='DNS Policy'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Sites", "mega-menu-columns-group-2-link-0", "(//h2[text()='Sites'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Site Templates", "mega-menu-columns-group-2-link-1", "(//h2[text()='Templates'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Assets", "mega-menu-columns-group-2-link-2", "(//h2[text()='Assets'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Insights", "mega-menu-columns-group-2-link-3", "(//h2[text()='Insights'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Charts", "mega-menu-columns-group-2-link-4", "(//h2[text()='Charts'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Settings", "mega-menu-columns-group-2-link-5", "(//h2[text()='Global'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Integrations", "mega-menu-columns-group-2-link-6", "(//h2[text()='Integrations'])"); 
  }

  async cloud(page: Page): Promise<void> {
      await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds["Cloud"], "Cloud");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Provisioning", "mega-menu-columns-group-0-link-0", "(//h2[text()='Provisioning & Configuration'])");

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Deployment Templates", "mega-menu-columns-group-0-link-1", "(//div[text()='Deployment Templates'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Cloud Connector Groups", "mega-menu-columns-group-0-link-2", "(//div[text()='Connector Groups'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Cloud Connector Monitoring", "mega-menu-columns-group-0-link-3", "(//h1[text()='Dashboard'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Partner Integrations", "mega-menu-columns-group-0-link-4", "(//span[@class='component-header-cc-group'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Forwarding Policy", "mega-menu-columns-group-1-link-0", "(//div[text()='Traffic Forwarding'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Log and Streaming", "mega-menu-columns-group-1-link-1", "(//div[text()='Log and Control Forwarding'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "DNS for VM", "mega-menu-columns-group-1-link-2", "(//div[text()='DNS Policy'])"); 

    //   await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
    //   await PlaywrightActions.checkPageRenderWithTheme(page, "Nanolog Streaming Service", "mega-menu-columns-group-2-link-0", "(//section[@class='heading-small page-title'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Advanced Settings", "mega-menu-columns-group-2-link-1", "(//span[@class='source-ip-groups'])"); 
  }

  async commonResources(page: Page): Promise<void> {
      await PlaywrightActions.verifyTextAndClick(page, this.subMenuIds["Common Resources"], "Common Resources");
  }

  async gateways(page: Page): Promise<void> {
      await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds["Gateways"], "Gateways");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Internet Access", "mega-menu-columns-group-0-link-0", "(//div[text()='Internet Access Gateway'])");

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Log and Control", "mega-menu-columns-group-0-link-1", "(//div[text()='Log and Control Gateway'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "DNS", "mega-menu-columns-group-0-link-2", "(//div[text()='DNS Gateway'])"); 
  }

  async application(page: Page): Promise<void> {
      await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds["Application"], "Application");
      await PlaywrightActions.checkPageRenderWithTheme(page, "IP Based", "mega-menu-columns-group-0-link-0", "(//h1[text()='Application Bypass'])");

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Process Based", "mega-menu-columns-group-0-link-1", "(//h1[text()='Application Bypass'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Source IP Groups", "mega-menu-columns-group-1-link-0", "(//div[text()='IP & FQDN Groups'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Destination IP Groups", "mega-menu-columns-group-1-link-1", "(//div[text()='IP & FQDN Groups'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "IP Pool", "mega-menu-columns-group-1-link-2", "(//div[text()='IP & FQDN Groups'])"); 
  }

  async deployment(page: Page): Promise<void> {
      await PlaywrightActions.verifyTextAndClick(page, this.verticalMenuIds["Deployment"], "Deployment");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Feature Roll out", "mega-menu-columns-group-0-link-0", "(//h1[text()='Client Connector App Store'])");

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Registered Devices", "mega-menu-columns-group-0-link-1", "(//h1[text()='Client Connector App Store'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Platform Releases", "mega-menu-columns-group-0-link-2", "(//h1[text()='Client Connector App Store'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "ZDX Releases", "mega-menu-columns-group-0-link-3", "(//h1[text()='Client Connector App Store'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "App Store for VDI", "mega-menu-columns-group-0-link-4", "(//div[text()='VDI App Store'])"); 

      await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
      await PlaywrightActions.checkPageRenderWithTheme(page, "Deployment Templates", "mega-menu-columns-group-1-link-0", "(//div[text()='Deployment Templates'])"); 

    //   await PlaywrightActions.verifyTextAndHover(page, this.menuIds["Infrastructure"], "Infrastructure");
    //   await PlaywrightActions.checkPageRenderWithTheme(page, "Device Images", "mega-menu-columns-group-2-link-0", "(//div[text()='Branch Connector Images'])"); 
  }

}

export default new infrastructure();
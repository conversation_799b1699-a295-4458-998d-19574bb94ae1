import { expect, <PERSON> } from "@playwright/test";
import PlaywrightActions from "../../../../../resources/utils/PlaywrightActions";
import { config } from 'dotenv';
import { SelectorsType } from "../../../../../resources/types/types";

config(); 
const url: string = process.env.ONE_UI_BASE_URL ?? "";

class TopNav {
  private page: Page;
  private docsList : SelectorsType;
  private docsListPageSelectors : SelectorsType;

  constructor(page: Page) {
    this.page = page;
    this.docsList = {
        "Help Docs": "https://help.zscaler.com/unified",
        "Get Support":`https://zscaler.my.site.com/customers/s/`,
        "Legal Notices":"https://help.zscaler.com/legal",
        "Privacy Policy":"https://www.zscaler.com/privacy/company-privacy-policy",
        "Subscription Agreement":"https://www.zscaler.com/legal/overview"
    };

    this.docsListPageSelectors = {
        "Help Docs": `(//h3[text()="Experience Center Help"])`,
        "Get Support":`(//h1[normalize-space()="Support Cases"])`,
        "Legal Notices": `(//h3[text()="Legal Notices"])`,
        "Privacy Policy": `(//h1[text()="Website Privacy Policy"])`,
        "Subscription Agreement": `(//h1[text()="Legal Overview"])`
    };
  }

  async openBasePage(): Promise<void> {
    if (!url) {
        throw new Error('ONE_UI_BASE_URL environment variable is not set');
      }
    await this.page.goto(url);
    await this.page.waitForTimeout(10000);
  }

  async clickAnalytics(): Promise<void> {
    if (!url) {
        throw new Error('ONE_UI_BASE_URL environment variable is not set');
      }
    await this.page.getByTestId("nav-pills-tab-0").click();
    await this.page.waitForTimeout(10000);
  }

  async checkZSLogo(): Promise<void> {
    await this.page.goto(url+"analytics/cybersecurity/advanced-threat");
    await this.page.waitForTimeout(10000);
    await this.page.getByTestId("navigation-logo").click();
    await expect(this.page).toHaveURL(url+"home");
  }

  async checkGlobalSearchBar(): Promise<void> {
    await this.page.getByTestId("nav-search-icon").click();
    await PlaywrightActions.waitForTestIdVisiblity(this.page, "search");
    await PlaywrightActions.waitForTestIdVisiblity(this.page, "global-search-nav-list-item");
    await PlaywrightActions.waitForPlaceHolderVisiblity(this.page, "Search menu");
    await PlaywrightActions.waitForLocatorVisiblity(this.page, `//input[@id="global-search-input"]`);
    await PlaywrightActions.waitForLocatorVisiblity(this.page, `//div[@data-testid="search"]/div/i`);
    await this.page.getByTestId("global-search-input").fill("digital");
    await PlaywrightActions.waitForTestIdVisiblity(this.page, "search-results");
    await this.page.getByTestId("search-result-0").click();
    await this.page.waitForSelector(`(//h3[@data-testid="digital-experience-page-header"])`, { timeout: 15000 });
    await expect(this.page).toHaveURL(url+"analytics/digital-experience");
    await this.page.getByTestId("nav-search-icon").click();
    await this.page.getByTestId("global-search-input").fill("Private Access Tenant");
    await PlaywrightActions.waitForTestIdVisiblity(this.page, "search-results");
    await this.page.getByTestId("search-result-0").click();
    await this.page.waitForSelector(`(//div[text()="Company"])`, { timeout: 15000 });
    await expect(this.page).toHaveURL(url+"private#company");
  }
  
  async checkDocs(): Promise<void> {
      await this.page.getByTestId("help-icon").click();
      await expect(this.page.getByTestId("help-popover")).toBeVisible();
      const entries = Object.entries(this.docsList);
      for(let idx = 0; idx < entries.length; idx++) {
          const [key, url] = entries[idx];
          const docOption = await this.page.getByTestId(`top-nav-options-${idx+1}-help-popover-link`);
          await expect(docOption).toHaveText(key);
          const [newPage] = await Promise.all([ 
              this.page.waitForEvent("popup"), 
              docOption.click(),
            ]);
            await newPage.waitForSelector(this.docsListPageSelectors[key], {timeout: 20000})
            await expect(newPage).toHaveURL(url);
            await this.page.bringToFront();
        }
    }
    
    async checkActivation(): Promise<void> {
      await this.page.getByTestId("activation-icon").click();
      await PlaywrightActions.waitForTestIdVisiblity(this.page, "activation-activation-popover");
      await PlaywrightActions.waitForTestIdVisiblity(this.page, "activation-activation-popover-pending-activation");
      await PlaywrightActions.waitForTestIdVisiblity(this.page, "z-button-activation-activation-popover-pending-activation-activate");
      await PlaywrightActions.verifyTextAndClick(this.page, "activation-activation-popover-0", "Pending Activation");
      await PlaywrightActions.verifyTextAndClick(this.page, "activation-activation-popover-1", "Queued Activation");
    }
    
    
    async checkAccount(): Promise<void> {
      await this.page.getByTestId("nav-account-popover").click();
      await PlaywrightActions.waitForTestIdVisiblity(this.page, "top-nav-options-account-popover");
      await PlaywrightActions.waitForTestIdVisiblity(this.page, "top-nav-options-account-popover-login");
      await PlaywrightActions.waitForTestIdVisiblity(this.page, "top-nav-options-account-popover-org-id");
      // await PlaywrightActions.waitForTestIdVisiblity(this.page, "top-nav-options-account-popover-0");
      // await PlaywrightActions.waitForTestIdVisiblity(this.page, "top-nav-options-account-popover-1");
      await expect(this.page.getByTestId("top-nav-options-account-popover-sign-out-1")).toHaveText("Sign Out");
      await PlaywrightActions.verifyTextAndClick(this.page, "top-nav-options-account-popover-option-0", "Account Settings");
      await PlaywrightActions.checkUrl(this.page, url + "account");
    }

    async switchToReports(): Promise<void> {
      await expect(this.page.getByTestId("switch-to-existing-reports-title")).toBeVisible();
      await expect(this.page.getByTestId("switch-to-existing-reports-toggle")).not.toBeChecked();
      await expect(this.page.getByTestId("networking-page-header")).toBeVisible();
      await PlaywrightActions.checkUrl(this.page, url + "analytics/networking");
      await this.page.getByTestId("switch-to-existing-reports-toggle").click();
      await this.page.waitForSelector("(//span[@id='dashboard-menu'])", { timeout:15000 });
      await expect(this.page.getByTestId("switch-to-existing-reports-toggle")).toBeChecked();
      await this.page.waitForSelector(`//span[@id="dashboard-menu"]`, { timeout: 10000 });
      await this.page.getByTestId("switch-to-existing-reports-toggle").click();
      await PlaywrightActions.checkUrl(this.page, url + "analytics/networking");
      await expect(this.page.getByTestId("networking-page-header")).toBeVisible();
      await expect(this.page.getByTestId("switch-to-existing-reports-toggle")).not.toBeChecked();
    }
    
}

export default TopNav;
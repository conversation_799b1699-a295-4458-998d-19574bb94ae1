import { createBdd } from "playwright-bdd";
const { Given, When, Then } = createBdd();
import zpaOnlyUser from '../../pages/RBAC/zpaOnlyUser_page';

Given('click on Switch to Existing Reports', async({page}) => {
    await zpaOnlyUser.disableSTER(page);
})

Given('User capture the screenshot for ZPA {string} to {string}', async({page}, menu: string, tab: string) => {
    await page.waitForTimeout(15000);
    await zpaOnlyUser.captureScreenshot(page, menu, tab);
})

Then('Verify the difference between the screenshots for ZPA', async() => {
 await zpaOnlyUser.verifyScreenshot();
})

Given('User capture the screenshot for ZPA {string} to {string} then {string}',async({page}, menu, tab, verticalMenu) => {
 await zpaOnlyUser.captureScreenshotWithVerticalMenu(page, menu, tab, verticalMenu);
})


import { Page, TestInfo } from "@playwright/test";
import { createBdd } from "playwright-bdd";
import Base from "../../pages/TopNav/topNav_page";

const { Before, When, Then } = createBdd();

let BasePage: Base;

Before({ tags: "@topnav" }, async ({ page }: { page: Page }) => {
    BasePage = new Base(page);
});

When("User is in Base Page", async () => {
  await BasePage.openBasePage();
});

When("User is in Analytics Page", async () => {
  await BasePage.openBasePage();
  await BasePage.clickAnalytics();
});

Then("Verify the Zscaler Logo", async () => {
  await BasePage.checkZSLogo();
});

Then("Verify the Global Search Bar", async () => {
  await BasePage.checkGlobalSearchBar();
});

Then("Verify the Docs", async () => {
  await BasePage.checkDocs();
});

Then("Verify the Activation Option", async () => {
  await BasePage.checkActivation();
});

Then("Verify the Account Option", async () => {
  await BasePage.checkAccount();
});

Then("Verify the Switch to Existing Reports", async () => {
  await BasePage.switchToReports();
});


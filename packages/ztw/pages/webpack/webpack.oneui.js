// const MiniCssExtractPlugin = require('mini-css-extract-plugin');
// const CleanWebpackPlugin = require('clean-webpack-plugin');
// const CompressionPlugin = require('compression-webpack-plugin');
// // const { GenerateSW } = require('workbox-webpack-plugin');
// const Dotenv = require('dotenv-webpack');
// const path = require('path');
// const commonPaths = require('./paths');

// module.exports = {
//   entry: {
//     main: commonPaths.libEntryPath, // src/lib-entry.js
//   },
//   stats: { warnings: false, errorDetails: true, errors: true },
//   mode: 'production',
//   // output: {
//   //   path: commonPaths.oneUiPath,
//   //   chunkFilename: `${commonPaths.jsFolder}/vendors[id].js`,
//   //   filename: `${commonPaths.jsFolder}/[name].js`,
//   //   clean: true,
//   //   // publicPath: '/ec/',
//   // },

//   output: {
//       path: commonPaths.oneUiPath,
//       filename: '[name].js',
//       library: {
//         name: 'ZidUI',
//         type: 'umd',
//       },
//       globalObject: 'this',
//       clean: true,
//       publicPath: '/ec/',
//     },

//   externals: [
//     /^react($|\/)/,
//     /^react-dom($|\/)/,
//     /^i18next($|\/)/,
//     /^react-i18next($|\/)/,
//   ],
//   module: {
//     rules: [
//       {
//         test: /\.(sa|sc|c)ss$/,
//         use: [
//           'style-loader',
//           MiniCssExtractPlugin.loader,
//           'css-loader',
//           'resolve-url-loader',
//           {
//             loader: 'sass-loader',
//             options: {
//               implementation: require.resolve('sass'),
//               sassOptions: {
//                 fiber: false,
//                 includePaths: [
//                   path.resolve(__dirname, '../node_modules/*'),
//                   path.resolve(__dirname, '../src/commonConnectedComponents/*'),
//                   path.resolve(__dirname, '../src/components/*'),
//                   path.resolve(__dirname, '../src/pages/*'),
//                 ],
//               },
//               sourceMap: true, // Required for resolve-url-loader to work correctly with sass-loader
//             },
//           },
//         ],
//       },
//       {
//         test: /\.(js|jsx)$/,
//         // exclude: /(node_modules)/,
//         // enforce: 'post',
//         use: {
//           // babel-loader to convert ES6 code to ES5 + and
//           // Cleaning requirejs code into simple JS code, taking care of modules to load as desired
//           loader: 'babel-loader',
//           options: {
//             presets: ['@babel/preset-env'],
//             plugins: [],
//           },
//         },
//       },
//     ],
//   },
//   optimization: {
//     // minimize: false,
//     splitChunks: {
//       cacheGroups: {
//         default: false,
//         vendors: false,
//         defaultVendors: {
//           reuseExistingChunk: true,
//         },
//         // vendor: {
//         //   chunks: 'all',
//         //   test: /node_modules/,
//         // },
//       },
//       // chunks: 'all',
//       // minSize: 500,
//     },
//   },
//   plugins: [
//     new CompressionPlugin({
//       cache: true,
//       include: /\/includes/,
//       test: /\.(js|css|html|svg)$/,
//       algorithm: 'gzip',
//     }),
//     new CleanWebpackPlugin([commonPaths.oneUiPath.split('/').pop()], {
//       root: commonPaths.root,
//     }),
//     new MiniCssExtractPlugin({
//       filename: `${commonPaths.cssFolder}/ecmain.css`,
//       // publicPath: '/ec/',
//       // allChunks: true,
//     }),
//     new Dotenv({ path: './.env.oneui' }),
//     // new GenerateSW({
//     //   clientsClaim: true,
//     // }),
//   ],
//   devtool: false,
//   experiments: {
//     topLevelAwait: true,
//   },
// };


const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
// const { GenerateSW } = require('workbox-webpack-plugin');
const Dotenv = require('dotenv-webpack');
const path = require('path');
const commonPaths = require('./paths');

module.exports = {
  entry: {
    main: commonPaths.entryPath,
  },
  stats: { warnings: false, errorDetails: true, errors: true },
  mode: 'production',
  output: {
    path: commonPaths.oneUiPath,
    chunkFilename: `${commonPaths.jsFolder}/vendors[id].js`,
    filename: `${commonPaths.jsFolder}/[name].js`,
    clean: true,
    publicPath: '/ec/',
  },
  module: {
    rules: [
      {
        test: /\.(sa|sc|c)ss$/,
        use: [
          'style-loader',
          MiniCssExtractPlugin.loader,
          'css-loader',
          'resolve-url-loader',
          {
            loader: 'sass-loader',
            options: {
              implementation: require.resolve('sass'),
              sassOptions: {
                fiber: false,
                includePaths: [
                  path.resolve(__dirname, '../node_modules/*'),
                  path.resolve(__dirname, '../src/commonConnectedComponents/*'),
                  path.resolve(__dirname, '../src/components/*'),
                  path.resolve(__dirname, '../src/pages/*'),
                ],
              },
              sourceMap: true, // Required for resolve-url-loader to work correctly with sass-loader
            },
          },
        ],
      },
      {
        test: /\.(js|jsx)$/,
        // exclude: /(node_modules)/,
        // enforce: 'post',
        use: {
          // babel-loader to convert ES6 code to ES5 + and
          // Cleaning requirejs code into simple JS code, taking care of modules to load as desired
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env'],
            plugins: [],
          },
        },
      },
    ],
  },
  optimization: {
    // minimize: false,
    splitChunks: {
      cacheGroups: {
        default: false,
        vendors: false,
        defaultVendors: {
          reuseExistingChunk: true,
        },
        // vendor: {
        //   chunks: 'all',
        //   test: /node_modules/,
        // },
      },
      // chunks: 'all',
      // minSize: 500,
    },
  },
  plugins: [
    new CompressionPlugin({
      cache: true,
      include: /\/includes/,
      test: /\.(js|css|html|svg)$/,
      algorithm: 'gzip',
    }),
    new CleanWebpackPlugin([commonPaths.oneUiPath.split('/').pop()], {
      root: commonPaths.root,
    }),
    new MiniCssExtractPlugin({
      filename: `${commonPaths.cssFolder}/ecmain.css`,
      // allChunks: true,
    }),
    new Dotenv({ path: './.env.oneui' }),
    // new GenerateSW({
    //   clientsClaim: true,
    // }),
  ],
  devtool: false,
  experiments: {
    topLevelAwait: true,
  },
};

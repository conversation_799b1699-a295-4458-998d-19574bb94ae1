const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const webpack = require('webpack');
const CopyWebpackPlugin = require('copy-webpack-plugin');
module.exports = (env, argv) => {
  const isProdMode = argv.mode === 'production';
  return {
    mode: isProdMode ? 'production' : 'development',
    entry: {
      index: './src/lib-entry.js',
    },
    devtool: 'source-map',
    output: {
      path: path.resolve(__dirname, '../dist'),
      filename: '[name].js',
      library: {
        name: 'EdgeUXLibrary',
        type: 'umd',
      },
      globalObject: 'this',
      publicPath: '',
      clean: true,
    },
    externals: [
      /^react($|\/)/,
      /^react-dom($|\/)/,
      /^react-router-dom($|\/)/,
      'redux',
      /^redux-form($|\/)/,
      /^react-redux($|\/)/,
      /^redux-thunk($|\/)/,
      /^redux-promise($|\/)/,
      /^redux-debounced($|\/)/,
      /^i18next($|\/)/,
      /^react-i18next($|\/)/,
      /^@material-ui\/core($|\/)/,
      /^@material-ui\/icons($|\/)/,
      /^@mui\/material($|\/)/,
      /^@mui\/icons-material($|\/)/,
      /^@fortawesome\/fontawesome-svg-core($|\/)/,
      /^@fortawesome\/react-fontawesome($|\/)/,
      /^@fortawesome\/pro-light-svg-icons($|\/)/,
      /^@fortawesome\/pro-regular-svg-icons($|\/)/,
      /^@fortawesome\/pro-solid-svg-icons($|\/)/,
      /^@zs-nimbus\/foundations($|\/)/,
      /^@zs-nimbus\/core($|\/)/,
      /^moment($|\/)/,
      /^moment-timezone($|\/)/,
      /^lodash($|\/)/,
      /^axios($|\/)/,
      /^classnames($|\/)/,
      /^recharts($|\/)/,
      /^d3($|\/)/,
      /^prop-types($|\/)/,
      /^styled-components($|\/)/,
      /^@emotion\/react($|\/)/,
      /^@emotion\/styled($|\/)/,
      /^react-data-grid($|\/)/,
      /^react-data-grid-addons($|\/)/,
      /^react-router($|\/)/,
      /^react-helmet($|\/)/,
      /^react-select($|\/)/,
      /^react-virtualized($|\/)/,
      /^react-window($|\/)/,
      /^react-beautiful-dnd($|\/)/,
      /^react-dnd($|\/)/,
      /^react-dnd-html5-backend($|\/)/,
      /^immutable($|\/)/,
      /^reselect($|\/)/,
      /^redux-saga($|\/)/,
      /^redux-persist($|\/)/,
    ],
    module: {
      rules: [
        {
          test: /\.(js|jsx|ts|tsx)$/,
          use: 'babel-loader',
          exclude: /node_modules/,
        },
        {
          test: /\.(css|scss|sass)$/,
          use: [
            isProdMode && MiniCssExtractPlugin.loader,
            !isProdMode && 'style-loader',
            'css-loader',
            'resolve-url-loader',
            {
              loader: 'sass-loader',
              options: {
                sourceMap: true,
              },
            },
          ].filter(Boolean),
        },
        {
          test: /\.(svg|png|jpg|jpeg|gif|woff|woff2|ttf|eot)$/,
          type: 'asset/resource',
        },
      ],
    },
    resolve: {
      modules: ['src', 'node_modules'],
      extensions: ['.js', '.jsx', '.ts', '.tsx', '.css', '.scss'],
      preferRelative: true,
      fallback: {
        fs: false,
        child_process: false,
        worker_threads: false,
        '@swc/core': false,
        esbuild: false,
      },
    },
    plugins: [
      new MiniCssExtractPlugin({
        filename: '[name].css',
      }),
      new webpack.DefinePlugin({
        'process.env.IS_LIBRARY_MODE': true,
      }),
      new CopyWebpackPlugin({
        patterns: [
          {
            from: path.resolve(__dirname, '../src/index.d.ts'),
            to: path.resolve(__dirname, '../dist/index.d.ts'),
          },
        ],
      }),
    ],
    optimization: {
      minimize: isProdMode,
      usedExports: true,
      sideEffects: false,
      ...(isProdMode && {
        minimizer: [
          new TerserPlugin({
            terserOptions: {
              compress: {
                drop_console: true,
                drop_debugger: true,
              },
            },
          }),
        ],
      }),
    },
  };
};

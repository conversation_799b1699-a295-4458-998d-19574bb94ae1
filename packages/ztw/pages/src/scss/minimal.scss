// Minimal SCSS for library mode - only essential styles

// Core layout and defaults
@import 'scss/defaults.scss';

// Essential components only (for the components we're actually exporting)
@import 'scss/components/button.scss';
@import 'scss/components/input.scss';
@import 'scss/components/modal.scss';
@import 'scss/components/table.scss';
@import 'scss/components/spinner.scss';
@import 'scss/components/notification.scss';

// Only the gateways page styles (since that's what we're exporting)
@import 'scss/pages/administration/gateways/index.scss';

// Basic layout for the minimal provider
.ec-root-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
}

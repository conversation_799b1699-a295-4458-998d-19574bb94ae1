@import 'scss/layout/index.scss';

@import 'scss/defaults.scss';

@import 'scss/admin.scss';

@import 'scss/pages.scss';

@import 'scss/widgets.scss';

@import 'scss/table/all.scss';

@import 'scss/components/_drop-down.scss';
@import 'scss/components/activation.scss';
@import 'scss/components/add-button.scss';
@import 'scss/components/add-new-button.scss';
@import 'scss/components/button.scss';
@import 'scss/components/checkbox-input-field.scss';
@import 'scss/components/checkbox-input.scss';
@import 'scss/components/column-layout-config.scss';
@import 'scss/components/common-dropdown.scss';
@import 'scss/components/config-table-filter.scss';
@import 'scss/components/config-table.scss';
@import 'scss/components/date-dropdown.scss';
@import 'scss/components/date-time-picker.scss';
@import 'scss/components/delete-confirmation-form.scss';
@import 'scss/components/drawer.scss';
@import 'scss/components/drilldown.scss';
@import 'scss/components/dropdown-main.scss';
@import 'scss/components/dropdown.scss';
@import 'scss/components/ec-option-group.scss';
@import 'scss/components/ec-radio-group.scss';
@import 'scss/components/ec-toggle.scss';
@import 'scss/components/edit-confirmation-form.scss';
@import 'scss/components/entity-dropdown.scss';
@import 'scss/components/errors.scss';
@import 'scss/components/existing-admin-confirmation-form.scss';
@import 'scss/components/external-link-button.scss';
@import 'scss/components/filter-dropdown.scss';
@import 'scss/components/flip-button.scss';
@import 'scss/components/footer.scss';
@import 'scss/components/form-error-summary.scss';
@import 'scss/components/geo-popup.scss';
@import 'scss/components/header.scss';
@import 'scss/components/help.scss';
@import 'scss/components/hoc.scss';
@import 'scss/components/info-apply.scss';
@import 'scss/components/info-box.scss';
@import 'scss/components/info-total.scss';
@import 'scss/components/input.scss';
@import 'scss/components/label.scss';
@import 'scss/components/leaflet-map.scss';
@import 'scss/components/line-chart.scss';
@import 'scss/components/list-builder.scss';
@import 'scss/components/main-nav.scss';
@import 'scss/components/map.scss';
@import 'scss/components/modal.scss';
@import 'scss/components/multi-select-dropdown.scss';
@import 'scss/components/nav-bar.scss';
@import 'scss/components/nav-tabs.scss';
@import 'scss/components/notification.scss';
@import 'scss/components/outof.scss';
@import 'scss/components/pagination-bar.scss';
@import 'scss/components/partner-box.scss';
@import 'scss/components/password-expiry.scss';
@import 'scss/components/permission-required.scss';
@import 'scss/components/pie.scss';
@import 'scss/components/radio-group.scss';
@import 'scss/components/radio-tab.scss';
@import 'scss/components/review-group.scss';
@import 'scss/components/score-bar.scss';
@import 'scss/components/score-diff.scss';
@import 'scss/components/search-box.scss';
@import 'scss/components/spinner.scss';
@import 'scss/components/subscription-required.scss';
@import 'scss/components/tab-switch.scss';
@import 'scss/components/table-cell-container.scss';
@import 'scss/components/table-pro.scss';
@import 'scss/components/table.scss';
@import 'scss/components/time-number.scss';
@import 'scss/components/toggle.scss';
@import 'scss/components/workload-discovery.scss';

// administration
@import 'scss/pages/administration/admin-management/index.scss';
@import 'scss/pages/administration/api-key-management/index.scss';
@import 'scss/pages/administration/appliances/index.scss';
@import 'scss/pages/administration/audit-logs/index.scss';
@import 'scss/pages/administration/branch-connector-images/index.scss';
@import 'scss/pages/administration/branch-connectors/index.scss';
@import 'scss/pages/administration/branch-provisioning-templates/index.scss';
@import 'scss/pages/administration/cloud-configuration-advanced-settings/index.scss';
@import 'scss/pages/administration/cloud-connectors/index.scss';
@import 'scss/pages/administration/cloud-providers-aws/index.scss';
@import 'scss/pages/administration/deployment-templates/index.scss';
@import 'scss/pages/administration/destination-ip-groups/index.scss';
@import 'scss/pages/administration/dns-gateways/index.scss';
@import 'scss/pages/administration/edgeconnector-groups/index.scss';
@import 'scss/pages/administration/edgeconnectors/index.scss';
@import 'scss/pages/administration/gateways/index.scss';
@import 'scss/pages/administration/ip-pool/index.scss';
@import 'scss/pages/administration/location-templates/index.scss';
@import 'scss/pages/administration/locations/index.scss';
@import 'scss/pages/administration/network-services/index.scss';
@import 'scss/pages/administration/nss-settings-cloud-feeds/index.scss';
@import 'scss/pages/administration/nss-settings-feeds/index.scss';
@import 'scss/pages/administration/nss-settings-servers/index.scss';
@import 'scss/pages/administration/partner-integrations/index.scss';
@import 'scss/pages/administration/provisioning-templates/index.scss';
@import 'scss/pages/administration/role-management/index.scss';
@import 'scss/pages/administration/source-ip-groups/index.scss';
@import 'scss/pages/administration/vdi-agent-app/index.scss';
@import 'scss/pages/administration/vdi-agent-forwarding-profile/index.scss';
@import 'scss/pages/administration/vdi-agent-templates/index.scss';
@import 'scss/pages/administration/vdi-device-management/index.scss';
@import 'scss/pages/administration/zero-trust-gateway/index.scss';

// analytics
@import 'scss/pages/analytics/dns-insights/index.scss';
@import 'scss/pages/analytics/dns-logs/index.scss';
@import 'scss/pages/analytics/session-insights/index.scss';
@import 'scss/pages/analytics/tunnel-insights/index.scss';
@import 'scss/pages/analytics/tunnel-logs/index.scss';

// dashboard
@import 'scss/pages/dashboard/connector-monitoring/index.scss';
@import 'scss/pages/dashboard/traffic-monitoring/index.scss';
@import 'scss/pages/dashboard/zero-trust-gateway/index.scss';
@import 'scss/pages/dashboard/index.scss';

@import 'scss/pages/login/index.scss';
@import 'scss/pages/one-identity-login/index.scss';
@import 'scss/pages/policy/index.scss';
@import 'scss/pages/profile/index.scss';
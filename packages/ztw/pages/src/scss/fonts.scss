/* Inter font */
// @use "~inter-ui/default" with (
//   $inter-font-display: swap,
//   $inter-font-path: '~inter-ui/Inter (web)'
// );
// @use "~inter-ui/variable" with (
//   $inter-font-display: swap,
//   $inter-font-path: '~inter-ui/Inter (web)'
// );

 @supports (font-variation-settings: normal) {
  html { font-family: "Inter var", "system-ui"; }
}

/* Font Awesome */
@font-face {
    font-family: 'Font Awesome Regular';
    src: url("~@fortawesome/fontawesome-pro/webfonts/fa-regular-400.woff2") format("woff2"),
    url("~@fortawesome/fontawesome-pro/webfonts/fa-regular-400.ttf") format("ttf");
}

@font-face {
    font-family: 'Font Awesome Solid';
    src: url("~@fortawesome/fontawesome-pro/webfonts/fa-solid-900.woff2") format("woff2"),
    url("~@fortawesome/fontawesome-pro/webfonts/fa-solid-900.ttf") format("ttf");
}
// @import 'scss/widgets.scss';
@import 'scss/pages.scss';
.ec-root-page {
.main-container-aws-azure-accounts{
  padding: 19px 25px;
  background-color: var(--semantic-color-background-primary);
  min-height: 100%;
  .page-title {
    padding-left: 0px;
  }
  .add-custom-app-form.cloud-configuration-advanced-settings{
    .form-sections-container {
      .cluster-child-icon{
        // padding-left:  24px;
        min-height: 21px;
        margin-right: 8px;
      }
      .cluster-child-icon-azure{
        padding-left:  29px;
        height: 20px;
        margin-right: 8px;
      }
      .form-section {
        background-color: var(--semantic-color-background-primary);
        color: var(--semantic-color-content-base-primary);
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 0.3125rem;
        padding: 1.8125rem 1.25rem;
        margin: 0rem 0px 1.875rem 0rem;
        flex-direction: column;
      }
      .form-sections-container-title{
        display: flex;
        align-items: center;
        margin-bottom: 8px;
      }
    }
    .form-field-label-wrapper .form-field-label {
      margin-left: 0px;
    }
    .check-box-container .toggle.disabled .toggle-label.on {
      background-color: var(--semantic-color-content-interactive-primary-disabled);
      border: 1px solid var(--semantic-color-content-interactive-primary-disabled);
      cursor: not-allowed;    
    }
    .check-box-container .toggle.disabled .toggle-label.off {
      background-color: var(--semantic-color-content-interactive-primary-disabled);
      border: 1px solid var(--semantic-color-content-interactive-primary-disabled);
      cursor: not-allowed;    
    }
    .modal-pieces-section-header {
      height: 20px;
      width: 33px;
      // color: #656666;
      color: var(--semantic-color-content-base-primary);
      font-size: 13px;
      letter-spacing: 1px;
      line-height: 20px;
    }
    // .cards-blank-display-cards-size {
    //   box-sizing: border-box;
    //   height: 113px;
    //   width: 1131px;
    //   border: 1px solid #E3E5E6;
    //   border-radius: 8px;
    //   background-color: var(--semantic-color-surface-base-primary);
    //   box-shadow: 1px 3px 12px 0 rgba(151,152,153,0.1);
    // }
    .dialog-footer {
      display: flex;
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 0.3125rem;
      background-color: var(--semantic-color-background-primary);
      align-content: space-between;
      // background: none;
      // border-top: none;
      // border-radius: 0 0 5px 5px;
      padding: 16px;
      width: 100%;
      button:disabled {
        background-color: transparent;
        background-color: initial;
        border: none;
        color: var(--semantic-color-content-interactive-primary-disabled);
        // box-sizing: border-box;
        // border: inherit;
        // border-radius: inherit; 
        // background-color: inherit; 
        // color: #CACBCC;
        cursor: not-allowed;

      }
      button.submit:disabled {
        background-color: var(--semantic-color-surface-fields-disabled);
        border: 1px solid var(--semantic-color-border-base-primary);
        color: var(--semantic-color-content-interactive-primary-disabled);
        cursor: not-allowed;
        box-shadow: none;
        // background: #f7f9fa;
        // border: 1px solid;
        // border-color: #6e6e6e;
        // border-radius: 5px;
        // color: #6e6e6e;
        // box-shadow: none;
        // cursor: default;
        // opacity: 0.6;
        // margin-left: 10px;
      }
    }
  }
}
}
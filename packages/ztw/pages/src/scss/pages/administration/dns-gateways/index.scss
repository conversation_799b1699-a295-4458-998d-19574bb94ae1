@import 'scss/pages.scss';
@import "scss/colors.scss";
@import 'scss/mixins.scss';

.ec-root-page {
.main-container {
  padding: 19px 25px;
}

.gateways-wrapper {
  .app-table-container {
    .ReactTable.app-table {
      .rt-thead {
        .header-title {
          font-size: 13px;
          line-height: 30px;
          display: inline-block;
        }
      }
      .has-nested-true {
        .rt-thead {
          .header-title {
            font-size: 13px;
            line-height: 16px;
          }
          .rt-th:nth-child(1) {
            display: block;
            .sorting-icon-cont {
              display: none;
            }
          }
          .rt-th:nth-child(2) {
            padding: 0;
            border: 0;
          }
          .rt-th:nth-child(3) {
            .rt-resizable-header-content {
              left:  -35px;
              position: relative;
              margin-right:  -35px;
            }
          }
        }
      }
    }
  }   
}

.modal-body.gateway-modal.dns-gateway {
  left: calc(50% - 33rem);
  width:  740px;
}

.add-custom-app-form.add-edit-dns-gateways {
    min-height: 320px;
  .form-sections-container {
    overflow: visible;
    .form-section {
      overflow: visible;
      padding: 16px 16px 30px;
      .g-row {  
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        width: 100%;
        &.extra-space {
          margin-bottom: 12px;
        }

          .select-container {
            min-width: 260px;
            margin-right: 28px;
            margin-bottom: 8px;
            border-radius: 8px;
            .my-select-with-search__control {
              border: none;
              background-color: var(--semantic-color-background-primary);
              max-height: 32px;
              min-height: 0;
            }
            .my-select-with-search__value-container {
              max-height: 30px;
              padding: 0;
              margin: 0;
            }
            .my-select-with-search__placeholder {
              padding: 0 8px;
            }
            .my-select-with-search__menu {
              .my-select-with-search__menu-list {
                background: var(--semantic-color-surface-base-secondary);
                .my-select-with-search__option {
                  color: var(--semantic-color-content-base-primary);
                  background: var(--semantic-color-surface-table-row-default);
                  &.my-select-with-search__option--is-selected {
                    color: var(--semantic-color-content-base-primary);
                    background: var(--semantic-color-surface-interactive-secondary-active);
                    &.my-select-with-search__option--is-focused {
                      background: var(--semantic-color-surface-interactive-secondary-active);
                    }
                  }
                  &.my-select-with-search__option--is-focused {
                    background: var(--semantic-color-background-secondary);
                  }
                }
              }
            }
          }
        // }
        &:first-child {
          .g-right{
            padding-left: 0;
          }
          .g-left {
            padding-right: 0;
          }
        }
        
        .g-fullwidth{
          padding: 12px 16px;
          width: 100%;
        }
        .g-left,.g-right {
          width: 48%;      
          padding: 12px 16px;
        }

        .input-container,.input-password-container {
          padding: 0;
          input {
            background-color: var(--semantic-color-background-primary);
            border: 1px solid var(--semantic-color-border-base-primary);
            border-radius: .25rem;
            color: var(--semantic-color-content-base-primary);
            cursor: text;
            display: block;
            height: 32px;
            padding: 9px 8px;
            text-align: left;
            min-width: 220px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &:active,&:focus {
              background-color: var(--semantic-color-surface-fields-active);
            }
            &:hover {
              background-color: var(--semantic-color-surface-fields-hover);
            }
            &:disabled {
              background: var(—surface-fields-disabled);
              color: var(--semantic-color-content-interactive-primary-disabled);
              border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
              cursor: not-allowed;
            }
          }
        }        
        .disabled-input {
          color: var(--semantic-color-content-interactive-primary-disabled);
          padding: 14px 0;
        }
        .g-right.failure-behavior {
          align-self: flex-start;
        }
    }
    .input-container .input-wrapper {
      margin-bottom: 8px;
    }
      .g-name, .gateway,
      .g-ip {
        width: 50%;
        float: left;
        .input-container {
          width: 50%;
        }
      }
      .form-left{
        float: left;
      }
      .form-right{
        float: right;
        padding-left: 5.3em;
      }
      label span {
        margin-left: 0;
        &.no-margin-bottom {
          margin-bottom: 0;
        }
      }
      .entity-dropdown .error-container {
        position: absolute;
      }
      .select-item.entity-dropdown {
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 0.3125rem; 
        .react-select__control {
          border: none;
          border: 1px solid var(--semantic-color-border-base-primary);
          background: var(--semantic-color-background-primary);
          border-radius: 8px;
          color: var(--semantic-color-content-base-primary);
          max-height: 32px;
          min-height: 0;
        }
        .react-select__value-container {
          max-height: 32px;
          min-height: 0;
        }
        .react-select__placeholder {
          padding-left: 8px;
        }
        .react-select__single-value {
          padding-left: 8px;
          line-height: 32px;
        }
        .ec-select__control {
          max-height: 32px !important;
          min-height: 24px !important;
          .ec-select__value-container {
            padding: 0 8px;
            max-height: 32px;
            margin-left: 4px;
            .ec-select__single-value {
              top: 56%;
            }
          }
        }

      }
      
      .row-parent {
        margin-bottom: 20px;
      }
      &.textarea-wrapper .input-container{
        width: 100% !important;
        float: left;
        .form-textarea {
          margin: 5px 2em .5em 0;
          resize: none;
        }
      }
      &.textarea-wrapper .input-container.error{
        width: 100% !important;
        float: left;
        .form-textarea {
          margin: 5px 2em .5em 0;
          resize: none;
          border-color:var(--semantic-color-content-status-danger-primary);
        }
      }
    }
  }
}
}
@import "scss/colors.scss";
@import 'scss/defaults.scss';
@import 'scss/mixins.scss';

.ec-root-page {
.lightest-gray-background {
  background-color: $lightest-gray-background;
  &.center {
    display: flex;
    align-items: center;
  }
  &.border {
   height: 50px !important;
   margin: 0 !important;
   border-bottom: 1px solid #DDE3EA;
   display: flex;
   align-items: center;
   }
 }
 .list-separator-border {
  height: 45px;
  border-bottom: 1px solid #DDE3EA;
  display: flex;
  align-items: center;
 }
 .list-separator-border-eventgrid {
  min-height: 60px!important;
  border-bottom: 1px solid #DDE3EA;
  display: flex;
  align-items: center;
  margin: 0!important;
 }

.main-container.partner-integrations-detail-container {
  background-color: #F7F9FA;
  display: flex;
  flex-direction: column;

  .azure-user-defined-tab-position {
    .config-container{
      position: fixed;
      top: 320px;
      right: 10px;
    }
  }

  .refresh-button {
    border: none;
    color: var(--semantic-color-content-interactive-primary-hover);
    background-color: var(--semantic-color-background-primary);
    .fa-arrows-rotate {
      margin-right: 4px;
    }
  }
  .actions-row {
    align-items: center ;
    .input-search-box {
      background-color: transparent;
      color: var(--semantic-color-content-base-secondary);
    }
    &.justify-content-flex-end {
      justify-content:flex-end;
    }
  }

  .header {
    // position: relative;
    width: 100%;
    font-size: 13px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0px;
    text-align: left;
  }
  .separator {
    height: 1px;
    background-color: #e0e1e3;
    margin-bottom: 16px;
    right: 0;
    left: 0;
  }
  .content-tabs-container {
    padding-top: 0;
  }
  .content {
    width: 100%;
    .action-refresh {
      justify-content: center;
      align-self: center;
      justify-self: center;
      position: relative;
      font-size: 13px;
      font-weight: 400;
      line-height: 20px;
      letter-spacing: 0em;
      text-align: left;
      padding-left: 16px;
      margin-top: 4px;
    }
    .content-box-flex {
      width: 100%;
      font: 14px Arial;
      display: flex;
      display: -webkit-flex;
      flex-direction: row;
      -webkit-flex-direction: row;
      box-sizing: border-box;
      min-height: 0em;
      // border: 1px solid #D6D7DB;
      border-radius: 8px;
      justify-content: space-between;
      margin-bottom: 16px;
      .left-border,.right-border {
        width: 48%;
      }
    }
    .key {
      float: left;
      min-height: 2em;
      color: #939393;
      font-size: 13px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 2em;
      width: 40%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
  }
    .value {
      float: right;
      min-height: 2em;
      color: var(--semantic-color-content-base-primary); 
      font-size: 13px;
      letter-spacing: 0.2px;
      line-height: 2em;
      width: 60%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-right: 10px;
    }
    div.key:hover {
      overflow: visible;
      white-space: pre-wrap;
      // background-color: #F7F9FA;
      color: #939393;
      }
    div.value:hover {
      overflow: visible;
      white-space: pre-wrap;
      // background-color: #F7F9FA;
      color: #939393;
      }
  }

  .drawer-container {
    .pagination-bar {
      .pagination-bar-left {
        width: 20%;
      }
      .pagination-bar-center {
        width: 30%;
      }
      .pagination-bar-left {
        width: 50%;
      }
    }
    .drawer{
      width: 800px;
      max-width: none;
      // .left,.right {
      //   max-width: none;
      // }      
    }
    .drawer-body {
      margin: 16px 32px;
      padding: 0;  
    }
    .drawer-header {
      max-height: 32px;
      margin: 16px 32px;
    }
    .header-details.flex-box {
      display: flex;
      align-items: center;
    }
    .drawer-header-icon {
      height: 20px;
      width: 20px;
      margin-right: 16px;
    }
    .drawer-header-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: left;
      color:#19191A;
    }
  } 
  .checkboxlink-cell-url {
    text-decoration: none;
    color: var(--semantic-color-content-status-info-primary);
  }
  .header-partner-integration-details {
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
    display: flex;
    align-items: center;
    .form-link-text.text-decoration-none {
      text-decoration: none;
    }
    .partner-label {
      color: var(--semantic-color-content-interactive-primary-default)
    } 
    .chevron-label {
      color:  var(--semantic-color-content-base-primary);
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 20px;
      margin:0 17px
    } 
    .account-label {
      color:  var(--semantic-color-content-base-primary);
    } 
  }
  .details-container, .details-container-table {
    background-color: var(--semantic-color-surface-base-primary);
    display: flex;  
    padding: 4px 12px 12px 12px;
    flex-direction: column;
    border: 1px solid var(--semantic-color-border-base-primary);
    // box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    .full-width {
      width: 100% ;
      float: none;
      display: flex;
      // flex-direction: column;
      margin-top: 8px;
      .info-detail {
        display: flex;
        .form-field-label {
          min-width: 130px;
          font-style: normal;
          font-weight: 400;
          font-size: 13px;
          line-height: 20px;
          color: #69696A;
        }
        .disabled-input {
          font-style: normal;
          font-weight: 400;
          font-size: 13px;
          line-height: 20px;
          color:  var(--semantic-color-content-base-primary);
        }
      }
    }
  }
  .details-container-table {
    margin-top: 16px;
    .header-large {
      font-size: 20px;
      font-weight: 500;
      line-height: 24px;
      letter-spacing: 0px;
      text-align: left;
      color:  var(--semantic-color-content-base-primary);
    }
    .container-row-cc-group {
      min-height: fit-content;
      margin-bottom: 0;
      .config-table-container {
        margin-bottom: 0;
      }
    }
  }
  .table-container .content .cell-container {
    cursor: default;
  }
}

.main-container.partner-integrations, .edgeconnector-page .edgeconnector-modal .partner-integrations-modal-content {
  padding: 19px 25px;
  .table-actions-container.group-filter {
    background-color: var(--semantic-color-surface-base-primary);
    margin-bottom: 16px;
    padding: 6px 6px 1px 12px;
    border-radius: 8px;
  }
  .filter-table-container{
    display: flex;
    align-items: center;    
    .form-field-label-wrapper { 
      min-width: fit-content;

    }
    .input-container {
      padding: 0;
    }
    .option-button-container.option-group-container {
      padding: 0;
      .option-buttons {
        min-width: 450px;
        .option-button {
          width: none;
        }
        label {
          justify-content: flex-start;
        }
      }
  }
  }
  .container-row-cc-group {
    min-height: fit-content;
    margin-bottom: 0;
    .config-table-container {
      margin-bottom: 0;
    }
  }
  .wizard-form .form-sections-container .form-section .disabled-input {
    margin-top: 0;
    margin-left: 0;
    color: #3F3F40;
    font-weight: 400;
  }
  .form-section.partner-configuration-form .external-id-buttons {
    min-height: 0;
  }
  .form-section.partner-configuration-azure-form  {
    min-width: 768px !important;
  }
  .partner-integration-table-container {
    .config-table-container { 
      max-height: none;
    }
    .ellipsis-menu {
      display: inline-flex;
      flex-direction: column;
      align-items: flex-start;

      border-radius: 4px;
      background: var(--deep-blue-surface-default-surface-00, var(--semantic-color-surface-base-primary));
      box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.16);
      button {
        border: none;
        display: flex;
        padding: 8px 12px;
        align-items: flex-start;
        gap: 10px;
        align-self: stretch;
        color: var(--semantic-color-content-status-info-primary);
        background-color: var(--semantic-color-surface-base-primary);
        &:first-child {
          border-bottom: 1px solid #E9E9E9;
        }
        &:hover {
          background: var(--deep-blue-surface-default-hover, #EEF8FF);
        }
      }
    }
    .delete-icon,
    .pencil-icon,
    .view-icon,
    .download-icon {
      margin: 0 5px;
    }
    .fa-caret-right,
    .fa-caret-down {
      margin: 0 5px;
      width: 15px;
    }
    .ReactTable.app-table {
      max-height: calc(100vh - 28rem);
    }
    .column-layout-config-container {
      border-left: 0;
    }
    .table-container .content .cell-container {
      border-right: none;
      cursor: default;
      .svg-inline--fa {
        cursor: pointer;
      }
    }
    .child-row {
      border-bottom: 1px solid white;
    }
    .checkboxlink-cell-url {
      text-decoration: none;
      color: var(--semantic-color-content-status-info-primary);
    }
    .config-table-container .cell-actions{
      float: right;
    }
    // .ReactTable.app-table {
    //   max-height: calc(100vh - 20rem);

    //   .rt-tbody {
    //     .rt-tr-group {
    //       .rt-tr {
    //         .rt-td {
    //           padding: 13px 18px;
    //           align-self: normal;
    //           height: auto;
    //           align-items: start;
    //           justify-content: start;
    //           flex-direction: column;
    //           span {
    //             &:last-child {
    //               padding-bottom: 0;
    //             }
    //               padding-bottom: 10px;
    //           }
    //         }
    //       }
    //     }
    //   }
    // }
  }
  .actions-row {
    align-items: center;
    &.space-between {
      display: flex;
      justify-content: space-between;
    }
    &.justify-content-flex-end {
      justify-content: flex-end;
    }
    .actions-items {      
      display: flex;
      justify-content: flex-start;
      padding-right: 4px;
      align-items: center;
      .sipg-fragment  { padding: 0 }
      .cloud-formation-options-link { 
        margin-left: 16px;
        .fa-download {
          width: 13px;
          height: 13px;
        }
      }
    }
    .select-item {
      margin-left: 16px;
      width: fit-content;
      min-width: 120px;
      .react-select__control{
        background: var(--semantic-color-background-primary);
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 4px;
        padding: 6px 12px;        
      }
      .react-select__menu {
        min-width: 250px;
        .react-select__menu-list {
          max-height: none;
        }
        .react-select__menu-list .react-select__option:first-of-type {
          border-bottom: 1px solid  var(--semantic-color-border-base-primary);
        }
      }
    }
  }
  .table-actions-container {
    background-color: var(--semantic-color-surface-table-row-default);
    padding: 12px;
  }
  .search-input-text {
    padding-left: 8px;
  }
  .search-box input {
    background-color: var(--semantic-color-background-pale);
    color: var(--semantic-color-content-base-secondary);
  }
  .search-box .fa-search, .search-box .fa-magnifying-glass {
    color: var(--semantic-color-content-base-secondary);
  }
  .wizard-form .form-sections-container .form-section .full-width {
    float: none;
  }
  .wizard-form .form-sections-container .form-section .form-field-label {
    margin-top: 0;
  }
  .wizard-form .form-sections-container .form-section.partner-configuration-form .form-field-label {
    margin-bottom: 0;
  }
  .wizard-form.review-page .form-sections-container {
    display: inline-block;
  }
  .wizard-form .form-sections-container .form-section {
    .input-container.review.full-width {
      margin: 0;
      height: fit-content;
      display: flex;
      // flex-direction: column;
      .region {
        min-height: fit-content;
      }
      .form-field-label-wrapper .form-field-label {
        margin-left: 0px;
        margin-right: 8px;
        width: 230px;
        color: #69696A;
        font-weight: 400;
      }
      .disabled-input {
        margin:0;
        font-style: normal;
        font-weight: 400;
        font-size: 13px;
        line-height: 20px;
        color:  var(--semantic-color-content-base-primary);
      }
    }
    .checkbox-container {
      .container { padding: 0 25px;}
      label span { min-width: 0;}
    }
    // label span {
    //   // min-width: 200px;
    // }
  }
  .wizard-form .form-sections-container .form-section .input-container {
    input {
      margin-top: 0;
      border: 1px solid var(--semantic-color-border-base-primary);
    }
    .external-id-buttons.trusted-account {
      float: left;
      .select-item {
        border-radius: 8px;
      }
    }
    &.full-width {  
      margin: 10px 0;
      height: auto;
      &.extra-margin-bottom {
        margin-bottom:  70px;
      }
      .disabled-input.trusted-account-id-value {
        width: fit-content;
        display: flex;
        align-items: center;
      }
      .disabled-input.event-bus-copy  {
        width: fit-content;
        white-space: nowrap;
        display: flex;
        align-items: center;
      }
      .svg-inline--fa {
        width: fit-content;
      }
      .svg-inline--fa.fa-arrows-rotate {
        color: var(--semantic-color-surface-base-primary);
        margin-right: 4px;
      }
      .svg-inline--fa.fa-copy {
        margin-right: 4px;
      }
    }
 }
 .wizard-form.partnerIntegration-region {
   .form-sections-container {
    overflow: visible;
   }
  .fa-info-circle-icon{
    margin-left: 4px;
    color: var(--semantic-color-content-status-info-primary);
  }
  .dropdown {
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 8px;
    margin-top: 4px;
    padding: 8px;
    background-color: var(--semantic-color-background-primary);
  }
 }
 .wizard-form.partner-configuration, .partner-acc-groups-page {
    // .form-sections-container {
    //  overflow: visible;
    // }
    .fa-info-circle-icon{
      margin-left: 4px;
      color: var(--semantic-color-content-status-info-primary);
    }
    .dropdown {
      border: none;
      border-radius: 8px;
      margin-top: 4px;
      padding: 4px 8px;
      background-color: var(--semantic-color-background-pale);
      min-height: 32px;
      .dropdown-display {
        min-width: 260px;
        width: 98%;
      }
    }
  }
}

.form-section.partner-configuration-form {
  display: flex;
  float: none;
  .color-optional {
    color:#69696A;

  }
  .fa-info-circle-icon{
      margin-left: 4px;
      color: var(--semantic-color-content-status-info-primary);
  }
  .svg-inline--fa .fa-arrows-rotate {
    color: var(--semantic-color-surface-base-primary);
  }
  .svg-inline--fa .fa-copy {
    color: #0275be;
  }
  .external-id-buttons {
    display: flex;
    min-height: 32px;
    float: right;
    .primary-button {
      align-items: center;
      display: flex;
      justify-content: center;
      width: 117px;
      background: var(--semantic-color-content-interactive-primary-default);
      border-radius: 4px;
      font-style: normal;
      font-weight: 500;
      font-size: 13px;
      line-height: 20px;
      text-align: center;
      color: var(--semantic-color-content-immutable-white);
      cursor: pointer;
    }
    .secondary-button {
      align-items: center;
      display: flex;
      justify-content: center;
      width: 75px;
      background: var(--semantic-color-background-primary);
      border-radius: 4px;
      font-style: normal;
      font-weight: 500;
      font-size: 13px;
      line-height: 20px;
      text-align: center;
      color: var(--semantic-color-content-status-info-primary);
      cursor: pointer;
    }
  }
}

.form-section.cloud-formation-container {
  display: flex;
  flex-direction: column;
  .cloud-formation-options {
      font-size: 13px;
      font-weight: 500;
      line-height: 20px;
      letter-spacing: 0px;
  } 
  .cloud-formation-dot {
    position: absolute;
    width: 6px;
    height: 6px;
    left: 0;
    top: calc(50% - 6px/2 + 1px);
    background: #69696A;
    border-radius: 10px;
  }
  .cloud-formation-options-link {
    position: relative;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
    padding-left: 16px;
    margin-top: 4px;
  }
  .cloud-formation-options-link-icon{
    width: 13px;
    height: 13px;
  }
  svg.svg-inline--fa.fa-xs.cloud-formation-options-link-icon.fa-external-link.fa-xs {
    margin-left: 8px;
  }
}

.edgeconnector-page.partner-integrations-page {
  &.partner-acc-groups-page {
    position: relative;
    overflow-y: auto;
    left: 0;
  }
  .back-to-ec.back-to-partner {
    .fa-arrow-left {
      position: initial;
      margin-right: 16px;
    }
  }
  .partner-integrations-form {
    .dropdown .dropdown-selector .dropdown-lists .dropdown-unselected .unselected-items {
      .checkbox-container.unselected{
        padding: 10px;
      }
    }
  }
  &.partner-acc-groups-page .dropdown .dropdown-selector .dropdown-lists .dropdown-unselected .unselected-items .unselected .container {
      padding: 0 25px;
  }
}

.main-container.partner-integrations, .main-container.partner-integrations-detail-container {
 // add filters
  .actions-row .select-item #CCActions .react-select__control {
    min-height: 35px;
    padding: 4.5px 12px;
  }  
 .filters {
  z-index: 1;
  margin: 10px 0;
  padding: 2px 12px;
  background: var(--semantic-color-background-primary);
  position: relative;
  .filters-section {
    width: 100%;
    min-height: 44px;
    padding: 0px;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -moz-flex;
    display: -webkit-flex;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    > div {
      padding-right: 8px;
      display: flex;
    }
    .filter-area {
      width: auto;
      margin:0;
      .add-filters {
        .drop-down-container{
          width:28px;
          height:28px;
          border-radius: 50%;
          padding:5px;
          background-color:var(--semantic-color-surface-base-primary);
          border-color:#99D8FF;
          margin-right: 8px;
          .fa-plus{
            position: relative;
            left: -4px;
            top: -5px;
            color: var(--semantic-color-content-interactive-primary-default);
            font-size: 16px;
            font-weight: 500;
          }
          .add-filter-list {
            max-width: none;
          }
          .add-filter-value {
            background: none;
            border: none;
          }
        }
      }
    }
  }
  .filter-action-buttons{
      display: flex;
      position: relative;
      right: 0px;
  }
  .reset-link {
      margin-left: 10px;
  }

  .apply-filters {
      box-shadow: none;
      padding:0px;
      height:28px;
  }


    .flex-box {
      justify-content: space-between;
    }
    .new-filter-component{
      .filter-name {
        font-size: 13px;
        float: left;
        padding: 0 10px;
      }
      .remove-filter-icon {
          color: var(--semantic-color-content-interactive-primary-hover);
          font-size: 14px;
          cursor: pointer;
      }
      .multi-select-filters {
          width: 90% !important;
          margin-top: 10px;
      }
      .drop-down-container{
        width:unset;
        border-radius: 40px;
        background-color: var(--semantic-color-surface-base-primary);
        border-color:#99D8FF;
        margin-right: 8px;
        .dropdown-box {
          max-width: inherit;
        }
        .drop-down-list {
          max-width: inherit;
          padding-left: 8px;
        }
      }
      .drop-down-selected-value{
        background-color: var(--semantic-color-background-primary);
        color:  var(--semantic-color-content-base-primary);
        width:unset;
        display:inline-block;
        box-shadow:none;
        max-width:280px;
        padding: 3px 2px 2px 2px;
        line-height: 13px;
        background: none;
        border: none;
        .label-selected-items,
        .dropdown-selected-label {
          width: calc(100%);
          padding-left: 8px;
          span {
            font-size: 13px;
            line-height: 20px;
            &.label-selected-value {
              color:  var(--semantic-color-content-base-primary);
            }
          }
        }
        .label-selected-items {
          font-weight: 600;
          font-size: 13px;
          line-height: 20px;
        }
        .dropdown-icon {
          padding-left: 0px;
          line-height: 22px;
          visibility: hidden;
          width: 15px;
        }
    }
    .remove-filter-icon{
        margin-right:5px;
        line-height: 28px;
        display: inline-block;
        vertical-align: top;
    }
  }
  .filter-area {
    .vdi-filter {
      display: inline-block;
      padding: 8px 0 10px 10px;
      min-height: 25px;
      .drop-down-selected-value {
        background-color: #f2faff;
        padding: 4px 12px;
        border-color: #99d8ff;
        margin-right: 8px;
        .dropdown-icon {
          float: none;
        }
        .dropdown-selected-label {
          width: auto;
        }
      }
      .drop-down-list.open {
        z-index: 1000;
      }
    }
  }
}
.table-filter-label {
  font-weight: 600;
}
}

.main-container.partner-acc-groups,  .main-container.partner-acc-accounts, .edgeconnector-page .edgeconnector-modal .branch-provisioning-modal-content.partner-integrations-modal-content.partner-acc-groups-modal-content {  
  padding: 19px 25px;
  flex-direction: column;
  min-height:  800px; 
  overflow-y: auto;

  .tabs {
    margin-bottom: 0;
    .tabs-items div {
      margin-bottom: 0;
    }
    .tabs-highlrighte{
      margin-bottom: 0;
    }
  }
  .folder-tabs.tabs  {
    margin-bottom: 30px;
  }
}

.modal-content.modal-body.branch-provisioning-modal-content.partner-integrations-modal-content.partner-acc-groups-modal-content {
  label span {
    margin-left: 0;
  }
  .input-container .max-width {
    padding-left: 0;
  }
  .fa-info-circle-icon{
    margin-left: 4px;
    color: var(--semantic-color-content-status-info-primary);
  }
  .dropdown {
    border: none;
    border-radius: 8px;
    margin-top: 4px;
    padding: 8px;
    background-color: var(--semantic-color-background-pale);
    min-height: 32px;
    .dropdown-display {
      min-width: 260px;
      width: 98%;
    }
  }
}
.modal-content {
  .partner-configuration-subscription-group{
    .container {
      padding: 0 2.5rem;
    }  
  }
}

.modal-overlay {
  .modal-body.modal-subscription-groups-add {
    min-width: 118rem;
    min-height: fit-content;
    left: calc(50% - 59rem);
    .modal-content {
      .container {
        padding: 0 2.5rem;
      }
      .wizard-form {
        .partner-configuration.configure-provisioning-template {
          min-height: 80rem;
        }
        .form-sections-container.form-sections-container-subscription-group {
          min-height: 80rem;
          min-width: 78rem;
          overflow: visible ;
        }
      }
    }
    .form-sections-container-subscription-group {
      .form-sectio-subscription-group {
        position: relative;
        display: flex;
        flex-direction: column;
        .dropdown {
          border: none;
          border-radius: 8px;
          margin-top: 4px;
          padding: 8px;
          background-color: #F1F2F3;
          .dropdown-display {
            min-width: 100%;
          }
        }
      }    
    }
  }
}


.edgeconnector-page.partner-integrations-page { 
  .partner-integrations-form.azure-form {
    .table-actions-container {
      .config-table-container {
        max-height: none;
      }
    }
    .table-row-menu-container .table-row-menu {
      .svg-inline--fa.fa-trash.delete-icon {
        margin-left: 6px;
      }
    }
  }
}

.edgeconnector-page .edgeconnector-modal .branch-provisioning-modal-content.partner-integrations-modal-content .adv-dropdown-with-async-search div.drop-down-container button.drop-down-selected-value .selected-value {
  min-width: fit-content;
}

.edgeconnector-page.partner-integrations-page.azure-form-page {
  position: relative;
  min-height: 960px;
  left: 0;
  padding: 20px 60px;
}

.edgeconnector-page.partner-integrations-page.azure-form-page .storage-prefix-max-width-100px{
  input {
    min-width: 0px !important;
    max-width: 100px !important;
  }
}

.main-container.partner-integrations .wizard-form .form-sections-container .form-section .input-container input, .edgeconnector-page .edgeconnector-modal .partner-integrations-modal-content .wizard-form .form-sections-container .form-section .input-container {
  #gcpZsAccEmail {
    min-height: 48px;
    max-height: 48px;
    min-width: 724px;
  }
  #gcpCustomerAccEmail {
    min-height: 48px;
    min-width: 724px;
  }
}
.z-index-4 {
  z-index: 4!important;
}
.z-index-3 {
  z-index: 3!important;
}
.z-index-2 {
  z-index: 2!important;
}


.wizard-nav {
  ul {
    @include DisplayFlex;
    justify-content: space-between;

    li {
      background: var(--semantic-color-surface-base-primary);
      display: inline-block;
      height: 50px;
      width: 33%;
      position: relative;

      &:before {
        content: " ";
        display: inline-block;
        height: 0;
        width: 0;
        position: absolute;
        border-style: solid;
        border-width: 25px 0 25px 10px;
        border-color: $transparent $transparent $transparent var(--semantic-color-background-primary);
        z-index: 0;
      }

      &:first-child:before {
        border-color: $transparent;
      }

      button {
        background: $transparent;
        border: 0;
        line-height: 50px;
        outline: none; // temp fix
        width: 100%;

        &:after {
          content: " ";
          display: inline-block;
          height: 0;
          width: 0;
          position: absolute;
          right: -10px;
          top: -0.5px;
          border-style: solid;
          border-width: 25px 0 25px 10px;
          border-color: $transparent $transparent $transparent var(--semantic-color-surface-base-primary);
          z-index: 1;
        }

        &:hover:not(:disabled) {
          background: $grey3;

          &:after {
            border-color: $transparent $transparent $transparent $grey3; 
          }
        }

        &:disabled {
          cursor: not-allowed;
        }

        .fa-layers {
          margin-right: 17px;

          .fa-circle {
            color: $grey48;
          }

          .fa-circle-dot {
            color: var(--semantic-color-surface-interactive-primary-default);
          }

          .fa-circle-check {
            color: var(--semantic-color-surface-interactive-primary-default);
          }

          .page-index {
            color: $white;
            top: -19px;
            left: 7px;
          }
        }
      }

      &.valid {
        background-color: $green7;

        button {
          &:after {
            border-color: $transparent $transparent $transparent $green7;
          }

          &:hover {
            background-color: $green7;

            &:after {
              border-color: $transparent $transparent $transparent $green7;
            }
          }

          .fa-circle {
            color: $green2;
          }

          .fa-check-circle {
            display: block;
          }

          .name {
            color: $green2;
          }
        }
      }

      &:last-of-type {
        button:after {
          border-width: 0;
        }
      }

      &.active {
        background: $white;

        button:hover {
          background-color: $white;
          cursor: default;

          &:after {
            border-color: $transparent $transparent $transparent $white;
          }
        }

        button {
          background: $white;
          z-index: 100;
        
          &:after {
            border-left-color: $white;
          }
        }
      }

      .name {
        color: $grey4;
        font-size: 13px;
      }
    }
  }
}


.wizard-nav {
  ul {
    @include DisplayFlex;
    justify-content: space-between;

    li {
      background: $grey14;
      display: inline-block;
      height: 50px;
      width: 33%;
      position: relative;

      &:before {
        content: " ";
        display: inline-block;
        height: 0;
        width: 0;
        position: absolute;
        border-style: solid;
        border-width: 25px 0 25px 10px;
        border-color: $transparent $transparent $transparent $white;
        z-index: 0;
      }

      &:first-child:before {
        border-color: $transparent;
      }

      button {
        background: $transparent;
        border: 0;
        line-height: 50px;
        outline: none; // temp fix
        width: 100%;

        &:after {
          content: " ";
          display: inline-block;
          height: 0;
          width: 0;
          position: absolute;
          right:  -10px;
          top: -0.5px;
          border-style: solid;
          border-width: 25px 0 25px 10px;
          border-color: $transparent $transparent $transparent $grey14;
          z-index: 1;
        }

        &:hover:not(:disabled) {
          background: $grey3;

          &:after {
            border-color: $transparent $transparent $transparent $grey3; 
          }
        }

        &:disabled {
          cursor: not-allowed;
        }

        .fa-layers {
          margin-right: 17px;

          .fa-circle {
            color: $grey48;
          }

          .fa-circle-dot {
            color: var(--semantic-color-surface-interactive-primary-default);
          }

          .fa-circle-check {
            color: var(--semantic-color-surface-interactive-primary-default);
          }

          .page-index {
            color: $white;
            top:  -19px;
            left: 7px;
          }
        }
      }

      &.valid {
        background-color: $green7;

        button {
          &:after {
            border-color: $transparent $transparent $transparent $green7;
          }

          &:hover {
            background-color: $green7;

            &:after {
              border-color: $transparent $transparent $transparent $green7;
            }
          }

          .fa-circle {
            color: $green2;
          }

          .fa-check-circle {
            display: block;
          }

          .name {
            color: $green2;
          }
        }
      }

      &:last-of-type {
        button:after {
          border-width: 0;
        }
      }

      &.active {
        background: $white;

        button:hover {
          background-color: $white;
          cursor: default;

          &:after {
            border-color: $transparent $transparent $transparent $white;
          }
        }

        button {
          background: $white;
          z-index: 100;
        
          &:after {
            border-left-color: $white;
          }
        }
      }

      .name {
        color:  var(--semantic-color-content-base-primary);
        font-size: 13px;
      }
    }
  }
}


.drawer-container.acc-group-sliding-drawer .drawer.right {
    min-width: 860px;
    padding: 0 32px;
    .content.account-groups-details-container {
        min-height: 600px;
        display: flex;
        flex-direction: column;
    }
    .drawer-header {
        height: 44px;
        color:  var(--semantic-color-content-base-primary);
        font-feature-settings: 'clig' off, 'liga' off;
        /* Header/M */
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px; /* 125% */
        .drawer-header-icon {
            margin-right: 8px;
        }
    }
    .container-in-line {
        .key {
            width: 120px;
            margin-right: 16px;
            color: var(--DeepBlue-Content-Default-Tertiary, #69696A);
            /* Paragraph/S | Regular */
            font-size: 13px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 153.846% */
        }
        .value {
            color:  var(--semantic-color-content-base-primary);
            /* Paragraph/S | Regular */
            font-size: 13px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 153.846% */
        }
    }
    .input-container {
        padding: 0;
    }
    .separator {
        height: 1px;
        background-color: #e0e1e3;
        margin: 20px 0;
        right: 0;
        left: 0;
    }
    .dropdown {
        border: none;
        border-radius: 8px;
        margin-top: 4px;
        padding: 4px 8px;
        background-color: #F1F2F3;
        min-height: 32px;
        .dropdown-display {
          min-width: 260px;
          width: 98%;
        }
    }
    .common-dropdown-wrapper .common-pill {
        margin: 16px 16px 16px 0;
    }

    .table-actions-container.group-filter {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .search-input-text {
            padding-left: 6px;
        }
    }
}


}
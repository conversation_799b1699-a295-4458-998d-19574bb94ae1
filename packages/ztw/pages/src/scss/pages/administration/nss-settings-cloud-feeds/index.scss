.ec-root-page {
.nss-title {
    height: 35px;
    width: 309px;
    #nss-settings-cloud-feeds-tab {
        p {
            height: 32px;
            font-weight: 500;
            letter-spacing: 0;
            line-height: 32px;
            white-space: nowrap;
        }
    }
}

.modal-body.nss-feeds-modal {
    left: calc(50% - 385px);
    width: 770px;
    top: 40px;
    .modal-body.nss-feeds-modal .modal-content {
        min-height: 18vh;
        max-height:  750px;
        overflow: visible;
    }
    .add-custom-app-form {
        .form-sections-container {
            overflow: inherit;
            padding: 0;
            max-height: none;
            overflow: visible;
            .form-field-label-wrapper {
                .form-field-label {
                    margin-bottom: 8px;
                    margin-left: 0px;
                }
            }
            .select-item {
                padding: 0;
                width:  330px;
            }
            .nss-feeds-text-area {
                margin: 0;
            }
        }
        .nss-formatting-block {
            .dropdown {
                width: 90%;
                .dropdown-display {
                    width: 29rem;
                }
                .dropdown-selector {
                    .container {
                        padding: 0 2.5rem;
                    }
                }
            }
        }
        .nss-cloud-feeds-filters-section {
            padding-top: 22px;
            padding-bottom: 50px;
        }
        .nss-feeds-filters-container {
            height: auto;
            min-height: 20vh;
            max-height: none;
            .dropdown {
                width: 90%;
                .dropdown-display {
                    width:  290px;
                }
                .dropdown-selector {
                    .container {
                        padding: 0 25px;
                    }
                }
            }
        }
      .form-footer {
        border-radius: 0 0 5px 5px;
        position: absolute;
        bottom:  320px;
        left: 0;
        width: 100%;
        margin-bottom: 60px;
      }
    }
}

.add-edit-nss-feeds {
    .input-container{
        width: 100% !important;
        float: left;
        .form-textarea {
          margin: 5px 2em .5em 0;
        }
        & {
            resize: none;
        }
    }
    .nss-feeds-dialog-radio-container {
        .radio-button-container {
            width: 60% !important;
        }
    }
}
.add-edit-cloud-nss-feeds {
    .input-container{
        width: 100% !important;
        float: left;
        .form-textarea {
          margin: 5px 2em .5em 0;
        }
        & {
            resize: none;
        }
    }
    .nss-feeds-dialog-radio-container {
        .radio-button-container {
            width: 60% !important;
        }
    }
    .form-sections-nss-container-parent {
        max-height:  690px;
        overflow: auto;
        .form-sections-container {
            .form-section {
                // .disabled-input {
                //     margin-top: 0;
                // }
                .input-container {
                    padding: 0;
                    input {
                      border: 1px solid var(--semantic-color-border-base-primary);
                      background: var(--semantic-color-background-pale);
                      border-radius: 8px;   
                      color: var(--semantic-color-content-base-secondary);
                      cursor: text;
                      display: block;
                      height: 32px;
                      padding: 9px 8px;
                      text-align: left;
                      min-width: 220px;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                      &:active,&:focus {
                        background-color: var(--semantic-color-surface-fields-active);
                      }
                      &:hover {
                        background-color: var(--semantic-color-surface-fields-hover);
                      }
                    }
                }
                .input-container.error input {
                    border-color: var(--semantic-color-border-status-danger-active);
                }
                .checkmark span {
                    left:  -2px;
                }
                .cloud-nss-max-batch-size-unit {
                    width: 100px;
                    text-align: center;
                    margin-right: 19px;
                    margin-top: 18px;
                    .select-item {
                        padding: 0;
                        width: 60px;
                    }
                }
                .hide-oauth2-flag {
                    visibility: hidden;
                }
                .reveal-show-hide-icon {
                    margin-left: -25px;
                    line-height: 32px;
                }
                .disabled-input {
                    color: var(--semantic-color-content-interactive-primary-disabled);
                    cursor: not-allowed;
                    margin-top: 11px;
                    margin-left: 7px;
                }
            }
        }
    }
    .add-new-key-value-pair-button {
        margin-bottom: 25px;
        .add-http-header-icon {
            color: #00578c;
            cursor: pointer;
            font-size: 16px;
            margin-right: 5px;
            position: relative;
            top: 1px;
        }
    }
}
.checkbox-container {
    #session-policy-action {
        .dropdown-selector {
            .dropdown-selected {
                height: 300px;
            }
            .dropdown-unselected {
                height: 300px;
            }
        }
    }
}
}
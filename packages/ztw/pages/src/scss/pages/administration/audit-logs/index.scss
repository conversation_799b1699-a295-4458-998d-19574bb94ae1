@import "scss/colors.scss";
@import "scss/mixins.scss";

.ec-root-page {
.main-container {
  padding: 19px 25px;
  &.audit-log-container {
    overflow-x: auto;
    min-height: 800px;
  }
}

.sipg-fragment {
  padding-top: 1.5em;
  padding-bottom: 10px;
}
.cloud-provider-wrapper {
  .app-table-container {
    .ReactTable.app-table {
      .rt-thead {
        .header-title {
          font-size: 13px;
          line-height: 30px;
          display: inline-block;
        }
      }
      .has-nested-true {
        .rt-thead {
          .header-title {
            font-size: 13px;
            line-height: 16px;
          }
          .rt-th:nth-child(1) {
            display: block;
            .sorting-icon-cont {
              display: none;
            }
          }
          .rt-th:nth-child(2) {
            padding: 0;
            border: 0;
          }
          .rt-th:nth-child(3) {
            .rt-resizable-header-content {
              left:  -35px;
              position: relative;
              margin-right:  -35px;
            }
          }
        }
      }
    }
  }   
}
.modal-overlay .audit-logs-modal {
  width:  800px !important;
}
// overwrite react table css
.audit-logs .ReactTable.app-table {
  max-height: calc(100vh - 28rem);
}

.cloud-provider-wrapper.audit-logs {
  // min-height: calc(100vh - 25rem);
  // margin-top: 122px;
  .data-error {
    position: relative;
    height: calc(100vh - 250px);
    > div {
      span {
        position: absolute;
        left: calc(50% - 100px);
        top: calc(50%);
      }
    }
  }
}
.no-items-message {
  justify-content: center;
  color:  var(--semantic-color-content-base-primary);
  height: 50px;
  align-items: center;
  font-size: 12px;
  font-style: italic;
}

/**ace diff**/
.acediff {
  position: relative;
  min-height: 300px;
}
.acediff__wrap {
  display: flex;
  flex-direction: row;
  position: absolute;
  bottom: 0;
  top: 0px;
  left: 0px;
  height: 100%;
  width: 100%;
  overflow: auto;
}

.acediff__gutter {
  flex: 0 0 60px;
  border-left: 1px solid #999;
  border-right: 1px solid #999;
  background-color: #efefef;
  overflow: hidden;
}
.acediff__gutter svg {
  background-color: #efefef;
}
.acediff__left,
.acediff__right {
  height: 100%;
  flex: 1;
}
.acediff__diffLine {
  background-color: #d8f2ff;
  border-top: 1px solid #a2d7f2;
  border-bottom: 1px solid #a2d7f2;
  position: absolute;
  z-index: 4;
}
.acediff__diffLine.targetOnly {
  height: 0px !important;
  border-top: 1px solid #a2d7f2;
  border-bottom: 0px;
  position: absolute;
}
.acediff__connector {
  fill: #d8f2ff;
  stroke: #a2d7f2;
}
.acediff__copy--right,
.acediff__copy--left {
  position: relative;
}
.acediff__copy--right div,
.acediff__copy--left div {
  color: #000;
  text-shadow: 1px 1px var(--semantic-color-surface-base-primary);
  position: absolute;
  margin: 2px 3px;
  cursor: pointer;
}
.acediff__copy--right div:hover {
  color: #004ea0;
}
.acediff__copy--left {
  float: right;
}
.acediff__copy--left div {
  right: 0px;
}
.acediff__copy--left div:hover {
  color: #c98100;
}

.modal-overlay .modal-body .modal-content .modal-footer {
  &.acediff-modal-footer {
    position: relative;
    margin-top: 0 ;
  }
}

.add-edit-sip {
  &.add-custom-app-form {
    .form-sections-container {
      .form-section { 
        margin-top: 10px;
      }
    }
  }
  .form-section {
    .sip-name,
    .sip-ip-address {
      width: 100%;
      float: left;
      .input-container {
        width: 100%;
      }
    }
    .dropdown-container,
    .checkbox-container {
      padding: 12px 0;
    }
    .bw-control-child {
      padding: 12px 0;
      .input-container {
        height: auto;
      }
    }
    .form-textarea {
      margin: 0;
      padding: 5px;
      resize: none;
    }
  }
  
}
.audit-logs {
  .select-item {
    margin-left: 0;
    margin-top: 10px;
    width: auto;
  }
  .css-bg1rzq-control {
    background-color: transparent;
  }
  .table-layout-header {
    position: relative;
  }
  .rdg-menu-editor {
    margin: 0;
  }
  .page-title {
    width: 100%;
    padding: 0 0 24px 0;
  }
  .dropdown {
    min-width: 120px;
    width: 120px;
  }
}
.add-edit-sip {
  width: 100%;
  min-width: 1200px;
  .ec-auditlogs-search {
    display: flex;
    width: 100%;
    flex-direction: row-reverse;
    .search-dropdown {
      width: 100%;
      padding: 0;
      .select-item {
        margin-top: 5px;
        .react-select__control {
          min-height: 32px;
          color: var(--semantic-color-content-base-secondary);
        }
      }
      .download {
        margin-top: .5em;
        margin-bottom: 1em;
        .download-button {
          margin-top: .5em;
        }
      }
    }
    .audit-log-search-container {
      .search-box {
        margin-top: 5px;
        margin-left: 10px;
      }
    }
  }
  .ec-auditlogs-filters {
    display: flex;
    width: 100%;
    .audit-filters-container {
      width: 64%;
      padding-left: 0;
      .dropdown {
        min-width: 120px;
        width: 120px;
        background-color: var(--semantic-color-background-primary);
        border-color: var(--semantic-color-border-base-primary);
        border-radius: 4px;
        border-style: solid;
        border-width: 1px;
        -webkit-box-align: center;
        min-height: 38.5px;
        position: relative;
        transition: 100ms;
        box-sizing: border-box;
        outline: 0px !important;
        margin-top: 14px;
        cursor: default;
        .dropdown-display {
          margin: 0 2px;
          color: var(--semantic-color-content-base-secondary);
          .dropdown-value {
            overflow: hidden;
            text-overflow: ellipsis;
            width: 90px;
            white-space: nowrap;
            margin-left: 2px;
          }
          .dropdown-icon {
            background-color: var(--semantic-color-background-primary);
            color: hsl(0,0%,80%);
            font-size: .7rem;
            border-left: 1px solid var(--semantic-color-border-base-primary);
          }
        }
      }
      .form-field-label-wrapper {
        .form-field-label {
          font-weight: normal;
          line-height: 21px;
          color: inherit;
        }
      }
      .entity-dropdown-container {
        .select-item {
          .audit_logs_category_dropdown {
            margin-top: 13px;
          }
          .audit_logs_interface_dropdown,
          .audit_logs_result_dropdown {
            margin-top: 13px;
          }
          .react-select__placeholder {
            color: var(--semantic-color-content-base-secondary);
          }
        }
      }
      .select-item {
        .react-select__control {
          .react-select__single-value {
            color: var(--semantic-color-content-base-secondary);
          }
        }
      }
      .filter-container {
        .dropdown-selector {
          background: var(--semantic-color-background-primary); 
          .dropdown-lists {
            .dropdown-unselected {
              .unselected-items {
                .unselected {
                  .container {
                    color: var(--semantic-color-content-base-primary);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.audit_logs_result_dropdown,
.audit_logs_interface_dropdown,
.audit_logs_timeframe_dropdown,
.audit_logs_category_dropdown {
  .react-select__menu {
    display: flex;
    position: absolute;
    color: var(--semantic-color-content-base-primary);
    top:  42px;
    left: 0;
    width: intrinsic;           /* Safari/WebKit uses a non-standard name */
    width: -moz-max-content;    /* Firefox/Gecko */
    width: -webkit-max-content; /* Chrome */
    width: max-content;
  }
  .react-select__option {
    padding-right: 20px;
  }
}
.audit_logs_result_dropdown {
  width:  70px;
}
.audit_logs_timeframe_dropdown {
  margin-top: 14px;
}
}
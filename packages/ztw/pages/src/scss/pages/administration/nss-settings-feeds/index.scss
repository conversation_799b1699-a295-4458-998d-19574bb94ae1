.ec-root-page {
.nss-title {
    height: 35px;
    width: 309px;
    #nss-settings-feeds-tab {
        p {
            height: 32px;
            font-weight: 500;
            letter-spacing: 0;
            line-height: 32px;
            white-space: nowrap;
        }
    }
}

.nss-feeds-page {
    margin-top: 35px;
}

.nss-feeds-wrapper {
    margin-top: 10px;
    .table-row-menu {
        svg {
            margin-right: 10px;
        }
    }
    .config-table-container {
        .more-button {
            color: var(--semantic-color-content-interactive-primary-default);
            cursor: pointer;
        }
        .multi-attribute-cell-fixed-height {
            div {
                white-space: normal;
                word-wrap: break-word;
                word-break: break-all;
            }
        }
        .disabled-test-connectivity-icon {
            color: #CACBCC;
            cursor: not-allowed;
        }
    }
}

.modal-body.nss-feeds-modal {
    left: calc(50% - 385px);
    width: 770px;
    top: 40px;
    .modal-content {
        min-height: 18vh;
        max-height:  750px;
        overflow: visible;
    }
    .add-custom-app-form {
        .form-sections-container {
            overflow: inherit;
            padding: 0;
            max-height: none;
            overflow: visible;
            .form-field-label-wrapper {
                .form-field-label {
                    margin-bottom: 8px;
                    margin-left: 0px;
                }
            }
            .select-item {
                padding: 0;
                width:  330px;
            }
            .nss-feeds-text-area {
                margin: 0;
            }
        }
        .nss-formatting-block {
            // min-height: 60vh;
        }
        .nss-feeds-filters-section {
            padding-top: 22px;
            padding-bottom: 50px;
        }
        .nss-feeds-filters-container {
            height: auto;
            min-height: 20vh;
            max-height: none;
            .dropdown {
                width: 90%;
                .dropdown-display {
                    width:  290px;
                }
                .dropdown-selector {
                    .container {
                        padding: 0 25px;
                    }
                }
            }
        }
      .form-footer {
        border-radius: 0 0 5px 5px;
        position: absolute;
        bottom:  320px;
        left: 0;
        width: 100%;
        margin-bottom: 60px;
      }
    }
}

.add-edit-nss-feeds {
    // .input-container {
    //     width: 100% !important;
    //     float: left;
    //     .form-textarea {
    //       margin: 5px 2em .5em 0;
    //     }
    //     resize: none;
    // }
    .nss-feeds-dialog-radio-container {
        .radio-button-container {
            width: 60% !important;
        }
    }
    .disabled-input {
        margin-left: 7px !important;
    }
    // .form-sections-container {
    //     .form-section {
    //         .disabled-input {
    //             margin-left: 7px !important;
    //         }
    //     }
    // }
}

.marginTop13 {
    margin-top: 13px !important;
}

.marginTop22 {
    margin-top: 22px !important;
}

.marginRight100Perc {
    margin-right: 100%;
}

.add-edit-nss-feeds {
  .form-sections-nss-container-parent {
    max-height:  690px;
    overflow: auto;
    .form-sections-container {
      .form-section {
        padding: 16px 16px 30px;
        .g-name, .gateway,
        .g-ip {
          width: 50%;
          float: left;
          .input-container {
            width: 50%;
          }
        }
        .form-left{
          float: left;
        }
        .form-right{
          float: right;
          padding-left: 5.3em;
        }
        label span {
          margin-left: 0;
        }
        .row-parent {
          margin-bottom: 20px;
        }
        .textarea-wrapper .input-container{
          width: auto;
        }
      }
    }
  }

  }

  .disabled-input {
    color: var(--semantic-color-content-interactive-primary-disabled);
    margin-top: 0px;
  }

  .nss-feeds-dialog-radio-container {
    padding: 5px 0;
  }

  .nss-feeds-filters-section {
    margin-bottom: 20px;
  }

  .nss-feeds-filters-container {
    max-height: none;
  }

  .checkbox-container {
    #session-policy-action {
        .dropdown-selector {
            .dropdown-selected {
                height: 300px;
            }
            .dropdown-unselected {
                height: 300px;
            }
        }
    }
}
  
}
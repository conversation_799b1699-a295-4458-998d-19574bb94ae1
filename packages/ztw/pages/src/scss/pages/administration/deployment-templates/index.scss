.ec-root-page {
.main-container {
  padding: 1.9rem 2.5rem;
  .as-title {
    font-weight: 400;
    font-size: 2rem;
  }
  .as-link {
    font-weight: 400;
    // font-size: 1.4rem;
    padding: 0.5rem
  }
  .as-icon {
    min-height: 21px;
    margin-right: 8px;
  }
}

.automation-scripts-wrapper {
  .app-table-container {
    .ReactTable.app-table {
      .rt-thead {
        .header-title {
          font-size: 13px;
          line-height: 30px;
          display: inline-block;
        }
      }
      .has-nested-true {
        .rt-thead {
          .header-title {
            font-size: 13px;
            line-height: 16px;
          }
          .rt-th:nth-child(1) {
            display: block;
            .sorting-icon-cont {
              display: none;
            }
          }
          .rt-th:nth-child(2) {
            padding: 0;
            border: 0;
          }
          .rt-th:nth-child(3) {
            .rt-resizable-header-content {
              left:  -35px;
              position: relative;
              margin-right:  -35px;
            }
          }
        }
      }
    }
  }
  ul {
    li {
      list-style: none;
      padding: 10px 0;
      color: var(--semantic-color-content-interactive-primary-default);
      font-weight: 500;
      line-height: 20px;
      cursor: pointer;
      display: table;
      &:hover {
        opacity: .8;
      }
      &:active {
        opacity: 1;
      }
      a {
        text-decoration: none;
        color: var(--semantic-color-content-interactive-primary-default);
      }
      .fa-download {
        margin-right: 8px;
      }
    }
  }
  .as-title {
    font-size: 1.4rem;
    font-weight: 300;
    padding: 1rem 0;
    color: #1a1a1a;
  }
}

.deployment-template-depreciated {
  padding: 16px;
  background: var(--semantic-color-background-primary);
  color: var(--semantic-color-content-base-primary);
  margin-bottom: 16px;
  font-size: 16px;
  cursor: default;
  border-radius: 8px;
}
// .add-custom-app-form .deployment-template-depreciated  .form-sections-container .form-section {
  .add-custom-app-form.deployment-template .form-sections-container .form-section {
  margin-left: 0;
  .form-sections-container-title {
    display: flex;
    color: var(--semantic-color-content-base-primary);
  }
}

.dark {
  .cluster-child-icon.as-icon.aws-logo {
    content: url(/images/aws-logo-dark-mode.png);
  }
}
}
.ec-root-page {
.main-container.ip-fqdn-container{
  padding: 19px 25px;
  width: 100%
}

.sip-add-new-btn {
  margin-bottom: 10px;
}

.ip-fqdn-dst-ip-groups .config-table-container .table-container {
  max-height: calc(100vh - 320px);  
}

.cloud-provider-wrapper {
  .app-table-container {
    .ReactTable.app-table {
      .rt-thead {
        .header-title {
          font-size: 13px;
          line-height: 30px;
          display: inline-block;
        }
      }
      .has-nested-true {
        .rt-thead {
          .header-title {
            font-size: 13px;
            line-height: 16px;
          }
          .rt-th:nth-child(1) {
            display: block;
            .sorting-icon-cont {
              display: none;
            }
          }
          .rt-th:nth-child(2) {
            padding: 0;
            border: 0;
          }
          .rt-th:nth-child(3) {
            .rt-resizable-header-content {
              left:  -35px;
              position: relative;
              margin-right:  -35px;
            }
          }
        }
      }
    }
  }   
}

// overwrite react table css

.destination-ip-group {
  .delete-icon,
  .pencil-icon,
  .view-icon,
  .download-icon {
    margin: 0 10px;
  }
  .ReactTable.app-table {
    max-height: calc(100vh - 21rem);
  }
  .column-layout-config-container {
    border-left: 0;
  }
}

.add-edit-dstnform.add-custom-app-form {
  .form-sections-container {
    overflow: visible;
    .form-section {
      margin-top: 10px;
      margin-bottom: 50px;
      background-color: var(--semantic-color-surface-base-primary);      
      max-height: inherit;
      overflow: auto;
      .sip-name,
      .sip-ip-address {
        width: 100%;
        float: left;
        .input-container {
          width: 100%;
        }
      }
      .desc-wrapper.full-width{
        margin-bottom: 30px;
      }
      .dropdown-container,
      .checkbox-container {
        padding: 12px 0;
      }
      .bw-control-child {
        padding: 12px 0;
        .input-container {
          height: auto;
        }
      }
      .form-textarea {
        margin: 0;
        padding: 5px;
        resize: none;
      }
      .full-width,
      .radio-button-container {
        margin-bottom: 20px;
        &.form-field-wrapper {
          margin-bottom: 5px;
        }
      }
      .radio-button-container {
        .radio-buttons {
          .radio-button {
            label {
              padding: 8px 10px;
            }
          }
        }
      }
      .input-container .input-label {
        margin-top: 0;
      }
      .form-field-label-wrapper {
        .form-field-label {
          margin-left: 0;
        }
      }
      .dest-ip-row {
        margin-bottom: 12px;
        &.full-width {
          .half-width {
            width: 50% !important;
          }
          .input-container {
            .input-wrapper {
              width: auto;
            }
          }
        }
        .list-builder {
          .list-builder-header {
              .list-builder-input-textarea {
                width: 100%;
              }
            }
          .list-builder-body  {
            width: calc(100% - 110px);
          }
        }
      }
    }
    .category-wrapper {
      position: relative;
      left: auto;
      top: auto;
      width: 48% !important;
      .form-field-label-wrapper {
        margin-top: 0;
      }
    }
    .country-wrapper {
      position: relative;
      left: auto;
      top: auto;
      width: 48% !important;
      .form-field-label-wrapper {
        margin-top: 0;
      }
    }
    .desc-wrapper {
      margin-top: 20px;
    }
  }
  .form-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    border-radius: 0 0 8px 8px;
  }
}

.modal-overlay {
  .destination-ip-form.modal-body {
  //   width:  800px;
  //   left: calc(50% - 38.5rem);
  background-color: var(--semantic-color-background-primary);
  border-radius: 8px;
  .modal-content {
      background-color: var(--semantic-color-surface-base-primary);
      position: unset;
      overflow: visible;
      label span {
        margin-left: 0;
      }
    }
  }
}

#ipCategories.dropdown,
#countries.dropdown {
  // width: 95%;
  .dropdown-selector .dropdown-lists .dropdown-unselected,
  .dropdown-selector .dropdown-lists .dropdown-selected {
    // height: 260px
    .selected-item,
    .unselected-items {
      // height: 180px
      .unselected {
        padding: 5px 18px 5px 16px;
        .container {
          padding: 0 0 0 30px;
          .checkmark span {
            margin-left: 0px;
          }
        }
      } 
    } 
  } 
}

.wizard-form .form-sections-container .form-section {
  width: calc(100% - 36px);
}

}
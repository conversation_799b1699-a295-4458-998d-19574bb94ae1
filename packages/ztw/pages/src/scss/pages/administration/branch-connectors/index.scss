@import "scss/colors.scss";

.ec-root-page {
.main-container {
  padding: 19px 25px;

  .component-header-cc-group {	
    height: 24px;
    width: 450px;
    color: var(--semantic-color-content-base-primary);
    font-size: 24px;
    font-weight: 500;
    line-height: 24px;
    text-align: left;
    float: left;
    margin-bottom: 16px;
  }
  .header {
    width: 100%;
    float: left;
  }

  .container-row-cc-group {
    margin: 30px 0;
    margin-top: 10px;
    min-height: 900px;
    .config-table-container {
      margin-bottom: 50px;
    }
  }
  .header {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    .configuration-nav-tab {
      width: 100%;
    }
    h2 {
      flex-grow: 1;
    }
    .search-container {
      flex-grow: 0;
      padding: 0;
      height: 10px;
    }
  }
  .actions-row {
    display: flex;
    justify-content: space-between;
    padding-right: 4px;
    div:only-child {
      align-self: flex-end;
      margin-left: auto;
    }
    .schedule-upgrade-calendar-icon{
      margin-right: 8px;
    }
    .bulk-upgrade-button{
      height: 30px;
      padding: 4px 20px;
      background: var(--semantic-color-content-interactive-primary-default);
      border: 1px solid var(--semantic-color-content-interactive-primary-default);
      border-radius: 5px;
      color: var(--semantic-color-surface-base-primary);
      cursor: pointer;
      display: inline-block;
      vertical-align: top;    
      font-size: 13px;
      line-height: 18px;
      max-width: 480px;    
      min-width: 81px;
      padding: 4px 20px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .config-buttons {
      flex-grow: 1;
      align-items: flex-start;
      justify-content: space-around;
      a,
      button {
        margin-right:  23px;
      }
    }
    .toggle-container {
      flex-grow: 0;
    }
    .search-container-cc-group {
      flex-grow: 0;
      padding: 0;
    }
  }
}

// overwrite react table css
.bc-connectors {
  .delete-icon,
  .pencil-icon,
  .view-icon,
  .download-icon {
    margin: 0 10px;
  }
  .fa-caret-right,
  .fa-caret-down {
    margin: 0 5px;
    width: 15px;
  }
  .ReactTable.app-table {
    max-height: calc(100vh - 28rem);
  }
  .column-layout-config-container {
    border-left: 0;
  }
  .table-container .content .cell-container {
    border-right: none;
  }
  .child-row {
    border-bottom: 1px solid white;
  }
  .ReactTable.app-table {
    max-height: calc(100vh - 20rem);
    .rt-tbody {
      .rt-tr-group {
        .rt-tr {
          .rt-td {
            padding: 13px 18px;
            align-self: normal;
            height: auto;
            align-items: start;
            justify-content: start;
            flex-direction: column;
            span {
              padding-bottom: 10px;
              &:last-child {
                padding-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
}

.branch-connector-group-form  {
  // overflow: auto;
  .form-sections-container {
    padding: 19px 25px;
    background: var(--semantic-color-background-primary);
    padding: 16px 24px;
    width: 100%;
    // max-height: 630px;
    overflow: hidden;
    .form-section-label {
      padding: 0px;
      color:  var(--semantic-color-content-base-primary);
      font-size: 13px;
      letter-spacing: 1px;
      line-height: 20px;
      margin-bottom: 8px;
      text-transform: uppercase;
      width: 100%;
    }
    .form-section-version {
      display: inline-block;
      flex-direction: column;
      background: var(--semantic-color-background-primary);
      width: 100%;
      padding: 16px 0px;
      .half-width {
        width: 47% !important;
        float: left; 
        padding: 0px;
      }
    }
    .form-section {
      display: inline-block;
      flex-direction: column;
      background-color: var(--semantic-color-surface-base-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 5px;
      width: 100%;
      // margin-bottom: 24px;
      padding: 16px;

      .half-width {
        width: 47% !important;
        float: left; 
        padding: 0px;
      }
      .input-container {       
        padding-left: 0;
        .cc-status {
          padding-left: 0;
        }
      }

      .form-field-label-wrapper {
        display: block;         
      }
      .filter-container .select-item {
        margin: 0px;
        #ziaTunnelMode, #zpaUserTunnel, #dnsCache, #failOpen {
          .react-select__control {
            // border: none; 
            border-radius: 5px;
            background-color: var(--semantic-color-background-primary);
          }
          .react-select__single-value {
            color:  var(--semantic-color-content-base-primary);
            font-size: 13px;
            letter-spacing: 0;
            line-height: 20px;
            margin-left: 8px;
          }
        }
      }

        .form-field-label-wrapper .form-field-label {
          margin: 0px;
          color: var(--semantic-color-content-base-primary);
          font-weight: 500;
          line-height: 20px;
          display: inline-block; 
        }
        .form-field-label-wrapper .form-field-label:hover + .rTooltip {
          display: block; 
        }
        .form-field-label-wrapper .top-right.rTooltip {
          top: auto;
          left: auto;
          max-width:  550px;
          min-width:  450px; 
        }
        .form-field-label-wrapper .form-field-tooltip-container {
          background: var(--semantic-color-background-primary);
          color:  var(--semantic-color-content-base-primary);
          border: 1px solid var(--semantic-color-border-base-primary);
          max-width: 550px;
          min-width: 260px;
          overflow: hidden;
          padding: 12px 16px;
          word-wrap: break-word; 
          white-space: break-spaces;
        }
        .disabled-input {
          color: var(--semantic-color-content-interactive-primary-disabled);
          cursor: not-allowed;
          margin-top: 8px; 
          margin-bottom: 26px;          
          overflow-wrap: break-word;
        }
        .disabled-input.no-margin-bottom {
          margin-bottom: 0px;            
        }
        .disabled-input.margin-botton-12px {
          margin-bottom: 12px;
        }
      
      .form-value-label {
        color: #939393;
        margin-top: 12px; }

      .cc-title-select-upgrade-window{
        color: var(--semantic-color-content-base-primary);
        font-size: 13px;
        letter-spacing: 0;
        line-height: 20px;
        margin-bottom: 4px;
      }
      .cc-upgrades-will-be-scheduled {
        color: #656666;
        font-size: 11px;
        letter-spacing: 0;
        line-height: 20px;
        margin-top: 4px;
      }
      .cc-upgrades-will-be-scheduled-location {
        color: var(--semantic-color-content-base-primary);
        font-size: 13px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 20px;
      
      }
      .cc-upgrades-will-be-scheduled-location .information-icon {
        color: var(--semantic-color-content-status-info-primary);
      }  
      .cc-upgrades-version {
        margin-top: 8px;
        display: flex;
        .cc-upgrades-version-row {
          justify-content: space-between;
          .cc-upgrades-version-item {
            width:50%
          }
        }
      }    
    }


    .cc-schedule-time-container {
      display: flex;
      align-items: center;
      &.cc-schedule-time-container-weekday {
        display: inherit;
        margin-bottom: 12px;
      }
      .cc-schedule-time-container-from {
        display: inline-flex;
        margin-right: 12px;
      }
      .cc-schedule-time-separator {
        height: 20px;
        width: 7px;
        color: var(--semantic-color-content-base-primary); 
        font-size: 13px;
        letter-spacing: 0;
        line-height: 20px;
      }
      .cc-schedule-time-container-to {
        display: inline-flex;
        margin-left: 20px;
      }
      .cc-schedule-time-combo, .filter-container {
        margin-right: 8px;
        .select-item, .dropdown {
          height: 32px;
          width: 54px;
          border-radius: 5px;
          background-color: var(--semantic-color-background-primary);
          margin: 0px;
          margin-right: 0px;
          border: none;
          .react-select__control,.dropdown-display { 
            border: 1px solid var(--semantic-color-border-base-primary);
            border-radius: 0.3125rem;   
            box-shadow: none;   
            background: var(--semantic-color-background-primary); 
            color: var(--semantic-color-content-base-primary);
            cursor: text;
            padding: 0px;
            min-height: auto;
            width: 100%;
            height: 32px;
            text-align-last: center;
            .dropdown-value{
              align-self: center;
            }
            .dropdown-icon{
              font-size: 12px !important;
            }
          .react-select__single-value {
            width: 21px;
            color:  var(--semantic-color-content-base-primary);
            font-size: 13px;
            letter-spacing: 0;
            line-height: 20px;
          }
          .react-select__single-value--is-disabled{
            color:  var(--semantic-color-content-interactive-primary-disabled);
          }
          .react-select__placeholder{
            width: 24px;
            color:  var(--semantic-color-content-base-secondary);
            font-size: 13px;
            letter-spacing: 0;
            line-height: 20px
          }
          }
        }
      }  
      .cc-schedule-time-combo.cc-schedule-weekday-combo {
        margin-right: 8px;
        width: 100%;       
        .select-item, .dropdown {
          width: 100%;        
          .react-select__single-value {
            width: 100%;
            text-align-last: left;
            margin-left: 8px;
          }
          .react-select__placeholder{
            width: 100%;
          }
        }
      }
    }
  }
}

// .container-row-cc-group .config.table-container {
  // input[type="checkbox"] {
  //   height: 10px;
  //   width: 10px;
  //   vertical-align: middle;
  //   margin: 0 0.4em 0.4em 0;
  //   border: 1px solid var(--semantic-color-content-interactive-primary-default);
  
  //   -webkit-appearance: none;
  //   -webkit-transition: box-shadow 200ms;  
  // }
  // input[type="checkbox"] {
  //   -webkit-border-radius:2px;
  //   border-radius:2px;
  // }
  // input[type="checkbox"]:not(:disabled):hover {
  //   border-color:var(--semantic-color-content-interactive-primary-default);
    
  // }
  // input[type="checkbox"]:active:not(:disabled) {
  //   border-color:var(--semantic-color-content-interactive-primary-default);
  // }
  // input[type="checkbox"]:checked {
  //   border-color:var(--semantic-color-content-interactive-primary-default);
  //   background-color: var(--semantic-color-content-status-info-primary);
  // }
  // input[type="checkbox"]:checked:before {
  //   content: '';
  //   display: block;
  //   width: 4px;
  //   height: 8px;
  //   border: solid var(--semantic-color-surface-base-primary);
  //   border-width: 0 2px 2px 0;
  //   -webkit-transform: rotate(45deg);
  //   transform: rotate(45deg);
  //   margin-left: 4px;
  //   margin-top: 1px;
  //   }

  //   /* disabled input */
  // input:disabled {
  //   opacity: .6;
  //   box-shadow: none;
  //   background: rgba(0, 0, 0, 0.1);
  //   box-shadow:none;
  //   }
    
  //   /* style label for disabled input */
  //   input:disabled + label {
  //   opacity: .6;
  //   cursor:default;
  //   -webkit-user-select: none;
  //   }

// }

.schdule-upgrade {
  .modal-content {
    max-height: 80vh;
  }
}

.main-container.cc-group {
  .select-item{
    margin-left: 0px;
    .react-select__control {
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 5px;
      background-color: var(--semantic-color-content-status-info-primary);
      min-height: 32px;
      width: 145px;
    }
    .react-select__indicator, .react-select__value-container {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .react-select__placeholder {
      color: var(--semantic-color-content-immutable-white) !important;
    }
    .react-select__dropdown-indicator{
      color: var(--semantic-color-content-immutable-white);
      padding-right: 8px;
    }
  }
}
}
@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

.main-container {
  padding: 19px 25px;

  .container-row{
    margin: 0.425rem 0;
  }
  
}

@media screen and (max-width: $templateMediaSize) {
  .main-container>.pie-container {
    flex-direction: column;
    width: 100%;
  }
}


.ec-root-page {
  textarea {
    font-family: inherit;
  }
  .full-width {
    width: 100% !important;
    float: left;
  }
  .half-width {
    width: 47% !important;
    float: left;
    &.with-border {
      border: 1px solid var(--semantic-color-border-base-primary);
    }
  }
  .margin-right-2 {
    margin-right: 2%;
  }
  .margin-left-2 {
    margin-left: 2%;
  }
  .edgeconnector-modal {
    // position: absolute;
    // top: 0;
    // left: 90px;
    // background: $white;
    // height: auto;
    // min-height: 100%;
    // width: calc(100vw - 90px);
    // padding: 25px 50px 100px;
    // box-sizing: border-box;
    // z-index: 1;
    // display: flex;
    // flex-flow: column;

    .modal-content {
      margin-top: 30px;
      display: flex;
      width: 100%;
      padding: 0;
      float: left;
      align-items: stretch;
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 10px;
      position: relative;
      min-height:  500px;
      .wizard-nav {
        width: 25%;
        float: left;
        border-right: 1px solid var(--semantic-color-border-base-primary);
        ul {
          display: -webkit-box;
          display: -moz-box;
          display: -ms-flexbox;
          display: -webkit-flex;
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          list-style: none;
          padding-top: 20px;
          li {
            width: 100%;
            background: none;
            .name {
              color:  var(--semantic-color-content-base-primary);
              line-height: 50px;
            }
            &.active {
              &:after {
                content: " ";
                display: inline-block;
                height: 100%;
                position: absolute;
                border-width: 0 3px 0 0;
                border-style: solid;
                border-color:var(--semantic-color-surface-interactive-primary-default);
                z-index: 1;
                top: 0;
                right: 0;
              }
              button {
                background-color: $blue22;
                color: var(--semantic-color-surface-interactive-primary-default);
                &:after {
                  border-color: var(--semantic-color-surface-interactive-primary-default);
                }
                .fa-dot-circle {
                  font-size: 15px;
                }
                .name {
                  color:  var(--semantic-color-content-base-primary);
                  font-weight: 500;
                  line-height: initial;
                }
              }
            }
            &.valid {
              button {
                &.fa-circle {
                    color: var(--semantic-color-surface-interactive-primary-default);
                }
                &:after {
                  border-color: var(--semantic-color-surface-interactive-primary-default);
                }
                .fa-circle {
                    color: var(--semantic-color-surface-interactive-primary-default);
                }
              }
            }
            &:last-of-type button:after {
              border-width: 0;
            }
            &:before {
              display: none;
            }
            button {
              text-align: left;
              padding-left: 20px;
              .fa-layers {
                .fa-circle {
                  font-size: 15px;
                }
              }
              &:hover {
                background-color: $white;
                &:not(:disabled) {
                  background-color: $blue22;
                }
              }
              &:after {
                content: " ";
                display: inline-block;
                height: 0;
                width: 0;
                position: absolute;
                border-width: 37px 0 0 0;
                border-style: solid;
                width: 1px;
                border-color: var(--semantic-color-surface-interactive-primary-default);
                z-index: 1;
                top: 32px;
                left: 28px;
              }
            }
          }
        }
      }
      .wizard-form {
        width: 75%;
        float: left;
        padding: 0;
        .form-sections-container {
          max-height: none;
          border: none;
          padding: 3% 4%;
          height: 100%;
          .form-section-label {
            padding: 22px 24px 22px 0;
          }
          .form-section {
            box-shadow: none;
            padding: 0;
            margin: 0;
            .dropdown-container {
              width: 100%;
              margin-top:  23px;
              overflow: hidden;
              .form-field-label {
                margin-top: 0;
              }
            }
            .image-wrapper {
              display: flex;
              flex-direction: row;
            }
            .input-container {
              &.review {
                .disabled-input {
                  float: left;
                  width: 90%;
                }
                .svg-inline--fa {
                  width: 10%;
                  cursor: pointer;
                  color: $blue23;
                }
              }
              &.small-width {
                width: 50px;
              }
              &.disabled {
                input:disabled {
                  border-bottom-color: var(--semantic-color-border-interactive-primary-disabled);
                  cursor: not-allowed;
                }
              }
            }
            .input-container,
            .select-item {
              width: 48%;
              height: 30px;
              margin-left: 0;
              margin-right: 2%;
            }
            
            .section-cloud {
              display: block;
              padding-bottom: 20px;
              width: 100%;
              overflow: hidden;
              p {
                font-size: 13px;
                line-height:  24px;
                text-align: left;
                color: $grey1;
                font-weight: 500;
              }
              &.fade-in {
                .image-radio-button-container .radio-buttons .radio-button label {
                  opacity: 1;
                }
              }
            }
          }
        }
      }
    }  
  }


.image-radio-button-container {
  .radio-buttons {
    @include DisplayFlex;

    &.disabled {
      .radio-button {
        label {
          background: var(—surface-fields-disabled);
          color: var(--semantic-color-content-interactive-primary-disabled);
          border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
          cursor: not-allowed;          
        }

        &.checked-true {
          label {
            background: var(--semantic-color-content-interactive-primary-default);
            color: var(--semantic-color-background-primary);
          }
        }
      }
    }

    .radio-button {
      &.checked-true {
        label {
          border: 3px solid var(--semantic-color-content-interactive-primary-default);
          border-radius: 5px;
          margin: 10px;
        }
      }
      &:first-child {
        label {
          margin-left: 0;
        }
      }
      label {
        @include DisplayFlex;
        cursor: pointer;
        position: relative;
        padding: 4px 20px 4px 5px;
        border: 1px solid $grey7;
        color: var(--semantic-color-content-interactive-primary-default);
        align-items: center;
        justify-content: center;
        margin: 10px;
        width: 178px;
        height:  106px;
        border-radius: 5px;

        input {
          display: none;
        }
        img {
          width: 100%;
          height: auto;
        }
      }
    }
  }
}

.page .wizard-nav {
  ul {
    @include DisplayFlex;
    justify-content: space-between;

    li {
      background: $grey14;
      display: inline-block;
      height: 50px;
      width: 33%;
      position: relative;

      &:before {
        content: " ";
        display: inline-block;
        height: 0;
        width: 0;
        position: absolute;
        border-style: solid;
        border-width: 25px 0 25px 10px;
        border-color: $transparent $transparent $transparent $white;
        z-index: 0;
      }

      &:first-child:before {
        border-color: $transparent;
      }

      button {
        background: $transparent;
        border: 0;
        line-height: 50px;
        outline: none; // temp fix
        width: 100%;

        &:after {
          content: " ";
          display: inline-block;
          height: 0;
          width: 0;
          position: absolute;
          right:  -10px;
          top: -0.5px;
          border-style: solid;
          border-width: 25px 0 25px 10px;
          border-color: $transparent $transparent $transparent $grey14;
          z-index: 1;
        }

        &:hover:not(:disabled) {
          background: $grey3;

          &:after {
            border-color: $transparent $transparent $transparent $grey3; 
          }
        }

        &:disabled {
          cursor: not-allowed;
        }

        .fa-layers {
          margin-right: 17px;

          .fa-circle {
            color: $grey4;
            font-size:  22px;
          }

          .fa-check-circle {
            left: 15px;
            bottom: 18px;
            border: 2px solid $white;
            border-radius: 16px;
            color: $green2;
            display: none;
            font-size: 16px;
          }

          .page-index {
            color: $white;
            top:  -19px;
            left: 7px;
          }
        }
      }

      &.valid {
        background-color: $green7;

        button {
          &:after {
            border-color: $transparent $transparent $transparent $green7;
          }

          &:hover {
            background-color: $green7;

            &:after {
              border-color: $transparent $transparent $transparent $green7;
            }
          }

          .fa-circle {
            color: $green2;
          }

          .fa-check-circle {
            display: block;
          }

          .name {
            color: $green2;
          }
        }
      }

      &:last-of-type {
        button:after {
          border-width: 0;
        }
      }

      &.active {
        background: $white;

        button:hover {
          background-color: $white;
          cursor: default;

          &:after {
            border-color: $transparent $transparent $transparent $white;
          }
        }

        button {
          background: $white;
          z-index: 100;
        
          &:after {
            border-left-color: $white;
          }
        }
      }

      .name {
        color:  var(--semantic-color-content-base-primary);
        font-size: 13px;
      }
    }
  }
}
}
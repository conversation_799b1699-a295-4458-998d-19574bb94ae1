.ec-root {
    .form-sections-container {
        .form-section {
            .form-field-label {
                margin-bottom: 8px;
                margin-left: 0px;
            }
            .input-container {
                padding: 0;
                input {
                  border: 1px solid var(--semantic-color-border-base-primary);
                  background: var(--semantic-color-background-primary);
                  border-radius: 8px;   
                  color:  var(--semantic-color-content-base-primary);
                  cursor: text;
                  display: block;
                  height: 32px;
                  padding: 9px 8px;
                  text-align: left;
                  min-width: 220px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  &:active,&:focus {
                    color:  var(--semantic-color-content-base-primary);
                  }
                }
            }
            .input-container.error input {
                border-color: var(--semantic-color-border-status-danger-active);
            }
        }
    }
    .nss-servers-deployment-type {
    
    }
    .nss-servers-deployment-users {
        margin-top: 16px;
    }
    .nss-servers-deployment-sessions {
        margin-top: 16px;
    }
    .nss-servers-deployment-dns {
        margin-top: 16px;
    }
    .nss-servers-deployment-platform {
        margin-top: 16px !important;
        margin-bottom: 24px !important;
    }
    .nss-servers-deployment-submit {
        margin-top: 20px;
    }
    
    .nss-servers-deployment-form-config-specs {
        p {
            margin-top: 10px;
            font-size: 13px;
            font-weight: 250;
        }
        .downloadVirtualApplianceVm {
            color: var(--semantic-color-content-status-info-primary);
            cursor: pointer;
            font-size: 13px;
            line-height: 20px;
            padding-right: 5px;
        }
    }
    
    .marginBottom15 {
        margin-bottom: 15px;
    }
    
    .custom-compute-button {
        background: var(--semantic-color-content-interactive-primary-default);
        border: 1px solid var(--semantic-color-content-interactive-primary-default);
        border-radius: 5px;
        color: var(--semantic-color-surface-base-primary);
        cursor: pointer;
        display: inline-block;
        vertical-align: top;
        height: 36px;
        font-size: 13px;
        line-height: 18px;
        max-width: 480px;
        min-width: 81px;
        padding: 8px 20px;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .external-link-button {
        .svg-inline--fa {
            margin-right: 6px;
        }
    }



.nss-title {
    height: 35px;
    width: 309px;
    #nss-settings-servers-tab {
        p {
            height: 32px;
            font-weight: 500;
            letter-spacing: 0;
            line-height: 32px;
            white-space: nowrap;
        }
    }
}

.nss-servers-page {
    margin-top: 35px;
    cursor: pointer;
    .nss-deployment-button {
        margin: 0px 18px;
        display: inline-block;
        color: var(--semantic-color-content-interactive-primary-default);
        span {
            margin-left: 6px;
        }
    }
    .download-mib-files {
        display: inline-block;
    }
}

.nss-servers-wrapper {
    margin-top: 10px;
    .table-row-menu {
        svg {
            margin-right: 10px;
        }
    }
}

.nss-server-status-container {
    margin-top: 28px !important;
    .nss-server-status {
        min-width: 200px;
        margin-top: 12px;
    }
}

.nss-server-state {
    margin-top: 28px;
    justify-content: flex-start;
    .disabled-input {
        margin-top: 10px;
    }
}

.nss-server-certificates {
    margin-top: 20px;
    width: 100%;
    .nss-server-certificate-links {
        margin-top: 10px;
        cursor: pointer;
        .download-nss-certificate {
            color: var(--semantic-color-content-status-info-primary);
            display: inline-block;
            font-size: 13px;
            line-height: 20px;
            padding-right: 5px;
            vertical-align: middle;
        }
        .with-left-border {
            border-left: 1px solid var(--semantic-color-border-base-primary);
            margin-left: 0;
            padding-left: 5px;
        }
    }
}

}

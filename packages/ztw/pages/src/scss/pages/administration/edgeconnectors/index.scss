@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';
@import 'scss/defaults.scss';

.main-container {
  padding: 19px 25px;

  .container-row{
    margin: 0.425rem 0;
  }
}

@media screen and (max-width: $templateMediaSize) {
  .main-container>.pie-container {
    flex-direction: column;  
    width: 100%;
  }
}


.ec-root-page {
.ec-modal-overlay {
  z-index: 9;
  background-color: $grey19;
  bottom: 0;
  left: 0;
  opacity: 0;
  position: fixed;
  right: 0;
  top: 0;
  transition: opacity 400ms ease-in-out;

  &.ReactModal__Overlay--after-open{
    opacity: 1;
  }

  &.ReactModal__Overlay--before-close{
    opacity: 0;
  }

  .ec-modal-body {
    bottom: auto;
    left: calc(50% - 55rem);
    position: absolute;
    right: auto;
    top: 50px;
    width:  1150px;

    .modal-header {
      @include DisplayFlex;
      align-items: center;
      background-color: var(--semantic-color-background-primary);
      color: var(--semantic-color-content-base-primary);
      border-radius: 5px 5px 0 0;
      font-size: 16px;
      font-weight: 500;
      justify-content: space-between;
      min-height: 50px;
      padding: 0 16px;
      
      .close-button {
        background-color: $transparent;
        border: none;

        .fa-times,.fa-xmark {
          color: var(--semantic-color-content-interactive-primary-default);
          font-size: 20px;
        }
      }
    }

    .modal-content {
      background: $white;
      border-radius: 0 0 5px 5px;

      .modal-footer {
        @include DisplayFlex;
        align-items: center;
        background: var(--semantic-color-background-primary);
        border-radius: 0 0 5px 5px;
        height: 60px;
        margin-top:  29px;
        padding: 0 16px;

        button {
          background-color: $transparent;
          border: none;
          color: var(--semantic-color-content-interactive-primary-default);
          font-size: 16px;
          line-height: 19px;

          &.primary-button {
            color: var(--semantic-color-content-immutable-white);
            border-radius: 5px;
            background-color: var(--semantic-color-content-interactive-primary-default);
            // box-shadow: 0 2px 10px 0 rgba(1,61,97,0.4);
            font-size: 16px;
            font-weight: 500;
            height: 32px;
            padding: 0 17px;
          }

          &:not(:first-of-type) {
            margin-left:  24px;
          }
        }
      }
    }
  }
}

.full-width {
    width: 100%;
  }
.wizard-form {
    // padding-bottom: 60px;
  
    .form-sections-container {
      max-height: 70vh;
      overflow: auto;
      padding-bottom:  22px;
  
      .form-section {
        @include DisplayFlex;
        border: none;
        border-radius: 5px;
        // box-shadow: 0 0 12px 0 $grey14;
        margin: 20px;
        padding: 16px;
        align-items: flex-start;
        flex-wrap: wrap;
        justify-content: space-between;
  
        .input-container,
        .radio-button-container,
        .select-item,
        .dropdown-container,
        .disabled-input {
          padding: 1px;
          width: 260px
        }
        .copy-icon {
          color: var(--semantic-color-content-interactive-primary-default);
          float: left;
          margin-right: 0.4em;
        }
        .full-width {
          width:  585px;
  
          .select-item {
            width: 100%;
          }
        }

        .info-block {
          background: $white1;
          border-radius: 5px;
          width:  955px;
          margin-top: 10px;
        }
       
        .copy-text {
          color: var(--semantic-color-content-interactive-primary-default); font-size: 13px;	line-height: 15px;
        }
        .radio-child {
            margin: 0  650px 20px 0;
            width: 260px;
            .select-item{
              margin-left: 0;
              width: 142%;
            }
          }
       
        .custom-header {
          width: 100%;
  
          > div > div {
            @include DisplayFlex;
            align-items: center;
            flex-wrap: wrap;
            width: 100%;
          }
  
          .input-label {
            margin-top: 32px;
          }
  
          .fields-wrapper {
            @include DisplayFlex;
            align-items: flex-start;
            flex-wrap: wrap;
            background-color: var(--semantic-color-content-inverted-base-secondary);
            border-radius: 5px;
            padding: 5px 8px;
            width: 96%;
  
            .input-container {
              &.value {
                margin-left: 48px;
                width: 228px;
              }
  
              input {
                background: $transparent;
                margin-top: 0;
              }
            }
  
            &.full-width {
              width: 100%;
  
              .value {
                width:  251px;
              }
            }
          }
  
          button {
            border: none;
            background: $transparent;
            color: var(--semantic-color-content-interactive-primary-default);
  
            &.add-more-button {
              font-size: 13px;
              margin-top: 16px;
  
              .fa-plus-circle {
                margin-right: 5px;
              }
            }
  
            &.remove-button {
              margin-left: 11px;
  
              .fa-times {
                font-size: 17px;
              }
            }
          }
        }
  
        .form-field-label {
          margin-top:  22px;
        }
  
        .review {
          width: 100%;
          margin-bottom: 32px;
        }

        .input-container {
          height: auto;


          &:not(:first-of-type) {
            .input-label {
              margin-top: 32px;
              padding-top: 0;
            }
  
            input {
              margin-top: 7px;
            }
          }
        }

        .max-width {
          width:  1050px;
        }
        .loc-container {
          width: 80em;
        }
        .loc-info {
          float: left;
        }
        .map{
          border: 1px solid #CCCCCC;
          border-radius: 5px;
          float: right;
          margin-top: 32px;
          width:  600px;
          // height:  315px;
          .map-legend-numeric {
            display: none;
          }
        }
        .disabled-input {
          color: var(--semantic-color-content-interactive-primary-disabled);
          cursor: not-allowed;
          margin-top: 11px;
          margin-left: 8px;
          word-wrap: break-word;
        }
        
        .data-error {
          div {
            width: 100%;
          }
          .text {
            text-align: left;
            color: $red2;
          }
        }
      }
    }
    
    .modal-footer {
      position: absolute;
      bottom: 0;
      width: 100%;
    }
    
    &.configure-monitor {
      .input-container:first-of-type .input-label,
      .radio-button-container p {
        margin-top: 0;
      }
    }
  }

  .wizard-nav {
  ul {
    @include DisplayFlex;
    justify-content: space-between;

    li {
      background: $grey14;
      display: inline-block;
      height: 50px;
      width: 33%;
      position: relative;

      &:before {
        content: " ";
        display: inline-block;
        height: 0;
        width: 0;
        position: absolute;
        border-style: solid;
        border-width: 25px 0 25px 10px;
        border-color: $transparent $transparent $transparent $white;
        z-index: 0;
      }

      &:first-child:before {
        border-color: $transparent;
      }

      button {
        background: $transparent;
        border: 0;
        line-height: 50px;
        outline: none; // temp fix
        width: 100%;

        &:after {
          content: " ";
          display: inline-block;
          height: 0;
          width: 0;
          position: absolute;
          right:  -10px;
          top: -0.5px;
          border-style: solid;
          border-width: 25px 0 25px 10px;
          border-color: $transparent $transparent $transparent $grey14;
          z-index: 1;
        }

        &:hover:not(:disabled) {
          background: $grey3;

          &:after {
            border-color: $transparent $transparent $transparent $grey3; 
          }
        }

        &:disabled {
          cursor: not-allowed;
        }

        .fa-layers {
          margin-right: 17px;

          .fa-circle {
            color: $grey4;
            font-size:  22px;
          }

          .fa-check-circle {
            left: 15px;
            bottom: 18px;
            border: 2px solid $white;
            border-radius: 16px;
            color: $green2;
            display: none;
            font-size: 16px;
          }

          .page-index {
            color: $white;
            top:  -19px;
            left: 7px;
          }
        }
      }

      &.valid {
        background-color: $green7;

        button {
          &:after {
            border-color: $transparent $transparent $transparent $green7;
          }

          &:hover {
            background-color: $green7;

            &:after {
              border-color: $transparent $transparent $transparent $green7;
            }
          }

          .fa-circle {
            color: $green2;
          }

          .fa-check-circle {
            display: block;
          }

          .name {
            color: $green2;
          }
        }
      }

      &:last-of-type {
        button:after {
          border-width: 0;
        }
      }

      &.active {
        background: $white;

        button:hover {
          background-color: $white;
          cursor: default;

          &:after {
            border-color: $transparent $transparent $transparent $white;
          }
        }

        button {
          background: $white;
          z-index: 100;
        
          &:after {
            border-left-color: $white;
          }
        }
      }

      .name {
        color:  var(--semantic-color-content-base-primary);
        font-size: 13px;
      }
    }
  }
}


.add-custom-app-form {
    .container {
        padding: 10px 15px;
        .container-fields, .input-container {
            flex-direction: column;
            padding: 1em;
        }
        p.title {
            padding-bottom: 10px;
            color: $grey4;
            font-size: 13px;
            text-transform: uppercase;
        }

        .disabled-input {
            word-wrap: break-word;
            white-space: normal;
            color: var(--semantic-color-content-interactive-primary-disabled);
            padding-top: .5em;
        }

        &-fields {
            @include DisplayFlex;
            background: $white;
            border-radius: 5px;
            background-color: $white;
            // box-shadow: 0 0 12px 0 $grey14;

            > div {
                flex: 1;
            }
        }
        .entity-dropdown-container {
            .entity-dropdown {
                width: 100%;
                margin: 0;
            }
        }
        .ecg-location-wrapper {
            width: 100%;
            display: block;
            padding-top: .5em;
            > div {
                width: 50%;
                float: left;
                padding-right: 2em;
            }
        }
        .management-ip-section {
            .form-field-label {
              padding: 2em .5em .5em;
            }
            .entity-dropdown {
              width: 100%;
              padding: .5em;
              margin: 0;
            }
          }
    }

    .form-footer {
        height: 60px;
        border-radius: 5px 5px 0 0;
        background-color: $white;
        padding-left: 10px;
        line-height: 60px;

        .cancel {
            margin-left: 10px;
        }
    }
}
}

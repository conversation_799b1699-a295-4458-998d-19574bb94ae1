@import "scss/colors.scss";

.ec-root-page {
.main-container {
  padding: 19px 25px;
}
.ip-fqdn-src-ip-groups .config-table-container .table-container {
  max-height: calc(100vh - 32rem);
}

.sipg-fragment {
  padding-top: 1.5em;
  padding-bottom: 10px;
}
.cloud-provider-wrapper {
  .app-table-container {
    .ReactTable.app-table {
      .rt-thead {
        .header-title {
          font-size: 13px;
          line-height: 30px;
          display: inline-block;
        }
      }
      .has-nested-true {
        .rt-thead {
          .header-title {
            font-size: 13px;
            line-height: 16px;
          }
          .rt-th:nth-child(1) {
            display: block;
            .sorting-icon-cont {
              display: none;
            }
          }
          .rt-th:nth-child(2) {
            padding: 0;
            border: 0;
          }
          .rt-th:nth-child(3) {
            .rt-resizable-header-content {
              left:  -35px;
              position: relative;
              margin-right:  -35px;
            }
          }
        }
      }
    }
  }   
}
  .wizard-form {
    &.sig-form {
      .review-section-title {
        color: var(--semantic-color-content-base-secondary);
        margin: 0;
      }
      .form-sections-container {
        .form-section {
          width: calc(100% - 36px);
          .disabled-input {
            margin-left: 0;
          }
        }
      }
    }
  }
  .add-edit-source-ip {
  &.add-custom-app-form {
    overflow: hidden;
    .form-sections-container {
      .form-section { 
        margin-top: 10px;
      }
    }
  }
  .form-section {
    .sip-name,
    .sip-ip-address {
      width: 100%;
      float: left;
      .input-container {
        width: 100%;
      }
    }
    .dropdown-container,
    .checkbox-container {
      padding: 12px 0;
    }
    .bw-control-child {
      padding: 12px 0;
      .input-container {
        height: auto;
      }
    }
    .form-textarea {
      margin: 0;
      border-color: var(--semantic-color-border-base-primary);
      border-radius: .25rem;
      border-style: solid;
      border-width: .0625rem;
      color: var(--semantic-color-content-base-secondary);
      outline: none;
      padding: 5px;
      resize: none;
    }
    .source-ip-row {
      margin-bottom: 12px;
      &.full-width {
        .half-width {
          width: 50% !important;
        }
        .input-container {
          .input-wrapper {
            width: auto;
          }
        }
      }
      .list-builder {
        .list-builder-header {
            .list-builder-input-textarea {
              width: 100%;
            }
          }
        .list-builder-body  {
          width: calc(100% - 110px);
        }
      }
    }
  }
  
}
.tooltip-text-list {
  padding: 15px;
}

}
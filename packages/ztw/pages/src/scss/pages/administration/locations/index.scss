@import "scss/colors.scss";

.ec-root-page {
  .main-container.locations-main-container {
  // padding: 43px 25px 19px 25px;
  // background-color: #f1f2f3;

  .container-row{
    margin: 0.425rem 0;
  }
  .location-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .align-self-end {
      align-self: end;
    }
    // margin-bottom: 20px;
    &.sublocation-header {
      justify-content: space-between;
    }
    .configuration-nav-tab {
      flex-grow: 1;
    }
    .search-container {
      flex-grow: 0;
      max-height: 32px;
    }
  }
  .actions-row {
    display: flex;
    justify-content: flex-end;
    padding-right: 4px;
    .config-buttons {
      flex-grow: 1;
      align-items: flex-start;
      justify-content: space-around;
      a,
      button {
        margin-right:  23px;
      }
    }
    .toggle-container {
      flex-grow: 0;
    }
  }
}

// overwrite react table css
.locations {
  .delete-icon,
  .pencil-icon,
  .view-icon,
  .download-icon {
    margin: 0;
    margin-right: 10px;
  }
  .ReactTable.app-table {
    max-height: calc(100vh - 20rem);
  }
  .column-layout-config-container {
    border-left: 0;
  }
}

.modal-overlay .modal-body.location-view-modal {  
  width: 1024px;
  left: calc(50% - 512px);
  .modal-content {
    overflow: auto;
    max-height: none;
  }
  &.sublocation {
    .modal-content {
      max-height:  850px;
      background-color: var(--semantic-color-background-primary);      
    }
    .container-row.sublocations {
      padding: 0 30px;
    }
    .modal-footer {
      background-color: var(--semantic-color-background-primary);
    }
  }
}

.wizard-form-location .form-sections-container {
  overflow-x: hidden;
  overflow-y: auto;
  label span {
    margin-left: 0;
  }

  .form-section {
    width: calc(100% - 36px);
    margin-top: 0px;    

    .form-field-label {
      margin-top: 0px;
    }
   
    &:last-child {
      margin-bottom: 60px;
    }

    .table-header-cell .table-header-cell-container .table-text {
      display: inline-block;
      vertical-align: top;
      max-width: calc(100%);
    }
    .disabled-input {
      margin-left: 0;
    }
    
    .input-container {
      padding: 12px 16px;
      .no-padding{
        padding: 0;
        width: fit-content;
      }

      &.review {
        &.prov-url {
          margin: 10px 0;
          height: auto;
          .disabled-input {
            color: var(--semantic-color-content-interactive-primary-disabled);
            background-color: var(--semantic-color-surface-fields-disabled);   
            padding: 14px;
            display: block;
            border-radius: 5px;
            span {
              color: var(--semantic-color-content-interactive-primary-disabled);
            }
            span.copy-prov-url {
              float: right;
              width: 180px;
              cursor: pointer;
              color: var(--semantic-color-content-interactive-primary-default);
            }
          }
          &.desc {
            margin-top: 0;
            .disabled-input {
              color: var(--semantic-color-content-interactive-primary-disabled);
              margin-top: 0;
            }
          }
        }
      }

      .select-item {        
        display: inline-block;
        outline: none;
        overflow: visible;
        position: relative;
        vertical-align: middle;
        width: 100%;      
        margin-left: 0;
        margin-top: 7px;
        .react-select__control{
          border: 1px solid var(--semantic-color-border-base-primary);
          box-shadow: none;   
          background: var(--semantic-color-background-primary);
          border-radius: 8px;   
          color:  var(--semantic-color-content-base-primary);
          cursor: text;
          padding: 0px 8px;
          min-height: auto;
          height: fit-content;
        }
        &.entity-dropdown {
          max-width:120px;
        }
      }


      .select-item .error-container {
        display: none;
      }
      .select-item.entity-dropdown{
        .react-select__value-container {
          height: 32px;
        }
        &.error {
          border: 1px solid var(--semantic-color-border-status-danger-active);
          border-radius: 4px;
          .react-select__value-container {
            height: 30px;
          }      
        }
      }
      
      .react-select__control{
        transform: none;
      }
      .entity-dropdown.error .react-select__indicator {
        color: var(--semantic-color-border-status-danger-active);
      }
  

      &.error .select-item { 
        border: 1px solid var(--semantic-color-border-status-danger-active);
        border-radius: 4px;
      }


      input {
        border: 1px solid var(--semantic-color-border-base-primary);
        background: var(--semantic-color-background-primary);
        border-radius: 8px;   
        color:  var(--semantic-color-content-base-primary);
        cursor: text;
        display: block;
        height: 34px;
        padding: 9px 8px;
        text-align: left;
        min-width: 220px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &:active,&:focus {
          background-color: var(--semantic-color-surface-fields-active);
        }
        &:hover {
          background-color: var(--semantic-color-surface-fields-hover);
        }
      }    
      &.error {
        input {
          border-color: $red4;
        }
      }  
    }

    .g-row {  
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      width: 100%;
      &.center{
        align-items: center;
      }
      &.flex-start{
        justify-content: flex-start;
      }
      &:first-child {
        .g-right{
          padding-left: 0;
        }
        .g-left {
          padding-right: 0;
        }
      }
      
      .g-fullwidth{
        padding: 12px 16px;
        width: 100%;
      }
      .g-left,.g-right {
        width: 50%;      
        padding: 12px 16px;
        input {
          min-width: 240px;
        }
      }
      .g-row-item {
        max-width:120px;
        padding: 0px;
        margin-right: 8px;
        input {
          min-width:120px;
        }
      }
    }

    .bwc-info {
      display: inline-block;
      margin-left: 4px;
      margin-top: 8px;
      color:  var(--semantic-color-content-base-primary);
    }
  }
}
}
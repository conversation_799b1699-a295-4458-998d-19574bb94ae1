@import 'scss/widgets.scss';
@import 'scss/pages.scss';
@import "scss/colors.scss";
@import 'scss/mixins.scss';

.ec-root-page {
.adminManagement-main-container{
  padding: 43px 25px 19px 25px;
}

.adminManagement-main-container,.adminManagement-form {
  .container {
    padding: 0;
    padding-left: 25px;
  }
  label span {
    margin-left: 0;
  }
  .rdg-menu-editor .ui-sortable {
    padding-top: 0; 
  }
  .page-title {
    padding-left: 0;
    padding-top: 0;  
  }
  .source-ip-groups {	
    height: 24px;
    width: 147px;
    color: var(--semantic-color-content-base-primary);
    font-size: 24px;
    font-weight: 500;
    line-height: 24px;
  }
  .tabP {
    margin-bottom: .6em;
    font-size: 16px;
    min-width: 120px;
    display: inline-block;
    vertical-align: top;
    color: var(--semantic-color-content-interactive-primary-default);
    padding: 0 16px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .tabPactive {
    margin-bottom: .6em;
    font-size: 16px;
    min-width: 120px;
    text-align: center;
    color: var(--semantic-color-content-base-primary);
    padding: 0 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .form-sections-container {
    padding: 19px 25px;
    background: var(--semantic-color-background-primary);
    border: 1px solid var(--semantic-color-border-base-primary);
    padding: 16px 24px;
    width: 100%;
    overflow: auto;
    .form-section-label {
      padding: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color:  var(--semantic-color-content-base-primary);
      font-size: 13px;
      letter-spacing: 0.5px;
      margin-bottom: 8px;
      text-transform: uppercase;
      width: 100%;
    }
    .form-section {
      flex-direction: column;
      background-color: var(--semantic-color-surface-base-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 5px;
      width: 100%;
      margin-bottom: 24px;
    }
    .form-section.no-padding {
      padding:0;
    }
  }
  

  .email-input-view-at {
    display: inline-block;
    color: var(--semantic-color-content-base-primary);
    vertical-align: middle;
    padding: 0 10px;
    margin-bottom: 20px;
    width: 32px;
    text-align: center;
  }
  .g-row {  
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    width: 100%;

    &:first-child {
      .g-right{
        padding-left: 0;
      }
      .g-left {
        padding-right: 0;
      }
    }
    
    .g-fullwidth{
      padding: 12px 16px;
      width: 100%;
    }
    .g-fullwidth.no-padding {
      padding: 0;
      .form-textarea {
        background-color: var(--semantic-color-surface-base-primary);
        font-size: 12px;
        line-height: 14px;
        padding: 8px;
        white-space: pre-line;
        resize: none;
        &:focus {
          color: var(--semantic-color-content-status-info-primary);
          border-color: var(--semantic-color-content-status-info-primary);
        }
      }
    }
    .g-left,.g-right {
      width: 50%;      
      padding: 12px 16px;
     
      .select-item,.dropdown {        
        display: inline-block;
        outline: none;
        overflow: visible;
        position: relative;
        vertical-align: middle;
        width: 100%;      
        margin-left: 0;
        border: none;
        min-height: 0;
        .react-select__control,.dropdown-display { 
          border: 1px solid var(--semantic-color-border-base-primary);
          border-radius: 0.3125rem; 
          box-shadow: none;   
          background: var(--semantic-color-background-primary);
          color: var(--semantic-color-content-base-primary);
          cursor: text;
          padding: 0px 8px;
          min-height: auto;
          width: 100%;
          height: 32px;
          .dropdown-value{
            align-self: center;
          }
          .dropdown-icon{
            font-size: 12px !important;
          }
        }
      }      
      .select-item .error-container {
        display: none;
      }
    }
    .g-enable-mobile-app {
      min-width: 260px;
    }
    .form-field-label-wrapper .top-right.rTooltip {
      min-width: 0;
    }
    .form-textarea {
      height: 150px;
      line-height: 14px;
      resize: none;
      vertical-align: middle;
      margin: 0px;
      width: 100%;
      overflow-y: auto;
      white-space: normal;
      background-color: var(--semantic-color-surface-base-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 5px;
      padding-left: 8px;    
    }
    .input-container,.input-password-container {
      padding: 0;
      input {
        background: var(--semantic-color-background-primary);
        color:  var(--semantic-color-content-base-primary);
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 8px;
        cursor: text;
        display: block;
        height: 32px;
        padding: 9px 8px;
        text-align: left;
        min-width: 220px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &:active,&:focus {
          background-color: var(--semantic-color-surface-fields-active);
        }
        &:hover {
          background-color: var(--semantic-color-surface-fields-hover);
        }
      }
    }

    .input-container.error input, .input-password-container.error input {
      border-color: var(--semantic-color-border-status-danger-active);
    }
    .dropdown.error, .entity-dropdown.error {
      border: 1px solid var(--semantic-color-border-status-danger-active);
      border-radius: 4px;
      .dropdown-value{
        color: var(--semantic-color-border-status-danger-active);
      }
      .dropdown-display .dropdown-icon {
        color: var(--semantic-color-border-status-danger-active);
      }
    }
    .input-container .error-container, .input-password-container .error-container  {
      display: none;
    }

    .input-password-wrapper {
      border: none;
    }


  
    .select-item {        
      display: inline-block;
      outline: none;
      overflow: visible;
      position: relative;
      vertical-align: middle;
      width: 100%;      
      .react-select__control{
        background: var(--semantic-color-background-primary);
        color:  var(--semantic-color-content-base-primary);
        // border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 8px;
        cursor: text;
        padding: 0px 8px;
        min-height: auto;
        height: 32px;
      }
    }
    .select-item .error-container {
      display: none;
    }
    .entity-dropdown.error .react-select__indicator {
      color: var(--semantic-color-border-status-danger-active);
    }


    

    .add-custom-app-form .form-sections-container .form-section .input-center-vertical
    { 
      display: flex;
      align-items: center;
      .input-container.limited-width  {
        width:  80px;
      }
    }

    .tooltip-navlink {
      text-decoration: none;
      color: $anchor-color;
    }
  }

    .search-container .search-input, .search-container .autocomplete-input-container {
      box-shadow: none; }
    .dropdown + .search-container, .reports-favorites-list + .search-container {
      margin-left: 5px; }

  .search-input-text-container {
    height: 100%;
    display: inline-block;
    position: relative; 
  }

  .search-input, .autocomplete-input-container {
    border: none;
    padding: 0px 12px 0px 8px;
    border-radius: 8px;
    vertical-align: middle; 
  }

  .search-input-text {
    display: inline-block;
    font-size: 13px;
    height: 32px;
    padding: 6px 26px 6px 0;
    vertical-align: middle;
    width: 280px; 
  }

  .search-icon, .autocomplete-icon {
    color: var(--semantic-color-content-status-info-primary);
    cursor: pointer;
    display: inline-block;
    font-size: 16px !important;
    font-weight: bold;
    height: 16px;
    margin-left: 8px;
    width: 16px;
    vertical-align: middle; 
  }
  .search-icon:hover, .autocomplete-icon:hover {
    color: var(--semantic-color-content-interactive-primary-hover);
  }

  .search-clear-icon {
    color: var(--semantic-color-content-status-info-primary);
    display: none;
    cursor: pointer;
    font-size: 14px !important;
    height: 14px;
    margin-left: 5px;
    width: 12px;
    vertical-align: middle;
    position: absolute;
    top: 9px;
    right: 8px; 
  }
  .search-clear-icon.fas {
    display: none; 
  }
  .search-clear-icon:hover {
    color: var(--semantic-color-content-interactive-primary-hover);
  }
  .search-clear-icon.visible {
    display: inline-block; 
  }
  .radio-button-container p {
  margin-top: 0;
  }

  .radio-button-container p {
    margin: 4px;
  }

  .dialog-footer .dialog-footer-left .submit:disabled {  
      background: var(--semantic-color-background-primary);
      color:  var(--semantic-color-content-base-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
      box-shadow: none;
      cursor:default;
      opacity: 0.6;
    }
  .dialog-footer .dialog-footer-left .cancel:disabled {  
    background: none;
    color:  var(--semantic-color-content-base-primary);
    border: none;
  }
  .saml-certificate-modal-form {
    width: 1000px;
  }

}

.adminManagement-main-container .add-custom-app-form {
  margin-top: 24px;
  .form-sections-container {
    padding: 0;
    border: none;
    padding-bottom: 24px;
    .form-section {
      margin: 0;
      padding: 0;
      .g-enable-password-expiration {
        padding: 12px 16px;
      }
      .form-iframe-empty {
        margin-right: 5px;
        color: var(--semantic-color-content-base-secondary);
      }
      .form-link-text {
        text-decoration: none;
        color: var(--semantic-color-content-status-info-primary);
        display: inline-block;
        font-size: 13px;
        line-height: 20px;
        padding-right: 5px;
        vertical-align: middle;      
        margin-left: 0;     
      }
      .form-link-text.with-border {
        border-left: 1px solid var(--semantic-color-content-base-secondary);
        margin-left: 0;
        padding-left: 5px;
      }
    }
  }
  .error-text {
    color: var(--semantic-color-content-status-danger-primary);
  }
}

.upload-confirm-form {
  .form-sections-container {
    background: var(--semantic-color-background-primary);
  }
  .form-section {
    background-color: var(--semantic-color-surface-base-primary);    
  }
  .form-field-label {
    margin-bottom: 8px;
  }
  .input-container .input-wrapper input {
    border: none;
  }
}


.adminManagement-main-container {
  .delete-icon,
  .pencil-icon,
  .view-icon,
  .download-icon {
    margin: 0;
    margin-right: 10px;
  }
  .ReactTable.app-table {
    max-height: calc(100vh - 21rem);
  }
  .column-layout-config-container {
    border-left: 0;
  }
  .source-ip-wrapper {
    overflow-x: auto ;
    min-height: 50vh;
    .config-table-container {
      min-width: 1232px;
    }
  }
}
/* Action Bar */
.main-container {
  padding: 19px 25px;
}

.source-ip-groups {	
  height: 24px;
  width: 147px;
  color: var(--semantic-color-content-base-primary);
  font-size: 24px;
  font-weight: 500;
  line-height: 24px;
}
.sipg-fragment {
  padding-top: 1.5em;
  padding-bottom: 10px;
}
.source-ip-wrapper {
  margin-bottom: 100px;
  .app-table-container {
    .ReactTable.app-table.has-nested-true {
      .rt-thead {
        .rt-th:nth-child(1) {
          display: block;
          .sorting-icon-cont {
            display: none;
          }
        }
        .rt-th:nth-child(2) {
          padding: 0;
          border: 0;
        }
        .rt-th:nth-child(3) {
          .rt-resizable-header-content {
            left: -35px;
            position: relative;
            margin-right: -35px;
          }
        }
      }
    }
  }   
}

.adminManagement-toolbar-left-buttons{
  margin-right: 16px;
  text-overflow: ellipsis;
}
// AdminManagementForm
.rbac-form {
    .g-row {
      margin-top: 1em;
    }
    .form-section {
      .g-name, .gateway,
      .g-ip {
        width: 50%;
        float: left;
        .input-container {
          width: 50%;
        }
      }
      .form-left{
        float: left;
      }
      .form-right{
        float: right;
        padding-left: 5.3em;
      }
    }
    
  }
}
@import "scss/colors.scss";

.ec-root-page {
.vdi-form {
  padding: 20px;
  .input-container {
    margin-bottom: 16px;
  }
  .section-divider {
    margin: 20px 0;
    width:  640px;
    height: auto;
    border-bottom:1px solid #e9e9e9;
  }
  .form-input-row {
    margin-bottom: 16px;
    .radio-group-container {
      padding: 0;
    }
  }
  .list-builder-search-container {
    position: relative;
    overflow: hidden;
  }
}
.edgeconnector-page .edgeconnector-modal .modal-content.vdi-profile.cc-provisioning-modal-content {
  min-height: 74vh;
  overflow-y: auto;
  height: auto !important;
  .vdi-form-wrapper,
  .vdi-form {
    float: left;
  }
}

.edgeconnector-page .edgeconnector-modal .modal-content.cc-provisioning-modal-content.vdi-profile {
  overflow: none;
}

.main-container.vdi-template-table .edgeconnector-page .edgeconnector-modal .modal-footer {
  top: 80vh;
}


.configure-vdi-template{
  .form-width {
    width: 640px;
  }
  .cc-provisioning-description {
    border-radius: 4px;
    padding: 7px;
    color: var(--semantic-color-content-base-primary);
    background-color: var(--semantic-color-background-primary);
    height: 32px;
    width:  640px;
    resize: none;
    border: 1px solid var(--semantic-color-border-base-primary);
  }
  .form-textarea {
    margin: 10px 0 0;
    padding: 7px;
    box-sizing: border-box;
    height: 32px;
    width:  640px;
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 5px;
    background-color: var(--semantic-color-surface-base-primary);
    // box-shadow: 0 0 2px 0 $grey1;
    resize: none;
  }
  .criteria-title-text {
    font-size: 16px;
    line-height: 20px;
    font-weight: 500;
  }
  .criteria-content {
    font-size: 13px;
    font-weight: 400;
    color: var(--semantic-color-content-interactive-primary-disabled);
  }
}


.wizard-form .form-sections-container.cc-provisoning {
  .review-section-title {
    margin-left: 20px;
    &:first-child {
      margin-top: 20px;
    }
    .review-section-title-description {
      margin-left: 20px;
    }
  }
  .form-section {
    width: calc(100% - 36px);
    
    .input-container {
      &.review {
        &.prov-url {
          margin: 10px 0;
          height: auto;
          .disabled-input {
            color: var(--semantic-color-content-interactive-primary-disabled);
            background-color: var(--semantic-color-background-secondary);
            padding: 14px;
            display: block;
            border-radius: 5px;
            span {
              color: var(--semantic-color-content-interactive-primary-disabled);
            }
            span.copy-prov-url {
              float: right;
              width: 180px;
              cursor: pointer;
              color: var(--semantic-color-content-interactive-primary-default);
            }
          }
          &.desc {
            margin-top: 0;
            .disabled-input {
              color: var(--semantic-color-content-interactive-primary-disabled);
              margin-top: 0;
            }
          }
        }
      }
    }
  }
}

}
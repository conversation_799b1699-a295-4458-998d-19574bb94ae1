@import "scss/colors.scss";
@import 'scss/defaults.scss';
@import "scss/mixins.scss";
.ec-root-page {
.main-container {
  padding: 19px 25px;  
}
.sipg-fragment {
  padding-top: 1.5em;
  padding-bottom: 10px;
}
.page-title {
  padding-left: 0;
}
.cloud-provider-wrapper {
  .app-table-container {
    .ReactTable.app-table {
      .rt-thead {
        .header-title {
          font-size: 13px;
          line-height: 30px;
          display: inline-block;
        }
      }
      .has-nested-true {
        .rt-thead {
          .header-title {
            font-size: 13px;
            line-height: 16px;
          }
          .rt-th:nth-child(1) {
            display: block;
            .sorting-icon-cont {
              display: none;
            }
          }
          .rt-th:nth-child(2) {
            padding: 0;
            border: 0;
          }
          .rt-th:nth-child(3) {
            .rt-resizable-header-content {
              left:  -35px;
              position: relative;
              margin-right:  -35px;
            }
          }
        }
      }
    }
  }   
}

// overwrite react table css
.prov-templates {
  .delete-icon,
  .pencil-icon,
  .view-icon,
  .download-icon {
    margin: 0 10px;
  }
  .ReactTable.app-table {
    max-height: calc(100vh - 28rem);
  }
  .column-layout-config-container {
    border-left: 0;
  }
}

.view-cloudconnector-provisioning-modal {
  .wizard-form.configure-provisioning-template {
    .form-sections-container {
      overflow: hidden;
      overflow-y: auto;
    }
  }
}

.wizard-form .form-sections-container.cc-provisoning {
  .review-section-title {
    margin-left: 20px;
    &:first-child {
      margin-top: 20px;
    }
    .review-section-title-description {
      margin-left: 20px;
    }
  }
  .form-section {
    width: calc(100% - 36px);
    
    .input-container {
      &.review {
        &.prov-url {
          margin: 10px 0;
          height: auto;
          .disabled-input {
            color: var(--semantic-color-content-interactive-primary-disabled);
            background-color: var(--semantic-color-background-secondary);
            padding: 14px;
            display: block;
            border-radius: 5px;
            span {
              color: $grey4;
            }
            span.copy-prov-url {
              float: right;
              width: 180px;
              cursor: pointer;
              color: var(--semantic-color-content-interactive-primary-default);
            }
          }
          &.desc {
            margin-top: 0;
            .disabled-input {
              color: var(--semantic-color-content-interactive-primary-disabled);
              margin-top: 0;
            }
          }
        }
      }
    }
  }
}

.provisioning-template-page-container {
  background-color: transparent;
  padding: 24px;
  position: relative;
  .tabs {
    margin-bottom: 0;
  }
  .main-container {
    padding: 0  25px;
    &.provisioning-template-navtabs {
      padding-bottom: 0;
    }
    &.provisioning-template-header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      max-height: 42px;
      .search-container {
        max-height: 32px;
      }
      .actions-row.refresh {
        .refresh-button {
          border: none;
          color: var(--semantic-color-content-base-primary);
          background: none;
          color:  var(--semantic-color-content-base-primary);
          margin-right: 8px;
        }
        .fa-arrows-rotate {
          color: var(--semantic-color-content-interactive-primary-default);
          margin-right: 4px;
        }
      }
    }
    &.provisioning-template-table {
      padding: 12px;
      padding-top: 0;
      margin: 0 19px 25px 25px;
      background: var(--semantic-color-background-primary);
      border-radius: 8px;
      // box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
    }
    .cloud-provider-wrapper {
      background-color: var(--semantic-color-background-primary);
    }
    .configuration-nav-tab {
      background-color: transparent;
    }
    .radio-button-container .radio-buttons {      
      border: none;
      .radio-button.checked-true label {
        border: none;
        min-height: none;
        .check-circle {
          margin-right: 4px;
        }
      }
    }
    .cell-container {
      .prov-status {
        .radio-button-container {
          .radio-buttons {
            min-width: auto;
            .radio-button {
              &:last-child {
                min-width: 125px;
                label {
                  padding-right: 10px;
                }
              }
              &:first-child {
                width: 85px;
                label {
                  padding-right: 5px;
                }
              }
              &.checked-false {
                label {
                  border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
                  cursor: pointer;
                  background: var(--semantic-color-content-interactive-primary-default);
                  color: var(--semantic-color-surface-interactive-secondary-default);
                }
                // label {
                //   border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
                //   cursor: pointer;
                //   color: var(--semantic-color-content-base-subdued);
                // }
                &.disabled {
                  label {
                    cursor: not-allowed;
                    color: var(--semantic-color-content-inverted-interactive-disabled);
                  }
                }
              }
              &.checked-true {
                label {
                  cursor: pointer;
                }
                &.disabled {
                  label {
                    cursor: not-allowed;
                    background: var(--semantic-color-border-interactive-primary-disabled);
                    color: var(--semantic-color-content-base-tertiary);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.wizard-form.configure-provisioning-template {
  .form-sections-container {
    .form-section.flex-direction-column {
      flex-direction: column;
    }

  }

}

.cc-prov-url-wrapper {
  padding: 15px 30px;
  height: auto;
  background-color: #eef8ff;
  width: 100%;
  position: absolute;
  left: 0;
  top: 60px;
}
.title-text {
  font-size: 13px;
  line-height: 16px;
  color: $grey4;
  padding-bottom: 10px;
}
.url-text {
  color: $grey23;
  font-weight: 500;
  font-size: 13px;
  line-height: 16px;
  letter-spacing:  0.59px;
  .fa-copy {
    font-size: 15px;
    margin-left: 10px;
    vertical-align: top;
    cursor: pointer;
    color: $blue24;
  }
}
.expanded-row {
  top: 50px;
}


.wizard-nav {
  ul {
    @include DisplayFlex;
    justify-content: space-between;

    li {
      background: $grey14;
      display: inline-block;
      height: 50px;
      width: 33%;
      position: relative;

      &:before {
        content: " ";
        display: inline-block;
        height: 0;
        width: 0;
        position: absolute;
        border-style: solid;
        border-width: 25px 0 25px 10px;
        border-color: $transparent $transparent $transparent $white;
        z-index: 0;
      }

      &:first-child:before {
        border-color: $transparent;
      }

      button {
        background: $transparent;
        border: 0;
        line-height: 50px;
        outline: none; // temp fix
        width: 100%;

        &:after {
          content: " ";
          display: inline-block;
          height: 0;
          width: 0;
          position: absolute;
          right:  -10px;
          top: -0.5px;
          border-style: solid;
          border-width: 25px 0 25px 10px;
          border-color: $transparent $transparent $transparent $grey14;
          z-index: 1;
        }

        &:hover:not(:disabled) {
          background: $grey3;

          &:after {
            border-color: $transparent $transparent $transparent $grey3; 
          }
        }

        &:disabled {
          cursor: not-allowed;
        }

        .fa-layers {
          margin-right: 17px;

          .fa-circle {
            color: $grey4;
            font-size:  22px;
          }

          .fa-check-circle {
            left: 15px;
            bottom: 18px;
            border: 2px solid $white;
            border-radius: 16px;
            color: $green2;
            display: none;
            font-size: 16px;
          }
          .fa-check-circle-normal.fa-check-circle {
            left: 0;
            bottom: 0;
            border: none;
            border-radius: 16px;
            color: $blue21;
            font-size: 15px;
          }

          .page-index {
            color: $white;
            top:  -19px;
            left: 7px;
          }
        }
      }

      &.valid {
        background-color: $green7;

        button {
          &:after {
            border-color: $transparent $transparent $transparent $green7;
          }

          &:hover {
            background-color: $green7;

            &:after {
              border-color: $transparent $transparent $transparent $green7;
            }
          }

          .fa-circle {
            color: $green2;
          }

          .fa-check-circle {
            display: block;
          }

          .name {
            color: $green2;
          }
        }
      }

      &:last-of-type {
        button:after {
          border-width: 0;
        }
      }

      &.active {
        background: $white;

        button:hover {
          background-color: $white;
          cursor: default;

          &:after {
            border-color: $transparent $transparent $transparent $white;
          }
        }

        button {
          background: $white;
          z-index: 100;
        
          &:after {
            border-left-color: $white;
          }
        }
      }

      .name {
        color:  var(--semantic-color-content-base-primary);
        font-size: 13px;
      }
    }
  }
}

.cc-provisioning-modal-content .wizard-form.configure-provisioning-template .form-sections-container {
  .form-section-label .form-field-label {
    margin-left: 0px;
  }
  .form-section {
    .disabled-input {
      color: var(--semantic-color-content-interactive-primary-disabled);
      margin-left: 0px;
    }
  }
  .review-section-title{
    margin-left: 0px;
    margin-top: 30px;
  }
}

// .review-section-title {
//   text-transform: uppercase;
//   color: $grey10;
//   font-size: 13px;
//   font-weight: 500;
//   letter-spacing: 0;
//   line-height: 16px;
//   border: none;
//   margin: 0px 0px 0px 10px;
// }
.review-changes-heading {
  color: $grey1;
  padding-bottom: 2px;
  display: block;
  width: 100%;
  font-size: 24px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 36px;
}
.edgeconnector-modal {
  .modal-content {
    .wizard-form {
      &.configure-provisioning-template {
        .form-sections-container {
          .review-changes-heading {
            .form-section-label {
              color:  var(--semantic-color-content-base-primary);
              padding-bottom: 2px;
              display: block;
              width: 100%;
              line-height:  36px;
              font-size:  24px;
              text-transform: none;
            }
          }
          
          .form-section {
            .input-container {
              &.review {
                margin: 10px 0;
                height: auto;
                .disabled-input {
                  color: var(--semantic-color-content-interactive-primary-disabled);
                }
                &.desc {
                  margin-top: 0;
                  .disabled-input {
                    color: var(--semantic-color-content-interactive-primary-disabled);
                    margin-top: 0;
                  }
                } 
              }
            }
          }
        } 
      }
    }
  }
}

.review-changes-heading {
  color: $grey1;
  padding-bottom: 2px;
  display: block;
  width: 100%;
  font-size: 24px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 36px;
}
.edgeconnector-modal {
  .modal-content {
    .wizard-form {
      &.configure-provisioning-template {
        .form-sections-container {
          .review-changes-heading {
            .form-section-label {
              color:  var(--semantic-color-content-base-primary);
              padding-bottom: 2px;
              display: block;
              width: 100%;
              line-height:  36px;
              font-size:  24px;
              text-transform: none;
            }
          }
          
          .form-section.provisioning-url {
            .form-field-label,
            .disabled-input {
             color: $grey10;
            }
            .input-container {
              &.review {
                &.prov-url {
                  margin: 10px 0;
                  height: auto;
                  .disabled-input {
                    color: var(--semantic-color-content-interactive-primary-disabled);
                    background-color: var(--semantic-color-background-secondary);
                    padding: 14px;
                    display: block;
                    border-radius: 5px;
                    span {
                      color: $grey4;
                    }
                    span.copy-prov-url {
                      float: right;
                      width: 180px;
                      cursor: pointer;
                      color: var(--semantic-color-content-interactive-primary-default);
                    }
                  }
                  &.desc {
                    margin-top: 0;
                    .disabled-input {
                      color: var(--semantic-color-content-interactive-primary-disabled);
                      margin-top: 0;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.configure-provisioning-template{
  .cc-provisioning-description {
    border-radius: 4px;
    padding: 5px;
    color: var(--semantic-color-content-base-primary);
    background-color: var(--semantic-color-background-primary);
    height: 90px;
    width:  625px;
    resize: none;
    border: 1px solid var(--semantic-color-border-base-primary);
  }
  .form-textarea {
    margin: 10px 0 0;
    padding: 5px;
    box-sizing: border-box;
    height: 90px;
    width:  625px;
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 5px;
    background-color: var(--semantic-color-surface-base-primary);
    // box-shadow: 0 0 2px 0 $grey1;
    resize: none;
  }
}

.cc-group-details {
    .capacity-field {
        float: left;
        display: flex;
        flex-flow: column;
        margin-right: 20px;
        input {
            width: 120px !important;
            min-width: auto !important;
        }
    }
}


.image-radio-button-container {
  .radio-buttons {
    @include DisplayFlex;

    &.disabled {
      .radio-button {
        label {
          background: var(—surface-fields-disabled);
          color: var(--semantic-color-content-interactive-primary-disabled);
          border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
          cursor: not-allowed;
        }

        &.checked-true {
          label {
            background: var(--semantic-color-content-interactive-primary-default);
            color: var(--semantic-color-background-primary);
          }
        }
      }
    }

    .radio-button {
      &.checked-true {
        label {
          border: 3px solid var(--semantic-color-content-interactive-primary-default);
          border-radius: 5px;
          margin: 10px;
        }
      }
      &:first-child {
        label {
          margin-left: 0;
        }
      }
      label {
        @include DisplayFlex;
        cursor: pointer;
        position: relative;
        padding: 4px 20px 4px 5px;
        border: 1px solid $grey7;
        color: var(--semantic-color-content-interactive-primary-default);
        align-items: center;
        justify-content: center;
        margin: 10px;
        width: 178px;
        height:  106px;
        border-radius: 5px;

        input {
          display: none;
        }
        img {
          width: 100%;
          height: auto;
        }
      }
    }
  }
}



}
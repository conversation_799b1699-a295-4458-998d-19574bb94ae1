// @import 'scss/widgets.scss';
@import "scss/colors.scss";
@import 'scss/pages.scss';

.ec-root-page {
.main-container {
  padding: 19px 25px;
  .react-grid-Cell .cell-table-action .delete-icon {
    margin-left: 17px;
  }
  .flex-direction-column {
    flex-direction: column;
  }
}

.table-layout-header-api-key {
  margin-top: 30px 0;
  width: 100%; 
  position: relative; }
  
.tips-text-container {
  padding: 8px 16px;
  background: var(--semantic-color-background-primary);
  border-radius: 8px;
  color: var(--semantic-color-content-base-primary);
  margin-bottom: 16px;
  font-size: 11px;}

.source-ip-groups {	
  height: 24px;
  width: 147px;
  color: var(--semantic-color-content-base-primary); 
  font-size: 24px;
  font-weight: 500;
  line-height: 24px;
}
// .sipg-fragment {
//   padding-top: 1.5em;
//   padding-bottom: 10px;
// }
.source-ip-wrapper {
  position: relative;
  margin-bottom: 100px;
  .app-table-container {    
    .ReactTable.app-table.has-nested-true {
      .rt-thead {
        .rt-th:nth-child(1) {
          display: block;
          .sorting-icon-cont {
            display: none;
          }
        }
        .rt-th:nth-child(2) {
          padding: 0;
          border: 0;
        }
        .rt-th:nth-child(3) {
          .rt-resizable-header-content {
            left: -35px;
            position: relative;
            margin-right: -35px;
          }
        }
      }      
    }
    .table-column-selector{
      width: 70px;
      height: 42px;
    }
  }   
}

// .search-container {
//   background-color: var(--semantic-color-surface-base-primary);
//   border: 1px solid var(--semantic-color-border-base-primary);
//   border-radius: .25rem;
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   width: 13.75rem; 
//   padding: 0 6px;
//   .search-icon {
//     color: var(--semantic-color-content-base-secondary);
//   }
//   .icon-wrapper {
//     background-color: var(--semantic-color-surface-base-primary);
//     height: 1.875rem;
//     justify-content: center;
//     line-height: 1.875rem;
//     text-align: center;
//     width: 2.1875rem;

//   }
// }
  .search-container .search-input, .search-container .autocomplete-input-container {
    box-shadow: none; }
  .dropdown + .search-container, .reports-favorites-list + .search-container {
    margin-left: 5px; }

.search-input-text-container {
  height: 100%;
  display: inline-block;
  position: relative; }

.search-input, .autocomplete-input-container {
  border: none;
  padding: 0px 12px 0px 8px;
  border-radius: 8px;
  vertical-align: middle; }

.search-input-text {
  display: inline-block;
  font-size: 13px;
  height: 32px;
  padding: 6px 26px 6px 0;
  vertical-align: middle;
  width: 280px; }

.search-icon, .autocomplete-icon {
  color: var(--semantic-color-content-status-info-primary);
  cursor: pointer;
  display: inline-block;
  font-size: 16px !important;
  font-weight: bold;
  height: 16px;
  margin-left: 8px;
  width: 16px;
  vertical-align: middle; }
  .search-icon:hover, .autocomplete-icon:hover {
    color: var(--semantic-color-content-interactive-primary-default); }

.search-clear-icon {
  color: var(--semantic-color-content-status-info-primary);
  display: none;
  cursor: pointer;
  font-size: 14px !important;
  height: 14px;
  margin-left: 5px;
  width: 12px;
  vertical-align: middle;
  position: absolute;
  top: 9px;
  right: 8px; }
  .search-clear-icon.fas {
    display: none; }
  .search-clear-icon:hover {
    color: var(--semantic-color-content-interactive-primary-default); }
  .search-clear-icon.visible {
    display: inline-block; }


.radio-button-container p {
margin-top: 0;
}

.rbac-form {
  .g-row {
    margin: 12px 0px;
  }
}

.radio-button-container p {
  margin: 4px;
}

.add-custom-app-form.edit-api-confirm-form {
  .form-input-rows {
    .form-input-row {
      .form-field-label.invalid {
        color: var(--semantic-color-content-status-danger-primary);
      }
    }
  }
}
// RegenerateApiKeyForm
.delete-confirm-form {
  &.add-custom-app-form {
    .form-sections-container {
      .form-section {
        margin-top: 20px;
      }
    }
  }
  .modal-text {
    width: 100%;
    padding-bottom: 10px;
    color: var(--semantic-color-content-base-primary);
  }
}

// EditApiKeyForm
.add-custom-app-form.edit-api-confirm-form {  
  .form-sections-container {
    .form-section {
      margin-top: 20px;
      .__react_component_tooltip {
        padding: 12px 16px;
      }
    }
  }
  .modal-text {
    width: 100%;
    padding-bottom: 10px;
    color: var(--semantic-color-content-base-primary);
  }
  .form-input-rows, .form-tab-input-rows {
    // background: var(--semantic-color-background-primary);
    display: flex;
    border-radius: 5px;
    width: 100%;
    .form-input-row{
      width: 50%;
      // display: inline-block;
      .input-container{
        padding: 0px;
        width: 100%;
        &.error {
          border-radius: 8px;
          border: 1px solid;
          border-color: $red4;
        }
      }
      #newApiKey {
        background: var(--semantic-color-background-primary);
        border-radius: 8px;
        border: 1px solid var(--semantic-color-border-base-primary);
        color: var(--semantic-color-content-base-primary);
        cursor: text;
        display: block;
        height: 32px;
        padding: 9px 8px;
        text-align: left;
        min-width: 280px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &.error {
          border-color: $red4;
        }
      }
      .form-input-label, .form-field-label{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: var(--semantic-color-content-base-primary);
        font-size: 13px;
        font-weight: 500;
        margin-bottom: 8px;
        height: 20px;
      }
      .form-input-text-disabled {
        color: var(--semantic-color-content-interactive-primary-disabled);
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
        width: auto;
        line-height: 32px;
     }
     .form-field-tooltip-container {
      background: var(--semantic-color-background-primary);
      color:  var(--semantic-color-content-base-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
      max-width: 550px;
      min-width: 260px;
      overflow: hidden;
      padding: 12px 16px;
      word-wrap: break-word;
      white-space: break-spaces;
      text-transform: none;
      }
     
    }
  }
}
// ApiKeyManagementSearch
.main-container {
  padding: 19px 25px;
}

.source-ip-groups {	
  height: 24px;
  width: 147px;
  color: var(--semantic-color-content-base-primary);
  font-size: 24px;
  font-weight: 500;
  line-height: 24px;
}
// .sipg-fragment {
//   padding-top: 1.5em;
//   padding-bottom: 10px;
// }
.source-ip-wrapper {
  margin-bottom: 100px;
  .app-table-container {
    .ReactTable.app-table.has-nested-true {
      .rt-thead {
        .rt-th:nth-child(1) {
          display: block;
          .sorting-icon-cont {
            display: none;
          }
        }
        .rt-th:nth-child(2) {
          padding: 0;
          border: 0;
        }
        .rt-th:nth-child(3) {
          .rt-resizable-header-content {
            left: -35px;
            position: relative;
            margin-right: -35px;
          }
        }
      }
    }
  }   
}
}

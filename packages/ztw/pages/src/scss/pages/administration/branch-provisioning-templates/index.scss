@import "scss/colors.scss";
@import "scss/mixins.scss";
@import 'scss/defaults.scss';

.ec-root-page {
.main-container {
  padding: 19px 25px;
  .page-title {
    padding-left: 0;
  }
}
.sipg-fragment {
  padding-top: 1.5em;
  padding-bottom: 10px;
}
.cloud-provider-wrapper {
  .app-table-container {
    .ReactTable.app-table {
      .rt-thead {
        .header-title {
          font-size: 13px;
          line-height: 30px;
          display: inline-block;
        }
      }
      .has-nested-true {
        .rt-thead {
          .header-title {
            font-size: 13px;
            line-height: 16px;
          }
          .rt-th:nth-child(1) {
            display: block;
            .sorting-icon-cont {
              display: none;
            }
          }
          .rt-th:nth-child(2) {
            padding: 0;
            border: 0;
          }
          .rt-th:nth-child(3) {
            .rt-resizable-header-content {
              left:  -35px;
              position: relative;
              margin-right:  -35px;
            }
          }
        }
      }
    }
  }   
}

// overwrite react table css
.branch-templates {
  .delete-icon,
  .pencil-icon,
  .view-icon,
  .download-icon {
    margin: 0 10px;
  }
  .ReactTable.app-table {
    max-height: calc(100vh - 28rem);
  }
  .column-layout-config-container {
    border-left: 0;
  }
}

/*radio image group*/
.edgeconnector-modal {
  .modal-content.branch-provisioning-modal-content, .modal-content.cc-provisioning-modal-content {
    .wizard-form.configure-provisioning-template{
      .form-sections-container  {
        .form-section {
          label{
            span {
              margin-left: 0px;
            }
          }
          .section-cloud {
            .image-radio-button-container {
              .radio-buttons {
                @include DisplayFlex;

                &.disabled {
                  .radio-button {
                    opacity: 30%;
                    label {
                      background: var(—surface-fields-disabled);
                      color: var(--semantic-color-content-interactive-primary-disabled);
                      border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
                      cursor: not-allowed;
                    }

                    &.checked-true {
                      opacity: 80%;
                      label {
                        background: var(--semantic-color-content-interactive-primary-default);
                        color: var(--semantic-color-background-primary);
                      }
                    }
                  }
                }

                .radio-button {
                  &.checked-true {
                    label {
                      border: 3px solid var(--semantic-color-content-interactive-primary-default);
                      border-radius: 5px;
                      margin: 10px;
                      

                    }
                  }
                  &:first-child {
                    label {
                      margin-left: 0;
                    }
                  }
                  label {
                    @include DisplayFlex;
                    cursor: pointer;
                    position: relative;
                    padding: 4px 20px 4px 5px;
                    // border: 1px solid $grey7;
                    border: 1px solid var(--semantic-color-border-base-primary);
                    // box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
                    border-radius: 8px;
                    color: var(--semantic-color-content-interactive-primary-default);
                    align-items: center;
                    justify-content: center;
                    margin: 10px;
                    width: 200px;
                    height: 88px;
                    border-radius: 5px;
                    opacity: 1;
                    &:hover{
                      width:  201px;
                      height: 90px;
                    }

                    input {
                      display: none;
                    }
                    img {
                      width: 100%;
                      height: auto;
                      background: var(--semantic-color-background-primary);
                      &.ZT400 {
                        width: 61px;
                        height: 47px;
                      }
                      &.ZT600 {
                        width: 61px;
                        height: 47px;
                      }
                      &.ZT800 {
                        width: 61px;
                        height: 47px;
                      }
                      /* DropShadow/Bottom/Light/3 | 10-2-6 */                      
                      

                      &.CENTOS {
                        position: absolute;
                        width: 180.8px;
                        height: 71px;
                        left: 10px;
                        top: 8px;
                      }
                      &.REDHAT_LINUX {
                        position: absolute;
                        width: auto;
                        height: 71px;
                        left: 39px;
                      }
                      &.VMWARE_ESXI {
                        position: absolute;
                        width: 165.02px;
                        height: 48px;
                        left: 17px;
                        top: 20px;
                      }
                      &.MICROSOFT_HYPER_V {
                        position: absolute;
                        width: 180px;
                        height: 77px;
                        left: 10px;
                        top: 5px;
                      }
                    }
                    span {
                      margin-left: 0px;
                    }
                  }
                }
              }  
            }
          }
          .input-container {
            .input-wrapper {
              input {
                &:-webkit-autofill,
                &:-webkit-autofill:hover,
                &:-webkit-autofill:focus {
                  -webkit-box-shadow: 0 0 0px 1000px var(--semantic-color-background-primary) inset;
                  -webkit-text-fill-color: var(--semantic-color-content-base-primary);
                  
                }
              }
            }
          }
        }
      }
    }
  }  
}

// ProvURL
.review-changes-heading {
  color: $grey1;
  padding-bottom: 2px;
  display: block;
  width: 100%;
  font-size: 24px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 36px;
}
.edgeconnector-modal {
  .modal-content {
    .wizard-form {
      &.configure-provisioning-template {
        .form-sections-container {
          .review-changes-heading {
            .form-section-label {
              color:  var(--semantic-color-content-base-primary);
              padding-bottom: 2px;
              display: block;
              width: 100%;
              line-height:  36px;
              font-size:  24px;
              text-transform: none;
            }
          }
          
          .form-section.provisioning-url {
            .bc-subtitle {
              width: 100%;
              margin-top: 20px;
              text-transform: uppercase;
            }
            .form-field-label,
            .disabled-input {
             color: var(--semantic-color-content-interactive-primary-disabled);
            }
            .input-container {
              &.review {
                &.prov-url {
                  margin: 10px 0;
                  height: auto;
                  .disabled-input {
                    color: var(--semantic-color-content-interactive-primary-disabled);
                    background-color: var(--semantic-color-background-secondary);
                    padding: 14px;
                    display: block;
                    border-radius: 5px;
                    span {
                      color: $grey4;
                    }
                    span.copy-prov-url {
                      float: right;
                      width: 180px;
                      cursor: pointer;
                      color: $blue2;
                    }
                  }
                  &.desc {
                    margin-top: 0;
                    .disabled-input {
                      color: var(--semantic-color-content-interactive-primary-disabled);
                      margin-top: 0;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.review-section-title {
  text-transform: uppercase;
  color: $grey10;
  font-size: 13px;
  font-weight: 600;
  letter-spacing: 0;
  line-height: 16px;
  border: none;
  margin: 0px 0px 0px 15px;
}
.section-inner-title {
  width: 100%;
  margin-top:  22px;
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  line-height: 20px;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  color:  var(--semantic-color-content-base-primary);
}
.review-changes-heading {
  color: $grey1;
  padding-bottom: 2px;
  display: block;
  width: 100%;
  font-size: 24px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 36px;
}
.edgeconnector-modal {
  .modal-content {
    .wizard-form {
      &.configure-provisioning-template {
        .form-sections-container {
          .review-changes-heading {
            .form-section-label {
              color:  var(--semantic-color-content-base-primary);
              padding-bottom: 2px;
              display: block;
              width: 100%;
              line-height:  36px;
              font-size:  24px;
              text-transform: none;
            }
          }
          
          .form-section {
            .input-container {
              &.review {
                margin: 10px 0;
                height: auto;
                .disabled-input {
                  color: var(--semantic-color-content-interactive-primary-disabled);
                }
                &.desc {
                  margin-top: 0;
                  .disabled-input {
                    color: var(--semantic-color-content-interactive-primary-disabled);
                    margin-top: 0;
                  }
                } 
              }
            }
          }
        } 
      }
    }
  }
}

.wizard-nav {
  ul {
    @include DisplayFlex;
    justify-content: space-between;

    li {
      background: $grey14;
      display: inline-block;
      height: 50px;
      width: 33%;
      position: relative;

      &:before {
        content: " ";
        display: inline-block;
        height: 0;
        width: 0;
        position: absolute;
        border-style: solid;
        border-width: 25px 0 25px 10px;
        border-color: $transparent $transparent $transparent $white;
        z-index: 0;
      }

      &:first-child:before {
        border-color: $transparent;
      }

      button {
        background: $transparent;
        border: 0;
        line-height: 50px;
        outline: none; // temp fix
        width: 100%;

        &:after {
          content: " ";
          display: inline-block;
          height: 0;
          width: 0;
          position: absolute;
          right:  -10px;
          top: -0.5px;
          border-style: solid;
          border-width: 25px 0 25px 10px;
          border-color: $transparent $transparent $transparent $grey14;
          z-index: 1;
        }

        &:hover:not(:disabled) {
          background: $grey3;

          &:after {
            border-color: $transparent $transparent $transparent $grey3; 
          }
        }

        &:disabled {
          cursor: not-allowed;
        }

        .fa-layers {
          margin-right: 17px;

          .fa-circle {
            color: $grey48;
          }

          .fa-circle-dot {
            color: var(--semantic-color-surface-interactive-primary-default);
          }

          .fa-circle-check {
            color: var(--semantic-color-surface-interactive-primary-default);
          }

          .page-index {
            color: $white;
            top:  -19px;
            left: 7px;
          }
        }
      }

      &.valid {
        background-color: $green7;

        button {
          &:after {
            border-color: $transparent $transparent $transparent $green7;
          }

          &:hover {
            background-color: $green7;

            &:after {
              border-color: $transparent $transparent $transparent $green7;
            }
          }

          .fa-circle {
            color: $green2;
          }

          .fa-check-circle {
            display: block;
          }

          .name {
            color: $green2;
          }
        }
      }

      &:last-of-type {
        button:after {
          border-width: 0;
        }
      }

      &.active {
        background: $white;

        button:hover {
          background-color: $white;
          cursor: default;

          &:after {
            border-color: $transparent $transparent $transparent $white;
          }
        }

        button {
          background: $white;
          z-index: 100;
        
          &:after {
            border-left-color: $white;
          }
        }
      }

      .name {
        color: $grey4;
        font-size: 13px;
      }
    }
  }
}

// Provisioning templates form
.margin-right-2 {
  margin-right: 2%;
}
.margin-left-2 {
  margin-left: 2%;
}
.edgeconnector-page {
  position: absolute;
  top: 0px;
  left: 0; // remove menu space
  background: var(--semantic-color-background-primary);
  height: auto;
  min-height: 100%;
  width: 100%;
  padding: 25px 50px 100px;
  box-sizing: border-box;
  z-index: 1;
  min-width: 980px;
  // display: flex;
  // flex-flow: column;
  .back-to-ec {
    font-size: 20px;
    color: $grey1;
    position: relative;
    line-height:  22px;
    .fa-arrow-left {
      color:$blue15;
      font-size: 20px;
      position: absolute;
      left:  -25px;
      cursor: pointer;
    }
  }
  .edgeconnector-modal {
    position: relative;
    // top: 0;
    // left: 90px;
    // background: #f5f5f5;
    // height: auto;
    // min-height: 100%;
    // width: calc(100vw - 275px);
    // padding: 25px 50px 100px;
    // box-sizing: border-box;
    // z-index: 1;msp
    // display: flex;
    // flex-flow: column;

    .modal-content {
      margin-top: 30px;
      display: flex;
      width: 100%;
      padding: 0;
      float: left;
      align-items: stretch;
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 10px;
      position: relative;
      min-height:  500px;
      &.branch-provisioning-modal-content, &.cc-provisioning-modal-content {
        overflow: visible;
        background: var(--semantic-color-background-primary);
        height: calc(100vh);
        min-width: 970px;
      }
      .select-item {
        border-radius: 4px;
        background-color: var(--semantic-color-background-primary);
        color: var(--semantic-color-content-status-info-primary);
        .react-select__control{
          // border: none;
          border-radius: 4px;
          padding: 0px 8px;
          background-color: var(--semantic-color-background-primary);
          color: var(--semantic-color-content-status-info-primary);
          min-height: 32px;
          box-shadow: none;
          border: 1px solid var(--semantic-color-border-base-primary);
          .react-select__single-value {
            color:  var(--semantic-color-content-base-primary);
          }
        }
      }
      .wizard-nav {
        width: 25%;
        float: left;
        border-right: 1px solid var(--semantic-color-border-base-primary);
        ul {
          display: -webkit-box;
          display: -moz-box;
          display: -ms-flexbox;
          display: -webkit-flex;
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          list-style: none;
          padding-top: 20px;
          li {
            width: 100%;
            background: none;
            height: 50px;
            .name {
              color:  var(--semantic-color-content-base-primary);
              line-height: 50px;
              font-size: 13px;
            }
            &.active {
              &:after {
                content: " ";
                display: inline-block;
                height: 100%;
                position: absolute;
                border-width: 0 3px 0 0;
                border-style: solid;
                border-color:var(--semantic-color-surface-interactive-primary-default);
                z-index: 1;
                top: 0;
                right: 0;
              }
              button {
                // background-color: var(--semantic-color-surface-base-primary);
                background-color: var(--semantic-color-surface-alpha-active);
                color: var(--semantic-color-surface-interactive-primary-default);
                &:after {
                  border-color: var(--semantic-color-surface-interactive-primary-default);
                  content: " ";
                  display: inline-block;
                  height: 0;
                  width: 0;
                  position: absolute;
                  border-width: 37px 0 0 0;
                  border-style: solid;
                  width: 1px;
                  z-index: 1;
                  top: 32px;
                  left: 28px;
                }
                .fa-dot-circle {
                  font-size: 15px;
                }
                .name {
                  color:  var(--semantic-color-content-base-primary);
                  font-weight: 500;
                  line-height: initial;
                }
              }
            }
            &.valid {
              button {
                &.fa-circle {
                    color: var(--semantic-color-surface-interactive-primary-default);
                }
                &:after {
                  border-color: var(--semantic-color-surface-interactive-primary-default);
                }
                .fa-circle {
                    color: var(--semantic-color-surface-interactive-primary-default);
                }
              }
            }
            &:last-of-type button:after {
              border-width: 0;
            }
            &:before {
              display: none;
            }
            button {
              text-align: left;
              padding-left: 20px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;

              .fa-layers {
                .fa-circle {
                  font-size: 15px;
                }
              }
              &:hover {
                // background-color: var(--semantic-color-background-primary);
                background-color: var(--semantic-color-surface-alpha-active);
                &:not(:disabled) {
                  background-color: var(--semantic-color-surface-alpha-active);
                  span {
                    color: var(--semantic-color-content-base-primary);
                  }
                }
              }
              &:after {
                content: " ";
                display: inline-block;
                height: 0;
                width: 0;
                position: absolute;
                border-width: 37px 0 0 0;
                border-style: solid;
                width: 1px;
                border-color: var(--semantic-color-border-base-primary);
                z-index: 1;
                top: 32px;
                left: 28px;
              }
            }
            button.gateway-details {            
              &:after {      
                border-width: 51px 0 0 0;
                border-style: solid;
                width: 1px;
                border-color: var(--semantic-color-surface-interactive-primary-default);
                z-index: auto;        
                top: 18px        
              }
            }
          }
        }
      }
      .wizard-form {
        width: 75%;
        float: left;
        padding: 0;
        &.branch-provisioning-container, &.app-connector  {
          .form-sections-container {
            overflow: visible;
            .form-section {
              .dropdown-container {
                min-height: 75px;
                padding-left: 1px;
                margin: 0;
                overflow: visible;
              }
              .input-container {
                height: auto;
                width: 38%;
                min-width: 300px;
                margin-bottom: 16px;
                input {
                  margin-top: 0;
                }
              }
              .form-field-label {
                margin: 0px;
                // margin-top:  22px;
                // margin-bottom: 8px;
              }
              .entity-dropdown-container {
                width: 38%;
                min-width: 300px;
                min-height: 32px;
                margin-bottom: 16px;
                .select-item {
                  width: 100%;
                }
                .error-container {
                  padding-top: 6px;
                }
              }
            }
          }
        }
        &.app-connector  {
          .form-sections-container {
            overflow: auto;
            .form-section {
              .input-container {
                .input-wrapper {
                  width: 260px;
                }
              }
              .label-title-value {
                font-weight: 400;
                margin-bottom: 16px;
              }
              .fa-info-circle-icon{
                color: var(--semantic-color-content-status-info-primary);
                margin-left: 4px;
                font-size: 13px;
                font-weight: 400;
              }
              .entity-dropdown-container {
                min-width: 260px;
                .react-select__control {
                  border-radius: 8px;
                }
                .react-select__value-container {
                  max-height: 32px;
                }
              }
              .disabled-input {
                color: var(--semantic-color-content-interactive-primary-disabled);
                margin-left: 0;
              }
            }
          }
        }
        &.bc-details {
          min-width: 700px;
          .section {
            margin: 10px;
            padding: 20px;
            border-radius: 5px;
            // box-shadow: 0 0 12px 0 #e0e1e3;
            margin-bottom: 30px;
          }
          .pad-left-1rem {
            padding-left: 10px;
          }
          .section-title {
            color: #656666;
            font-weight: 500;
            letter-spacing:  0.2px;
            text-transform: uppercase;
          }
          .form-sections-container {
            .form-section {
              .lan-dhcp-container {
                display: flex;           
                .service-ip-del-container.dhcp-clear {
                  display: flex;
                  align-self: center;
                }     
              }
              .eye-icon, .pencil-icon, .delete-icon {
                color: var(--semantic-color-content-interactive-primary-default);
                cursor: pointer;
              }
              .delete-icon{
                margin-left: 7px,
              }
              .lan-title-ip-address .input-container input {
                // max-width: 130px;
                min-width: none;
              }
              .amf-add-more-container {          
                margin-bottom: 12px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: flex-start;
                width: 100%;
                .amf-add-more {
                  color: var(--semantic-color-content-status-info-primary);
                  font-weight: 500;
                  cursor: pointer;
                  &.disabled {
                    background: var(—surface-fields-disabled);
                    color: var(--semantic-color-content-interactive-primary-disabled);
                    cursor: not-allowed;
                    word-wrap: break-word;
                  }
                }
              }
              .static-route {
                margin-bottom: 4px;
                position: relative;
                padding: 0 4px 0 0;
                .input-container {
                  min-width: 1px;
                  input {
                    min-width: 1px;
                    width: 100%;
                  }
                }
                .input-wrapper {
                  width: 100%;
                }
                .service-ip-del-container {
                  position: absolute;
                  right: 0;
                  bottom: 0;
                }
              }
              .netInterface-container, .upLink-container, .vlan-container {
                position: relative;
                padding-right: 30px;
                width: 100%;
                .error {
                  .react-select__control {
                    border: 1px solid var(--semantic-color-border-status-danger-active);
                    border-radius: 4px;
                  }
                }
                .react-select__input {
                  height: 18px;
                  max-height: 18px;
                }
                .service-ip-del-container {
                  position: absolute;
                  right: 0;
                  bottom: 0;
                  .delete-button, .delete-icon {
                    border: none;
                    background-color: transparent;
                    &.disabled {
                      background: var(—surface-fields-disabled);
                      color: var(--semantic-color-content-interactive-primary-disabled);
                      cursor: not-allowed;
                      word-wrap: break-word;
                    }
                  }
                }
              }
              .include-address-range {
                display: flex;
                .input-container {
                  min-width: 1px;
                  // max-width: 125px;
                  input {
                    min-width: 1px;
                    // max-width: 124px;
                  }
                }
                .separator {
                  margin-right: 16px;
                  align-self: center;
                }
              }
              .dhcp-include-address-range-container {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 4px;
                .input-container {
                  margin-right: 0px;
                  // min-width: 132px;
                }
                .dhcp-include-address-range-list-container {
                  position: relative;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  width: 575px;
                  padding-right: 0px;
                  margin-right: 2%;
                }
                .separator {
                  margin: 0 4px;
                }
                .service-ip-del-container {
                  position: absolute;
                  right: -24px;
                  bottom: 0;
                  // bottom: 4px;
                }
              }
              .default-lease-time {
                max-width: 287px;
                margin: 0;
              }
              .dhcp-options-container {
                position: relative;
                display: flex;
                margin-bottom: 4px;
                min-width: fit-content;
                min-height: 32px;
                // margin-right: 2%;
                .select-item {
                  margin-right: 0;
                }
                .react-select__value-container {
                  height: 28px;
                  align-content: flex-end;
                }
                .dhcp-options-container-custom-option-main-label-continer {
                  display: flex;
                  min-width: 50%;
                  min-height: 32px;
                  background-color: var(--background-main,#f1f2f3);
                  align-items: center;
                }
                .dhcp-options-container-custom-option-label-continer {
                  display: flex;
                  min-width: 50%;
                }
                .input-container {
                  margin-right: 0;
                }
                .select-container-with-search {
                  min-width: 130px;
                }
                .form-field-label-wrapper {
                  min-width: 130px;
                  width: 100%;
                  display: flex;
                  align-items: center;
                  background-color: var(--semantic-color-background-primary);
                  margin-right: 4px;
                  border-radius: 4px;
                  .form-field-label.label-title {
                    padding: 0 8px;
                  }
                }
                .select-container {
                  // min-width: 260px;
                  margin-right: 4px;
                  border-radius: 4px;
                  .my-select-with-search__control {
                    color:  var(--semantic-color-content-base-primary);
                    border: 1px solid var(--semantic-color-border-base-primary);
                    background-color: var(--semantic-color-background-primary);
                    max-height: 32px;
                    min-height: 0;
                  }
                  .my-select-with-search__menu {
                    background-color: var(--semantic-color-background-primary);
                    border: 1px solid var(--semantic-color-border-base-primary);
                    .my-select-with-search__option {
                      background-color: var(--semantic-color-background-primary) !important;
                      color: var(--semantic-color-content-base-primary);
                    }
                    .my-select-with-search__option--is-focused {
                        background-color: var(--semantic-color-content-interactive-primary-focus) !important;
                        color: var(--semantic-color-content-immutable-white);
                        line-break: anywhere;
                    }
                    .my-select-with-search__option--is-selected {
                      background-color: var(--semantic-color-content-interactive-primary-active) !important;
                      color: var(--semantic-color-content-immutable-white);
                      // color: var(--semantic-color-content-inverted-base-primary);
                      line-break: anywhere;
                    }
                  }
                  .my-select-with-search__value-container {
                    max-height: 30px;
                    padding: 0;
                    margin: 0;
                  }
                  .my-select-with-search__placeholder {
                    padding: 0 8px;
                  }
                }
              }
              .static-lease-container, .dhcp-options {
                margin-top: 16px;
                .input-container {
                  margin-right: 0px;
                  margin-bottom: 0px;
                  min-width: 1px;
                  &:first-child {
                    margin-right: 4px;
                  }
                }
                .dhcp-options-row.input-container {
                  margin-right: 2%;
                }
                .static-lease-container-item {
                  margin-bottom: 4px;
                }
                .static-lease-list-container {
                  display: inline-flex;
                  margin-right: 2%;
                  position: relative;
                  // padding-right: 30px;
                  // min-width: fit-content;
                  // .input-container {
                  //   // margin-right: 26px;
                  // }
                }
                .service-ip-del-container {
                  position: absolute;
                  right: -24px;
                  bottom: 0;
                }
              }
              .subInterface-add-more-ranges-container, .subInterface-add-more-lease-container {
                margin-left: 192px;
              }
              // .subInterface-add-more-container {
              //   margin-left: 50px;
              // }
              .subInterface-container {
                margin-left: 0;
                margin-top: 8px;
                .subInterface-main-line-container {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;                  
                  position: relative;
                  .service-ip-del-container {
                    position: absolute;
                    right: 50px;
                    bottom: 0;
                  }
                }
                &.bottom-border {                  
                  // margin-left: 50px;
                  border-bottom: 1px solid #E3E5E6;
                }
              }
              .sub-interface-top-items-container {
                max-width: 20%;
                .select-item {
                  min-width: 100px;
                }
                .react-select__menu {
                  overflow: hidden;
                  .react-select__menu-list {
                    max-height: fit-content;
                    min-height: fit-content;
                    overflow: visible;
                  }
                }
                .input-container input {
                  min-width: 0;
                  max-width: 100px;
                }
                &.description {
                  max-width: unset;
                  min-width: 370px;
                  padding-right: 80px;
                  margin-right: 0;
                }
              }
              .netInterface-name-container {
                min-width: 250px;
                margin-left: 12px;
                .react-select__menu {
                  overflow: hidden;
                  .react-select__menu-list {
                    max-height: fit-content;
                    min-height: fit-content;
                    overflow: visible;
                  }
                }
              }  
              // .static-route-container {
              //   margin-bottom: 8px;
              //   position: relative;
              //   .service-ip-del-container {
              //     position: absolute;
              //     right: 0;
              //     bottom: 0;
              //     // bottom: 5px;
              //   }
              // }
              .disabled-input.device-model {
                color: var(--semantic-color-content-interactive-primary-disabled);
                font-size: 13px;
                font-weight: 500;
                line-height: 20px;
                letter-spacing: 0.04em;
                text-align: left;
                text-transform: uppercase;
              }
              .entity-dropdown--disabled {
                .react-select__single-value--is-disabled {
                  color: var(--semantic-color-content-interactive-primary-disabled);
                  font-size: 13px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 20px; /* 153.846% */
                }
              }
              .manual-text {
                color: #000;                
                font-size: 13px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px; 
              }
              .service-ip-add-more {
                color: var(--semantic-color-content-status-info-primary);
                font-weight: 500;
                cursor: pointer;  
              }
              .input-container.fit-content {
                height: fit-content;
              }
              .input-container.model-number-container {
                margin-bottom: 16px;                              
              }
              .portMap-container {
                display: inline-block;
                width: 100%;
                .portMap-image {
                  display: block;
                  margin-left: auto;
                  margin-right: auto;
                }
              }
              .input-container.device-port-container {
                min-height:68px;
                float: none;
                &.is-service {
                  padding: 10px 0;
                }
                .device-port-title {
                  display: flex;
                }
                .device-port-icon {
                  margin-right: 4px;
                  width: 10px;
                  height: 20px;
                  font-weight: 900;
                  font-size: 13px;
                  line-height: 20px;
                  color: #000000;
                }
              }
              .input-container.review {
                margin: 10px 20px 10px 0;
                height: auto;
                &.half-width {
                  width: 47%;
                  float: left;
                }
                &.full-width {
                  width: 100%;
                  float: none;
                }                
                // width: 45%;
                .svg-inline--fa {
                  width: 100%;
                  &.invalid {
                    color: var(--semantic-color-border-status-danger-active);
                  }
                }
                &.service-ip-container {
                  .service-ip-input {
                    display: flex;
                    flex-direction: row;
                    justify-content: flex-start;
                    .input-container {
                      margin: 0;
                    }
                  }
                  .service-ip-del-container {
                    align-self: end;  
                  }
                }
                .review-item {
                  display: flex;
                  .entity-dropdown-container {
                    width: 87%;
                    .select-item {
                      width: 97%;
                    }
                  }
                  .radio-button-container {
                    margin: 0 8px;
                    width: calc(86% - 16px);
                  }
                  .entity-dropdown, .disabled-input, .input-container {
                    width: 86%;
                  }
                  .select-item {
                    width: 86%;
                  }
                  .form-textarea, .disabled-input-text-area {
                    width: 85%;
                    margin: 8px;
                  }
                }             
                .dns-ip-icon {
                  width: 15px;
                  height: 35px;
                  border: none;
                  background: none;
                  display: inline-block;
                }
                .input-container {
                  width: 90%;
                  float: left;
                }
                .bg-detail-form-value {
                  width: 90%;
                  float: left;
                }
              }
              .section-inner-title {
                width: 100%;
                margin: 0px;
                color: #939393;
                font-size: 12px;
                margin-top: 32px;
                &:first-of-type {
                  margin-top: 0px;
                }
              }
              .bc-connector {
                margin-bottom: 20px;
                color: #656666;
                font-size: 13px;
                font-weight: 500;
                letter-spacing: 0;
                line-height: 16px;
              }
              .form-value {
                font-weight: normal;
              }

              // .form-field-label-wrapper {               
              //   width: fit-content;
              // }
              .form-field-label {
                margin: 0px;
                font-weight: 500;
                color: var(--semantic-color-content-interactive-primary-disabled);
                &.invalid {
                  color: var(--semantic-color-content-status-danger-primary);
                }
                &.model-number {
                  font-style: normal;
                  font-weight: 500;
                  font-size: 13px;
                  line-height: 20px;
                }
              }
              .label-title {
                font-weight: 500;
                color:  var(--semantic-color-content-base-primary);
                &.invalid {
                  color: var(--semantic-color-content-status-danger-primary);
                }
              }
              .main-label-title{
                font-size: 13px;
                font-weight: 500;
                color: $darkest-gray-text;
                &.invalid {
                  color: var(--semantic-color-content-status-danger-primary);
                }
              }
              .main-label-title-up-s {
                color: #939393;
                font-size: 13px;
                font-weight: 500;
                line-height: 20px;
                letter-spacing: 0.04em;
                text-align: left;
                text-transform: uppercase;
                &.invalid {
                  color: var(--semantic-color-content-status-danger-primary);
                }
              }
              .separator-line {
                width: 100%;
                float: left;
              }
            }
          }
        }
        &.bc-group-details, &.cc-group-details {
          .form-sections-container {
            // overflow: visible;
            .form-section {
              .form-field-label {
                margin: 0px;
              }
              .form-field-label.cellular-configuration {
                margin-top: 16px;
              }
              .cellular-configuration-section {
                display: flex;
                flex-direction: column;
                row-gap: 16px;
                margin-top: 16px;
                
                .radio-button-container {
                  margin-bottom: 0;
                }

                .disabled-input {
                  margin: 0;
                  margin-top: 4px;
                  cursor: unset;
                  color: inherit;
                }

                .form-field-label.cellular-configuration {
                  margin-top: 0px;
                }
              }
              .cellular-configuration-mode{
                margin-top: 8px;
                width: fit-content;
                display: flex;
                column-gap: 4px;
                label.form-field-label-wrapper {
                  display: inline;
                  span.form-field-label.disabled-input {
                    font-weight: normal;
                    line-height: unset;
                    width: fit-content;
                    margin-right: 4px;
                  }
                }
              }
              .cellular-configuration-select:has(.react-select__menu) {
                margin-bottom: 150px;
              }
              .information-icon {
                color: var(--semantic-color-content-status-info-primary);
                font-size: initial;
              } 
              .input-container {
                margin: 0;
                height: auto;
                width: 38%;
                input {
                  margin-top: 0;
                  margin-bottom: 16px;
                }
              }
              .form-textarea {
                margin: 0;
                width: 63%;
                padding: 5px;
                resize: none;
              }
              
              .entity-dropdown-container {
                width: 38%;
                margin-bottom: 20px;
                float: left;
                .select-item {
                  width: 100%;
                }
                .error-container {
                  padding-top: 6px;
                }
              }
              .dropdown-container {
                overflow: visible;
                float: left;
              }
              .bc-available-info {
                margin-left: 2%;
                float: left;
                margin-top: 10px;
                color: $blue15;
                span {
                  margin-left: 5px;
                }
              }
            }
          }
        }
        &.name-container {
          .form-sections-container {
            .form-section {
              .input-container {
                margin: 10px 20px 10px 0;
                height: auto;
                width: 38%;
              }
            }
          }
        }
        &.appliance-container {
          .form-sections-container {
            // overflow: visible;
            .form-section {
              .fa-info-circle-icon{
                margin-left: 4px;
                color: var(--semantic-color-content-status-info-primary);
              }
              .form-tab-group {
                color: #005e9b;
                border-bottom: 3px solid #e0e1e3;
                height: 26px;
                margin-bottom: 12px;
              }
              .port-no-separator-line {
                margin: 16px 0 0 0;
                height: 1px;
                background-color: #E3E5E6;
              }
              .tab-row-container {
                position: relative;
                .tabs-items-add-more {
                  right: 0;
                  top: 0;
                  position: absolute;
                }
              }
              .tabs-items .invalid {
                .tabPactive, .tabP {
                  color: var(--semantic-color-content-status-danger-primary);
                }
              }
              .tabs-items div {
                margin-right: 20px;             
              }
              .tabs-items div div.highlighter { 
                margin-top: -6px;
                // height: 2px;
              }
              .review-container .review-title-container {
                margin-bottom: 16px;
              }
              .bc-details-panel-container {
                width: 100%;
                display: flex;
                .nw-interface .service-ip-del-container {
                  right: 30px;
                }
                .sub-nw-interface .service-ip-del-container {
                  left: 55px;
                }
                .nw-interface, .sub-nw-interface {
                  display: flex;
                  position: relative;
                  .interface-title {
                    color:  var(--semantic-color-content-base-primary);
                    font-size: 13px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 20px; 
                  }
                  .service-ip-del-container {                        
                    // position: absolute;
                    // right: 30px;
                    .delete-button, .delete-icon {
                      border: none;
                      background-color: transparent;
                      cursor: pointer;                         
                      .fa-trash {
                        cursor: pointer; 
                      }
                      &.disabled {
                        background: var(—surface-fields-disabled);
                        color: var(--semantic-color-content-interactive-primary-disabled);
                        cursor: not-allowed;
                        word-wrap: break-word;
                        .fa-trash {
                          background: var(—surface-fields-disabled);
                          color: var(--semantic-color-content-interactive-primary-disabled);
                        }
                      }
                    }
                  }
                }
                .bc-details-panel-left {
                  min-width: 140px;
                  max-width: 140px;                  
                  .input-container.review.half-width {
                    margin: 0 0 12px 0;
                  }
                  .disabled-input {
                    margin-top: 0;  
                    margin-top: 0;
                    color: var(--semantic-color-content-interactive-primary-disabled);
                    font-size: 13px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 20px;
                    cursor: default;
                    display: flex;
                    width: fit-content;
                  }
                }
                .bc-details-panel-right {
                  display: flex;
                  flex-direction: column;
                  min-width: 100px;
                  max-width: 900px;
                  width: 100%;
                  .bc-details-panel-right-item {
                    width: 100%;
                    display: flex;
                    position: relative;
                    margin-bottom: 4px;
                    padding-right: 16px;
                    // &.lease-time {
                    //   // padding-right: 21px;
                    // }
                    &.upLink-container, &.vlan-container {
                      margin-bottom: 8px;
                    }
                    .title-label {
                      min-width: 180px;
                      max-width: 180px;
                      align-self: center;
                      margin-right: 12px;
                    }
                    .title-label-review-group {
                      min-width: 162px;
                      max-width: 140px;
                      align-self: center;
                      // margin-right: 12px;
                    }
                    .input-container input, .input-wrapper {
                      width: 100%;
                      min-width: 1px;
                    }
                    .option-button-container.option-group-container {
                      padding: 0;
                      .option-buttons .option-button label {
                        justify-content: left;
                      }
                    }
                  }                  
                }
              }
              .form-textarea {
                height: 30px;
              }
              .dropdown-container{
                position: relative;
                width: 100%;
                display: inline-block;
                color: var(--semantic-color-content-interactive-primary-default);
                min-height:  550px;
                ul.dropdown-list.open {
                  border: 1px solid var(--semantic-color-background-pale);
                }
                .dropdown-search {
                  input {
                    margin-top:0 ;
                  }
                }
              }
              

            //   div.drop-down-container ul.drop-down-list {
            //     position: absolute;
            //     margin-top: -1px;
            //     background-color: var(--semantic-color-surface-base-primary);
            //     width: 100%;
            //     list-style-type: none;
            //     border: 1px solid #D1DCF3;
            //     border-radius: 10px;
            //     visibility: hidden;
            //     opacity: 0;
            //     transition: visibility 0s, opacity 0.1s linear;
            //     z-index: 9;
            // }


              .form-textarea {
                margin: 0;
                padding: 6px 8px;
                width: 100%;
                &.invalid {
                  border: 1px solid #d81903;
                }
              }
              .select-item {
                width: 100%;
              }
              .input-container {
                // margin: 10px 20px 10px 0;
                // margin-bottom: 30px;
                height: auto;
                width: 100%;
                // margin-bottom: 16px;
                input {
                  border-radius: 4px;
                  margin-top: 0;
                }
                #provUrlData\.deviceTemplate\.name {
                  margin-top: 0;
                }
                &.inputText {                  
                  padding-right: 4%;
                }
              }
              .form-field-label {
                margin: 0px;
                // margin-top:  22px;
                // margin-bottom: 8px;
              }
              .entity-dropdown-container {
                width: 38%;
                // margin-bottom: 40px;
                .select-item {
                  width: 100%;
                }
                .error-container {
                  padding-top: 6px;
                }
              }
            }
          }
        }
        &.location-container {
          .form-sections-container {
            overflow: visible;
            .form-section {
              .form-textarea {
                margin: 0;
                padding: 6px 8px;
              }
              .select-item {
                width: 100%;
              }
              .input-container {
                // margin: 10px 20px 10px 0;
                // margin-bottom: 30px;
                height: auto;
                width: 38%;
                input {
                  border-radius: 4px;
                  &.dropdown-search-text{
                    border-radius: 8px;
                  }
                }
              }
              .dropdown-search{
                max-width: 91%;                
              }
              .form-field-label {
                margin: 0px;
                // margin-top:  22px;
                // margin-bottom: 8px;
              }
              .entity-dropdown-container {
                width: 38%;
                // margin-bottom: 40px;
                .select-item {
                  width: 100%;
                }
                .error-container {
                  padding-top: 6px;
                }
              }
            }
          }
        }
        .form-sections-container {
          max-height: none;
          border: none;
          padding: 3% 4%;
          height: 100%;
          .review-show-more {
            color: var(--semantic-color-content-status-info-primary);
            font-weight: 500;
            cursor: pointer;
          }
          .margin-top-review {
            margin-top: 12px;
          }
          .form-section-label {
            padding: 22px 24px 22px 0;
          }
          .form-section {
            box-shadow: none;
            padding: 0;
            margin: 0;
            .section-inner-title {
              width: 100%;
              margin-bottom: 20px;
              color: #939393;
              font-size: 12px;
            }
            .dropdown-container {
              width: 100%;
              margin-top:  23px;
              overflow: hidden;
              .form-field-label {
                margin-top: 0;
              }
            }
            .image-wrapper {
              display: flex;
              flex-direction: row;
            }
            .input-container {
              &.review {
                .disabled-input {
                  float: left;
                  width: 90%;
                  white-space: break-spaces;
                  max-height: 90px;
                  overflow: auto;
                }
                .svg-inline--fa {
                  width: 0;
                  cursor: pointer;
                  color: $blue23;
                }
              }
              &.small-width {
                width: 50px;
              }
              &.disabled {
                input:disabled {
                  border-bottom-color: var(--semantic-color-border-interactive-primary-disabled);
                  cursor: not-allowed;
                }
              }
            }
            .input-container,
            .select-item {
              width: 48%;
              height: 30px;
              margin-left: 0;
              margin-right: 2%;
            }
            
            .section-cloud {
              display: block;
              padding-bottom: 20px;
              width: 100%;
              overflow: hidden;
              p {
                font-size: 13px;
                line-height:  24px;
                text-align: left;
                color: $grey1;
                font-weight: 500;
              }
              &.fade-in {
                .image-radio-button-container .radio-buttons .radio-button label {
                  opacity: 1;
                }
              }
            }
            .radio-button-container  {
              margin-bottom: 16px;
            }
          }
        }
      }
    }
    .modal-footer {
      position: absolute;
      background: var(--semantic-color-background-primary);
      bottom:  -60px;
      left: 0;
      width: 97%;
      display: flex;
      justify-content: space-between;
      padding-left: 0;
      padding-right: 0;
      button {
        background-color: $transparent;
        border: none;
        color: var(--semantic-color-content-interactive-primary-default);
        line-height: 16px;
        font-weight: 500;
        min-width:  75px;
        &.primary-button {
          color: var(--semantic-color-content-inverted-base-primary);
          border-radius: 5px;
          background-color: var(--semantic-color-content-interactive-primary-default);
          height: 32px;
          padding: 0 17px;
          float: right;
        }
        &.previous {
          display: inline-block;
        }
        &.secondary-button,
        &.quit-wizard {
          box-sizing: border-box;
          height: 32px;
          padding: 0 17px;
          border: 1px solid var(--semantic-color-content-interactive-primary-default);
          border-radius: 5px;
          background-color: inherit;
          margin: 0 5px;
        }
        &:disabled {
          background-color: var(--semantic-color-content-interactive-primary-disabled);
          cursor: not-allowed;
        }
      }
    }
  }

.edgeconnector-modal.provisioning-view-modal {
  padding: 0px;
  width: 8 70px;
  min-height: fit-content;
  .modal-content {
    // min-height: fit-content; 
    overflow: hidden;
    margin: 0px;
    .wizard-form {
      width: 100%;
      float: left;
      padding: 0;
      .form-sections-container {
        padding-bottom: 60px;
        .form-section .input-container.review .disabled-input {
          width: 100%;
        }
      }
    }
    .modal-footer {
      margin-bottom: 60px;
      width: 100%;
    }
  }
}
}

.bc-provisioning-general-info-title,.cc-provisioning-general-info-title {
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 20px;
  color:  var(--semantic-color-content-base-primary);
  .bc-provisioning-general-info-sub-title,.cc-provisioning-general-info-sub-title {
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    text-align: left;
  }
  .bc-provisioning-general-info-description,.cc-provisioning-general-info-description {
    width: 550px;
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;    
    color:  var(--semantic-color-content-base-primary);
    margin-top: 8px;
    margin-bottom: 16px;
    white-space: break-spaces;
  }
}


.input-container .input-wrapper input {
  align-items: center;
  padding: 6px 8px;
  width: 260px;
  height: 32px;

  /* DeepBlue/Surface/Default/Surface-03 */
  background: var(--semantic-color-background-primary);
  border-radius: 4px;

  /* Inside auto layout */
  flex: none;
  order: 0;
  flex-grow: 1;
  border: 1px solid var(--semantic-color-border-base-primary);
}

.view-branch-connector.provisioning-view-modal {
  .modal-content {
    overflow: hidden;
    .wizard-form.bc-details {
      .form-sections-container {
        .form-section {
          .disabled-input {
            color: var(--semantic-color-content-interactive-primary-disabled);
            margin-top: 0;
          }
        }
      }
    }

  }
}
.edgeconnector-page .edgeconnector-modal .modal-content .wizard-form.appliance-container .form-sections-container .form-section .bc-details-panel-container .bc-details-panel-right .bc-details-panel-right-item .review-title.full-width .title-label-review-group {
  min-width: 152px;
}

.review-custom-options {
  display: block;  
  .space-tab {
    padding-left: 30px !important;
  }
}

.max-width-270px {
  max-width: 270px;
}
.margin-bottom-0px {
  margin-bottom: 0px !important;
}
.margin-bottom-4px {
  margin-bottom: 4px !important;
}
.margin-bottom-16px {
  margin-bottom: 16px !important;
}
.margin-bottom-50px {
  margin-bottom: 50px !important;
}
.position-relative {
  position: relative !important;
}

.margin-left-0px {
  margin-left: 0px !important;
}
.margin-left-20px {
  margin-left: 20px !important;
}
.margin-left-26px {
  margin-left: 26px !important;
}
.min-height-32px {
  min-height: 32px !important;
}
.min-height-50px {
  min-height: 50px !important;
}
.margin-top-16px {
  margin-top: 16px !important;
}
.margin-right-8px {
  margin-right: 8px !important;
}
.margin-right-32px {
  margin-right: 32px !important;
}
.min-width-287px {
  min-width: 287px !important;
}
.min-width-350px {
  min-width: 350px !important;
}
.margin-bottom-10px {
  margin-bottom: 10px !important;
}
.margin-bottom-12px {
  margin-bottom: 12px !important;
}
.center-height {
  display: flex;
  align-items: center;
}
.width-38percent {
  width: 38% !important;
}
.width-18percent {
  width: 18% !important;
}
.min-width-100px {
  min-width: 100px !important;
}

.max-width-100px {
  min-width: 0 !important;
  max-width: 100px !important;
}

.prov-url-conatiner {
  display: flex;
  padding: 10px 30px;
  height: auto;
  background-color: var(--semantic-color-background-secondary);
  width: 100%;
  position: absolute;
  left: 0;
  top: 45px;
  .prov-url-wrapper {
    // padding: 15px 30px;
    height: auto;
    background-color: transparent;
    width: 60%;
    // position: absolute;
    // left: 0;
    // top: 45px;
  }
  .prov-children-wrapper {
    padding: 10px 0;
    display: flex;
  }
  .title-text {
    font-size: 13px;
    line-height: 16px;
    color: var(--semantic-color-content-base-secondary);
    padding-bottom: 10px;
  }
  .url-text {
    color: var(--semantic-color-content-base-primary);
    font-weight: 500;
    font-size: 13px;
    line-height: 16px;
    letter-spacing:  0.59px;
    .fa-copy {
      font-size: 15px;
      margin-left: 10px;
      vertical-align: top;
      cursor: pointer;
      color: var(--semantic-color-content-interactive-primary-default);
    }
  }
}

}

// #applianceName.dropdown {
//   border-radius: 4px;
//   min-height: 32px;
//   height: 32px;
//   background: #f1f2f3;
//   border: none;
//   .dropdown-display {
//     width: 100%;
//     padding-left: 8px;
//     padding-right: 4px;
//   }
// }
@import 'scss/pages.scss';

.ec-root-page {
.main-container-gateways {
  padding: 19px 25px;
  background-color: var(--semantic-color-background-primary);
  min-height: 100%;
}

.gateways-title {	
  height: auto;
  width: auto;
  color: var(--semantic-color-content-base-primary);
  .page-title {
    padding-left: 0px;
  }
}
.gateways-fragment {
  margin-bottom: .625rem;
  display: flex;
  justify-content: space-between;
}

.gateways-wrapper {
  .app-table-container {
    .ReactTable.app-table {
      .rt-thead {
        .header-title {
          font-size: 13px;
          line-height: 30px;
          display: inline-block;
        }
      }
      .has-nested-true {
        .rt-thead {
          .header-title {
            font-size: 13px;
            line-height: 16px;
          }
          .rt-th:nth-child(1) {
            display: block;
            .sorting-icon-cont {
              display: none;
            }
          }
          .rt-th:nth-child(2) {
            padding: 0;
            border: 0;
          }
          .rt-th:nth-child(3) {
            .rt-resizable-header-content {
              left:  -35px;
              position: relative;
              margin-right:  -35px;
            }
          }
        }
      }
    }
  }   
}

.configure-gateways-template {
  &.wizard-form .form-sections-container{
    overflow-x: hidden;
    border: 1px solid var(--semantic-color-border-base-primary);
  }
  .fa-check-square, .fa-times-square {
    margin: 10px 0 10px 5px;
    color: #939393;
  }
}

// overwrite react table css
.zia-gateways {
  .delete-icon,
  .pencil-icon,
  .view-icon,
  .download-icon {
    margin: 0 10px;
  }
  .ReactTable.app-table {
    max-height: calc(100vh - 28rem);
  }
  .column-layout-config-container {
    border-left: 0;
  }
}

.modal-body.gateway-modal {
  left: calc(50% - 345px);
  width: 770px;
  .add-custom-app-form {
    .form-sections-container {
      max-height: none;
      margin-bottom: 50px;
    }
    .form-footer {
      border-radius: 0 0 5px 5px;
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
    }
  }
}

.add-edit-dns-gateways,
.add-edit-gateways {
  &.add-custom-app-form .form-sections-container {
    border: 1px solid var(--semantic-color-border-base-primary);
    .form-section {
      .input-container {
        .input-wrapper {
          width: 280px;
          &.textarea {
            width: auto;
          }
          input {
            width: 280px;
            &:-webkit-autofill,
            &:-webkit-autofill:hover,
            &:-webkit-autofill:focus {
              -webkit-box-shadow: 0 0 0px 1000px var(--semantic-color-background-primary) inset;
              -webkit-text-fill-color: var(--semantic-color-content-base-primary);
              
            }
          }
        }
      }
      .entity-dropdown-container {
        .entity-dropdown {
          width: 280px;
        }
      }
    }
  }
  .textarea-wrapper {
    .input-container {
      width: auto;
    }
  }
}

.add-edit-gateways {
  .form-section {
    padding: 16px 16px 30px;
    .g-name, .gateway,
    .g-ip {
      width: 50%;
      float: left;
      .input-container {
        width: 50%;
      }
    }
    .form-left{
      float: left;
    }
    .form-right{
      float: right;
      padding-left: 5.3em;
    }
    label span {
      margin-left: 0;
    }
    .row-parent {
      margin-bottom: 20px;
    }
    &.textarea-wrapper .input-container{
      width: 100% !important;
      float: left;
      .form-textarea {
        margin: 5px 2em .5em 0;
        resize: none;
      }
    }
    &.textarea-wrapper .input-container.error{
      width: 100% !important;
      float: left;
      .form-textarea {
        margin: 5px 2em .5em 0;
        resize: none;
        border-color: var(--semantic-color-content-status-danger-primary);
      }
    }
  }
}

}
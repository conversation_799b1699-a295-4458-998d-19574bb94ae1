@import "scss/colors.scss";

.ec-root-page {
.main-container.vdi-device-managenent    {
  // overflow: auto;
  .container-row-cc-group.vdi-table-container{
    .config-table-container .table-container {
      overflow: hidden;
    }
  }
  .svg-inline--fa.fa-pencil.pencil-icon {
    margin-right: 4px;
  }
  .tabP, .tabPactive  {
    //styleName: Tabs / M;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0em;
    text-align: center;
  }
  .search-box {
    input {
      background-color: var(--semantic-color-surface-base-secondary);
      color: var(--semantic-color-content-base-primary);
    }
  }
  .table-actions-container {
    padding: 12px;
    background-color: var(--semantic-color-background-primary);
    border-radius: 8px;
  }
  .container-row-cc-group{
    min-height: max-content;
  }  

}

.modal-content.modal-body.vdi-modal-content .wizard-form .form-sections-container .form-section .checkbox-container label span {
  left: 4px;
}
.modal-content.modal-body.vdi-modal-content {
  padding: 19px 25px;
  .config-table-container .table-container {
    overflow: hidden;
  }
  // .config-table-container .table-container .content .list-container {
  //   overflow: visible;
  // }
  .dropdown {
    background: 	var(--semantic-color-background-primary);
    color:  var(--semantic-color-content-base-primary);
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 0.25rem;
    max-width: 330px;
    justify-content: flex-start;
    padding-left: 8px;
    .dropdown-value {
      min-width:  290px;
    }
  }
  // .drop-down-selected-value {
  //   max-width: 320px;
  //   min-height: 40px;
  // }
  // input#hostNamePrefix {
  //   min-height: 40px;
  //   border-radius: 4px;
  // }
  // .dropdown .dropdown-selector .dropdown-lists .dropdown-unselected {
  //   & .dropdown-search {
  //     background-color: var(--semantic-color-surface-base-primary);
  //     & input {
  //       border-radius: 8px;
  //       padding-left: 12px;
  //       min-width: 220px
  //     }
  //   }
  //   & .dropdown-search-text-add-button {
  //     width: 200px
  //   }        
  //   & .dropdown-search-text-add-button-icon { 
  //     color: $blue2,;
  //     font-size: 12px;
  //     cursor: pointer;
  //   }
  // }
  // .search-container-device-group {
  //   display: flex;
  //   align-self: end;
  //   margin-bottom: 8px;
  // }
  .wizard-form.configure-provisioning-template, .wizard-form.vdi-devices-criteria {
    .form-sections-container {
      overflow: visible;
    }
    .select-container-with-search {
      min-width: 300px;
    }
    .entity-dropdown-container-criteria .select-container-with-search {
      min-width: 200px;
      margin-bottom: 10px;
      .my-select-with-search__placeholder {
        color: var(--semantic-color-content-base-primary);
        text-align: center;

        /* Buttons/S */
        font-size: 13px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px; /* 153.846% */
        }
        .my-select-with-search__control {
          color:  var(--semantic-color-content-base-primary);
          border: 1px solid var(--semantic-color-border-base-primary);
          background-color: var(--semantic-color-background-primary);
        }
        .my-select-with-search__value-container, .my-select-with-search__indicator {
          padding: 0;
        }
        .my-select-with-search__menu {
          background-color: var(--semantic-color-background-primary);
          border: 1px solid var(--semantic-color-border-base-primary);
          box-shadow: none;
          .my-select-with-search__option {
            background-color: var(--semantic-color-background-primary) !important;
            color: var(--semantic-color-content-base-primary);
          }
          .my-select-with-search__option--is-focused {
              background-color: var(--semantic-color-content-interactive-primary-focus) !important;
              color: var(--semantic-color-content-immutable-white);
              line-break: anywhere;
          }
          .my-select-with-search__option--is-selected {
            background-color: var(--semantic-color-content-interactive-primary-active) !important;
            color: var(--semantic-color-content-immutable-white);
            // color: var(--semantic-color-content-inverted-base-primary);
            line-break: anywhere;
          }
        }
        
        .my-select-with-search__menu-list {
          max-height: 120px;
          background-color: var(--semantic-color-surface-base-primary);
        }

    }
    .form-section.vdi-device-management-form, .form-section {
      .form-section-device-criteria {
        position: relative;
        width: 100%;
        padding: 0 50px;
        .link-criteria {
          position: absolute;
          top: 21px;
          left: 25px;
          .link-criteria-item {
            border: 1px solid #E9E9E9;
            border-right: none;
            border-bottom: none;
            height: 82px;
            width: 24px;
            display: flex;
            align-items: center;
            .link-criteria-item-label {
                left: -15px;
                position: absolute;
                color: #69696A;
                background-color: #F1F2F3;
                font-size: 11px;
                font-weight: 400;
                line-height: 14px;
                letter-spacing: 0.01em;
                text-align: left;
                padding: 4px;
                border-radius: 4px;
            }
            &:last-of-type {
              border-bottom: 1px solid #E9E9E9;
            }
          }
        }
      }
      .form-textarea {
        // border: none;
        box-shadow: none;
        background-color: var(--semantic-color-surface-base-primary);
      }
      .input-container.full-width, .g-row {
        .vdi-x-mark-icon.svg-inline--fa {
          color: var(--semantic-color-content-status-danger-primary);
          margin-left: 8px;
        }
      }

    }
  }
}
.edgeconnector-page .edgeconnector-modal .modal-content.vdi-modal-content .wizard-form.review-page .form-sections-container .form-section 
  .input-container.review.half-width {
  margin-bottom: 0;
}



.modal-content.modal-body.vdi-modal-content {
  padding: 19px 25px;
  .wizard-form .form-sections-container .form-section .disabled-input {
    margin-top: 0;
    margin-left: 0;
    color: #3F3F40;
    font-weight: 400;
  }
  .form-section.partner-configuration-form .external-id-buttons {
    min-height: 0;
  }
  .partner-integration-table-container {
    .ellipsis-menu {
      display: inline-flex;
      flex-direction: column;
      align-items: flex-start;

      border-radius: 4px;
      background: var(--deep-blue-surface-default-surface-00, var(--semantic-color-surface-base-primary));
      box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.16);
      button {
        border: none;
        display: flex;
        padding: 8px 12px;
        align-items: flex-start;
        gap: 10px;
        align-self: stretch;
        color: var(--semantic-color-content-status-info-primary);
        background-color: var(--semantic-color-surface-base-primary);
        &:first-child {
          border-bottom: 1px solid #E9E9E9;
        }
        &:hover {
          background: var(--deep-blue-surface-default-hover, #EEF8FF);
        }
      }
    }
    .delete-icon,
    .pencil-icon,
    .view-icon,
    .download-icon {
      margin: 0 5px;
    }
    .fa-caret-right,
    .fa-caret-down {
      margin: 0 5px;
      width: 15px;
    }
    .ReactTable.app-table {
      max-height: calc(100vh - 28rem);
    }
    .column-layout-config-container {
      border-left: 0;
    }
    .table-container .content .cell-container {
      border-right: none;
      cursor: default;
      .svg-inline--fa {
        cursor: pointer;
      }
    }
    .child-row {
      border-bottom: 1px solid white;
    }
    .checkboxlink-cell-url {
      text-decoration: none;
      color: var(--semantic-color-content-status-info-primary);
    }
    .config-table-container .cell-actions{
      float: right;
    }
    // .ReactTable.app-table {
    //   max-height: calc(100vh - 20rem);

    //   .rt-tbody {
    //     .rt-tr-group {
    //       .rt-tr {
    //         .rt-td {
    //           padding: 13px 18px;
    //           align-self: normal;
    //           height: auto;
    //           align-items: start;
    //           justify-content: start;
    //           flex-direction: column;
    //           span {
    //             &:last-child {
    //               padding-bottom: 0;
    //             }
    //               padding-bottom: 10px;
    //           }
    //         }
    //       }
    //     }
    //   }
    // }
  }
  .actions-row {
    align-items: center;
    .actions-items {      
      display: flex;
      justify-content: flex-start;
      padding-right: 4px;
      align-items: center;
      .sipg-fragment  { padding: 0 }
      .cloud-formation-options-link { 
        margin-left: 16px;
        .fa-download {
          width: 13px;
          height: 13px;
        }
      }
    }
    .select-item {
      margin-left: 16px;
      width: fit-content;
      min-width: 120px;
      .my-select-with-search__control{
        background: var(--semantic-color-background-primary);
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 4px;
        padding: 6px 12px;        
      }
      .my-select-with-search__menu {
        min-width: 250px;
        .my-select-with-search__menu-list {
          max-height: none;
        }
        // .my-select-with-search__menu-list .my-select-with-search__option:first-of-type {
        //   // border-bottom: 1px solid #E9E9E9;
        // }
      }
    }
  }
  .table-actions-container {
    background-color:	var(--semantic-color-background-primary);
    padding: 12px;
  }
  .search-input-text {
    padding-left: 8px;
  }
  .search-box input {
    background-color: var(--semantic-color-surface-base-secondary);
    color: var(--semantic-color-content-base-primary);
  }
  .search-box .fa-search, .search-box .fa-magnifying-glass {
    color: var(--semantic-color-content-base-primary);
  }
  .wizard-form .form-sections-container .form-section .full-width {
    float: none;
  }
  .wizard-form .form-sections-container .form-section .form-field-label {
    margin-top: 0;
  }
  .wizard-form .form-sections-container .form-section .form-field-label.hostname-prefix {
    min-width: 150px;
  }
  .wizard-form .form-sections-container .form-section.partner-configuration-form .form-field-label {
    margin-bottom: 0;
  }
  .wizard-form.review-page .form-sections-container {
    display: inline-block;
  }
  .wizard-form .form-sections-container .form-section {
    .disabled-input.break-spaces.full-width.location {
      max-height: 40px;
    }
    .input-container.review.full-width {
      margin: 0;
      height: fit-content;
      display: flex;
      // flex-direction: column;
      .region {
        min-height: fit-content;
      }
      .form-field-label-wrapper .form-field-label {
        margin-left: 0px;
        margin-right: 8px;
        width: 230px;
        color: var(--semantic-color-content-base-primary);
        font-weight: 400;
      }
      .disabled-input {
        margin:0;
        font-style: normal;
        font-weight: 400;
        font-size: 13px;
        line-height: 20px;
        color:  var(--semantic-color-content-base-primary);
      }
    }
    .checkbox-container {
      .container { padding: 0 25px;}
      label span { min-width: 0;}
      label span span { margin-left: 0;}
    }
    // label span {
    //   // min-width: 200px;
    // }
  }
  .wizard-form .form-sections-container .form-section .input-container {
    input {
      margin-top: 0;
    }
    .external-id-buttons.trusted-account {
      float: left;
      .select-item {
        border-radius: 8px;
      }
    }
    &.full-width {  
      margin: 22px 0;
      height: auto;
      &.extra-margin-bottom {
        margin-bottom:  70px;
      }
      .disabled-input.trusted-account-id-value {
        width: fit-content;
        display: flex;
        align-items: center;
      }
      .disabled-input.event-bus-copy  {
        width: fit-content;
        white-space: nowrap;
        display: flex;
        align-items: center;
      }
      // .svg-inline--fa {
      //   // width: fit-content;
      // }
      .svg-inline--fa.fa-arrows-rotate {
        color: var(--semantic-color-background-primary);
        margin-right: 4px;
      }
      .svg-inline--fa.fa-copy {
        margin-right: 4px;
      }
    }
 }
 .wizard-form.partnerIntegration-region {
   .form-sections-container {
    overflow: visible;
   }
  .fa-info-circle-icon{
    margin-left: 4px;
    color: var(--semantic-color-content-status-info-primary);
  }
  .dropdown {
    border: none;
    border-radius: 8px;
    margin-top: 4px;
    padding: 8px;
    background-color: var(--semantic-color-background-primary);
  }
 }
}

.edgeconnector-page .edgeconnector-modal .modal-content.vdi-modal-content .wizard-form .form-sections-container .form-section .input-container {
  height: 40px;
}
.edgeconnector-page .edgeconnector-modal .modal-content.vdi-modal-content .wizard-form .form-sections-container .form-section .input-container.margin-bootom-28px {
  margin-bottom: 28px;
}
.edgeconnector-page .edgeconnector-modal .modal-content.vdi-modal-content .wizard-form .form-sections-container .form-section .input-container.review .disabled-input {
  height: 40px;
}

.modal-content.modal-body.vdi-modal-content .wizard-form.configure-provisioning-template .form-section.vdi-device-management-form .form-textarea{
  margin: 0;
}
.edgeconnector-page {
  &.vdi-device-management {
    position: relative;
  }
}
}
.ec-root-page {
.modal-overlay {
  z-index: 11;
  .modal-body.locationTemplate-view-modal , .modal-body.location-view-modal {  
    width: 740px;
    left: calc(50% - 370px);
    .modal-content {
      overflow: hidden;
      max-height:  800px;
    }
    .wizard-form-location .form-sections-container{
      // min-height:  760px;
      // max-height:  860px;
      border: 1px solid var(--semantic-color-border-base-primary);
      .form-section{
        border: 1px solid var(--semantic-color-border-base-primary);
      }
      .form-section-title {      
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        border: none;
        border-radius: 5px;
        background-color: var(--semantic-color-background-primary);
        margin: 20px;
        padding: 0;
        align-items: flex-start;
        flex-wrap: wrap;
        justify-content: space-between;
      }
    }
    .wizard-form-location .form-sections-container .form-section .form-field-label.title {
      padding: 0px 16px;
    }
    .wizard-form .form-sections-container .form-section .input-container input#templatePrefix {
      margin-top: 0px;
    }

    .wizard-form .form-sections-container .form-section.no-margin-bottom {
      margin-bottom: 0px;
    }

    .wizard-form .form-sections-container .form-section.description .input-container {
      padding: 0px;
      .form-textarea {
        margin: 0px;
      }
    }

  }
}
// .main-container {
//   padding: 19px 25px;
// }

// .sipg-fragment {
//   padding-top: 1.5em;
//   padding-bottom: 10px;
// }
// .cloud-provider-wrapper {
//   .app-table-container {
//     .ReactTable.app-table {
//       .rt-thead {
//         .header-title {
//           font-size: 13px;
//           line-height: 30px;
//           display: inline-block;
//         }
//       }
//       .has-nested-true {
//         .rt-thead {
//           .header-title {
//             font-size: 13px;
//             line-height: 16px;
//           }
//           .rt-th:nth-child(1) {
//             display: block;
//             .sorting-icon-cont {
//               display: none;
//             }
//           }
//           .rt-th:nth-child(2) {
//             padding: 0;
//             border: 0;
//           }
//           .rt-th:nth-child(3) {
//             .rt-resizable-header-content {
//               left:  -35px;
//               position: relative;
//               margin-right:  -35px;
//             }
//           }
//         }
//       }
//     }
//   }   
// }

// overwrite react table css

.location-templates {
  .delete-icon,
  .pencil-icon,
  .view-icon,
  .download-icon {
    margin: 0;
    margin-right: 10px;
  }
  .ReactTable.app-table {
    max-height: calc(100vh - 21rem);
  }
  .column-layout-config-container {
    border-left: 0;
  }
}

.add-edit-sip {
  &.add-custom-app-form {
    .form-sections-container {
      .form-section { 
        margin-top: 10px;
      }
    }
  }
  .form-section {
    .sip-name,
    .sip-ip-address {
      width: 100%;
      float: left;
      .input-container {
        width: 100%;
      }
    }
    .dropdown-container,
    .checkbox-container {
      padding: 12px 0;
    }
    .bw-control-child {
      padding: 12px 0;
      .input-container {
        height: auto;
      }
    }
    .form-textarea {
      margin: 0;
      padding: 5px;
      resize: none;
    }
  }
  
}
}
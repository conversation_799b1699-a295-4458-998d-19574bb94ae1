.ec-root-page {
.download-progress-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 170px;
  .download-progress{
    text-align: center;
    width: 100%;
  }
}
.bc-images-main-container {
  padding: 19px 25px;
  .page-title {
    padding-left: 0;
    .bc-images-general-info-description {
      width: 600px;
      font-style: normal;
      font-weight: 400;
      font-size: 13px;
      line-height: 20px;    
      color:  var(--semantic-color-content-base-primary);
      margin-top: 8px;
      margin-bottom: 16px;
    }
  }
  .tooltip-navlink {
    color: var(--semantic-color-content-status-info-primary);
    // text-decoration: underline;
    // text-decoration-color: #f7f9fa;
  }
  .bc-images-general-info-detail {
    width: 830px;
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 20px;    
    color:  var(--semantic-color-content-base-primary);
    margin-top: 8px;
    margin-bottom: 16px;
    text-align: justify;
  }
  .side-header {
    height: 24px;
    width: '100%';
    padding-bottom: 2em;
    color: var(--semantic-color-content-base-primary); 
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    text-align: left;
    float: left;
  }
  .dashboard-row{
    margin: 0;
    &.table-content {
      padding: 12px;
    }
  }
  .review-container {
    .review-title {
      font-size: 16px;
      margin-right: 4px;
    }
  }

  .table-content {    
    border-radius: 5px;
    background-color: var(--semantic-color-surface-table-row-default);
    // box-shadow: 0 8px 18px 0 rgba(176,186,197,0.4);
    .download-icon {
      &.disabled {
        color: var(--semantic-color-content-interactive-primary-disabled);
        cursor: auto;
      }
    }
  }
  .app-table-container .ReactTable.app-table {
    width: 100%;
  }
}

.automation-scripts-wrapper {
  .app-table-container {
    .ReactTable.app-table {
      .rt-thead {
        .header-title {
          font-size: 13px;
          line-height: 30px;
          display: inline-block;
        }
      }
      .has-nested-true {
        .rt-thead {
          .header-title {
            font-size: 13px;
            line-height: 16px;
          }
          .rt-th:nth-child(1) {
            display: block;
            .sorting-icon-cont {
              display: none;
            }
          }
          .rt-th:nth-child(2) {
            padding: 0;
            border: 0;
          }
          .rt-th:nth-child(3) {
            .rt-resizable-header-content {
              left:  -35px;
              position: relative;
              margin-right:  -35px;
            }
          }
        }
      }
    }
  }
  ul {
    li {
      list-style: none;
      padding: 10px 0;
      color: var(--semantic-color-content-interactive-primary-default);
      font-weight: 500;
      line-height: 20px;
      cursor: pointer;
      &:hover {
        opacity: .8;
      }
      &:active {
        opacity: 1;
      }
      a {
        text-decoration: none;
        color: var(--semantic-color-content-interactive-primary-default);
      }
      .fa-download {
        margin-right: 8px;
      }
    }
  }
  .as-title {
    font-size: 14px;
    font-weight: 500;
    padding: 10px 0;
    color: #1a1a1a;
  }
}
.modal-overlay{
  .modal-body.download-progress-modal {
    .modal-content {
      min-height: 5vh;
      background: none;
    }
  }
}
}
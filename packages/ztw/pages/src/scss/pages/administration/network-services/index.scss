@import "scss/pages.scss";
@import "scss/colors.scss";
@import "scss/mixins.scss";
@import "scss/widgets.scss";

.ec-root-page {
.networkService-main-container {
  padding: 43px 25px 19px 25px;
  .grid-toolbar-right.networkService-toolbar-right {
    display: inline-flex;
    align-items: flex-start;
  }
} 
.modal-overlay .modal-body.network-service-modal .modal-content {
  overflow: visible;
}
.networkService-main-container,
.networkService-form {
  .container {
    padding: 0;
    padding-left: 25px;
  }
  label span {
    margin-left: 0;
  }
  .rdg-menu-editor .ui-sortable {
    padding-top: 0;
  }
  .page-title {
    padding-left: 0;
    padding-top: 0;
  }
  .source-ip-groups {
    height: 24px;
    width: 147px;
    color: var(--semantic-color-content-base-primary);
    font-size: 24px;
    font-weight: 500;
    line-height: 24px;
  }
  .table-layout-header {
    margin-top: 0;
  }
  .tabP {
    margin-bottom: 0.6em;
    font-size: 16px;
    min-width: 120px;
    display: inline-block;
    vertical-align: top;
    color: var(--semantic-color-content-interactive-primary-default);
    padding: 0 16px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .tabPactive {
    margin-bottom: 0.6em;
    font-size: 16px;
    min-width: 120px;
    text-align: center;
    color: var(--semantic-color-content-base-primary);
    padding: 0 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  // .tabs {
  //   display: inline-block;
  //   position: relative;
  //   width: 100%;
  //   &-items {
  //     @include DisplayFlex;
  //     div {
  //       div.highlighter {
  //         margin-bottom: 0;
  //       }
  //     }
  //   }
  //   &-highlrighte {
  //     height: 2px;
  //     border-radius: 0 5.5px;
  //     background: $grey12;
  //     position: absolute;
  //     width: 100%;
  //     bottom: 0;
  //     z-index: -1;
  //   }
  // }
  .form-sections-container {
    padding: 19px 25px;
    background: var(--semantic-color-background-primary);
    border: 1px solid var(--semantic-color-border-base-primary);
    padding: 16px 24px;
    width: 100%;
    max-height: 630px;
    overflow: auto;
    .form-section-label {
      padding: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color:  var(--semantic-color-content-base-primary);
      font-size: 13px;
      letter-spacing: 0.5px;
      margin-bottom: 8px;
      text-transform: uppercase;
      width: 100%;
    }
    .form-section {
      flex-direction: column;
      background-color: var(--semantic-color-surface-base-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 5px;
      width: 100%;
      margin-bottom: 24px;
    }
  }

  .g-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    width: 100%;

    .g-fullwidth {
      padding: 12px 16px;
      width: 100%;
      word-break: break-all;
    }
    
    .g-left,
    .g-right {
      width: 50%;
      padding: 12px 16px;
      word-break: break-all;
    }
    .form-field-label-wrapper .top-right.rTooltip {
      min-width: 0;
    }
    .form-textarea {
      height: 150px;
      line-height: 14px;
      resize: none;
      vertical-align: middle;
      margin: 0px;
      width: 100%;
      overflow-y: auto;
      white-space: normal;
      background-color: var(--semantic-color-surface-base-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
      outline: none;
      border-radius: 5px;
      padding-left: 8px;
    }
    .input-container {
      padding: 0;
      input {
        border: 1px solid var(--semantic-color-border-base-primary);
        background: var(--semantic-color-background-pale);
        border-radius: 8px;
        color: var(--semantic-color-content-base-secondary);
        cursor: text;
        display: block;
        height: 32px;
        padding: 9px 8px;
        text-align: left;
        min-width: 220px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &:active,
        &:focus {
          color: var(--semantic-color-content-status-info-primary);
        }
      }
    }

    .input-container.error input{
      border-color: var(--semantic-color-border-status-danger-active);
    }
    .input-container .error-container{
      display: none;
    }

    .add-custom-app-form .form-sections-container .form-section .input-center-vertical {
      display: flex;
      align-items: center;

      .input-container.limited-width {
        width:  80px;
      }
    }

    .tooltip-navlink {
      text-decoration: none;
      color: $anchor-color;
    }
  }

  .search-container {
    display: inline-flex;
    justify-content: space-between;
    vertical-align: middle;
    padding: 0 12px 0 8px;
    background-color: var(--semantic-color-surface-base-secondary);
    border-radius: 0.5rem;
  }
  .search-container .search-input,
  .search-container .autocomplete-input-container {
    box-shadow: none;
  }
  .dropdown + .search-container,
  .reports-favorites-list + .search-container {
    margin-left: 5px;
  }

  .search-input-text-container {
    height: 100%;
    display: inline-block;
    position: relative;
  }

  .search-input,
  .autocomplete-input-container {
    border: none;
    padding: 0px 12px 0px 8px;
    border-radius: 8px;
    vertical-align: middle;
  }

  .search-input-text {
    display: inline-block;
    font-size: 13px;
    height: 32px;
    padding: 6px 26px 6px 0;
    vertical-align: middle;
    width: 280px;
  }

  .search-icon,
  .autocomplete-icon {
    color: var(--semantic-color-content-status-info-primary);
    cursor: pointer;
    display: inline-block;
    font-size: 16px !important;
    font-weight: bold;
    height: 16px;
    margin-left: 8px;
    width: 16px;
    vertical-align: middle;
  }
  .search-icon:hover,
  .autocomplete-icon:hover {
    color: var(--semantic-color-content-interactive-primary-default);
  }

  .search-clear-icon {
    color: var(--semantic-color-content-status-info-primary);
    display: none;
    cursor: pointer;
    font-size: 14px !important;
    height: 14px;
    margin-left: 5px;
    width: 12px;
    vertical-align: middle;
    position: absolute;
    top: 9px;
    right: 8px;
  }
  .search-clear-icon.fas {
    display: none;
  }
  .search-clear-icon:hover {
    color: var(--semantic-color-content-interactive-primary-default);
  }
  .search-clear-icon.visible {
    display: inline-block;
  }
  .radio-button-container p {
    margin-top: 0;
  }

  .radio-button-container p {
    margin: 4px;
  }

  .dialog-footer .dialog-footer-left .submit:disabled {
    background: #f7f9fa;
    border: 1px solid;
    border-color: #979899;
    color: #979899;
    box-shadow: none;
    cursor: default;
    opacity: 0.6;
  }
  .dialog-footer .dialog-footer-left .cancel:disabled {
    background: none;
    color: #979899;
  }
  .saml-certificate-modal-form {
    width: 1000px;
  }
}

.networkService-main-container,
.add-custom-app-form {
  .form-sections-container {
    padding: 0;
    border: none;
    padding-bottom: 24px;
    .form-section {
      margin: 0;
      padding: 0;
      .form-iframe-empty {
        margin-right: 5px;
        color: #7a7a7a;
      }
      .form-link-text {
        text-decoration: none;
        color: var(--semantic-color-content-status-info-primary);
        display: inline-block;
        font-size: 13px;
        line-height: 20px;
        padding-right: 5px;
        vertical-align: middle;
        margin-left: 0;
      }
      .form-link-text.with-border {
        border-left: 1px solid var(--semantic-color-border-base-primary);
        margin-left: 0;
        padding-left: 5px;
      }
    }
  }
  .error-text {
    color: var(--semantic-color-content-status-danger-primary);
  }
}

.rbac-form {
  .g-row {
    margin-top: 1em;
  }
  .form-section {
    .g-name {
      width: 50%;
      float: left;
      .input-container {
        width: 50%;
      }
    }
    .form-left {
      float: left;
    }
    .form-right {
      float: right;
      padding-left: 5.3em;
    }
  }
}

.main-container {
  padding: 19px 25px;
}

.source-ip-groups {
  height: 24px;
  width: 147px;
  color: var(--semantic-color-content-base-primary);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
.sipg-fragment {
  padding-top: 1.5em;
  padding-bottom: 10px;
}

.dropdown-label {
  color: #77797c;
  margin-right: 5px;
  margin-top: 8px;
}

.dropdown-label:after {
  content: ":";
}

.networkService-main-container{
  .drop-down-container {
    width: 200px;
    margin-right: 5px;
  }
}

.source-ip-wrapper {
  .app-table-container {
    .ReactTable.app-table.has-nested-true {
      .rt-thead {
        .rt-th:nth-child(1) {
          display: block;
          .sorting-icon-cont {
            display: none;
          }
        }
        .rt-th:nth-child(2) {
          padding: 0;
          border: 0;
        }
        .rt-th:nth-child(3) {
          .rt-resizable-header-content {
            left: -35px;
            position: relative;
            margin-right: -35px;
          }
        }
      }
    }
  }
}

.networkService-toolbar-left {
  margin-bottom: 0.425rem;
}

.networkService-toolbar-left-buttons {
  margin-right: 16px;
  text-overflow: ellipsis;
}

}

.appliance-view-modal{
  min-width: 700px;
  .form-section {
    .form-textarea {
      width: 100%;
      margin: 0;
      padding: 8px;
    }
  }
  .wizard-form.appliance-form .form-sections-container {
    overflow: hidden;
    .form-section-label {
      padding: 20px 20px 8px 20px;
      font-style: normal;
      font-weight: 500;
      font-size: 13px;
      line-height: 20px;
      letter-spacing: 0.08em;
      text-transform: uppercase;
      color:  var(--semantic-color-content-base-primary);
    }
    .form-section {
      margin: 0 20px;
      .disabled-input {        
        margin:0;
        margin-top: 10px;
      }
      .form-field-label {
        margin: 0;
      }
      .appliance-desc {
        margin-top: 10px;
      }
      .separator-line {
        margin: 10px 0;
      }
    }  
  }
  .g-row {  
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    width: 100%;

    &:first-child {
      .g-right{
        padding-left: 0;
      }
      .g-left {
        padding-right: 0;
      }
    }
    
    .g-fullwidth{
      padding: 12px 16px;
      width: 100%;
    }
    .g-left,.g-right {
      width: 50%;      
      padding: 12px 16px;
    }
  }
}

.main-container.appliance-container {
  width: 100%;  
  .appliance-content {
      display: flex;
      flex-direction: column;
      background-color: var(--semantic-color-surface-base-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 8px;
      padding: 12px;
      .search-container {
        // float: right;
        align-self:flex-end;      
        width: fit-content;
        margin-bottom: 12px;
        .search-box {
          background-color: var(--semantic-color-background-pale);
          color: var(--semantic-color-content-base-primary);
          border-radius: 4px;
          input {
            width: 260px;
            background-color: var(--semantic-color-background-pale);
            color: var(--semantic-color-content-base-primary);
          }
        }
      }
      .appliance-table {
        .container-row {
          margin: 0;
          .config-table-container .column-layout-config-container {
            border-left: none;
          }
          .svg-inline--fa {
            margin: 0 10px;
          }
          .failed-icon,
          .success-icon,
          .disabled-icon,
          .inactive-icon,
          .disabling-icon  {
            margin-left: 0;
          }
        }
      }
  }
}

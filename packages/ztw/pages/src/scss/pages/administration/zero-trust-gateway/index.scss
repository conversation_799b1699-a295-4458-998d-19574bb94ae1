@import "scss/colors.scss";
@import 'scss/mixins.scss';
@import 'scss/variables.scss';
@import 'scss/defaults.scss';

.insights-container {
  background: #fff;
  border: 1px solid #E0E1E3;
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  margin-top: 1em;

  .container-row{
    margin: 3rem 0;
  }
  
}

@media screen and (max-width: $templateMediaSize) {
  .main-container>.pie-container {
    flex-direction: column;
  }
  .main-container>.pie-container> {
    width: 100%;
  }
}

.ec-root-page {
  .main-container.zero-trust-gateway-detail-container {
    background-color: var(--semantic-color-surface-base-secondary);
    display: flex;
    flex-direction: column;
    min-height: 100vh;

    .cloud-connector-group-form.configure-provisioning-template {
      background-color: var(--semantic-color-background-primary);
      overflow: visible;
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 8px;
      .form-sections-container {
        overflow: visible;
        &:first-child {
          border-top-left-radius: 8px;
          border-top-right-radius: 8px;
        }
        &:last-child {
          border-bottom-left-radius: 8px;
          border-bottom-right-radius: 8px;
        }
      }
      .form-section {
        .input-container {
          margin-bottom: 8px;
        }
      }
    }

    &.time-frame {
      background-color: var(--semantic-color-background-primary);    
      padding-bottom: 0;
      max-width: 1000px;
    }
    .form-tab-group {
      width: 100%;
      .tabs { 
        width: 100%;
        .tabs-items {
          display: flex;
          justify-content: flex-start;    
          & div {
            margin-bottom: 0;
            margin-top: 10px;
          }
        }
      }
    }

    .header-ztg-details {
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
      display: flex;
      align-items: center;
      .form-link-text.text-decoration-none {
        text-decoration: none;
      }
      .partner-label {
        color: $anchor-color
      } 
      .chevron-label {
        color: var(--semantic-color-content-base-primary);
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        margin:0 17px
      } 
      .account-label {
        color: $dark-mode-gray-gray-6-background
      } 
    }

    .half-width.gateway-info-detail {
      min-width: 160px;
      label.form-field-label-wrapper span.form-field-label { 
        min-width: 160px;
      }
      .has-copy-button {
        border: none;
        background: transparent;
        margin-left: 4px;
      }
    }

    .refresh-button {
      border: none;
      color: var(--semantic-color-content-interactive-primary-default);
      background-color: var(--semantic-color-background-primary);
      .fa-arrows-rotate {
        margin-right: 4px;
      }
    }
    .actions-row {
      align-items: center ;
      .input-search-box {
        background-color: var(--semantic-color-surface-base-secondary);
        color: #3F3F40;
      }
    }

    .header {
      // position: relative;
      width: 100%;
      font-size: 13px;
      font-weight: 500;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: left;
    }
    .separator {
      height: 1px;
      background-color: #e0e1e3;
      margin-bottom: 16px;
      right: 0;
      left: 0;
    }
    .content-tabs-container {
      padding-top: 0;
    }
    .content {
      width: 100%;
      .action-refresh {
        justify-content: center;
        align-self: center;
        justify-self: center;
        position: relative;
        font-size: 13px;
        font-weight: 400;
        line-height: 20px;
        letter-spacing: 0em;
        text-align: left;
        padding-left: 16px;
        margin-top: 4px;
      }
      .content-box-flex {
        width: 100%;
        font: 14px Arial;
        display: flex;
        display: -webkit-flex;
        flex-direction: row;
        -webkit-flex-direction: row;
        box-sizing: border-box;
        min-height: 0em;
        // border: 1px solid #D6D7DB;
        border-radius: 8px;
        justify-content: space-between;
        margin-bottom: 16px;
        .left-border,.right-border {
          width: 48%;
        }
      }
      .key {
        float: left;
        min-height: 2em;
        color: #939393;
        font-size: 13px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 2em;
        width: 40%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
      .value {
        float: right;
        min-height: 2em;
        color: #1E1F22;
        font-size: 13px;
        letter-spacing: 0.2px;
        line-height: 2em;
        width: 60%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 10px;
      }
      div.key:hover {
        overflow: visible;
        white-space: pre-wrap;
        // background-color: #F7F9FA;
        color: #939393;
        }
      div.value:hover {
        overflow: visible;
        white-space: pre-wrap;
        // background-color: #F7F9FA;
        color: #939393;
        }
    }

    .drawer-container {
      .pagination-bar {
        .pagination-bar-left {
          width: 20%;
        }
        .pagination-bar-center {
          width: 30%;
        }
        .pagination-bar-left {
          width: 50%;
        }
      }
      .drawer.active {
        width: 800px;
        max-width: none;
        // .left,.right {
        //   max-width: none;
        // }      
      }
      .drawer-body {
        margin: 16px 32px;
        padding: 0;  
      }
      .drawer-header {
        max-height: 32px;
        margin: 16px 32px;
      }
      .header-details.flex-box {
        display: flex;
        align-items: center;
      }
      .drawer-header-icon {
        height: 20px;
        width: 20px;
        margin-right: 16px;
      }
      .drawer-header-title {
        font-size: 16px;
        font-weight: 500;
        line-height: 20px;
        letter-spacing: 0px;
        text-align: left;
        color:var(--semantic-color-content-interactive-primary-disabled);
      }
    } 
    .checkboxlink-cell-url {
      text-decoration: none;
      color: var(--semantic-color-content-interactive-primary-default);
    }
    .header-zero-trust-gateway-details {
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
      display: flex;
      align-items: center;
      .form-link-text.text-decoration-none {
        text-decoration: none;
      }
      .zero-trust-gateway-label {
        color: var(--semantic-color-content-interactive-primary-default);
      } 
      .chevron-label {
        color: var(--semantic-color-content-interactive-primary-disabled);
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        margin:0 17px
      } 
      .account-label {
        color: #19191A
      } 
    }
    .details-container, .details-container-table {
      .filters-container.time-frame {
        padding: 19px 25px;
        margin-bottom: 0;
        .form-field-label-wrapper {
          margin-right: 8px;
          min-width: 90px;
          display: flex;
          align-items: center;
        }
        .select-item {
          margin-left: 0;
        }
      }
      background-color: var(--semantic-color-background-primary);
      display: flex;  
      padding: 4px 12px 12px 12px;
      flex-direction: column;
      box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      .full-width {
        width: 100% ;
        float: none;
        display: flex;
        // flex-direction: column;
        margin-top: 8px;
        .info-detail {
          display: flex;
          .form-field-label {
            min-width: 130px;
            font-style: normal;
            font-weight: 400;
            font-size: 13px;
            line-height: 20px;
            color: var(--semantic-color-content-base-secondary);
          }
          .disabled-input {
            font-style: normal;
            font-weight: 400;
            font-size: 13px;
            line-height: 20px;
            color: var(--semantic-color-content-interactive-primary-disabled);
            word-break: break-all;
            word-wrap: break-word;
            overflow-wrap: break-word;
          }
        }
      }
    }
    .details-container-table {
      margin-top: 16px;
      &.no-padding {
        padding: 0;
      }
      &.no-margin {
        margin: 0;
      }
      .header-large {
        font-size: 20px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: 0px;
        text-align: left;
        color: var(--semantic-color-content-interactive-primary-disabled);
      }
      .container-row-cc-group {
        min-height: fit-content;
        margin-bottom: 0;
        .config-table-container {
          margin-bottom: 0;
        }
      }
    }
    .table-container .content .cell-container {
      cursor: default;
    }
  }

  .main-container.zero-trust-gateway, .edgeconnector-page .edgeconnector-modal .zero-trust-gateway-modal-content {
    padding: 19px 25px;  
    .refresh-button {
      border: none;
      color: var(--semantic-color-content-interactive-primary-default);
      background-color: var(--semantic-color-background-primary);
      .fa-arrows-rotate {
        margin-right: 4px;
      }
      margin-right: 4px;
    }
    .table-actions-container.group-filter {
      background-color: var(--semantic-color-background-primary);
      margin-bottom: 16px;
      padding: 6px 6px 1px 12px;
      border-radius: 8px;
    }
    .filter-table-container{
      display: flex;
      align-items: center;    
      .form-field-label-wrapper { 
        min-width: fit-content;

      }
      .input-container {
        padding: 0;
      }
      .option-button-container.option-group-container {
        padding: 0;
        .option-buttons {
          min-width: 450px;
          .option-button {
            width: none;
          }
          label {
            justify-content: flex-start;
          }
        }
    }
    }
    .container-row-cc-group {
      min-height: fit-content;
      margin-bottom: 0;
      .config-table-container {
        margin-bottom: 0;
      }
    }
    .wizard-form.zero-trust-gateway-configuration .form-sections-container .form-section .disabled-input {
      margin-top: 0;
      margin-left: 0;
      color: var(--semantic-color-content-interactive-primary-disabled);
      font-weight: 400;
    }
    .form-section.zero-trust-gateway-configuration-form .external-id-buttons {
      min-height: 0;
    }
    .zero-trust-gateway-table-container {
      .config-table-container { 
        max-height: none;
      }
      .ellipsis-menu {
        display: inline-flex;
        flex-direction: column;
        align-items: flex-start;

        border-radius: 4px;
        background: var(--deep-blue-surface-default-surface-00, #FFF);
        box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.16);
        button {
          border: none;
          display: flex;
          padding: 8px 12px;
          align-items: flex-start;
          gap: 10px;
          align-self: stretch;
          color: var(--semantic-color-content-interactive-primary-default);
          background-color: var(--semantic-color-background-primary);
          &:first-child {
            border-bottom: 1px solid #E9E9E9;
          }
          &:hover {
            background: var(--deep-blue-surface-default-hover, #EEF8FF);
          }
        }
      }
      .delete-icon,
      .pencil-icon,
      .view-icon,
      .download-icon {
        margin: 0 5px;
      }
      .fa-caret-right,
      .fa-caret-down {
        margin: 0 5px;
        width: 15px;
      }
      .ReactTable.app-table {
        max-height: calc(100vh - 280px);
      }
      .column-layout-config-container {
        border-left: 0;
      }
      .table-container .content .cell-container {
        border-right: none;
        cursor: default;
        .svg-inline--fa {
          cursor: pointer;
        }
      }
      .child-row {
        border-bottom: 1px solid white;
      }
      .checkboxlink-cell-url {
        text-decoration: none;
        color: var(--semantic-color-content-interactive-primary-default);
      }
      .config-table-container .cell-actions{
        float: right;
      }
      // .ReactTable.app-table {
      //   max-height: calc(100vh - 20rem);

      //   .rt-tbody {
      //     .rt-tr-group {
      //       .rt-tr {
      //         .rt-td {
      //           padding: 1.3rem 1.8rem;
      //           align-self: normal;
      //           height: auto;
      //           align-items: start;
      //           justify-content: start;
      //           flex-direction: column;
      //           span {
      //             &:last-child {
      //               padding-bottom: 0;
      //             }
      //               padding-bottom: 1rem;
      //           }
      //         }
      //       }
      //     }
      //   }
      // }
    }
    .actions-row {
      align-items: center;
      .actions-items {      
        display: flex;
        justify-content: flex-start;
        padding-right: 4px;
        align-items: center;
        .sipg-fragment  { padding: 0 }
        .cloud-formation-options-link { 
          margin-left: 16px;
          .fa-download {
            width: 13px;
            height: 13px;
          }
        }
      }
      .select-item {
        margin-left: 16px;
        width: fit-content;
        min-width: 120px;
        .react-select__control{
          background: var(--semantic-color-background-primary);
          border: 1px solid var(--semantic-color-content-interactive-primary-default);
          border-radius: 4px;
          padding: 6px 12px;        
        }
        .react-select__menu {
          min-width: 250px;
          .react-select__menu-list {
            max-height: none;
          }
          .react-select__menu-list .react-select__option:first-of-type {
            border-bottom: 1px solid #E9E9E9;
          }
        }
      }
    }
    .table-actions-container {
      background-color: var(--semantic-color-background-primary);
      padding: 12px;
    }
    .search-input-text {
      padding-left: 8px;
    }
    .search-box input {
      background-color: var(--semantic-color-surface-base-secondary);
      color: #3F3F40;
    }
    .search-box .fa-search, .search-box .fa-magnifying-glass {
      color: #3F3F40;
    }
    .wizard-form.zero-trust-gateway-configuration .form-sections-container .form-section .full-width {
      float: none;
    }
    .wizard-form.zero-trust-gateway-configuration .form-sections-container .form-section .form-field-label {
      margin-top: 0;
      &.error-text {
        color: #C00303;
      }
    }
    .wizard-form.zero-trust-gateway-configuration .form-sections-container .form-section.zero-trust-gateway-configuration-form .form-field-label {
      margin-bottom: 0;
      &.error-text {
        color: #C00303;
      }
    }
    .wizard-form.zero-trust-gateway-configuration.review-page .form-sections-container {
      display: inline-block;
      overflow: visible;
    }
    .wizard-form.zero-trust-gateway-configuration .form-sections-container .form-section {
      .adv-dropdown-with-async-search div.drop-down-container button.drop-down-selected-value {
        margin-top: 4px;
        border-radius: 8px;
      }
      .input-container.review.full-width {
        margin: 5px 0;
        height: fit-content;
        display: flex;
        // flex-direction: column;
        .region {
          min-height: fit-content;
        }
        .form-field-label-wrapper .form-field-label {
          margin-left: 0px;
          margin-right: 8px;
          width: 230px;
          color: var(--semantic-color-content-base-secondary);
          font-weight: 400;
        }
        .disabled-input {
          margin:0;
          font-style: normal;
          font-weight: 400;
          font-size: 13px;
          line-height: 20px;
          color: var(--semantic-color-content-interactive-primary-disabled);
        }
      }
      .checkbox-container {
        .container { padding: 0 25px;}
        label span { min-width: 0;}
      }
      // label span {
      //   // min-width: 200px;
      // }
    }
    .wizard-form .form-sections-container .form-section .input-container {
      input {
        margin-top: 0;
        &#location {
          margin-top: 4px;
        }
      }
      .external-id-buttons.trusted-account {
        float: left;
        .select-item {
          border-radius: 8px;
        }
      }
      &.full-width {  
        margin: 1rem 0;
        height: auto;
        &.no-margin {
          margin: 0;
        }
        &.extra-margin-bottom {
          margin-bottom: 7rem;
        }
        .disabled-input.trusted-account-id-value {
          width: fit-content;
          display: flex;
          align-items: center;
        }
        .disabled-input.event-bus-copy  {
          width: fit-content;
          white-space: nowrap;
          display: flex;
          align-items: center;
        }
        .svg-inline--fa {
          width: fit-content;
        }
        .svg-inline--fa.fa-arrows-rotate {
          color: var(--semantic-color-background-primary);
          margin-right: 4px;
        }
        .svg-inline--fa.fa-copy {
          margin-right: 4px;
        }
      }
    }
  .wizard-form.zero-trust-gateway-region {
    .form-sections-container {
      overflow: visible;
    }
    .fa-info-circle-icon{
      margin-left: 4px;
      color: var(--semantic-color-content-interactive-primary-default);
    }
    .dropdown {
      border: none;
      border-radius: 8px;
      margin-top: 4px;
      padding: 8px;
      background-color: var(--semantic-color-surface-base-secondary);
    }
  }
  .wizard-form.zero-trust-gateway-configuration, .zero-trust-gateway-acc-groups-page {
      // .form-sections-container {
      //  overflow: visible;
      // }
      .fa-info-circle-icon{
        margin-left: 4px;
        color: var(--semantic-color-content-interactive-primary-default);
      }
      .dropdown {
        border: none;
        border-radius: 8px;
        border: 1px solid var(--semantic-color-border-base-primary);
        margin-top: 4px;
        padding: 4px 8px;
        background-color: var(--semantic-color-background-primary);
        min-height: 32px;
        .dropdown-display {
          min-width: 260px;
          width: 98%;
        }
      }
    }
  }

  .form-section.zero-trust-gateway-configuration-form {
    display: flex;
    float: none;
    .color-optional {
      color:var(--semantic-color-content-base-secondary);

    }
    .fa-info-circle-icon{
        margin-left: 4px;
        color: var(--semantic-color-content-interactive-primary-default);
    }
    .svg-inline--fa .fa-arrows-rotate {
      color: var(--semantic-color-background-primary);
    }
    .svg-inline--fa .fa-copy {
      color: #0275be;
    }
    .external-id-buttons {
      display: flex;
      min-height: 32px;
      float: right;
      .primary-button {
        align-items: center;
        display: flex;
        justify-content: center;
        width: 117px;
        background: var(--semantic-color-content-interactive-primary-default);
        border-radius: 4px;
        font-style: normal;
        font-weight: 500;
        font-size: 13px;
        line-height: 20px;
        text-align: center;
        color: var(--semantic-color-background-primary);
        cursor: pointer;
      }
      .secondary-button {
        align-items: center;
        display: flex;
        justify-content: center;
        width: 75px;
        background: var(--semantic-color-background-primary);
        border-radius: 4px;
        font-style: normal;
        font-weight: 500;
        font-size: 13px;
        line-height: 20px;
        text-align: center;
        color: var(--semantic-color-content-interactive-primary-default);
        cursor: pointer;
      }
    }
  }

  .form-section.cloud-formation-container {
    display: flex;
    flex-direction: column;
    .cloud-formation-options {
        font-size: 13px;
        font-weight: 500;
        line-height: 20px;
        letter-spacing: 0px;
    } 
    .cloud-formation-dot {
      position: absolute;
      width: 6px;
      height: 6px;
      left: 0;
      top: calc(50% - 6px/2 + 1px);
      background: var(--semantic-color-content-base-secondary);
      border-radius: 10px;
    }
    .cloud-formation-options-link {
      position: relative;
      font-weight: 400;
      line-height: 20px;
      letter-spacing: 0em;
      text-align: left;
      padding-left: 16px;
      margin-top: 4px;
    }
    .cloud-formation-options-link-icon{
      width: 13px;
      height: 13px;
    }
    svg.svg-inline--fa.fa-xs.cloud-formation-options-link-icon.fa-external-link.fa-xs {
      margin-left: 8px;
    }
  }

  .edgeconnector-page.zero-trust-gateway-page {
    &.zero-trust-gateway-acc-groups-page {
      position: relative;
      overflow-y: auto;
      left: 0;
    }
    .back-to-ec.back-to-zero-trust-gateway {
      .fa-arrow-left {
        position: initial;
        margin-right: 16px;
      }
    }
    .zero-trust-gateway-form {
      .dropdown .dropdown-selector .dropdown-lists .dropdown-unselected .unselected-items {
        .checkbox-container.unselected{
          padding: 10px;
        }
      }
    }
    &.zero-trust-gateway-acc-groups-page .dropdown .dropdown-selector .dropdown-lists .dropdown-unselected .unselected-items .unselected .container {
        padding: 0 25px;
    }
  }

  .main-container.zero-trust-gateway, .main-container.zero-trust-gateway-detail-container {
    .time-filter {
      height: 33px;
    }
    .css-bg1rzq-control {
      min-height: 30px;
    }
    div.time-filter-dropdown .calendar-container {
      align-items: flex-start;
      .start {
        max-height: 330px;
      }
      .end {
        max-height: 330px;
      }
    }
    .status-line {
      display: flex;
      // justify-content: space-around;
      align-items: center;
      .title {
        min-width:200px;
        line-height: 40px;
      }
      .status-indicators {
        display: flex;
        align-items: center;
        .status {
          min-width:200px;
          .fa-triangle-exclamation {
            color: orange;
            margin-right: 4px;
          }
          .fa-circle-check {
            color: green;
            margin-right: 4px;
          }
          .fa-circle-x-mark-icon {
            margin-right: 4px;
          }
        }
      }
    }
    .public-ip-container {
      flex-wrap: wrap;
    }
  // add filters
    .actions-row .select-item #CCActions .react-select__control {
      min-height: 35px;
      padding: 4.5px 12px;
    }  
  .filters {
    z-index: 1;
    // width: calc( 100% - 44px);
    margin: 10px 0;
    border: 0.5px solid #e0e1e3;
    border-radius: 5px;
    padding: 2px 12px;
    background: var(--semantic-color-background-primary);
    position: relative;
    .filters-section {
      width: 100%;
      min-height: 44px;
      padding: 0px;
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: -moz-flex;
      display: -webkit-flex;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      > div {
        padding-right: 8px;
        display: flex;
      }
      .filter-area {
        width: auto;
        margin:0;
        //min-height: 30px;
        //margin-bottom: 10px;

        .add-filters {
          //width: 220px;
          .drop-down-container{
            width:28px;
            height:28px;
            border-radius: 50%;
            padding:5px;
            background-color:#F2FAFF;
            border-color:#99D8FF;
            margin-right: 8px;
            .fa-plus{
              position: relative;
              left: -4px;
              top: -5px;
              color: #00578c;
              font-size: 16px;
              font-weight: 500;
            }
            .add-filter-list {
              max-width: none;
            }
            .add-filter-value {
              background: none;
              border: none;
            }
          }
        }
      }
    }
    .filter-action-buttons{
        display: flex;
        position: relative;
        right: 0px;
    }
    .reset-link {
        //width: 120px;
        margin-left: 10px;
    }

    .apply-filters {
        box-shadow: none;
        //width: 80px;
        padding:0px;
        height:28px;
    }


      .flex-box {
        justify-content: space-between;
      }
      .new-filter-component{
        .filter-name {
          font-size: 13px;
          float: left;
          padding: 0 10px;
        }
        .remove-filter-icon {
            color: #006aab;
            font-size: 14px;
            cursor: pointer;
            // margin-right: 8px;
        }
        .multi-select-filters {
            width: 90% !important;
            margin-top: 10px;
        }
        .drop-down-container{
          width:unset;
          border-radius: 40px;
          background-color:#F2FAFF;
          border-color:#99D8FF;
          margin-right: 8px;
          .dropdown-box {
            max-width: inherit;
          }
          .drop-down-list {
            max-width: inherit;
            padding-left: 8px;
          }
        }
        .drop-down-selected-value{
          background-color: var(--semantic-color-background-primary);
          color: var(--semantic-color-content-interactive-primary-disabled);
          width:unset;
          display:inline-block;
          box-shadow:none;
          max-width:280px;
          padding: 3px 2px 2px 2px;
          line-height: 13px;
          background: none;
          border: none;
          .label-selected-items,
          .dropdown-selected-label {
            width: calc(100%);
            padding-left: 8px;
            span {
              font-size: 13px;
              line-height: 20px;
              &.label-selected-value {
                color: var(--semantic-color-content-interactive-primary-disabled);
              }
            }
          }
          .label-selected-items {
            font-weight: 600;
            font-size: 13px;
            line-height: 20px;
          }
          .dropdown-icon {
            padding-left: 0px;
            line-height: 22px;
            visibility: hidden;
            width: 15px;
          }
      }
      .remove-filter-icon{
          margin-right:5px;
          line-height: 28px;
          display: inline-block;
          vertical-align: top;
      }
      // .compliance_check {
      //   .drop-down-container {
      //     .drop-down-selected-value {

      //     }
      //   }
      // }
    }
    .filter-area {
      .vdi-filter {
        display: inline-block;
        padding: 8px 0 10px 10px;
        min-height: 25px;
        .drop-down-selected-value {
          background-color: #f2faff;
          padding: 4px 12px;
          border-color: #99d8ff;
          margin-right: 8px;
          .dropdown-icon {
            float: none;
          }
          .dropdown-selected-label {
            width: auto;
          }
        }
        .drop-down-list.open {
          z-index: 1000;
        }
      }
    }
  }
  .table-filter-label {
    font-weight: 600;
  }
  }

  .main-container.zero-trust-gateway-acc-groups,  .main-container.zero-trust-gateway-acc-accounts, .edgeconnector-page .edgeconnector-modal .branch-provisioning-modal-content.zero-trust-gateway-modal-content.zero-trust-gateway-acc-groups-modal-content {  
    padding: 19px 25px;
    flex-direction: column;
    min-height: 800px; 
    overflow-y: auto;

    .tabs {
      margin-bottom: 0;
      // .tabP, .tabPactive {
      //   margin-bottom: 0;
      // }
      .tabs-items div {
        margin-bottom: 0;
      }
      .tabs-highlrighte{
        margin-bottom: 0;
        // top: 20px;
      }
    }
    .folder-tabs.tabs  {
      margin-bottom: 30px;
    }
  }

  .modal-content.modal-body.branch-provisioning-modal-content.zero-trust-gateway-modal-content.zero-trust-gateway-acc-groups-modal-content {
      label span {
        margin-left: 0;
      }
      .input-container .max-width {
        padding-left: 0;
      }
      .fa-info-circle-icon{
        margin-left: 4px;
        color: var(--semantic-color-content-interactive-primary-default);
      }
      .dropdown {
        border: none;
        border-radius: 8px;
        margin-top: 4px;
        padding: 8px;
        background-color: var(--semantic-color-surface-base-secondary);
        min-height: 32px;
        .dropdown-display {
          min-width: 260px;
          width: 98%;
        }
      }
    }

  .form-section.zero-trust-gateway-configuration-form {
    display: flex;
    float: none;
    .color-optional {
      color:#69696A;

    }
    .fa-info-circle-icon{
        margin-left: 4px;
        color: #005F99;
    }
    .svg-inline--fa .fa-arrows-rotate {
      color: #FFFFFF;
    }
    .svg-inline--fa .fa-copy {
      color: #0275be;
    }
    .external-id-buttons {
      display: flex;
      min-height: 32px;
      float: right;
      .primary-button {
        align-items: center;
        display: flex;
        justify-content: center;
        width: 117px;
        background: #005F99;
        border-radius: 4px;
        font-style: normal;
        font-weight: 500;
        font-size: 13px;
        line-height: 20px;
        text-align: center;
        color: #FFFFFF;
        cursor: pointer;
      }
      .secondary-button {
        align-items: center;
        display: flex;
        justify-content: center;
        width: 75px;
        background: #FFFFFF;
        border-radius: 4px;
        font-style: normal;
        font-weight: 500;
        font-size: 13px;
        line-height: 20px;
        text-align: center;
        color: #005F99;
        cursor: pointer;
      }
    }
  }

  .form-section.cloud-formation-container {
    display: flex;
    flex-direction: column;
    .cloud-formation-options {
        font-size: 13px;
        font-weight: 500;
        line-height: 20px;
        letter-spacing: 0px;
    } 
    .cloud-formation-dot {
      position: absolute;
      width: 6px;
      height: 6px;
      left: 0;
      top: calc(50% - 6px/2 + 1px);
      background: #69696A;
      border-radius: 10px;
    }
    .cloud-formation-options-link {
      position: relative;
      font-weight: 400;
      line-height: 20px;
      letter-spacing: 0em;
      text-align: left;
      padding-left: 16px;
      margin-top: 4px;
    }
    .cloud-formation-options-link-icon{
      width: 13px;
      height: 13px;
    }
    svg.svg-inline--fa.fa-xs.cloud-formation-options-link-icon.fa-external-link.fa-xs {
      margin-left: 8px;
    }
  }

  .edgeconnector-page.zero-trust-gateway-page {
    z-index: 0;
    position: relative;
    &.zero-trust-gateway-acc-groups-page {
      position: relative;
      overflow-y: auto;
      &.configuration {
        overflow: visible;
      }
    }
    .edgeconnector-page .edgeconnector-modal .modal-content .wizard-form.zero-trust-gateway-accounts .form-sections-container {
      overflow-y: auto;
    }
    .main-container.zero-trust-gateway-detail-container .config-table-container .svg-inline--fa {
      margin-right: 4px;
    }
  }

  .modal-overlay .modal-body.view-compare-versions-modal {
    bottom: auto;
    left: calc(50% - 400px);
    position: absolute;
    right: auto;
    top: 50px;
    width: 800px;
    .modal-content {
      position: relative;
      max-height: none;
      overflow: visible;
      .modal-body {
        position: relative;      
        left: 0;
        top: 0;
        background-color: white;
        width: 100%;
        max-height: 660px;
        overflow-y: auto;
        form {
          padding: 18px 18px 0 18px;
          display: inline-block;
          height: fit-content;
          .select-item {
            margin: 0;
          }
          .input-container {
            padding: 0;
          }
        }
      }
      .modal-footer {
        position: relative;
        margin-top: 0;
      }
      .drop-down-list.open {
        z-index: 90;
      }
    }
  }
  
  .z-index-4 {
    z-index: 4!important;
  }
  .z-index-3 {
    z-index: 3!important;
  }
  .z-index-2 {
    z-index: 2!important;
  } 


.wizard-nav {
  ul {
    @include DisplayFlex;
    justify-content: space-between;

    li {
      background: $grey14;
      display: inline-block;
      height: 5rem;
      width: 33%;
      position: relative;

      &:before {
        content: " ";
        display: inline-block;
        height: 0;
        width: 0;
        position: absolute;
        border-style: solid;
        border-width: 2.5rem 0 2.5rem 1rem;
        border-color: $transparent $transparent $transparent $white;
        z-index: 0;
      }

      &:first-child:before {
        border-color: $transparent;
      }

      button {
        background: $transparent;
        border: 0;
        line-height: 5rem;
        outline: none; // temp fix
        width: 100%;

        &:after {
          content: " ";
          display: inline-block;
          height: 0;
          width: 0;
          position: absolute;
          right: -1rem;
          top: -0.05rem;
          border-style: solid;
          border-width: 2.5rem 0 2.5rem 1rem;
          border-color: $transparent $transparent $transparent $grey14;
          z-index: 1;
        }

        &:hover:not(:disabled) {
          background: $grey3;

          &:after {
            border-color: $transparent $transparent $transparent $grey3; 
          }
        }

        &:disabled {
          cursor: not-allowed;
        }

        .fa-layers {
          margin-right: 1.7rem;

          .fa-circle {
            color: $grey48;
          }

          .fa-circle-dot {
            color: $green19;
          }

          .fa-circle-check {
            color: $green19;
          }

          .page-index {
            color: $white;
            top: -1.9rem;
            left: 0.7rem;
          }
        }
      }

      &.valid {
        background-color: $green7;

        button {
          &:after {
            border-color: $transparent $transparent $transparent $green7;
          }

          &:hover {
            background-color: $green7;

            &:after {
              border-color: $transparent $transparent $transparent $green7;
            }
          }

          .fa-circle {
            color: $green2;
          }

          .fa-check-circle {
            display: block;
          }

          .name {
            color: $green2;
          }
        }
      }

      &:last-of-type {
        button:after {
          border-width: 0;
        }
      }

      &.active {
        background: $white;

        button:hover {
          background-color: $white;
          cursor: default;

          &:after {
            border-color: $transparent $transparent $transparent $white;
          }
        }

        button {
          background: $white;
          z-index: 100;
        
          &:after {
            border-left-color: $white;
          }
        }
      }

      .name {
        color: $grey4;
        font-size: 1.3rem;
      }
    }
  }
}
}

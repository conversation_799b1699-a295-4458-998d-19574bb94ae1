@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

.history {
  background: var(--semantic-color-surface-base-primary);
  width: 97%;
  margin: 0 auto;
  .arrows {
    color:  var(--semantic-color-content-base-primary);
  }
  .slick-slider {
    box-sizing:border-box;
    -webkit-user-select:none;
    -moz-user-select:none;
    -ms-user-select:none;
    user-select:none;
    -webkit-touch-callout:none;
    -khtml-user-select:none;
    -ms-touch-action:pan-y;
    touch-action:pan-y;
    -webkit-tap-highlight-color:transparent;
  }
  .slick-list,.slick-slider{position:relative;display:block;}
  .slick-list{
    overflow:hidden;
    // margin:0;
    padding:0;
    margin-right: 1em;
    margin-left: 1em;
  }
  .slick-list:focus{outline:none;}
  .slick-slider .slick-list,.slick-slider .slick-track{
    -ms-transform:translateZ(0);
    transform:translateZ(0);
    background: var(--semantic-color-content-inverted-interactive-disabled);
  }
  .slick-track{position:relative;top:0;left:0;display:block;margin-left:auto;margin-right:auto;}
  .slick-track:after,.slick-track:before{display:table;content:"";}
  .slick-track:after{clear:both;}
  .slick-slide{
    display:none;float:left;height:100%;min-height:1px;
    width: 80px !important;
  }
  .slick-initialized .slick-slide{display:block;}
  .slick-next,.slick-prev{font-size:0;line-height:0;position:absolute;top:50%;display:block;width:20px;height:20px;padding:0;-ms-transform:translateY(-50%);transform:translateY(-50%);cursor:pointer;border:none;}
  .slick-next,.slick-next:focus,.slick-next:hover,.slick-prev,.slick-prev:focus,.slick-prev:hover{color:transparent;outline:none;}
  .slick-next:focus:before,.slick-next:hover:before,.slick-prev:focus:before,.slick-prev:hover:before{opacity:1;}
  .slick-next:before,.slick-prev:before{font-family:slick;font-size:20px;line-height:1;opacity:.75;color:var(--semantic-color-content-interactive-primary-default);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}
  .slick-prev{
    left:-20px;
    display: block; 
    background: var(--semantic-color-content-inverted-interactive-disabled);
    height: 98.45px; 
    padding-top: 40px; 
    padding-right: 5px;
  }
  .slick-prev:before{
    // content:"\2190";
    content:"\003C";
    margin-top: 2em;
    height: 24px;
    width: 12px;
    color: var(--semantic-color-content-base-primary);
    // font-family: "Font Awesome 5 Pro";
    font-size: 20px;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: bold;
  }
  .slick-next{
    right:-20px;
    display: block; 
    background: var(--semantic-color-content-inverted-interactive-disabled);
    height: 98.45px; 
    padding-top: 40px; 
    padding-left: 5px;
  }
  .slick-next:before{
    content:"\003E";
    margin-top: 2em;
    height: 24px;
    width: 12px;
    color: #656666;
    // font-family: "Font Awesome 5 Pro";
    font-size: 20px;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: bold;
  }
  .slick-dots{position:absolute;bottom:-25px;display:block;width:100%;padding:0;margin:0;list-style:none;text-align:center;}
  .slick-dots li{position:relative;display:inline-block;margin:0 5px;padding:0;}
  .slick-dots li,.slick-dots li button{width:20px;height:20px;cursor:pointer;}
  .slick-dots li button{font-size:0;line-height:0;display:block;padding:5px;color:transparent;border:0;outline:none;background:transparent;}
  .slick-dots li button:focus,.slick-dots li button:hover{outline:none;}
  .slick-dots li button:focus:before,.slick-dots li button:hover:before{opacity:1;}
  .slick-dots li button:before{font-family:slick;font-size:6px;line-height:20px;position:absolute;top:0;left:0;width:20px;height:20px;content:"\2022";text-align:center;opacity:.25;color:#000;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}
  .slick-dots li.slick-active button:before{opacity:.75;color:#000;}
  h3{
    background: #6b6d70;color:var(--semantic-color-surface-base-primary);font-size:36px;line-height:100px;margin:10px;padding:2%;position:relative;text-align:center;
  }
  .element {
    background: #6b6d70;
    color:var(--semantic-color-surface-base-primary);
    // font-size:36px;
    line-height:100px;
    // margin:10px;
    margin: 0.7em 0.7em 2em 0.7em;
    padding:2%;
    position:relative;
    text-align:left;
  }
  .element-icon {
    margin-top: 24px;
  }
  .insights-chart-icon{font-size:24px!important;}
  .history-thumbnail-element-icon{line-height:44px!important;}
  .history-thumbnail-belt{top:0;left:0;bottom:0;margin:0;padding:0;white-space:nowrap;position:absolute;}
  .history-element{display:inline-block;position:relative;top:8px;border:1px solid var(--semantic-color-content-base-secondary);}
  .history-element.history-thumbnail-element{background:var(--semantic-color-background-primary);cursor:pointer;display:inline-block;font-size:13px;height:62px;width:70px;cursor:pointer;margin-right:5px;}
  .history-element.history-thumbnail-element:first-child{margin-left:5px;}
  .history-element.history-thumbnail-element:hover{background: var(--semantic-color-background-secondary); opacity: .85;}
  .history-element.history-thumbnail-element.history-thumbnail-selected {
    background: var(--semantic-color-background-primary);
  }
  .history-thumbnail-element-header{color:#6b6d70;border-bottom:1px solid var(--semantic-color-content-base-secondary);height:16px;padding:0 4px;}
  .history-thumbnail-element-header.history-thumbnail-selected {
    color: var(--semantic-color-border-interactive-primary-default);
  }
  .history-thumbnail-element-header .history-thumbnail-element-header-number{color:#6b6d70;display:inline-block;vertical-align:top;width:50%;height:100%;line-height:15px;}
  .history-thumbnail-element-header .history-thumbnail-element-header-delete{color:#6b6d70;display:inline-block;text-align:right;vertical-align:top;line-height:14px;width:50%;}
  .history-thumbnail-element-header .history-thumbnail-element-header-delete:hover{color:var(--semantic-color-content-base-secondary);}
  .history-thumbnail-element-header .history-thumbnail-element-header-delete.history-thumbnail-selected:hover{color:var(--semantic-color-content-base-secondary);}
  .history-thumbnail-element-body{text-align:center;color:var(--semantic-color-content-base-secondary);width:100%;height:44px;}
  .history-thumbnail-element-body.history-thumbnail-selected {
    color: var(--semantic-color-border-interactive-primary-default);
  }

  .slick-slider {
    margin:30px auto 50px;
    background: var(--semantic-color-surface-base-primary);
    border: 1px solid var(--semantic-color-border-base-primary);
  }
  .slick-dots{margin-left:0;}
  @media (max-width:768px){
  h3{font-size:24px;padding:0;}
  .slick-arrow{display:none!important;}
  }
  .slick-next:before,.slick-prev:before{color:#000;}
}

@media screen and (max-width: $templateMediaSize) {
  .main-container>.pie-container {
    flex-direction: column;
    width: 100%;
  }
}


.insights-container {
  background: var(--semantic-color-background-primary);
  border: 1px solid var(--semantic-color-border-base-primary);
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  margin-top: 1em;

  .container-row{
    margin: 30px 0;
  }
  
}

@media screen and (max-width: $templateMediaSize) {
  .main-container>.pie-container {
    flex-direction: column;
    width: 100%;
  }
}

.ec-root-page {
  .main-insights-container {
  padding: 19px 25px;
  .rdg-wrapper .react-grid-Canvas {
    background-color: var(--semantic-color-background-primary);
  }
  .component-header {	
    height: 24px;
    width: fit-content;
    color: var(--semantic-color-content-base-primary);
    font-size: 24px;
    font-weight: 500;
    line-height: 24px;
    text-align: left;
    float: left;
    white-space: nowrap;
  }
  .header {
    width: 80%;
    float: left;
  }
  .printView {
    float: right;
    // margin: 1em 1em 1em 0em;
    cursor: pointer;
  }
  .table-wrapper {
    padding-top: 0em;
  }
  .data-filter-container {
    width: 25%;

    .drop-down-selected-value {
      border: none;
      border-bottom: 1px solid #3669d6;
      border-radius: 0;
      background: none;
    }

    .drop-down-container {
      .drop-down-list li { 
        border-radius: 0px;
        button { text-align: left; }
      }
      .dropdown-search {
        height: 32px;
        border-radius: 8px;
        background-color: var(--semantic-color-background-pale);
        margin: 10px;
        display: flex;
        max-width: 85%;
        margin-left: auto;
        margin-right: auto;
      }
      .dropdown-search-text {
        border: none;
        color: var(--semantic-color-content-base-primary);
        border-radius: 8px;
        background: inherit;
        width: 85%;
        padding: 10px 10px 10px 10px;
      }
      .clear-button {
        width: auto;
      }
      .action-buttons{
        float: right;
        white-space: nowrap;
      }
    }


    .drop-down-container ul.drop-down-list.open {
      visibility: visible;
      opacity: 1;
      .items-container {
        max-height: 300px;
        overflow-x: hidden;
        overflow-y: auto;
        width: 100%;
      }
    }
  }
  .remove-button {
    width: 10%;
  }
}

.accordion-container {
  width: 100%;
  height: 100vh;
  // background-color: whitesmoke;
  position: relative;
}

.outer-layer {
  width: 100%;
  height: 100vh;
  position: relative;
  display: flex;
  // overflow: hidden;
  .knob-holder{
    transform: scaleX(-1);
    font-size: 16px;
    letter-spacing: 0.46px;
    line-height: 27px;
    height: 35px;
    width: 23px;
    // border-radius: 0 5px 5px 0; //opposite box
    border-radius: 5px 0px 0px 5px;
    background: var(--semantic-color-background-pale);
    z-index: 0;
    padding-left: 1.5em;
  }
  .knob{
    height: 27px;
    width: 11px;
    transform: scaleX(-1);
    color: var(--semantic-color-content-base-primary);
    font-family: "Font Awesome 5 Pro";
    font-size: 16px;
    letter-spacing: 0.46px;
    line-height: 27px;
  }
  .slide {
    height: 100%;
    position: relative;
    text-align: center;
    // transition: 0.1s;
  }
  
  .filter{
      // width: 0.1%;
      width: 1px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: left;
  }
 
  .filter-expand {
    width:  400px;
    direction:rtl; 
    // background-color: burlywood; // change
    background-color: var(--semantic-color-background-primary);
    height: 100%;
    // overflow-y: auto;
    padding: 0;
    margin: 0;
    position: absolute;
    .filter-expand-width {
      width:  400px;
      float: left;
      direction:ltr; 
      // width:  400px;
      // overflow: visible;
      height: 100%;
      // background-color: #000000;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: left;
      // overflow-x: visible;
      .expander {
        border: 1px solid var(--semantic-color-border-base-primary);
      }
    }
  }
  .filter-expand-space {
    float: left;
    direction:ltr; 
    width:  400px;
    overflow: visible;
    height: 100%;
    // background-color: burlywood;//change
    // background-color: var(--semantic-color-background-primary);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: left;
    z-index: -1;
    // overflow-x: visible;
  }

  .tablesec{
    width: 98%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: left;
    padding-right:10px;
  }
  .tablesec-shrink {
    width: calc(100% - 32rem);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: left;
    padding-right:10px;
    overflow-y: auto;
  }
}

div.drop-down-container ul.drop-down-list li {
  width: 100%;
}

.insights-filters-container {
  padding: 24px;
  height: 100%;
  .tabs-items div a {
    font-size: 24px;
  }
  .time-filter .time-filter-dropdown {
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 4px;
  }
  .separator-line {
    margin: 0;
  }
  .separator-line-header {
    margin: 30px 0;
  }
  .tabPactive {
    color: var(--semantic-color-content-base-primary);
    // z-index: 2;
  }
  .tabs-items div div.highlighter {
    z-index: 2;
    position: relative;
  }
  .tabs-highlrighte {
    background: var(--semantic-color-border-base-primary);
    z-index: 1;
  }
  label {
    span {
      margin-left: 0px;
    }
  }
  .header{
    padding-bottom: 2em;
    margin-top: 19px;
  }
  .title{
    height: 19px;
    width: 37px;
    color: var(--semantic-color-surface-base-primary);
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 19px;
    float: left;
  }
  .clear {
    height: 16px;
    color: var(--semantic-color-content-interactive-primary-default);
    font-family: "Font Awesome 5 Pro";
    font-size: 12px;
    letter-spacing: 0;
    line-height: 16px;
    white-space: nowrap;
    cursor: pointer;
    float: right;
  }
  .refresh-text{
    padding-left: 0.25em;
  }
  .filter-box {
    min-height: 100px;
    padding: 1em 0em 1em 0em;
    white-space: nowrap;
    .checkbox-container.unselected {
      display: flex;
    }
  }
  .side-header{
    padding-bottom: 2em;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .filter-sideheader {
    height: 16px;
    // width: 62px;
    color: var(--semantic-color-content-base-primary);
    font-size: 13px;
    letter-spacing: 0;
    line-height: 15px;
    float: left;
    svg {
      color: var(--semantic-color-content-base-tertiary);
      width: 23px;
      height: 16px;
    }
  }
  .filter-card-small {
    height: 49px;
    width: 320px;
    border-radius: 5px;
    background-color: var(--semantic-color-background-primary);    
    margin-top: 2em;
    .radio-button-container.radio-group-container {
      padding: 0;
      height: 100%;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
    .select-item {
      .react-select__control {
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 4px;
      }
    }
    .react-select__menu {      
      height: 28em;
      text-align: left;
      .react-select__menu-list {
        min-height: 20em;
        overflow: visible;
      }
    }
    .single {
      width: 100%;
    }
    .drop-down-container{
      width: 90%!important;
    }
    button.drop-down-selected-value {
      border: none !important;
      box-shadow: none !important;
      // border-radius: 8px;
      // border-bottom: 2px solid var(--semantic-color-content-interactive-primary-default);
      margin: 2px 1.5px 0   1.5px;
    }
    // .radio-button-container .radio-buttons .radio-button  {
    //   margin-right: 3px;
    //   label{
    //   padding: 4px 15px 4px 0;
    //   border-radius: 4px;
    //   }
    // }
    .check-circle {
      padding-left: 6px;
    }
    .select-item {
      width: 250px;
      margin-left: 0px;
      // .css-1szy77t-control {
      //   border-left: 0;
      //   border-right: 0;
      //   border-top: 0;
      //   border-radius: 0;
      //   box-shadow: none;
      //  &:hover {
      //   box-shadow: none;
      //   }
      // }
    }
  }
  .expander {
    max-height: 100%;
  }
  .filter-card-large {
    // max-height: 55em;
    width: 100%;
    border: none;
    .radio-button-container  .radio-buttons {
      border: 1px solid var(--semantic-color-border-base-primary);
      padding: 4px 6px;
      background-color: var(--semantic-color-background-pale);
      border-radius: 10px;
      .radio-button {
        .chart-label {
          width: fit-content;
        }
        label {
          padding: 8px 0;
          min-height: 20px;
          border: none;
        }
      }
    }

    .add-dropdown {
      .fa-angle-down, .fa-angle-up  {
        display: none;
      }
      .drop-down-selected-value {
        text-align: right;      
        border: none;
      }
    }
    .drop-down-container{
      width: 94%
    }
    .drop-down-selected-value{
      padding: 3px;
      border: none !important;
      box-shadow: none !important;
      // border-radius: 8px;
      // border-bottom: 2px solid var(--semantic-color-content-interactive-primary-default);
      margin: 2px 1.5px 0   1.5px;
    }
    .filter-container{
      padding: 5px 0  5px 0;
      .fa-xmark {
        color: var(--semantic-color-content-interactive-primary-default);
      }
      .select-item {
        width: 280px;
        margin-left: 10px;
      }
      .filter-card-header{
        padding-bottom: 2em;
      }
      .filter-card-label{
        height: 16px;
        color: var(--semantic-color-content-base-primary); 
        font-weight: 500;
        font-size: 13px;
        letter-spacing: 0;
        line-height: 15px;
        float: left;
        padding: 10px;
        &.invalid {
          color: var(--semantic-color-content-status-danger-primary);
        }
      }
      .filter-clear{
        height: 16px;
        color:  var(--semantic-color-content-base-primary);
        font-family: "Font Awesome 5 Pro";
        font-size: 12px;
        letter-spacing: 0;
        line-height: 16px;
        white-space: nowrap;
        padding: 10px 20px 0  0;
        float: right;
        cursor: pointer;
      }
      .couple-filters{
        padding-top: 10px;
        background-color: var(--semantic-color-background-primary);
        .input-container .input-wrapper {
          border: 1px solid var(--semantic-color-border-base-primary);
          border-radius: 4px;
        }
      }
      .couple-filters-bottom{
        padding-bottom: 20px;
      }
      .dropdown{
        width: 90%;
        margin-left: 10px;
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 8px;
        margin-top: 4px;
        min-height: 34px
      }
      .radio-button-container.radio-group-container {
        padding: 0 10px;
      }
      #requestedDomainMatchType .entity-dropdown .react-select__value-container, .entity-dropdown .react-select__indicator {
        min-width: fit-content;
      }
      .react-select__control {
        min-width: 90%;
        margin-left: 10px;
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 8px;
        margin-top: 4px;
        min-height: 36px;
      }
      .input-label {
        margin-top: 10px;
        text-align: left;
        font-size: 13px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 16px;
      }
      .input-container input{
        background-color: var(--semantic-color-background-pale);
      }
      // added for AddFilter Dropdown
      .drop-down-container {
        .drop-down-list li { 
          border-radius: 0px;
          button { text-align: left; }
        }
        .drop-down-list > li:first-child:hover {
          background-color: var(--semantic-color-background-primary);
          color: var(--semantic-color-content-interactive-primary-default);
        }
        .items-container > li:first-child:hover {
          background: var(--semantic-color-content-interactive-primary-default);
          color: var(--semantic-color-background-primary);
        }
        .dropdown-search {
          height: 32px;
          // width: 18em;
          border-radius: 8px;
          background-color: var(--semantic-color-background-pale);
          margin: 10px;
          display: flex;
          max-width: 85%;
          margin-left: auto;
          margin-right: auto;
        }
        .dropdown-search-text {
          border: none;
          color: var(--semantic-color-content-base-primary);
          border-radius: 8px;
          background: inherit;
          width: 85%;
          padding: 10px 10px 10px 10px;
          border-radius: 8px;
        }
        .clear-button {
          width: auto;
        }
        .action-buttons{
          width: 10px;
        }
      }
     
      .drop-down-container ul.drop-down-list.open {
        visibility: visible;
        opacity: 1;
        .items-container {
          max-height: 300px;
          overflow-x: hidden;
          overflow-y: auto;
          width: 100%;
        }
      }
      .prefix {
        float: left;
        .select-item{
          width: 165px;
          margin-left: 0;
        }
        .react-select__menu {
          width: 14em;
        }
      }
      .suffix {
        .input-container {
          padding: 0.6em 0.5em 0em 0.1em;
        }
        .input-container .input-wrapper input {
          border: 1px solid var(--semantic-color-border-base-primary);
          border-radius: 4px;
        }
        .empty-container {
          padding: 0.6em 0.5em 0em 0.1em;
          width: 11em;
          height: 3em;
          border: none;
        }
      }
    }
    .entity-dropdown-container{
      .select-item{
        width: 90%;
        margin-left: 12px;
        // remove outline for react-select
        // none of the below css works - need to fix
        .css-1szy77t-control :hover{
          border-color: var(--semantic-color-surface-base-primary);
          outline: 0 !important;
        }
        .react-select__control{
          outline: 0 !important;
        }
        // react-select__control--is-focused
        .react-select__control--menu-is-open{
          border: 1px solid var(--semantic-color-border-base-primary) !important;
        }
        .react-select__control--is-focused{
          outline: 0 !important;
          }
        // till here
      }

    }
  }
  .apply-filters{
    width: 20.5em;
    margin-top: 3em;
    margin-bottom: 30em;
    .svg-inline--fa.fa-arrow-rotate-right  {
      transform: rotateY(180deg);
    }
  }

}

.filter-card-small {
  color:  var(--semantic-color-content-base-primary);
  .css-dvua67-singleValue {
    color:  var(--semantic-color-content-base-primary);
  } 
  .filter-card-small-units {
    margin-top: 0.0em;
  }
  .metric-dropdown {
    height: 49px;
    width: 250px;
    border-radius: 5px; 
    min-width: 322px;   
    background-color: var(--semantic-color-background-primary);
    margin-top: 2em;
    .react-select__menu {
      height: 13em;
      text-align: left;
      .react-select__menu-list {
        min-height: 11em;
        overflow: visible;
      }
    }
  }
}

.filter-card-small-chart {
  height: 44px;
  width: 268px;
  border-radius: 5px;
  background-color: var(--semantic-color-surface-base-primary);
  margin-top: 2em;
  p {
    display: none;
  }
  .radio-button-container {
    padding: 0;
    .charts {
      height: 0;
      margin: 0;
    }
  }
}

.radio-button-container  .radio-buttons .radio-button {
  .chart-label {
    width: fit-content;
  }
  label {
    padding: 8px 0;
  }
}

.title-back{
    color: var(--semantic-color-content-interactive-primary-default);
    cursor: pointer;
  }
.no-pointers {
  pointer-events: none;
  height: 7.5in;
  margin-bottom: 2em;
}
/*print button container*/
.print-view-button-wrapper {
    top: 57px;
    width: 100%;
    margin: 0 auto;
    background: var(--semantic-color-surface-base-primary);
    border-bottom: 1px solid var(--semantic-color-border-base-primary);
    position: relative;
    z-index: 1000;
    .back {
        padding-left: 5px;
        color: var(--semantic-color-content-base-primary);
    }
}
.print-widget-filter-label-value, .print-report-filter-label-value {
  display: inline-block;
  font-weight: 600;
  color: var(--semantic-color-content-base-primary);
  max-width: 565px;
  vertical-align: bottom;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
  .print-view-button-container {
    display: block;
    margin: 0 auto;
    width: 9.5in;
    height: 128px;
    padding: 50px 0; }
  
    .print-view-widget-filters {
      display: inline-block;
      vertical-align: top;
      position: absolute;
      right: 40px;
  }
  .exit-print-view {
    text-align: left !important; }
    .exit-print-view .control-button-reports {
      display: inline-block;
      vertical-align: middle;
      position: relative;
      color: var(--semantic-color-content-base-primary);
    }
  
  .print-column {
    width: 2.83in;
    display: inline-block;
    text-align: center;
    vertical-align: top;
    height: 28px; }
    .print-column.exit-print-view {
      padding-left: 24px; }
    .print-column.print-view-btn {
      padding-right: 24px; }
    .print-column .print-view-title {
      font-size: 16px;
      color: var(--semantic-color-content-base-primary);
      position: relative;
      top: 5px; }
  
  .print-view-btn {
    text-align: right; }
  
  .print-page-container {
    padding-top: 130px;
    padding-bottom: 1px;
    height: auto;
    background: var(--semantic-color-surface-base-secondary); }
  
    .print-report-overflow-container {
      display: none;
      padding: 16px 0 0;
      color: #939393;
  }

img{border:0;}
:-moz-placeholder{font-weight:300;}
::-webkit-input-placeholder{font-weight:300;}
::-ms-clear{display:none;}
::-ms-reveal{display:none;}
.column{display:inline-block;vertical-align:top;}
.column::-moz-selection{background:transparent;}
.column::selection{background:transparent;}
.copyright-statement:before{content:" ";}
@media print{
.print .print-page{margin:0;margin-bottom:0;-webkit-box-shadow:none!important;-moz-box-shadow:none!important;box-shadow:none!important;}
.print .print-page .print-view-content{height:9.25in!important;font-family:"arial";}
.print .print-page .print-view-content .print-report-filter-label{font-family:"arial";}
.print .print-page .print-view-content .print-report-filter-label-value{font-family:"arial";font-weight:bold;}
.print .print-page .print-view-footer,.print .print-page .print-view-header{font-family:"arial";}
}
.print-view-header{width:100%;height:0.75in;}
.print-view-content{
  // height:calc(100% - 1.25in);
  width:100%;
}
.print-view-footer{color:#939393;font-size:13px;height:0.5in;}
.print-page{background:var(--semantic-color-surface-base-primary);height:10.5in;width:8.5in;margin:0 auto;margin-bottom:0.40in;padding:0.25in;page-break-after:always;page-break-inside:avoid!important;position:relative;-webkit-box-shadow:0px 1px 4px 0px rgba(0, 0, 0, 0.45);-moz-box-shadow:0px 1px 4px 0px rgba(0, 0, 0, 0.45);box-shadow:0px 1px 4px 0px rgba(0, 0, 0, 0.45);}
.print-view-left-footer{width:80%;vertical-align:middle;}
.print-view-right-footer{width:20%;vertical-align:middle;}
.print-view-footer-content{display:inline-block;vertical-align:middle;}

.print-view-logo{width:50%;display:inline-block;vertical-align:middle;}
.print-view-header-text{width:50%;display:inline-block;text-align:right;vertical-align:middle;}
.print-view-header-title{font-size:16px;color:#00bce4;}
.print-view-header-index{text-align:right;}
.print-view-header-index:after{
  content: "Page " counter(chapter);
  counter-increment: chapter;
  padding-left: 5px;
}
.print-report-filter-label{display:inline-block;padding-right:10px;color:#6b6d70;vertical-align:bottom;}
.print-report-filter-label-value{display:inline-block;font-weight:600;color:var(--semantic-color-content-base-secondary);max-width:565px;vertical-align:bottom;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.print-view-information-panel{display:inline-block;vertical-align:top;width:100%;}
.print-view-information-panel.fullwidth{width:100%;}
.print-report-filter-label:after{content:":";}
.print-view-information{border:1px solid var(--semantic-color-border-base-primary);border-radius:3px;-moz-border-radius:3px;-webkit-border-radius:3px;background-color:var(--semantic-color-background-primary);height:auto;padding:16px;}
.print-view-filters{padding-bottom:16px;font-size:13px;}
.print-view-filters:last-child{padding-bottom:0px;}
}


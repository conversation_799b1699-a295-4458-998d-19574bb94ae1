@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

*:focus {
  outline: none;
}
.ec-root-page {
.filters-container {
  padding: 24px;
  height: 100%;
  // margin-bottom: 350px;
  // .tabPactive {
  //   color: var(--semantic-color-surface-base-primary);
  // }
  .time-filter .time-filter-dropdown {
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 4px;
  }
  .separator-line {
    margin: 0;
  }
  .separator-line-header {
    margin: 30px 0;
  }
  .tabs-items {
    .tabP, .tabPactive {
      font-size: 24px;
    }
  }
  .tabs-items div div.highlighter {
    z-index: 2;
    position: relative;
  }
  .tabs-highlrighte {
    background: var(--semantic-color-border-base-primary);
    z-index: 1;
  }
  label {
    span {
      margin-left: 0px;
    }
  }
  .header{
    padding-bottom: 2em;
    margin-top: 19px;
  }
  .title{
    height: 19px;
    width: 37px;
    color: var(--semantic-color-surface-base-primary);
    font-size: 24px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 19px;
    float: left;
  }
  .clear {
    height: 16px;
    color: var(--semantic-color-content-interactive-primary-default);
    font-family: "Font Awesome 5 Pro";
    font-size: 12px;
    letter-spacing: 0;
    line-height: 16px;
    white-space: nowrap;
    cursor: pointer;
    float: right;
    text-decoration: none;
  }
  .refresh-text{
    padding-left: 0.25em;
  }
  .filter-box {
    min-height: 100px;
    padding: 1em 0em 1em 0em;
    white-space: nowrap;
    .checkbox-container.unselected {
      display: flex;
    }
  }
  .side-header{
    padding-bottom: 2em;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .filter-sideheader {
    height: 16px;
    // width: 62px;
    color: var(--semantic-color-content-base-primary);
    font-size: 13px;
    letter-spacing: 0;
    line-height: 15px;
    float: left;
    svg {
      color: var(--semantic-color-content-base-tertiary);
      width: 23px;
      height: 16px;
    }
  }
  .filter-card-small {
    height: 49px;
    width: 320px;
    border-radius: 5px;
    // background-color: var(--semantic-color-surface-base-primary);
    margin-top: 2em;
    div.time-filter-dropdown div.drop-down-container button.drop-down-selected-value {
      box-shadow: none;
      border: none;
    }
    &.download-display{
      padding: 7px 15px;
    }

    .react-select__menu {
      height: 35em;
      text-align: left;
      .react-select__menu-list {
        min-height: 25em;
        overflow: visible;
      }
    }
    
    .drop-down-container{
      width: 90%!important;
    }
    .drop-down-selected-value{
      border: 0;
      border-radius: 2px;
      border-bottom: 2px solid var(--semantic-color-content-interactive-primary-default);
      margin: 2px 1.5px 0   1.5px;
    }
    .radio-button-container .radio-buttons .radio-button label{
     padding: 4px 15px 4px 0;
    }
    .check-circle {
      padding-left: 6px;
    }
    .select-item {
      width: 230px;
      margin-left: 20px;
    }
  }
  .simple-dropdown {
    .select-item {
      width: 240px;
      margin-left: 12px;
    }
  }
  .expander {
    max-height: 100%;
  }
  .filter-card-large {
    // height: 49px;
    width: 100%;
    border: none;
    .radio-button-container  .radio-buttons {
      border: 1px solid var(--semantic-color-border-base-primary);
      padding: 4px 6px;
      background-color: var(--semantic-color-background-pale);
      border-radius: 10px;
      .radio-button {
        .chart-label {
          width: fit-content;
        }
        label {
          padding: 8px 0;
          min-height: 20px;
          border: none;
        }
      }
    }
    .add-dropdown {
      .fa-angle-down, .fa-angle-up  {
        display: none;
      }
      .drop-down-selected-value {
        text-align: right;
        border: none;
      }
    }
    .drop-down-container{
      width: 94%
    }
    .drop-down-selected-value{
      border: 0;
      border-radius: 2px;
      border-bottom: 2px solid var(--semantic-color-content-interactive-primary-default);
      margin: 2px 1.5px 0   1.5px;
    }
    .filter-container{
      padding: 5px 0  5px 0;
      .fa-xmark {
        color: var(--semantic-color-content-interactive-primary-default);
      }
      .select-item {
        width: 280px;
        margin-left: 10px;
      }
      .filter-card-header{
        padding-bottom: 2em;
      }
      .filter-card-label{
        height: 16px;
        color: var(--semantic-color-content-base-primary);
        font-weight: 500;
        font-size: 13px;
        letter-spacing: 0;
        line-height: 15px;
        float: left;
        padding: 10px;
        &.invalid {
          color: var(--semantic-color-content-status-danger-primary);
        }
      }
      .filter-clear{
        height: 16px;
        color:  var(--semantic-color-content-base-primary);
        font-family: "Font Awesome 5 Pro";
        font-size: 12px;
        letter-spacing: 0;
        line-height: 16px;
        white-space: nowrap;
        padding: 10px 20px 0  0;
        float: right;
        cursor: pointer;
      }
      .couple-filters{
        padding-top: 10px;
        background-color: var(--semantic-color-background-primary);
        .input-container .input-wrapper {
          border: 1px solid var(--semantic-color-border-base-primary);
          border-radius: 4px;
        }
      }
      .couple-filters-bottom{
        padding-bottom: 20px;
      }
      .dropdown{
        width: 90%;
        margin-left: 10px;
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 8px;
        margin-top: 4px;
        min-height: 34px
      }
      .radio-button-container.radio-group-container {
        padding: 0 10px;
      }
      .entity-dropdown .react-select__value-container, .entity-dropdown .react-select__indicator {
        min-width: fit-content;
      }
      .react-select__control {
        min-width: 90%;
        margin-left: 10px;
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 8px;
        margin-top: 4px;
        min-height: 36px;
      }
      .input-label {
        margin-top: 10px;
        text-align: left;
        font-size: 13px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 16px;
      }
      .input-container input{
        background-color: var(--semantic-color-background-pale);
        width: auto;
      }
      // added for AddFilter Dropdown
      .drop-down-container {
        .drop-down-list li button {
          text-align: left;
        }
        .drop-down-list > li:first-child:hover {
          background-color: var(--semantic-color-background-primary);
          color: var(--semantic-color-content-interactive-primary-default);
        }
        .items-container > li:first-child:hover {
          background: var(--semantic-color-content-interactive-primary-default);
          color: var(--semantic-color-background-primary);
        }
        .dropdown-search {
          height: 32px;
          // width: 18em;
          border-radius: 8px;
          background-color: var(--semantic-color-background-pale);
          margin: 10px;
          display: flex;
          max-width: 85%;
        }
        .dropdown-search-text {
          border: none;
          color: var(--semantic-color-content-base-primary);
          background: inherit;
          width: 85%;
          padding: 10px 10px 10px 10px;
          border-radius: 8px;
        }
        .clear-button {
          width: auto;
        }
        .action-buttons{
          width: 10px;
        }
      }
     
      .drop-down-container ul.drop-down-list.open {
        visibility: visible;
        opacity: 1;
        background: var(--semantic-color-background-primary);
        .items-container {
          max-height: 300px;
          overflow-x: hidden;
          overflow-y: auto;
          width: 100%;
        }
      }
      .prefix {
        float: left;
        .select-item{
          width: 165px;
          margin-left: 0;
        }
        .react-select__menu {
          width: 14em;
        }
      }
      .suffix {
        .input-container {
          padding: 0.6em 0.5em 0em 0.1em;
          width: 11em;
        }
        .input-container .input-wrapper input {
          border: 1px solid var(--semantic-color-border-base-primary);
          border-radius: 4px;
        }
      }
      .empty-suffix {
        .input-container {
          padding: 0.6em 0.5em 0em 0.1em;
          width: 11em;
          visibility: hidden;
        }
      }
    }
    .entity-dropdown-container{
      .select-item{
        width: 90%;
        margin-left: 12px;
        // remove outline for react-select
        // none of the below css works - need to fix
        .css-1szy77t-control :hover{
          border-color: var(--semantic-color-surface-base-primary);
          outline: 0 !important;
        }
        .react-select__control{
          outline: 0 !important;
        }
        // react-select__control--is-focused
        .react-select__control--menu-is-open{
          outline: 0 !important;
          // border-color: var(--semantic-color-surface-base-primary);
        }
        .react-select__control--is-focused{
          outline: 0 !important;
          }
        // till here
      }

    }
  }
  .apply-filters{
    width: 20.5em;
    margin-top: 3em;
    margin-bottom: 19.5em;
    .svg-inline--fa.fa-arrow-rotate-right  {
      transform: rotateY(180deg);
    }
  }

}


.filter-card-small-log {
  height: 34px;
  width: 268px;
  border-radius: 5px;
  background-color: transparent;
  margin-top: 2em;
  p {
    display: none;
  }
  .radio-button-container {
    padding: 0;
    background-color: transparent;
    .charts {
      height: 0;
      margin: 0;
    }
  }
}

// .radio-button-container .radio-buttons .radio-button {
//   .chart-label {
//     width: fit-content;
//   }
//   .checked-true label {
//     padding: 8px 0;    
//   }  
//   .checked-false label {
//     padding: 8px 0;
//     .check-circle {
//       width: fit-content;
//     }
//   }  
// }

.radio-button-container .radio-buttons .radio-button label .check-circle {
  width: fit-content;
}


.main-container {
  .container-row{
    margin: 30px 0;
  }
  
}
.no-overflow-text {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.display-flex-space-between {
  display: flex;
  justify-content: space-between;
}

@media screen and (max-width: $templateMediaSize) {
  .main-container>.pie-container {
    flex-direction: column;
    width: 100%;
  }
}
.main-container {
  padding: 19px 25px;
  .component-header {	
    height: 24px;
    width: fit-content;
    color: var(--semantic-color-content-base-primary);
    font-size: 24px;
    font-weight: 500;
    line-height: 24px;
    text-align: left;
    float: left;
    white-space: nowrap;
  }
  .header {
    width: 100%;
    float: left;
    display: flex;
    justify-content: space-between;
  }
  .download {
    float: left;
    margin: 1em;
    cursor: default;
    display: flex;
  }
  .download-button {
    margin-left: 8px;
  }
}

.accordion-container {
  width: 100%;
  height: 100vh;
  // background-color: whitesmoke;
  position: relative;
  .ecui-waiting-overlay  {
    background: none;
  }
}

.outer-layer {
  width: 100%;
  height: 100vh;
  position: relative;
  display: flex;
  // overflow: hidden;
  .knob-holder{
    transform: scaleX(-1);
    font-size: 16px;
    letter-spacing: 0.46px;
    line-height: 27px;
    height: 35px;
    width: 23px;
    // border-radius: 0 5px 5px 0; //opposite box
    border-radius: 5px 0px 0px 5px;
    background: var(--semantic-color-background-pale);
    z-index: 0;
    padding-left: 1.5em;
  }
  .knob{
    height: 27px;
    width: 11px;
    transform: scaleX(-1);
    color:  var(--semantic-color-content-base-primary);
    font-family: "Font Awesome 5 Pro";
    font-size: 16px;
    letter-spacing: 0.46px;
    line-height: 27px;
  }
  .slide {
    height: 100%;
    position: relative;
    text-align: center;
    // transition: 0.1s;
  }
  
  .filter{
      width: 1px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: left;
  }
 
  .filter-expand {
    // width: 25%;
    width:  400px;
    // background-color: #000000;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: left;
    min-height: max-content;
    height: max-content;
  }

  .tablesec{
      width: 98%;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: left;
  }
  .tablesec-shrink {
    width: calc(100% - 32rem);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: left;
    padding-right:10px;
    overflow-y: auto;
  }

  .logs-insights-tooltip-container {
		min-height: 30px;
		overflow: hidden;
		padding: 12px 16px;
		word-wrap: break-word;
	}
}


}

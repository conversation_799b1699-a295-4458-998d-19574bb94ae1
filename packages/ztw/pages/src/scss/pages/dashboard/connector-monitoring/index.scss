@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

.ec-root-page {
.last-config-template-failed {
  color: #CE4035;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 153.846% */
  margin-right: 16px;
  .last-config-template-failed-icon {
    margin-right: 5px;
  }
}
.global-ux-score {
  position: relative;
  min-height: 280px;

  .selected-locations-header {
    width: 100%;
    p{
      float: left;
    }
    .fa-times {
      float: right;
      color: var(--semantic-color-content-interactive-primary-default);
      cursor: pointer;
    }
  }
}

.cloudconnector-info, .deploy-as-gateway-info {
  // background: #F5F5F5;
  margin-bottom: 50px;
  .title-container {
    padding-right: 25px;
  }
  .space-between {
    display: flex;
    display: -webkit-flex;
    justify-content: space-between;
  }
  .title-back{
    color: var(--semantic-color-content-interactive-primary-default);
    cursor: pointer;
  }
  .refresh {
    cursor: pointer;
    color: var(--semantic-color-content-interactive-primary-default);
    /* float: right; */
    font-weight: 500;
    font-size: 13px;
    line-height: 20px;
    letter-spacing: 0;
    cursor: pointer;
    align-items: center;
    .fa-rotate {
      margin-right: 5px;
    }
  }
  .side-header {
    // // padding-left: 3.5em;
    // color: #656666;
    // font-size: 13px;
    // font-weight: 500;
    // letter-spacing: 0.2px;
    // line-height: 16px;
    // text-transform: uppercase;
    color:  var(--semantic-color-content-base-primary);
    font-style: normal;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 20px;

  }
  .title-nav{
   display: initial;
   padding-left: 1em;
   color: #949494;
   font-size: 16px;
   font-weight: 500;
   letter-spacing: 0;
   line-height: 24px;
  }
  .title-nav-right{
    display: initial;
    padding: 0em 1em 0em 1em;
    color: var(--semantic-color-content-base-primary); 
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 24px;
   }
   .filter-icon {
    height: 15px;
    padding-right: 5px;
  }
  .config-navagation-tab {
    padding: 1em 2em 0em 0em;
  }
  .status{
    display: initial;
    padding-left: 25%;
    color:  var(--semantic-color-content-base-primary);
    font-size: 13px;
    letter-spacing: 0.2px;
    line-height: 24px;
    text-align: center;
  }
  .refresh{
    color: var(--semantic-color-content-interactive-primary-default);
    float: right;
  }
  .divider{
    box-sizing: border-box;
    height: 10em;
    width: 0.25px;
    border: 0.25px solid #D6D7DB;
    border-radius: 0.25px;
  }

  .dashboard-detail-panel {
    /* Auto layout */
    // display: flex;
    // flex-direction: column;
    // align-items: flex-start;
    padding: 12px;
    // gap: 16px;
    margin-bottom: 20px;

    // position: absolute;
    // width: 1142px;
    // height: 268px;
    // left: 0px;
    // top: 0px;

    /* Surface/Default */
    background: var(--semantic-color-background-primary);
    /* DropShadow/Bottom/Light/4 | 10-2-8 */
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    
    .content{
      &.connector-monitoring {
        // padding: 1em 3.5em 1em 3.5em;
        padding:0;
        padding-top: 16px;
        .separator-line-dashboard {
          height: 1px;
          background-color: var(--semantic-color-border-base-primary);
          margin: 10px 52px;
          padding: 0 52px;
          width: calc(100% - 104px);
        }
        &.interfaces {
          padding-top: 0;
          // .drawer-container .dialog-mask {
          //   opacity: 0.25;
          // }

          .first-row-interface, .last-row-interface {
            width: 100%;
            padding: 12px 8px;
            position: relative; 
            min-height: 44px;
            color:  var(--semantic-color-content-base-primary);
            font-size: 13px;
            font-style: normal;
            font-weight: 500;
            line-height: 20px; /* 153.846% */
            // justify-content: space-between;
            border: 0px solid var(--DeepBlue-Borders-Dividers-Border-02, #E9E9E9);
            background: var(--DeepBlue-Background-BG-01, #FAFAFA);
            &.no-backgrond-color {
              background: var(--semantic-color-background-primary);
            }
            .light-color {
              color: #69696A
            }
            .row-item-1 {
              position: absolute; 
              width: 200px;
              left: 12px;
            }
            .row-item-2 {
              position: absolute; 
              width: 200px;
              left: 236px;
            }
            .row-item-3 {
              position: absolute; 
              width: 200px;
              left: 460px;
            }
            .row-item-4 {
              position: absolute; 
              width: 200px;
              left: 684px;
            }
            .row-item-5 {
              position: absolute; 
              width: 200px;
              left: 908px;
            }
          }
          .interface-details-review-group{
            // width: 100%;
            // padding: 12px 0px; 
            // height: 44px;
            display: flex;
            justify-content: space-between;
            color:  var(--semantic-color-content-base-primary);
            font-size: 13px;
            font-style: normal;
            font-weight: 500;
            line-height: 20px; /* 153.846% */
            .light-color {
              color:  #69696A;
            }
            
          }
          .interface-details {
            width: 100%;
            // padding: 12px 0px; 
            height: 44px;
            display: flex;
            justify-content: space-between;
            color:  var(--semantic-color-content-base-primary);
            font-size: 13px;
            font-style: normal;
            font-weight: 500;
            line-height: 20px; /* 153.846% */
            .light-color {
              color: #69696A
            }
            .row-item-1 {
              position: absolute; 
              width: 200px;
              left: 26px;
              top: 12px;
            }
            .row-item-2 {
              position: absolute; 
              width: 200px;
              left: 250px;
              top: 12px;
              overflow: hidden;
            }
            .row-item-3 {
              position: absolute; 
              width: 200px;
              left: 451px;
              top: 12px;
            }
            .row-item-4 {
              position: absolute; 
              width: 200px;
              left: 675px;
              top: 12px;
            }
            .row-item-5 {
              position: absolute; 
              width: 200px;
              left: 882px;
              top: 12px;
            }            
          }
          .interface-details-window {
            display: flex;              
            justify-content: space-between;
            align-items: flex-start;
            flex-direction: column;
            // padding: 0px 52px;
            align-self: stretch;
            border: 0px solid var(--DeepBlue-Borders-Dividers-Border-02, #E9E9E9);
            background: var(--DeepBlue-Surface-Table-Expand, #EEF8FF);
            &.background-white {
              background-color: var(--semantic-color-surface-base-primary);
            }
            .interface-details-window-row {
              display: flex;
              position: relative;
              min-height: 44px;
              align-items: center;
              padding: 12px 0px;
              gap: 24px;
              flex: 1 0 0;
              align-self: stretch;
              .view-info {
                color: var(--DeepBlue-Interactive-Primary-Default, var(--semantic-color-content-interactive-primary-default)); 
                text-decoration: none;
                &.disabled-input {
                  color: var(--semantic-color-content-interactive-primary-disabled);
                  cursor: not-allowed;
                }
              }
              .operational-staus-icon .active {
                margin-left: 0;
              }
              .light-color {
                color:  #69696A;
              }
              &.line-value-title {
                padding-bottom: 0;
                min-height: 20px;
              }
              &.line-value {
                padding-top: 0;
                min-height: 36px;
              }
              &.line-value-multi {
                padding-top: 0;
                min-height: 24px;
              }
              .row-item-1 {
                position: absolute; 
                width: 156px;
                left: 52px;
                top: 12px;
              }
              .row-item-2 {
                position: absolute; 
                width: 230px;
                left: 235px;
                top: 12px;
              }
              .row-item-3 {
                position: absolute; 
                width: 400px;
                left: 477px;
                top: 12px;
              }
              .row-item-4 {
                position: absolute; 
                width: 200px;
                left: 677px;
                top: 12px;
              }
              .row-item-5 {
                position: absolute; 
                width: 200px;
                left: 877px;
                top: 12px;
              }
              .row-item-6 {
                position: absolute; 
                width: 200px;
                left: 1077px;
                top: 12px;
              }

            }
          }
        }
      }
      .content-box-flex{
        width: 100%;
        font: 14px Arial;
        display: flex;
        display: -webkit-flex;
        flex-direction: row;
        -webkit-flex-direction: row;

        box-sizing: border-box;
        min-height: 0em;
        border: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 8px;
        .right-border{
          border: 1px solid var(--semantic-color-border-base-primary);
        }
        .content-header {
          height: 40px;
          padding: 1em;
          background-color: var(--semantic-color-background-pale);
          color: var(--semantic-color-content-base-secondary);
          font-size: 13px;
          font-weight: 500;
          letter-spacing: 0;
          line-height: 18px;
        }
      }
      .content-box-flex > div{
        width: 30px;
        flex: 1 1 auto;
        -webkit-flex: 1 1 auto;
        transition: width 0.7s ease-out;	
        -webkit-transition: width 0.7s ease-out;
      }
      .container {
        padding: 1em;
      }
      .key{
        float: left;
        min-height: 2em;
        color: #939393;
        font-size: 13px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 2em;
        width: 40%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .value{
        float: right;
        min-height: 2em;
        color: var(--semantic-color-content-base-primary); 
        font-size: 13px;
        letter-spacing: 0.2px;
        line-height: 2em;
        width: 60%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      div.key:hover {
        overflow: visible;
        white-space: pre-wrap;
        background-color: #F7F9FA;
        color: #939393;
        }
      div.value:hover {
        overflow: visible;
        white-space: pre-wrap;
        background-color: #F7F9FA;
        color: #939393;
        }
    }
  }
  .content {
    .content-box-flex {
      min-height: 16em;
    }
  }
}

.deploy-as-gateway-info .dashboard-detail-panel .content .content-box-flex .right-border {
  border-right: none;
}

.deploy-as-gateway-info .dashboard-detail-panel .content .content-box-flex .right-border {
  // min-width: 495px;
  min-width: 0px;
}

.deploy-as-gateway-info .dashboard-detail-panel .content .key-bold .key {
  color:  var(--semantic-color-content-base-primary);
  font-size: 13px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px; /* 153.846% */
}

.deploy-as-gateway-info .dashboard-detail-panel .content .description .value {
  white-space: break-spaces;
}

.content {
  .dashboard{
    .main-container {
      padding-bottom: 0;
    }
    div#connector-monitoring-tab {
      margin-bottom: 0;
    }
  }
}

.container-in-line {
  display: flex;
  .operational-staus-icon .active {
    margin-left: 0;
  }
  .operational-staus-icon .inactive {
    margin-left: 0;
  }
  .operational-staus-icon .disabled {
    margin-left: 0;
  }
}
.min-width-1280px {
  min-width: 1280px !important;
}
.min-width-1120px {
  min-width: 1120px !important;
}
.min-width-8px {
  min-width: 8px !important;
}
.dashboard {
  // padding: 19px 25px;

  .appliance-notification-box {
    box-sizing: border-box;
    display: flex;
    align-items: flex-start;
    padding: 8px;
    background: var(--semantic-color-surface-base-primary);
    color:  var(--semantic-color-content-base-primary);
    border: 1px solid var(--semantic-color-border-base-primary);
    
    border-radius: 4px;
    flex-direction: column;
    // flex: none;
    // order: 1;
    // flex-grow: 0;
    margin-bottom: 16px;
    .appliance-notification-title{
      display: flex;
      justify-content: space-between;
      line-height: 20px;
      width: 100%;
      .left {
        width: fit-content;
        .svg-inline--fa {
          font-style: normal;
          font-weight: 400;
          font-size: 13px;
          line-height: 20px;
          margin-right: 8px;
          // display: flex;
          // align-items: center;
          // text-align: center;
          color: var(--semantic-color-content-interactive-primary-default);
          &.fa-circle-info {
            font-style: normal;
            font-weight: 900;
            font-size: 13px;
            line-height: 20px;
            color: var(--semantic-color-content-interactive-primary-default);
          }
        }
      }
      .right {
        width: fit-content;
        cursor: pointer;
        padding: 0;
        .svg-inline--fa {
          font-style: normal;
          font-weight: 400;
          font-size: 13px;
          line-height: 20px;
          margin-right: 8px;
          color: var(--semantic-color-content-interactive-primary-default);
        }
      }
    }
    .appliance-notification-message {
      padding-left:  36px;
      line-height: 20px;
    }
  }
  .dashboard-row{
    margin: 30px 0;
  }

  .pie-container {
    @include DisplayFlex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;

    .ux-donut {
      align-self: stretch;
      border: 1px solid var(--semantic-color-border-base-primary);
      background: var(--semantic-color-surface-base-primary);
      border-radius: 5px;
      padding-top: 12px;
      margin-top:  24px;
      min-height: 140px;
      width: 49.5%;
      height: 28em;
      min-width:  550px;
      vertical-align: middle;
      .ux-donut-title {
        margin-bottom: 18px;
      }

      h2 {
        margin-left: 16px;
      }

      img {
        vertical-align: bottom;
      }

      .section {
        vertical-align: top;
      }
    }
  }

  .table-content {
    padding-bottom: 90px;
    border-radius: 5px;
    // background-color: var(--semantic-color-surface-base-primary);
    // box-shadow: 0 8px 18px 0 rgba(176,186,197,0.4);
  }
}
.entity-dropdown-container {
  .filter-icon {
    height: 15px;
    padding-right: 5px;
  }
  .filter-icon-disabled {
    height: 15px;
    padding-right: 5px;
    color: $grey21;
  }
  .filter-icon-branch {
    color: #A940FF;
    height: 15px;
    padding-right: 5px;
  }
  label {
    color:  var(--semantic-color-content-base-primary);
    span {
      margin-left: 0;
    }
  }
}
.trafficFlow {
  display: none;
}
.hideVisibility {  visibility: hidden; }

.map-content {
  border-radius: 5px;
  background: var(--semantic-color-surface-base-primary);
}

.cloud-monitoring-table {
  .delete-icon,
  .pencil-icon,
  .view-icon,
  .download-icon {
    margin: 0 10px;
  }
  .ReactTable.app-table {
    max-height: 300px;
  }
  .column-layout-config-container {
    border-left: 0;
  }
}

.operational-staus-icon {
  background-color: transparent;    
  .active{
    color: var(--semantic-color-border-severity-lowest-active);
    margin: 0 4px 0 7px;
  }
  .inactive{
    color: var(--semantic-color-border-severity-high-active);
    margin: 0 4px 0 7px;
  }
  .disabled{
    color: $button-disabled-text-color;
    margin: 0 4px 0 7px;
  }
}
.value.margin-left-0px {
  .operational-staus-icon {
    .active{
      margin-left: 0;
    }
    .inactive{
      margin-left: 0;
    }
    .disabled{
      margin-left: 0;
    }
  }
}
@media screen and (max-width: $templateMediaSize) {
  .dashboard>.pie-container {
    flex-direction: column;
  }
  .dashboard>.pie-container>.content-box {
    width: 100%;
  }
}


.container-row-ha-status {
  .os-green-circle,
  .os-yellow-circle,
  .os-gray-circle
   {
    width: 0;
    padding-left: 10px;
    position: relative;
    text-align: left;
    display: inline-block;
    &::before {
      content: '';
      width: 8px;
      height: 8px;
      position: absolute;
      top: -8px;
      left: -1px;
      border-radius: 4px;
    }
  }
  .os-green-circle {
    &::before {
      background: var(--semantic-color-border-severity-lowest-active);
    }
  }
  .os-yellow-circle {
    &::before {
      background: #F4C351;
    }
  }
  .os-gray-circle {
    &::before {
      background: $button-disabled-text-color;
    }
  }
}
}
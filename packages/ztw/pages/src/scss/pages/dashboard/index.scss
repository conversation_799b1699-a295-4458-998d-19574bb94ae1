@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

.ec-root-page {
.dull {
  fill-opacity: 0.5;
}
.events-container{
  margin: 0;
 font-size: 13px;
 width: 1000px;
}
#container {
 -webkit-box-sizing: border-box;
 -moz-box-sizing: border-box;
 box-sizing: border-box;
 padding: 10px;
 width: 800px;
 height: 800px;
 background-color: var(--semantic-color-surface-base-primary);
}
.line {
 box-sizing: border-box;
 border: 0.05em solid #D6D7DB;
//  margin-bottom: 20px;
}
.recharts-xAxis {
 stroke: #D6D7DB;
 stroke-width: 1;
}
.shadow {
 -webkit-filter: drop-shadow( 3px 3px 2px rgba(0, 0, 0, .7));
 filter: drop-shadow( 3px 3px 2px rgba(0, 0, 0, .7));
}
.ec-network {
  position: relative;
  background: $white;
  min-height: 280px;
  // padding-right: 2em;
}
.global-ux-score {
  position: relative;
  min-height: 280px;

  .selected-locations-header {
    width: 100%;
    p{
      float: left;
    }
    .fa-times {
      float: right;
      color: var(--semantic-color-content-interactive-primary-default);
      cursor: pointer;
    }
  }
}
.main-container {
  padding: 19px 25px;

  .container-row{
    margin: 30px 0;
  }
  .logs-header {
    padding-bottom: 1em;

    .logs-tab {
      float: left;
    }
    .logs-filters {
      float: right;
    }
  }
  .logs-filter-container {
    box-sizing: border-box;
    min-height: 11.750em;
    overflow: auto;
    width: 100%;
    border: 1px solid #D6D7DB;
    border-radius: 5px;
    background-color: var(--semantic-color-surface-base-primary);
    margin-top: 2em;

    .left-pane {
      width:  450px;
      margin: 1em;
      float: left;
    }
    .right-pane {
      width: 300px;
      margin: 15px 150px 15px 15px;
      float: right;
    }
    .filter-container {
      width:  450px;
    }
    .filters-section {
      min-height: 10em;
    }
    .filter-container-right {
      width:  400px;
    }
    .filter-label {
      float: left;
      line-height: 2.5em;
      margin-bottom: 1.5em;
      min-width: 10em;
    }
    .filter-label-right {
      line-height: 2.5em;
      margin-bottom: 1.5em;
      float:left;
    }

    .filter-dropdown-left {
      width: 20em;
      float: right;
      margin-bottom: 1.5em;

      .drop-down-selected-value {
        border-radius: 5px;
        background-color: #F5F6F7;
      }
    }
    .buttons {
      width: 20em;
      float: right;
      margin: 0em 0em 1.5em 0em;
    }
    .clear-button {
      height: 1em;
      width: 8em;
      color: var(--semantic-color-content-interactive-primary-default);
      font-size: 13px;
      line-height: 15px;
      border: none;
      padding-left: 2em;
    }
  }
}

@media screen and (max-width: $templateMediaSize) {
  .main-container>.pie-container {
    flex-direction: column;
    width: 100%;
  }
}

  .ec-info {
    height: 12.56em;	width: 100%;	border-radius: 5px;	background-color: #F5F6F7;
  }
.ec-network {
  position: relative;
  background: $white;
  min-height: 280px;
  z-index: 0;
}
.ec-menu {
  height: 3.5em;	width: 100%;	background-color: var(--semantic-color-surface-base-primary);	box-shadow: 0 2px 4px 0 rgba(30,31,34,0.15);
}
.traffic-content{
  width: 100em;

  .fontStyle{
    height: 22px;
    width: 28px;
    padding: 2px;
  }

  .subnet {
    width: 14em;
    margin-left: 4.2em;
  }
  .service {
    width: 14em;
  }

  .ec-left {
    float:left;
    margin-top: 1em;
    z-index: 1;
    .dotDarkGrey .dotGrey {
      padding: 0.3em 0.3em 0.3em 0.7em;
    }
  }
  
  .ec-right {
    float:right;
    margin-top: 1em;
    z-index: 1;
    margin-left: 0.5em;
    .dotDarkGrey .dotGrey {
      padding: 0.3em 0.3em 0.3em 0.7em;
    }
  }
  .dotDarkGrey {
    height: 25px;
    width: 25px;
    background-color:  var(--semantic-color-content-base-primary);
    border-radius: 50%;
    display: inline-block;
    color: var(--semantic-color-surface-base-primary);
    padding: 0.5em;
    box-shadow: 0 0 2px 0 rgba(30,31,34,0.09);
  }

  .dotGrey {
    height: 25px;
    width: 25px;
    border: 1px solid #D6D7DB;
    background-color: #F5F6F7;
    border-radius: 50%;
    display: inline-block;
    color: #000000;
    padding: 0.5em;
  }

}
.traffic-path-root svg {
  position: fixed;
}
svg .traffic-path-root:hover path {
  stroke: #0082E0;
}

// svg #0010:hover {
//   stroke: #0082E0;
// }

.nodes-holder{
  top: 1em;
}
.node-source {
  top: 1em;
  z-index: 1;
  position: relative;
  float: left;
}
.node-section {
  top: 1em;
  margin-left: 3em;
  z-index: 1;
  float: left;
  position: relative;
}

.node-container{
  color: white;
  text-align: center;
  padding: 10px;

  margin-left: 1em;
  margin-bottom: 2em;
  position: static;
  left: 15em;
}
.node-edge-connector{
  position: sticky;
  width: 10em;
  color: white;
  text-align: center;
  padding: 10px;
  margin-left: 0.5em;
  margin-top: 0.5em;
}

.node {
  box-sizing: border-box;	
  max-height: 19.563em;
  width: 13.063em;	
  border: 1px solid #D6D7DB;	
  border-radius: 5px;
  overflow: auto;
}
.node-content {
  height: 2.750em;	width: 11.25em;	border-radius: 5px;	background-color: var(--semantic-color-surface-base-primary);	color: var(--semantic-color-content-base-primary);  box-shadow: 0 0 4px 0 rgba(42,44,48,0.24);
}
.node-content-max {
  display: inline-block;
  width: 9em;
  height: 2.750em; border-radius: 5px;	background-color: var(--semantic-color-surface-base-primary);	color: var(--semantic-color-content-base-primary);  box-shadow: 0 0 4px 0 rgba(42,44,48,0.24);
}
.nodeXs {
  border: 2px solid #D6D7DB;
  padding: 10px;
  border-radius: 10px;
  color: #D6D7DB;
  height: 20px;
}
.edge-connector-node {
  display: inline-block;
  position: relative;
  height: 2.750em;	width: 170px;	border-radius: 5px;	background-color: var(--semantic-color-content-interactive-primary-default);	box-shadow: 0 2px 8px 0 rgba(0,69,112,0.2);
}

  .ec-traffic {
  position: relative;
  background: $white;
  height: 100%;
}
.ec-traffic-header{
  height: 42px;	width: 100%;	background-color: var(--semantic-color-content-interactive-primary-default);
}
.ec-traffic-header-content{
  padding: 10px; height: 16px;	min-width: 20em;	color: var(--semantic-color-surface-base-primary);
  font-size: 13px;	font-weight: 500;	line-height: 16px;
}
.ec-traffic-topology{
  position: fixed;
  width: 100%;
  height: 54em;
  overflow: auto;
}
.dashboard {
  padding: 20px 40px 20px 20px;

  .dashboard-row{
    margin: 30px 0;
    .app-table-container {
      margin-bottom: 50px;
      max-height: 38em;
      overflow: scroll;
    }
  }
  .dual-container {
    @include DisplayFlex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    .page-title {
      padding: 0 24px;
    }

    .left-section {
      white-space: nowrap;
      span {
        color:  var(--semantic-color-content-base-primary);
        font-weight: 400;
        font-size: 13px;
        line-height: 20px;
        letter-spacing: 0.2px;
        line-height: 24px;
      }
    }
    .right-section {
      padding-right: 20px;
      vertical-align: middle;
      span {
        color: var(--semantic-color-content-interactive-primary-default);
        font-weight: 500;
        font-size: 13px;
        line-height: 20px;
        letter-spacing: 0;
        cursor: pointer;
      }
    }
  }
  .pie-container {
    @include DisplayFlex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;

    .ux-donut {
      align-self: stretch;      
      border: 1px solid var(--semantic-color-border-base-primary);
      background: var(--semantic-color-surface-base-primary);
      border-radius: 5px;
      padding-top: 12px;
      margin-top:  24px;
      min-height: 140px;
      width: 49.5%;
      height: 28em;
      min-width:  550px;
      vertical-align: middle;
      .ux-donut-title {
        margin-bottom: 18px;
      }

      h2 {
        margin-left: 16px;
      }

      img {
        vertical-align: bottom;
      }

      .section {
        vertical-align: top;
      }
    }
  }
  .entity-dropdown-container{  
    .filter-icon-gcp {
      height:  20px;
      margin-right: 5px;
      color: $grey21;
      margin-bottom:  -5px;
    }
    label {
      display: flex;
    }
  }
}
.trafficFlow {
  display: none;
    font-style: normal;
    font-weight: 500;
    font-size: 13px;
}
.hideVisibility {  visibility: hidden; }
.filter-icon-disabled {
  height: 15px;
  padding-right: 5px;
  color: $grey21;
}

.map-content {
  border-radius: 5px;
  background: var(--semantic-color-surface-base-primary);
}

@media screen and (max-width: $templateMediaSize) {
  .dashboard>.pie-container {
    flex-direction: column;
  }
  .dashboard>.pie-container>.content-box {
    width: 100%;
  }
}

.totalTxn {
  color:  var(--semantic-color-content-base-primary);
}
}
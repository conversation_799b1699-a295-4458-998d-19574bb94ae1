@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

.ec-root-page {
  .dashboard {
    // padding: 1.9rem 2.5rem;
    
    .appliance-notification-box {
      box-sizing: border-box;
      display: flex;
      align-items: flex-start;
      padding: 8px;
      background: var(--semantic-color-surface-base-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 4px;
      flex-direction: column;
      // flex: none;
      // order: 1;
      // flex-grow: 0;
      margin-bottom: 1.6rem;
      .appliance-notification-title{
        display: flex;
        justify-content: space-between;
        line-height: 20px;
        width: 100%;
        .left {
          width: fit-content;
          .svg-inline--fa {
            font-style: normal;
            font-weight: 400;
            font-size: 13px;
            line-height: 20px;
            margin-right: 8px;
            // display: flex;
            // align-items: center;
            // text-align: center;
            color: #006BAD;
            &.fa-circle-info {
              font-style: normal;
              font-weight: 900;
              font-size: 13px;
              line-height: 20px;
              color: #388ECF;
            }
          }
        }
        .right {
          width: fit-content;
          cursor: pointer;
          padding: 0;
          .svg-inline--fa {
            font-style: normal;
            font-weight: 400;
            font-size: 13px;
            line-height: 20px;
            margin-right: 8px;
            color: #006BAD;
          }
        }
      }
      .appliance-notification-message {
        padding-left: 3.6rem;
        line-height: 20px;
      }
    }
    .dashboard-row{
      margin: 3rem 0;
    }

    .pie-container {
      @include DisplayFlex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: flex-start;

      .ux-donut {
        align-self: stretch;
        border: 0.1rem solid var(--semantic-color-border-base-primary);
        border-radius: 0.5rem;
        background: var(--semantic-color-surface-base-primary);
        padding-top: 1.2rem;
        margin-top: 2.4rem;
        min-height: 14rem;
        width: 49.5%;
        height: 28em;
        min-width: 550px;
        vertical-align: middle;
        .ux-donut-title {
          margin-bottom: 1.8rem;
        }

        h2 {
          margin-left: 1.6rem;
        }

        img {
          vertical-align: bottom;
        }

        .section {
          vertical-align: top;
        }
      }
    }

    .table-content {
      padding-bottom: 9rem;
      border-radius: 5px;
      // background-color: #FFFFFF;
      // box-shadow: 0 8px 18px 0 rgba(176,186,197,0.4);
    }
  }
  .entity-dropdown-container {
    .filter-icon {
      height: 1.5rem;
      padding-right: 0.5rem;
    }
    .filter-icon-disabled {
      height: 1.5rem;
      padding-right: 0.5rem;
      color: $grey21;
    }
    .filter-icon-branch {
      color: #A940FF;
      height: 1.5rem;
      padding-right: 0.5rem;
    }
    label {
      span {
        margin-left: 0;
      }
    }
  }
  .trafficFlow {
    display: none;
  }
  .hideVisibility {  visibility: hidden; }

  .map-content {
    border-radius: 5px;
    background-color: #FFFFFF;
    box-shadow: 0 8px 18px 0 rgba(176,186,197,0.4);
  }

  .cloud-monitoring-table {
    .delete-icon,
    .pencil-icon,
    .view-icon,
    .download-icon {
      margin: 0 1rem;
    }
    .ReactTable.app-table {
      max-height: 300px;
    }
    .column-layout-config-container {
      border-left: 0;
    }
  }

  .operational-staus-icon {
    background-color: transparent;    
    .active{
      color: $green17;
      margin: 0 4px 0 7px;
    }
    .inactive{
      color: $indicator-negative-color;
      margin: 0 4px 0 7px;
    }
    .disabled{
      color: $button-disabled-text-color;
      margin: 0 4px 0 7px;
    }
  }
  .value.margin-left-0px {
    .operational-staus-icon {
      .active{
        margin-left: 0;
      }
      .inactive{
        margin-left: 0;
      }
      .disabled{
        margin-left: 0;
      }
    }
  }
  @media screen and (max-width: $templateMediaSize) {
    .dashboard>.pie-container {
      flex-direction: column;
    }
    .dashboard>.pie-container>.content-box {
      width: 100%;
    }
  }


  .container-row-ha-status {
    .os-green-circle,
    .os-yellow-circle,
    .os-gray-circle
    {
      width: 0;
      padding-left: 10px;
      position: relative;
      text-align: left;
      display: inline-block;
      &::before {
        content: '';
        width: 8px;
        height: 8px;
        position: absolute;
        top: -8px;
        left: -1px;
        border-radius: 4px;
      }
    }
    .os-green-circle {
      &::before {
        background: $green17;
      }
    }
    .os-yellow-circle {
      &::before {
        background: #F4C351;
      }
    }
    .os-gray-circle {
      &::before {
        background: $button-disabled-text-color;
      }
    }
  }
}
@import 'scss/colors.scss';
@import 'scss/mixins.scss';
@import 'scss/variables.scss';

.ec-root-page {

.chart-container {
  position: relative;
  padding: 1em;
  min-height: 280px;

  .chart-section{
    padding: 0em 0em 0em 0em;
    .chart-sub-header {
      height: 20px;
      color: var(--semantic-color-content-base-primary); 
      font-size: 13px;
      font-weight: 500;
      letter-spacing: 0;
      padding: 0em 0em 3em 1.5em
    }
  }
  .content{
    padding: 0em;

    .content-box-flex{
      width: 100%;
      height: 30px;
      font: 14px Arial;
      display: flex;
      display: -webkit-flex;
      flex-direction: row;
      -webkit-flex-direction: row;
      border: none;
    }
    .content-box-flex > div{
      width: 30%;
      flex: 1 1 auto;
      -webkit-flex: 1 1 auto;
      transition: width 0.7s ease-out;	
      -webkit-transition: width 0.7s ease-out;	
    }
    .middle-flex{
      margin-left: 10em;
    }
    .last-flex{
      margin-right: 0.25em;
      float: right;
      width: 150px;
    }
    .container {
      width: '60%';
      padding: 0em 3em 0em 3em;
    }
  }
}

.section-holder {
  position: relative;
  min-height: 280px;

  .sub-header{
    height: 20px;
    // width: 360px;
    color: var(--semantic-color-content-base-primary); 
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0;
    line-height: 20px;
    padding: 1em 0em 2em 0.25em;
    .interval {
      position: absolute;
      margin-left: 25%;
      .middle-flex{
        width: 30%;
        float: right;
      }
    }
    .trendDropDown{
      .drop-down-selected-value {
        border: none;
        border-radius: 0;
        background: none;
      }
    }
  }

  .sub-header-hr{
    box-sizing: border-box;
    height: 1px;
    border: 1px solid var(--semantic-color-border-base-primary);
    margin-bottom: 1.75em;
  }
  .section{
    padding: 1em 0em 0em 0em;

    .section-flip{
      float: left;
      color: var(--semantic-color-content-interactive-primary-default);
    }
    .section-header{
      height: 24px;
      width: 360px;
      color: var(--semantic-color-content-base-primary);
      font-size: 18px;
      font-weight: 500;
      letter-spacing: 0.32px;
      // line-height: 24px;
      padding: 0em 0em 0em 0.25em;
    }
    .section-table{
      padding: 0em 1em 0em 1.25em;
      .app-table-container{
        margin-top: 1em;
      }
    }
  }

}

.section-holder {
  position: relative;
  min-height: 280px;

  .sub-header{
    height: 20px;
    // width: 360px;
    color: var(--semantic-color-content-base-primary); 
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0;
    line-height: 20px;
    padding: 1em 0em 2em 0.25em;
  }

  .sub-header-hr{
    box-sizing: border-box;
    height: 1px;
    border: 1px solid var(--semantic-color-border-base-primary);
    margin-bottom: 1.75em;
  }
  .section{
    padding: 1em 0em 0em 0em;

    .section-flip{
      float: left;
      color: var(--semantic-color-content-interactive-primary-default);
    }
    .section-header{
      height: 24px;
      width: 360px;
      color: var(--semantic-color-content-base-primary);
      font-size: 18px;
      font-weight: 500;
      letter-spacing: 0.32px;
      // line-height: 24px;
      padding: 0em 0em 0em 0.25em;
    }
    .section-table{
      padding: 0em 1em 0em 1.25em;
      .app-table-container{
        margin-top: 1em;
      }
    }
  }

}
.chart-container {
  position: relative;
  padding: 1em;
  min-height: 280px;

  .chart-section{
    padding: 0em 0em 0em 0em;
    .chart-sub-header {
      height: 20px;
      color: var(--semantic-color-content-base-primary); 
      font-size: 13px;
      font-weight: 500;
      letter-spacing: 0;
      padding: 0em 0em 3em 1.5em
    }
  }
  .content{
    padding: 0em;

    .content-box-flex{
      width: 100%;
      height: 30px;
      font: 14px Arial;
      display: flex;
      display: -webkit-flex;
      flex-direction: row;
      -webkit-flex-direction: row;
      border: none;
    }
    .content-box-flex > div{
      width: 30%;
      flex: 1 1 auto;
      -webkit-flex: 1 1 auto;
      transition: width 0.7s ease-out;	
      -webkit-transition: width 0.7s ease-out;	
    }
    .middle-flex{
      margin-left: 10em;
    }
    .last-flex{
      margin-right: 0.25em;
      float: right;
      width: 150px;
    }
    .container {
      width: '60%';
      padding: 0em 3em 0em 3em;
    }
  }
}


.global-ux-score {
  position: relative;
  min-height: 280px;

  .selected-locations-header {
    width: 100%;
    p{
      float: left;
    }
    .fa-times {
      float: right;
      color: var(--semantic-color-content-interactive-primary-default);
      cursor: pointer;
    }
  }
}

.cloudconnector-info {
  margin-bottom: 50px;
  .title-back{
    color: var(--semantic-color-content-interactive-primary-default);
    cursor: pointer;
  }
  .title-nav{
   display: initial;
   padding-left: 1em;
   color: #949494;
   font-size: 16px;
   font-weight: 500;
   letter-spacing: 0;
   line-height: 24px;
  }
  .title-nav-right{
    display: initial;
    padding: 0em 1em 0em 1em;
    color: var(--semantic-color-content-base-primary); 
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 24px;
   }
   .filter-icon {
    height: 15px;
    padding-right: 5px;
  }
  .config-nav-tab {
    padding: 1em 2em 0em 2.5em;
  }
  .status{
    display: initial;
    padding-left: 25%;
    color:  var(--semantic-color-content-base-primary);
    font-size: 13px;
    letter-spacing: 0.2px;
    line-height: 24px;
    text-align: center;
  }
  .refresh{
    color: var(--semantic-color-content-interactive-primary-default);
    float: right;
  }

  .overview-tabs{
    padding: 0em 3.5em 1em 2.5em;

    .content-box-flex{
      width: 100%;
      height: 200px;
      border: none;
      font: 14px Arial;
      display: flex;
      display: -webkit-flex;
      flex-direction: row;
      -webkit-flex-direction: row;
    }
    .content-box-flex > div{
      width: 30px;
      flex: 1 1 auto;
      -webkit-flex: 1 1 auto;
      transition: width 0.7s ease-out;	
      -webkit-transition: width 0.7s ease-out;	
    }
    .container {
      width: '60%';
      padding: 0em 3em 0em 3em;
    }
    .key{
      float: left;
      min-height: 2em;
      min-width: 20em;
      color: #939393;
      font-size: 13px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 16px;
    }
    .value{
      float: right;
      min-height: 2em;
      min-width: 10em;
      color: var(--semantic-color-content-base-primary); 
      font-size: 13px;
      letter-spacing: 0.2px;
      line-height: 15px;
    }
  }

  .overview-container{
    padding: 0em 3.5em 1em 1.75em;
  }
  
}
.dashboard {
  // padding: 19px 25px;

  .dashboard-row{
    margin: 30px 0;
  }

  .pie-container {
    @include DisplayFlex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;

    .ux-donut {
      align-self: stretch;
      border: 1px solid var(--semantic-color-border-base-primary);
      background: var(--semantic-color-surface-base-primary);
      border-radius: 5px;
      padding-top: 12px;
      margin-top:  24px;
      min-height: 140px;
      width: 49.5%;
      height: 28em;
      min-width:  550px;
      vertical-align: middle;
      .ux-donut-title {
        margin-bottom: 18px;
      }

      h2 {
        margin-left: 16px;
      }

      img {
        vertical-align: bottom;
      }

      .section {
        vertical-align: top;
      }
    }
  }

  .table-content {
    padding-bottom: 90px;
    border-radius: 5px;
    // background-color: var(--semantic-color-surface-base-primary);
    // box-shadow: 0 8px 18px 0 rgba(176,186,197,0.4);
  }
}
.entity-dropdown-container {
  .filter-icon {
    height: 15px;
    padding-right: 5px;
  }
  .filter-icon-disabled {
    height: 15px;
    padding-right: 5px;
    color: $grey21;
  }
  .filter-icon-branch {
    color: #A940FF;
    height: 15px;
    padding-right: 5px;
  }
  label {
    color:  var(--semantic-color-content-base-primary);
    span {
      margin-left: 0;
    }
  }
}
.trafficFlow {
  display: none;
}
.hideVisibility {  visibility: hidden; }

.map-content {
  border-radius: 5px;
  background: var(--semantic-color-surface-base-primary);
}

.traffic-monitoring-table {
  .delete-icon,
  .pencil-icon,
  .view-icon,
  .download-icon {
    margin: 0 10px;
  }
  .ReactTable.app-table {
    max-height: 300px;
  }
  .column-layout-config-container {
    border-left: 0;
  }
}

@media screen and (max-width: $templateMediaSize) {
  .dashboard>.pie-container {
    flex-direction: column;
  }
  .dashboard>.pie-container>.content-box {
    width: 100%;
  }
}
}
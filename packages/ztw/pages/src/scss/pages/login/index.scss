.login-page-container {
  background: var(--semantic-color-background-primary);
  width: 100%;
  height: 100%;
}
.login-page-hidden {
  display: none !important;
}
.login-page {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  min-height: 667px;
  max-height: 870px;
  min-width: 1060px;
  max-width: 1280px;
  overflow: hidden;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);

  @media (max-height: 870px) {
    box-shadow: 0 0 0 0;
    position: static;
    transform: none;
    margin: 0 auto;
  }

  .login-page-header-container {
    height: 120px;
    background: var(--semantic-color-background-primary);
    font-size: 0;
    border-bottom: 1px solid var(--semantic-color-border-base-primary);

    .login-page-header-logo-container {
      display: inline-block;
      vertical-align: middle;
      width: 344px;
      height: 100%;
      padding: 30px 36px;

      @media (max-width: 1095px) {
        padding: 30px 24px;
        width: 320px;
      }

      .login-page-header-logo {
        height: 60px;
        width: auto;
      }
    }
      
    .not-supported-container{
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 344px);
        height: 100%;
        padding: 22px 36px 22px 0;

        @media (max-width: 1095px) {
          padding-right: 16px;
          width: calc(100% - 320px);
        }

        .cookie-disabled-panel-content, .browser-not-supported-content {
          background: #fdeeed;
          border: 1px solid #d81903;
          color: var(--semantic-color-content-base-primary);
          border-radius: 5px;
          text-align: center;
          padding: 16px;
          height: 100%;
          width: 100%;

          &.browser-not-supported-content {
            padding: 8px 0;
            .no-script-header, .no-script-content {
              margin-bottom: 4px;
              padding: 0 8px;
            }
          }

          .no-script-header {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 8px;
          }

          .no-script-content {
            font-size: 12px;
          }
        }
    }
          
    .login-content {
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 344px);
      height: 100%;
      padding: 16px 36px 16px 0;
      position: relative;

      @media (max-width: 1095px) {
        padding-right: 16px;
        width: calc(100% - 320px);
      }

      .input-container {
          display: inline-block;
          margin-right: 12px;
          vertical-align: bottom;
          height: auto;
          width: auto;
          padding: 0;
          input {
            height: 100%;
          }
          .login-text-label {
            color: var(--semantic-color-content-base-primary);
            font-size: 13px;
            font-weight: 600;
            text-transform: uppercase;
            display: block;
            margin-bottom: 8px;
          }
          
        &.login-remember-me {
          color: var(--semantic-color-content-base-primary);
          .login-text-label {
            cursor: pointer;
            text-transform: none;
            display: inline-block;
            vertical-align: top;
            margin: 0;
            padding-left: 7px;
          }
          .login-remember-username {
            font-size: 16px;
            margin: 0;
            cursor: pointer;
          }
        }

        &.login-signin-button {
          margin-right: 0;
        }
      }

      .login-input-container {
        float: right;
        padding-right: 60px;

        @media (max-width: 1160px) {
          padding-right: 0;
        }
      }

      .login-remember-container {
        margin-top: 12px;
        .input-container {
          width: 280px;
          vertical-align: top;				
        }
      }
    }
    .login-error-container {
      background: #d81903;
      top: 120px;
      padding: 8px 16px;
      position: absolute;
      width: 572px;
      border-radius: 0 0 5px 5px;
      color: var(--semantic-color-surface-base-primary);
      font-size: 14px;
      z-index: 1;
      height: 36px;
    }
  }

  .login-banner-loader {
    height: calc(100% - 120px);
    width: 100%;
    text-align: center;
  }

  .login-page-banner-content {
    width: 100%;
    height: calc(100% - 120px);
    position: relative;
    
    .login-banner-container {
      width: 100%;
      height: calc(100% - 40px);

      .login-banner {
          display: inline-block;
          vertical-align: middle;
          cursor: pointer;
          text-align: center;
          width: 100%;
          height: calc(100% - 150px);
          overflow: hidden;
          position: relative;

          &:hover {
            opacity: 0.95;
          }
          
          &.login-banner-small {
            min-width: 0;
            max-width: none;
            width: 25%;
            height: 100%;
            border-left: 1px solid var(--semantic-color-border-base-primary);
            border-top: 1px solid var(--semantic-color-border-base-primary);
            
            &:last-child {
                border-right: 0;
            }

            &:first-child {
                border-left: 0px;
            }
          }

          img {
            position: absolute;
            bottom: 0;
            left: 0;
          }
        }
        .login-banner-small-container {
            font-size: 0;
            height: 150px;
            position: relative;
            width: 100%;
        }
    }

    .login-page-footer-container {
      background: var(--semantic-color-content-immutable-black);
      color: var(--semantic-color-content-immutable-white);
      height: 40px;
      width: 100%;
      padding: 12px 36px;
      font-size: 14px;
      text-align: left;

      @media (max-width: 1095px) {
        padding: 12px 24px;
      }
    }
  }
}

.ec-login {
  .input-container {
    display: inline-block;
    margin-right: 12px;
    vertical-align: bottom;
    .login-text-label {
      font-size: 13px;
      font-weight:500;
      text-transform: uppercase;
      display: block;
      margin-bottom: 8px;
    }
    .login-text-input {
      position: relative;
    }
  
    .obsfuscation-icon {
      position: absolute;
      top: 9px;
      left: 245px;
      width: 15px;
      height: 15px;
    }
  }
  button {
    &.login-btn {
      &.submit {
        padding:  9px 15px;
      }
    }
  }
  label {
    &.rember-my-login {
      color: var(--semantic-color-content-base-primary);
      font-size: 13px;
      margin-top: 10px;
      display: inline-block;
      cursor: pointer;
    }
    span {
      margin-left: 7px;
    }
  }
  
  .language-selector {
      width: auto;
      vertical-align: top;
      display: inline-block;
      min-width: 110px;
      font-size: 12px;
      .login-language-container {
        display: flex;
        position: relative;
        color: var(--semantic-color-content-base-primary);
        &:hover {
          color: var(--semantic-color-content-interactive-primary-default);
        }
        .icon-container {
          display: inline-block;
          top: 7px;
          position: absolute;
          left: 0;
        }
        .login-dropdown {
          width: 100%;
          //drop overridden styles
          .drop-down-container {
            font-size: 13px;
            vertical-align: top;
            white-space: nowrap;
            color: var(--semantic-color-content-base-primary);
            &:hover {
              color: var(--semantic-color-content-interactive-primary-default);
            }
            button {
              font-size: 13px;
              background: none;
              border: none;
              text-align: left;
              display: inline-block;
              padding-left: 20px;
              
              span {
                padding-right: 7px;
              }
            }
            .drop-down-list {
              button {
                padding: 8px 16px;
                &:hover {
                  background-color: #e4faff;
                }
              }
            }
          }
        }
      }
      .login-language {
        margin-top: 10px;
      }
  }
  
  .login-remember-me {
    width: 280px;
    display: inline-block;
    margin-right: 10px;
  }
  
  .login-text-input {
      background: var(--semantic-color-background-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 5px;
      display: inline-block;
      padding: 8px 12px;
      width: 280px;
      height: 36px;
      .login-text-input-text {
        border: none;
        background: var(--semantic-color-background-primary);
        color: var(--semantic-color-content-base-primary);
        font-size: 14px;
        padding: 0;
        width: 100%;
      }
      span {
        color: var(--semantic-color-content-base-primary);
      }
      input:required,
      input:invalid {
        box-shadow: none;
      }
      input:focus {
        outline: none;
      }
  }
  .error-summary {
    position: relative;
    top: 12px;
    z-index: 1;
    font-size: 13px;
  }
  
  .change-password {
    background: rgba(38, 38, 38, 0.7);
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    
  }
  
  .dialog-mask, .nested-dialog-mask {
    background: rgba(38, 38, 38, 0.7);
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
  }
  
  .dialog, .-js-nested-dialog, .dialog-box {
    border-radius: 5px;
    margin: 10px auto;
    opacity: 0.5;
    position: relative;
    width: 700px;
    z-index: 1002;
    opacity: 1;
    background-color: rgb(255, 255, 255);
    margin: 339px auto;
  }
  
  .dialog-header {
    padding: 16px;
    background: var(--semantic-color-content-interactive-primary-default);
    border-radius: 5px 5px 0 0;
  }
  .dialog-header-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    font-size: 16px;
    color: var(--semantic-color-surface-base-primary);
    position: relative;
    vertical-align: top;
    width: 100%;
  }
  .dialog-header-close {
    cursor: pointer;
    color: var(--semantic-color-surface-base-primary);
    position: absolute;
    right: 0px;
  }
  
  .dialog-body, .confirm-dialog-body {
    background: var(--semantic-color-background-pale);
    border: 1px solid var(--semantic-color-border-base-primary);
    overflow-x: hidden;
    overflow-y: scroll;
    padding: 16px 24px;
    width: 100%;
  }
  
  .form-section:last-child, .form-tab-section:last-child, .reports-tab-section:last-child {
    padding-bottom: 8px;
  }
  .dialog .form-input-rows, .-js-nested-dialog .form-input-rows, .dialog-box .form-input-rows, .dialog .form-tab-input-rows, .-js-nested-dialog .form-tab-input-rows, .dialog-box .form-tab-input-rows {
    overflow: visible;
  }
  .password-change-container .form-input-row, .password-change-container .filters-list-item, .password-change-container .filters-add-filter-container {
    width: 100% !important;
    position: relative;
  }
  .password-change-container .password-expired-header {
    border: 1px solid #f7cb8a;
    border-radius: 5px;
    background-color: #fbedd9;
  }
  .password-change-container .password-expired-header .form-input-label, .password-change-container .password-expired-header .filter-item-header, .password-change-container .password-expired-header .form-input-label-text, .password-change-container .password-expired-header .free-form-listbox-label, .password-change-container .password-expired-header .multi-select-listbox-label, .password-change-container .password-expired-header .insights-filter-label, .password-change-container .password-expired-header .data-loss-msg {
    color: #f19409;
  }
  .form-input-label span, .filter-item-header span, .form-input-label-text span, .free-form-listbox-label span, .multi-select-listbox-label span, .insights-filter-label span, .data-loss-msg span {
    cursor: help;
    display: inline-block;
    margin-right: 10px;
    width: auto;
  }
  
  .form-input-label, .filter-item-header, .form-input-label-text, .free-form-listbox-label, .multi-select-listbox-label, .insights-filter-label, .data-loss-msg {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--semantic-color-content-base-primary);
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 8px;
    width: 100%;
  }
  
  .password-change-container .show-password {
    position: absolute;
    right: 16px;
    bottom: 20px;
    cursor: pointer;
    color: #ccc;
  }
  
  .dialog-mask.password-expiry-view div.dialog-body.-js-content-body, .password-expiry-view.nested-dialog-mask div.dialog-body.-js-content-body, .dialog-mask.password-expiry-view div.-js-content-body.confirm-dialog-body, .password-expiry-view.nested-dialog-mask div.-js-content-body.confirm-dialog-body {
    overflow-y: auto;
  }
  
  .dialog-footer {
    background: var(--semantic-color-background-primary);
    border-top: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 0 0 5px 5px;
    padding: 16px;
    width: 100%;
  }
  
  .form-field-label.invalid,.form-field-label-wrapper.invalid, .form-input-label.invalid, .invalid.filter-item-header, .invalid.form-input-label-text, .invalid.free-form-listbox-label, .invalid.multi-select-listbox-label, .invalid.insights-filter-label, .invalid.data-loss-msg {
    color: var(--semantic-color-content-status-danger-primary);
  }
  .list-builder-input-textarea.invalid, .form-input-text.list-builder-input.invalid, .list-builder-input.invalid.form-input-text-check-box, .list-builder-input.invalid.number-input-text, .list-builder-input.invalid.form-comments-input-text, .list-builder-input.invalid.multiple-element-search-input, .list-builder-input.invalid.insights-input-text, .list-builder-input.invalid.reports-input-text {
    border: 1px solid #d81903;
    box-shadow: none;
  }
}


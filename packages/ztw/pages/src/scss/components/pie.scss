@import 'scss/colors.scss';
@import 'scss/mixins.scss';

.ec-root-page {
  .insight-pie {
    @include DisplayFlex;
    margin-top: 18px;

    .sub-section {
      flex-grow: 1;
      margin: 0 11px 0 16px;

      .score-bar-big {
        margin-top: 7px;
      }
    }

    .insider {
      height: 40px;
      color: #818181;
      font-size: 24px;
      font-weight: 500;
      letter-spacing: .5px;
      line-height: 45px;
      text-align: center;
    }

  }
  .recharts-wrapper {
    margin-top: 13px;
  }

h2 .box-label {
  text-transform: none;
  color: var(--semantic-color-content-base-primary);
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
  line-height: 19px;
  padding: 30px;
}

.pie-tooltip {
  display: flex;
  align-items: center;
  .pie-tooltip-square {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
}

.line-chart-field-tooltip-container {
  max-width: 550px;
  min-width: 260px;
  overflow: hidden;
  padding: 12px 16px;
  word-wrap: break-word;
  text-transform: none;
  height: 240px;
  width:320px;
  left: calc(50% - 160px);
  top: calc(50% - 110px);
  position: absolute;
}

.ux-donut {
  width: 100%;
  .section {
    @include DisplayFlex;
    margin-top: 18px;

    .sub-section {
      flex-grow: 1;
      margin: 0 11px 0 16px;

      .score-bar-big {
        margin-top: 7px;
      }
    }
  }
  .recharts-wrapper {
    margin-top: 13px;
  }
  .pie-inner-text {
    color: var(--semantic-color-content-base-primary);
    font-size: 13px;
    line-height: 20px;
  }
}
h2 .box-label {
  text-transform: none;
  color: var(--semantic-color-content-base-primary);
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
  line-height: 19px;
  padding: 30px;
}

.totalTxn {
    height: 40px;
    color: var(--semantic-color-content-base-primary); 
    font-size: 24px;
    font-weight: 500;
    letter-spacing: .5px;
    line-height: 45px;
    text-align: center;
}
}

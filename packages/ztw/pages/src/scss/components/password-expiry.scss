@import 'scss/mixins.scss';
@import 'scss/colors.scss';

.ec-root-page {
.modal-content {

  min-height: 18vh;
  max-height: 70vh;
  overflow: auto;
  &.no-max-height{
    max-height: none;
  }

  .ecui-waiting-overlay  {
    align-self: center;
  }

  .container {
    padding: 20px 15px;



    &-fields {
      @include DisplayFlex;
      background: $white;
      border-radius: 2px;
      background-color: $white;
      box-shadow: 0 0 11px 0 $grey14;

      >div {
        flex: 0.5;
      }
    }
  }
  .modal-footer.change-password-modal-footer {
    position: relative;
    bottom: 0;
    width: 100%;
  }
  .change-password-warning {
    color: #f69f00;
    margin: 15px;
    line-height: 18px;
    font-size: 13px;
  }
}

.password-expire-confirm-form {
  max-width: 480px;
  &.add-custom-app-form {
    .form-sections-container {
      .form-section {
        margin-top: 20px;
      }
    }
  }
  .modal-text {
    width: 100%;
    padding-bottom: 10px;
    color: var(--semantic-color-content-base-primary);
  }
}

.password-banner {
  @include DisplayFlex;
      cursor: pointer;
      color: $red5;
      padding: 4px 16px;
      position: absolute;
      top: 30%;
      width: auto;
      border-radius: 3px;
      left: 30%;

  .fa-exclamation-triangle {
      color: $grey1;
      font-size: 14px;
      margin-right: 5px;
  }
  .fa-times-circle {
      color: $red5;
      font-size: 14px;
      margin-right: 5px;
  }

  .text-label {
      font-size: 12px;
      cursor: pointer;
  }

  .toggle-label-button {
      border: none;
      background: $transparent;
      color: var(--semantic-color-content-interactive-primary-default);
      font-size: 10px;
      font-weight: bold;
      margin-left: 5px;
  }
}
}


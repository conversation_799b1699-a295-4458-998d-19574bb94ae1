@import "scss/colors.scss";

.ec-root-page {
.entity-dropdown {
  margin-left: 0;
  
  .react-select__control {
    background-color:	var(--semantic-color-background-primary);
    color:  var(--semantic-color-content-base-primary);
    border: 1px solid var(--semantic-color-border-base-primary);
    min-width: 150px;
  }

  &.error {
    .react-select__control {
      border-color: var(--semantic-color-content-status-danger-primary);
      color: var(--semantic-color-content-interactive-primary-default);
    }

    .drill-down-placeholder, .react-select__indicator {
      color: var(--semantic-color-content-status-danger-primary);
    }
  }

  .react-select__value-container, .react-select__indicator {
    padding: 0;
  }
  .react-select__menu {
    background-color: var(--semantic-color-background-primary);
    border: 1px solid var(--semantic-color-border-base-primary);
    .react-select__option {
      background-color: var(--semantic-color-background-primary) !important;
      color: var(--semantic-color-content-base-primary);
    }
    .react-select__option--is-focused {
        background-color: var(--semantic-color-content-interactive-primary-focus) !important;
        color: var(--semantic-color-content-immutable-white);
        line-break: anywhere;
    }
    .react-select__option--is-selected {
      background-color: var(--semantic-color-content-interactive-primary-active) !important;
      color: var(--semantic-color-content-immutable-white);
      // color: var(--semantic-color-content-inverted-base-primary);
      line-break: anywhere;
    }
  }
  
  .react-select__menu-list {
    max-height: 120px;
    background-color: var(--semantic-color-content-interactive-primary-active);
  }

  .error-container {
    color: var(--semantic-color-content-status-danger-primary);
    padding-top: 2px;
  }
}




  .error {
    .react-select__control {
      border-color: var(--semantic-color-content-status-danger-primary);
    }

    .drill-down-placeholder, .react-select__indicator {
      color: var(--semantic-color-content-status-danger-primary);
    }
  }

  .react-select__value-container, .react-select__indicator {
    padding: 0;
  }
  .react-select__menu {
    background-color: var(--semantic-color-background-primary) !important;
    border: 1px solid var(--semantic-color-border-base-primary);
    .react-select__option {
      background-color: var(--semantic-color-background-primary) !important;
      color: var(--semantic-color-content-base-primary);
    }
    .react-select__option--is-focused {
        background-color: var(--semantic-color-content-interactive-primary-focus) !important;
        color: var(--semantic-color-content-immutable-white);
        line-break: anywhere;
    }
    .react-select__option--is-selected {
      background-color: var(--semantic-color-content-interactive-primary-active) !important;
      color: var(--semantic-color-content-immutable-white);
      // color: var(--semantic-color-content-inverted-base-primary);
      line-break: anywhere;
    }
  }
  
  .react-select__menu-list {
    max-height: 120px;
    background-color: var(--semantic-color-background-primary) !important;
  }

  .error-container {
    color: var(--semantic-color-content-status-danger-primary);
    padding-top: 2px;
  }
}
@import 'scss/mixins.scss';
@import 'scss/colors.scss';

.ec-root-page {
.footer {
  @include DisplayFlex;
	position: fixed;
	bottom: 0;
  padding: 10px  24px;
  justify-content: center;
  align-items: center;
  background-color: var(--semantic-color-surface-base-tertiary);
  color: var(--semantic-color-content-base-secondary);
  font-size: 11px;
  z-index: 10;
  width: 100%;
  margin-left: 8em;
  
  .left-section {
    @include DisplayFlex;

    .copyright, .version {
      padding-right: 5px;
      margin-right: 5px;
      border-right: 1px solid var(--semantic-color-border-base-primary);
    }

    .copyright {
      .copyright-symbol {
        margin: 0 5px;
      }

      .year {
        margin-right: 5px;
      }
    }

    .patents {
      .patents-tooltip-text {
        width:  400px;
      }

      a {
        color: var(--semantic-color-content-interactive-primary-default);
        text-decoration: none;
      }
    }
  }
}
}
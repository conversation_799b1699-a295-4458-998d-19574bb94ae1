@import 'scss/colors.scss';
@import 'scss/mixins.scss';
.ec-root-page {
  table {
    border-collapse: collapse;
    border-spacing: 0; }

  button,
  input,
  select,
  textarea {
    font-size: 100%;
    margin: 0;
    vertical-align: baseline; }

  button:focus,
  input:focus,
  select:focus,
  textarea:focus {
    outline: none; }

  input[disabled] {
    background: inherit; }

  input[type="checkbox"] {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0; }

  textarea {
    overflow: auto;
    vertical-align: top; }

  [hidden] {
    display: none; }

  :-moz-placeholder {
    font-weight: 300; }

  ::-webkit-input-placeholder {
    font-weight: 300; }

  iframe {
    border: none; }

  ::-ms-clear {
    display: none; }

  ::-ms-reveal {
    display: none; }

  /*
      Common Styles:

      -   Grid layout
      -   Generic classes
      -   UI elements (e.g., buttons, inputs)
  */
  /* Column Layout */
  .column {
    display: inline-block;
    vertical-align: top; }
    .column::-moz-selection {
      background: transparent; }
    .column::selection {
      background: transparent; }

  .column.column-last {
    margin-right: 0; }

  .column.column-padded {
    padding-right: 40px; }

  .column-2 {
    margin-right: 1%;
    width: 49%; }

  .column-2 + .column-2 {
    margin-left: 1%; }

  /* Generic Classes */
  .hidden {
    display: none; }

  .invisible {
    visibility: hidden; }

  .max-width {
    width: 100%; }

  .check-box {
    width: 100%;
    display: block;
    font-size: 13px;
    padding: 0 6px; }
    .check-box.disabled {
      color: var(--semantic-color-content-interactive-primary-disabled) !important; 
    }
      .check-box.disabled .check-box-button {
        border: 1px solid var(--semantic-color-border-interactive-primary-disabled)!important;
        cursor: default; 
      }
      .check-box.disabled .check-box-label {
        color: var(--semantic-color-content-interactive-primary-disabled) !important; 
      }

  .check-box-button {
    background: var(--semantic-color-background-primary);
    border: 1px solid var(--semantic-color-content-base-secondary);
    cursor: pointer;
    display: inline-block;
    height: 18px;
    padding: 0px 2px;
    vertical-align: middle;
    width: 18px; }
    .check-box-button i {
      display: none; }
    .check-box-button.on i {
      display: inline-block; }

  .check-box-label {
    color: var(--semantic-color-content-base-primary);
    margin-left: 8px;
    display: inline-block;
    // vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
    .check-box-label.help-tooltip {
      cursor: help; }


  .table-wrapper {
    height: 100%; }

  .table-layout, .url-categories-container {
    height: 100%;
    width: 100%;
    background: var(--semantic-color-surface-table-row-default);
    padding: 16px;
    border: 1px solid var(--semantic-color-border-base-primary);
  }

  .table-layout-header {
    // margin-top: 30px;
    width: 100%; 
    position: relative; }

  .table-layout-body {
    width: 100%;
    height: calc(100% - 56px);
    position: relative !important; }
    .table-layout-body.no-header {
      height: 100%; }

  .table {
    background: var(--semantic-color-surface-base-primary);
    font-size: 13px;
    height: 100%;
    width: 100%;
    border-radius: 5px; }

  .table-header {
    color: var(--semantic-color-content-base-primary);
  }

  .table-header-viewport {
    width: 100%;
    border: 1px solid var(--semantic-color-border-base-primary);
    background: #f3f3f3;
    border-radius: 5px 5px 0 0;
    position: relative; }

  .table-header-sortable-container, .table-body-sortable-container {
    width: calc(100% - 80px);
    display: inline-block;
    vertical-align: top; }

  .table-column-selector {
    position: absolute;
    font-size: 21px;
    top: 0px;
    right: 0px;
    color: var(--semantic-color-content-accent-blue-secondary);
    cursor: pointer;
    width: 40px;
    height: 33px;
    text-align: center;
    background: var(--semantic-color-surface-table-header-default);
    border-top-right-radius: 5px;
    border: 1px solid $grey12;
    border-left: 0;  
    z-index: 1; }
    .table-column-selector:hover {
      color: $anchor-color; }
    .table-column-selector .table-column-selector-button {
      width: 100%;
      height: 100%; }
    .table-column-selector .table-column-menu-container {
      position: absolute;
      right: 0px;
      background: var(--semantic-color-surface-base-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 5px 0 5px 5px;
      width: 180px; }
      .table-column-selector .table-column-menu-container .table-column-menu-header {
        font-size: 11px;
        padding: 8px;
        cursor: default;
        border-bottom: 1px solid var(--semantic-color-border-base-primary); }
        .table-column-selector .table-column-menu-container .table-column-menu-header .table-column-menu-header-text {
          text-transform: uppercase;
          color: var(--semantic-color-content-base-primary);
          width: calc(100% - 13px);
          display: inline-block;
          vertical-align: middle;
          text-align: left; }
        .table-column-selector .table-column-menu-container .table-column-menu-header .svg-inline--fa {
          color: var(--semantic-color-content-interactive-primary-default);
          vertical-align: middle;
          cursor: pointer; 
        }
      .table-column-selector .table-column-menu-container .form-input-row, .table-column-selector .table-column-menu-container .filters-list-item, .table-column-selector .table-column-menu-container .filters-add-filter-container {
        padding: 8px 4px;
        left: 0;
        position: relative;
        cursor: move; }
        .table-column-selector .table-column-menu-container .form-input-row .form-input, .table-column-selector .table-column-menu-container .filters-list-item .form-input, .table-column-selector .table-column-menu-container .filters-add-filter-container .form-input, .table-column-selector .table-column-menu-container .form-input-row .filter-item-body, .table-column-selector .table-column-menu-container .filters-list-item .filter-item-body, .table-column-selector .table-column-menu-container .filters-add-filter-container .filter-item-body, .table-column-selector .table-column-menu-container .form-input-row .form-comments-input, .table-column-selector .table-column-menu-container .filters-list-item .form-comments-input, .table-column-selector .table-column-menu-container .filters-add-filter-container .form-comments-input, .table-column-selector .table-column-menu-container .form-input-row .form-comments-input-with-label, .table-column-selector .table-column-menu-container .filters-list-item .form-comments-input-with-label, .table-column-selector .table-column-menu-container .filters-add-filter-container .form-comments-input-with-label, .table-column-selector .table-column-menu-container .form-input-row .slider-container, .table-column-selector .table-column-menu-container .filters-list-item .slider-container, .table-column-selector .table-column-menu-container .filters-add-filter-container .slider-container {
          width: 100%; }
          .table-column-selector .table-column-menu-container .form-input-row .form-input .check-box, .table-column-selector .table-column-menu-container .filters-list-item .form-input .check-box, .table-column-selector .table-column-menu-container .filters-add-filter-container .form-input .check-box, .table-column-selector .table-column-menu-container .form-input-row .filter-item-body .check-box, .table-column-selector .table-column-menu-container .filters-list-item .filter-item-body .check-box, .table-column-selector .table-column-menu-container .filters-add-filter-container .filter-item-body .check-box, .table-column-selector .table-column-menu-container .form-input-row .form-comments-input .check-box, .table-column-selector .table-column-menu-container .filters-list-item .form-comments-input .check-box, .table-column-selector .table-column-menu-container .filters-add-filter-container .form-comments-input .check-box, .table-column-selector .table-column-menu-container .form-input-row .form-comments-input-with-label .check-box, .table-column-selector .table-column-menu-container .filters-list-item .form-comments-input-with-label .check-box, .table-column-selector .table-column-menu-container .filters-add-filter-container .form-comments-input-with-label .check-box, .table-column-selector .table-column-menu-container .form-input-row .slider-container .check-box, .table-column-selector .table-column-menu-container .filters-list-item .slider-container .check-box, .table-column-selector .table-column-menu-container .filters-add-filter-container .slider-container .check-box {
            color: var(--semantic-color-content-interactive-primary-default); }
            .table-column-selector .table-column-menu-container .form-input-row .form-input .check-box .check-box-button, .table-column-selector .table-column-menu-container .filters-list-item .form-input .check-box .check-box-button, .table-column-selector .table-column-menu-container .filters-add-filter-container .form-input .check-box .check-box-button, .table-column-selector .table-column-menu-container .form-input-row .filter-item-body .check-box .check-box-button, .table-column-selector .table-column-menu-container .filters-list-item .filter-item-body .check-box .check-box-button, .table-column-selector .table-column-menu-container .filters-add-filter-container .filter-item-body .check-box .check-box-button, .table-column-selector .table-column-menu-container .form-input-row .form-comments-input .check-box .check-box-button, .table-column-selector .table-column-menu-container .filters-list-item .form-comments-input .check-box .check-box-button, .table-column-selector .table-column-menu-container .filters-add-filter-container .form-comments-input .check-box .check-box-button, .table-column-selector .table-column-menu-container .form-input-row .form-comments-input-with-label .check-box .check-box-button, .table-column-selector .table-column-menu-container .filters-list-item .form-comments-input-with-label .check-box .check-box-button, .table-column-selector .table-column-menu-container .filters-add-filter-container .form-comments-input-with-label .check-box .check-box-button, .table-column-selector .table-column-menu-container .form-input-row .slider-container .check-box .check-box-button, .table-column-selector .table-column-menu-container .filters-list-item .slider-container .check-box .check-box-button, .table-column-selector .table-column-menu-container .filters-add-filter-container .slider-container .check-box .check-box-button {
              border: 1px solid var(--semantic-color-content-interactive-primary-default);
              border-radius: 3px; }
            .table-column-selector .table-column-menu-container .form-input-row .form-input .check-box .check-box-label, .table-column-selector .table-column-menu-container .filters-list-item .form-input .check-box .check-box-label, .table-column-selector .table-column-menu-container .filters-add-filter-container .form-input .check-box .check-box-label, .table-column-selector .table-column-menu-container .form-input-row .filter-item-body .check-box .check-box-label, .table-column-selector .table-column-menu-container .filters-list-item .filter-item-body .check-box .check-box-label, .table-column-selector .table-column-menu-container .filters-add-filter-container .filter-item-body .check-box .check-box-label, .table-column-selector .table-column-menu-container .form-input-row .form-comments-input .check-box .check-box-label, .table-column-selector .table-column-menu-container .filters-list-item .form-comments-input .check-box .check-box-label, .table-column-selector .table-column-menu-container .filters-add-filter-container .form-comments-input .check-box .check-box-label, .table-column-selector .table-column-menu-container .form-input-row .form-comments-input-with-label .check-box .check-box-label, .table-column-selector .table-column-menu-container .filters-list-item .form-comments-input-with-label .check-box .check-box-label, .table-column-selector .table-column-menu-container .filters-add-filter-container .form-comments-input-with-label .check-box .check-box-label, .table-column-selector .table-column-menu-container .form-input-row .slider-container .check-box .check-box-label, .table-column-selector .table-column-menu-container .filters-list-item .slider-container .check-box .check-box-label, .table-column-selector .table-column-menu-container .filters-add-filter-container .slider-container .check-box .check-box-label {
              max-width: calc(100% - 38px); }
        .table-column-selector .table-column-menu-container .form-input-row i.fa.fa-bars, .table-column-selector .table-column-menu-container .filters-list-item i.fa.fa-bars, .table-column-selector .table-column-menu-container .filters-add-filter-container i.fa.fa-bars {
          position: absolute;
          right: 8px;
          top: 12px;
          font-size: 10px;
          color: #747272; }
        .table-column-selector .table-column-menu-container .form-input-row:hover, .table-column-selector .table-column-menu-container .filters-list-item:hover, .table-column-selector .table-column-menu-container .filters-add-filter-container:hover {
          background: #e4faff; }
        .table-column-selector .table-column-menu-container .form-input-row:last-child:hover, .table-column-selector .table-column-menu-container .filters-list-item:last-child:hover, .table-column-selector .table-column-menu-container .filters-add-filter-container:last-child:hover {
          border-radius: 0 0 5px 5px; }
        .table-column-selector .table-column-menu-container .form-input-row:first-child:hover, .table-column-selector .table-column-menu-container .filters-list-item:first-child:hover, .table-column-selector .table-column-menu-container .filters-add-filter-container:first-child:hover {
          border-radius: 5px 0 0 0; }

  .table-body {
    position: relative;
    width: calc(100% - 20px);}

  .table-body-no-records{
    position: absolute;
    top: 100px;
    width: 100%;
  }

  .table-body-viewport {
    border: 1px solid var(--semantic-color-border-base-primary);
    border-top: 0px;
    border-radius: 0 0 5px 5px;
    height: calc(100% - 56px);
    width: 100%;
    overflow-y: scroll;
    overflow-x: hidden; }

  .table-row, .multi-data-column-table-row {
    background-color: #f3f3f3;
    min-height: 40px;
    position: absolute;
    white-space: nowrap;
    width: 100%; }
    .table-row.alt, .alt.multi-data-column-table-row {
      background-color: var(--semantic-color-surface-base-primary); }
      .table-row.alt.selected, .alt.selected.multi-data-column-table-row {
        background: #f2fafd; }
    .table-row.selected, .selected.multi-data-column-table-row {
      background: #e9f3f6; }
    .table-row.expanded, .expanded.multi-data-column-table-row {
      background: #f2fafd;
      height: 40px; }
    .table-row.sublocation, .sublocation.multi-data-column-table-row {
      background: #e9f3f6;
      box-shadow: none; }
      .table-row.sublocation i.fa, .sublocation.multi-data-column-table-row i.fa {
        margin-left: 20px;
        margin-right: 6px;
        padding: 0px; }
      .table-row.sublocation.alt, .sublocation.alt.multi-data-column-table-row {
        background: #f2fafd; }
      .table-row.sublocation.last-sublocation, .sublocation.last-sublocation.multi-data-column-table-row {
        box-shadow: inset 0px -8px 16px -8px rgba(0, 90, 119, .25); }
      .table-row.sublocation.first-sublocation, .sublocation.first-sublocation.multi-data-column-table-row {
        box-shadow: inset 0px 8px 16px -8px rgba(0, 90, 119, .25); }
      .table-row.sublocation.last-sublocation.first-sublocation, .sublocation.last-sublocation.first-sublocation.multi-data-column-table-row {
        box-shadow: inset 0px 1px 16px 0px rgba(0, 90, 119, .25); }
      .table-row.sublocation.alt, .sublocation.alt.multi-data-column-table-row {
        background: #f2fafd; }

  .multi-data-column-table-row {
    height: auto;
    position: relative; }

  .table-cell, .table-header-cell, .table-row-menu-container {
    // border-right: 1px solid var(--semantic-color-surface-base-primary);
    display: inline-block;
    padding: 12px 8px;
    vertical-align: top;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative; }
    .table-cell:last-child, .table-header-cell:last-child, .table-row-menu-container:last-child {
      border-right: 0; }
    .table-cell.hidden, .hidden.table-header-cell, .hidden.table-row-menu-container {
      display: none; }
    .table-cell i.inline-icon, .table-header-cell i.inline-icon, .table-row-menu-container i.inline-icon {
      display: inline-block;
      padding: 2px 4px;
      font-size: 11px;
      cursor: pointer; }
      .table-cell i.inline-icon.fa-chevron-down, .table-header-cell i.inline-icon.fa-chevron-down, .table-row-menu-container i.inline-icon.fa-chevron-down, .table-cell i.inline-icon.fa-chevron-right, .table-header-cell i.inline-icon.fa-chevron-right, .table-row-menu-container i.inline-icon.fa-chevron-right {
        color: var(--semantic-color-content-interactive-primary-default); }
        .table-cell i.inline-icon.fa-chevron-down:hover, .table-header-cell i.inline-icon.fa-chevron-down:hover, .table-row-menu-container i.inline-icon.fa-chevron-down:hover, .table-cell i.inline-icon.fa-chevron-right:hover, .table-header-cell i.inline-icon.fa-chevron-right:hover, .table-row-menu-container i.inline-icon.fa-chevron-right:hover {
          color: #00bce4; }
    .table-cell .select-row, .table-header-cell .select-row, .table-row-menu-container .select-row {
      width: 14px;
      height: 14px;
      margin-right: 8px;
      cursor: pointer; }
    .table-cell input[type="checkbox"][disabled], .table-header-cell input[type="checkbox"][disabled], .table-row-menu-container input[type="checkbox"][disabled] {
      cursor: default; }
    .table-cell .table-cell-icon, .table-header-cell .table-cell-icon, .table-row-menu-container .table-cell-icon {
      cursor: pointer; }
      .table-cell .table-cell-icon.warning, .table-header-cell .table-cell-icon.warning, .table-row-menu-container .table-cell-icon.warning {
        color: #cb9300; }
      .table-cell .table-cell-icon.deleted, .table-header-cell .table-cell-icon.deleted, .table-row-menu-container .table-cell-icon.deleted {
        color: #d61a00; }

  .table-header-cell {
    display: inline-block;
    border-right: 0;
    padding: 0; }
    .table-header-cell .table-header-cell-container {
      width: 100%;
      height: 100%;
      // border-right: 1px solid #e0e1e3;
      padding: 12px 8px; }
      .table-header-cell .table-header-cell-container .table-text {
        display: inline-block;
        vertical-align: top;
        max-width: calc(100% - 20px); }
    .table-header-cell.table-header-index-cell {
      min-width: 50px; }
    .table-header-cell.ecui-sortable-handle {
      cursor: move; }
    .table-header-cell.ui-sortable-placeholder {
      position: relative;
      font-size: 16px;
      overflow: visible;
      color: #939393;
      // border-right: 1px solid #e0e1e3; 
      }
      .table-header-cell.ui-sortable-placeholder .placeholder-icon {
        position: absolute;
        top: -18px;
        left: -4px; }
    .table-header-cell.ui-sortable-helper {
      border-left: 1px solid var(--semantic-color-border-base-primary);
      border-right: 1px solid var(--semantic-color-border-base-primary);
      text-shadow: 0 0 7px rgba(0, 0, 0, .2);
      box-shadow: 0 0 7px rgba(0, 0, 0, .2); }

  .table-row-menu-container {
    border-right: 0px !important;
    text-align: center; }
    .table-row-menu-container .table-row-menu {
      color: var(--semantic-color-content-accent-blue-secondary);
      cursor: pointer;
    }
    .table-row-menu-container .table-row-menu-button {
      cursor: pointer;
      text-align: center;
      width: 40px;
      display: inline-block; }
      .table-row-menu-container .table-row-menu-button.table-row-menu-button-sublocation .fa.fa-level-up {
        margin-right: 2px; }
      .table-row-menu-container .table-row-menu-button:hover {
        color: #00bce4; }
      .table-row-menu-container .table-row-menu-button .fa.fa-code {
        font-size: 16px; }
      .table-row-menu-container .table-row-menu-button.disabled {
        color: var(--semantic-color-content-interactive-primary-disabled);
        width: 100%; }
        .table-row-menu-container .table-row-menu-button.disabled i.fa-exclamation-triangle {
          margin-right: 8px; }

  .table-empty-message {
    border: 1px solid var(--semantic-color-border-base-primary);
    color: var(--semantic-color-content-interactive-primary-default);
    font-style: italic;
    text-align: center;
    width: 100%;
    padding: 12px 8px; }

  .table-loading-container {
    background: var(--semantic-color-surface-base-primary);
    bottom: 0;
    left: 0;
    position: absolute;
    width: 100%; }

  .table-link {
    color: var(--semantic-color-content-interactive-primary-default);
    cursor: pointer; }
    .table-link:hover {
      color: var(--semantic-color-content-interactive-primary-default);
      text-decoration: none; }
    .table-link.more:after {
      content: "..."; }

  .table-load-more {
    bottom: 0;
    height: 28px;
    left: 0;
    line-height: 28px;
    position: absolute;
    text-align: center;
    width: 100%; }

  .table-load-more-link {
    color: var(--semantic-color-content-interactive-primary-default);
    cursor: pointer; }
    .table-load-more-link:hover {
      text-decoration: underline; }

  .table-text {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
    .table-text.do-word-break {
      white-space: normal;
      word-wrap: break-word;
      word-break: break-all; }
    .table-text i.fa.success {
      color: #66B300; }
    .table-text i.fa.failure {
      color: #FD4239; }
    .table-text.html-content-present {
      width: calc(100% - 22px);
      display: inline-block;
      vertical-align: middle; }
      .table-text.html-content-present.tooltip-cue:hover {
        cursor: pointer;
        color: #00bce4; }
    .table-text.table-text-wrap {
      white-space: normal; }

  .table-cell-html {
    padding-left: 8px;
    width: 22px; }

  .table-text-list span:after {
    content: ", "; }

  .table-text-list span:last-child:after {
    content: ""; }

  .multi-data-row-item {
    margin-bottom: 8px; }
    .multi-data-row-item:last-child {
      margin-bottom: 0; }

  .multi-data-row-label {
    color: #747272; }

  .sort-icon-container {
    color: #CCCCCC;
    cursor: pointer;
    width: 16px;
    height: 16px;
    font-size: 16px;
    text-align: center;
    position: relative;
    margin-left: 4px;
    display: inline-block;
    vertical-align: top; }
    .sort-icon-container:hover {
      color: #939393; }
    .sort-icon-container .sort-table {
      vertical-align: top; }
    .sort-icon-container .fa-circle-thin {
      width: 100%; }
    .sort-icon-container .fa-angle-up {
      font-size: 10px;
      position: absolute;
      left: 4.75px;
      top: 0px; }
    .sort-icon-container .fa-angle-down {
      font-size: 10px;
      position: absolute;
      left: 4.75px;
      bottom: 1px; }
    .sort-icon-container.sort-ascending-container {
      color: var(--semantic-color-content-interactive-primary-default); }
      .sort-icon-container.sort-ascending-container:hover {
        color: #00bce4;
        opacity: .8; }
      .sort-icon-container.sort-ascending-container .sort-table {
        font-weight: 900; }
      .sort-icon-container.sort-ascending-container .fa-angle-down {
        bottom: 3px; }
    .sort-icon-container.sort-descending-container {
      color: var(--semantic-color-content-interactive-primary-default); }
      .sort-icon-container.sort-descending-container:hover {
        color: #00bce4;
        opacity: .8; }
      .sort-icon-container.sort-descending-container .sort-table {
        font-weight: 900; }
      .sort-icon-container.sort-descending-container .fa-angle-up {
        top: 2px; }

  .dialog-table .table {
    // border: 1px solid #aeb0b0; 
  }
    .dialog-table .table .th-cell,
    .dialog-table .table .tr-cell {
      display: inline-block;
      // border-right: 1px solid var(--semantic-color-surface-base-primary); 

    }
      .dialog-table .table .th-cell:last-child,
      .dialog-table .table .tr-cell:last-child {
        border-right: 0; }
    .dialog-table .table .th-cell {
      background: var(--semantic-color-background-primary);
      border: 1px solid var(--semantic-color-border-base-primary);
    }
    .dialog-table .table .tr-cell {
      vertical-align: top; }
    .dialog-table .table .col-1 {
      width: 40%; }
    .dialog-table .table .col-2,
    .dialog-table .table .col-3 {
      width: 30%; }
    .dialog-table .table .col-1,
    .dialog-table .table .col-2,
    .dialog-table .table .col-3 {
      padding-left: 5px; }
    .dialog-table .table .th {
      border-bottom: 1px solid #aeb0b0;
      color: var(--semantic-color-surface-base-primary);
      line-height: 30px; }
    .dialog-table .table .tr {
      border-bottom: 1px solid #aeb0b0;
      line-height: 25px; }
      .dialog-table .table .tr:nth-child(2n) {
        background-color: #f3f3f3; }
      .dialog-table .table .tr:last-child {
        border-bottom: 0; }

  .multiselect-list, .freeform-message-list {
    min-height: 194px;
    max-height: 194px;
    min-width: 230px;
    max-width: calc(100% - 1px); }

  .multiselect-list-item {
    color: var(--semantic-color-content-base-primary);
    padding-left: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
    .multiselect-list-item:hover {
      color: var(--semantic-color-content-base-primary); }
    .multiselect-list-item input[type="checkbox"] {
      display: inline-block;
      font-size: 16px;
      margin: 0 8px 0 0;
      padding: 0; }
    .multiselect-list-item.parent {
      background: transparent;
      color:  var(--semantic-color-content-base-primary);
      font-weight: bold; }
    .multiselect-list-item.child {
      padding-left: 25px; }
    .multiselect-list-item.child.active {
      background: var(--semantic-color-content-interactive-primary-default);
      color: var(--semantic-color-surface-base-primary); }
    .multiselect-list-item.invalid {
      color: var(--semantic-color-content-status-danger-primary);
      vertical-align: top; }

  .multiselect-selected-list-item {
    color: var(--semantic-color-content-base-primary);
    cursor: default;
    padding-right: 27px;
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
    .multiselect-selected-list-item:hover {
      color: var(--semantic-color-content-base-primary); }
    .multiselect-selected-list-item:hover .multiselect-remove-icon {
      color: var(--semantic-color-content-base-primary); }

  .multiselect-selected-items-counter, .multiselect-unselected-items-counter {
    color: var(--semantic-color-surface-base-primary);
    font-size: 13px;
    padding: 12px 16px; }

  .multiselect-remove-icon {
    color: #ddd;
    cursor: pointer;
    float: right;
    font-size: 12px;
    position: absolute;
    right: 10px;
    width: 12px; }
    .multiselect-remove-icon:hover {
      color:  var(--semantic-color-content-base-primary); }

  .multiselect-invalid-icon {
    color: var(--semantic-color-content-status-danger-primary);
    cursor: pointer;
    font-size: 16px;
    margin-right: 5px;
    width: 16px;
    z-index: 10000; }

  .multi-select-add {
    margin-left: 5px; }


  /*
  *  String Search Filter
  */
  .filter-string-search-class {
    position: relative; }

  .filter-string-search-input {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    display: inline-block;
    margin-right: 4px;
    height: 24px;
    width: 48%;
    vertical-align: middle; }

  .filter-string-search-matches {
    display: inline-block;
    width: 50%; }

  /*
  *  URL Class Filter
  */
  .filter-url-class {
    position: relative; }

  /*
  *  Combination Filter
  */
  .filter-string-ip-input {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    height: 24px;
    width: 100%; }

  .filter-item-combo-element {
    margin-bottom: 8px; }
    .filter-item-combo-element:last-child, .filter-item-combo-element:only-child {
      margin-bottom: 0px; }
    .filter-item-combo-element.filter-exclude-location {
      margin-bottom: 16px; }
    .filter-item-combo-element .insights-input-unit.filter.-js-dropdown-type {
      width: 100%; }
    .filter-item-combo-element .insights-filter-input {
      padding: 16px 0; }

  .filter-boolean-container-child {
    margin-top: 5px; }

  .filter-item-body-search-enable {
    margin-top: 0; }

  .filter-string-ip-text {
    word-wrap: break-word;
    white-space: normal;
    line-height: 16px;
    margin-top: 8px; }

  .filter-bytes-input {
    display: inline-block;
    height: 24px;
    margin-right: 4px;
    width: 64%; }

  .filter-bytes-units-min, .filter-bytes-units-max {
    margin-left: 4px;
    width: 20%; }

  .filter-bytes-input-label {
    width: 37px; }

  .filter-bytes-custom {
    margin-top: 4px; }

  .filter-range-input-label {
    width: 37px;
    margin-right: 5px;
    text-align: right; }

  /*widget filters*/
  .filters {
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    background: #ddd;
    border-radius: 3px;
    padding: 7px 5px; }

  .filters-list-item {
    position: relative;
    padding: 12px 0 !important; }
    .filters-list-item.filter-searchpick-container .dropdown-button-label {
      width: calc(100% - 20px); }

  .filter-item-body {
    position: relative;
    max-height: none; }

  .filters-add-filter-container {
    padding: 12px 0 !important; }

  .filters-delete {
    color: #999;
    font-size: 10px;
    position: absolute;
    right: 5px;
    line-height: 32px;
    height: 32px;
    cursor: pointer; }

  .filters-clear-all {
    display: inline-block;
    margin-top: -2px;
    vertical-align: top;
    text-align: right;
    width: 70%; }

  .filters-clear-all.hidden {
    display: none; }

  .filters-adjustable {
    height: 16px;
    line-height: 16px;
    margin-top: 3px; }

  .filters-adjustable-label {
    color: var(--semantic-color-content-base-primary);
    font-size: 10px;
    margin-left: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }

  .insights-sidebar .filter-input {
    width: 250px; }

  .filter-range-container .filter-input {
    width: 220px; }

  .filter-input.filter-input-full {
    width: 100%; }

  .filter-input.filter-input-string-search {
    width: 220px; }

  .filters-text {
    color:  var(--semantic-color-content-base-primary);
    display: inline-block;
    font-weight: 300;
    padding: 0 5px; }

  .insights-composite-filters .insights-filter-input {
    padding-left: 0px; }

  .filter-bytes-custom-container .insights-input-text {
    width: 59%;
    display: inline-block;
    vertical-align: top;
    min-width: 0px; }

  .report-filters .wrapper-filters {
    display: inline-block;
    height: 28px;
    position: relative;
    margin-left: 10px; }
    .report-filters .wrapper-filters .insights-filter-input {
      padding: 0px; }

  .wrapper-filters .report-filter {
    height: 28px; }

  .wrapper-filters .dropdown-button {
    height: 28px; }

  .overflow-auto {
    overflow: auto; }


  /*
  *  Progress Bar
  */
  .progress-bar-holder {
    background-color: #F6F5F5;
    left: 25%;
    top: 16px;
    position: absolute;
    width: auto;
    padding: 8px 16px;
    border-radius: 5px; }

  .progress-bar-child-container {
    display: inline-block;
    height: 100%; }

  .progress-bar-top-row {
    padding-top: 5px; }

  .progress-bar-bottom-row {
    color: var(--semantic-color-content-base-primary);
    padding-bottom: 15px;
    padding-top: 5px;
    font-size: 11px; }

  .progress-bar-throbber {
    width: 35%; }

  .progress-bar-container-content {
    background-color: var(--);
    border-top: none;
    display: inline-block;
    height: 100%;
    vertical-align: top;
    width: 325px;
    height: 100%;
    vertical-align: middle; }

  .progress-bar-holder-child-element {
    background-color: var(--semantic-color-background-primary);
    border-radius: 3px;
    border: 1px solid var(--semantic-color-border-base-primary);
    min-height: 75px;
    left: 5px;
    padding: 16px;
    right: 5px;
    text-align: center;
    top: 5px;
    z-index: 2001; }

  .progress-bar-recordCount {
    white-space: pre-wrap;
    margin-top: 5px; }

  .progress-bar-container {
    background-color: #F6F5F5;
    border: 1px solid var(--semantic-color-content-interactive-primary-default);
    height: 20px;
    width: 400px; }

  .progress-bar-container-title {
    color: var(--semantic-color-content-base-primary);
    display: inline-block;
    font-size: 11px;
    margin-right: 94px;
    width: 107px;
    vertical-align: top;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }

  .progress-bar-container-outer {
    background-color: var(--semantic-color-content-status-info-primary);
    border: 1px solid var(--semantic-color-content-interactive-primary-default);
    display: inline-block;
    height: 10px;
    vertical-align: top;
    width: 100px; }

  .progress-bar-container-outer.progress-bar-child-element {
    height: 18px;
    margin-right: 8px;
    vertical-align: middle; }

  .progress-bar-container-inner {
    background-color: var(--semantic-color-content-status-info-primary);
    height: 100%;
    left: 0;
    top: 0; }

  .progress-bar-cancel-button {
    background: var(--semantic-color-content-interactive-primary-default);
    border: 1px solid var(--semantic-color-content-interactive-primary-default);
    border-radius: 5px;
    color: var(--semantic-color-background-primary);
    cursor: pointer;
    display: inline-block;
    vertical-align: top;
    height: 20px;
    font-size: 13px;
    line-height: 18px;
    max-width: 480px;
    min-width: 81px;
    padding: 0px 20px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; 
    vertical-align: middle; }

  .search-container {
    // display: inline-block;
    vertical-align: middle; }
    .dropdown + .search-container, .reports-favorites-list + .search-container {
      margin-left: 5px; }

  .search-input-text-container {
    height: 100%;
    display: inline-block;
    position: relative; }

  .search-input, .autocomplete-input-container {
    background: #f3f3f3;
    border: none;
    padding: 12px 16px; }
    .search-input.wide, .autocomplete-input-container {
      padding-right: 16px; }

  .search-input-text {
    display: inline-block;
    font-size: 13px;
    height: 28px;
    padding: 6px 26px 6px 0;
    vertical-align: middle;
    width: 280px; }

  .search-icon, .autocomplete-icon {
    color: #939393;
    display: inline-block;
    font-size: 16px !important;
    font-weight: bold;
    height: 16px;
    margin-left: 8px;
    width: 16px;
    vertical-align: middle; }
    .search-icon:hover, .autocomplete-icon:hover {
      color:  var(--semantic-color-content-base-primary); }

  .search-clear-icon {
    color: #999;
    display: none;
    cursor: pointer;
    font-size: 14px !important;
    height: 14px;
    margin-left: 5px;
    width: 12px;
    vertical-align: middle;
    position: absolute;
    top: 7px;
    right: 8px; }
    .search-clear-icon.fa {
      display: none; }
    .search-clear-icon:hover {
      color:  var(--semantic-color-content-base-primary); }
    .search-clear-icon.visible {
      display: inline-block; }

  .content-tabs-content-item .subscription-required {
    border-radius: 0 0 3px 3px; }
  /*
      Dashboard Styles

      -   Dashboard page
      -   Dashboard builder page
      -   Reset dashboard dialog
      -   Quicklinks
  */
  /* Dashboard */
  .dashboard {
    height: 100%;
    width: calc(100% + 24px); }

  /* Dashboard Builder */
  .dashboard-builder-content {
    position: absolute;
    top: 53px;
    bottom: 0px;
    left: 0px;
    right: 0px; }

  .dashboard-builder-top {
    margin-bottom: 24px;
    padding: 10px 24px;
    white-space: nowrap;
    width: 100%; }

  .dashboard-builder-top .button, .dashboard-builder-top .icon-button, .dashboard-builder-top .slider-button {
    vertical-align: top; }

  .dashboard-builder-bottom {
    bottom: 24px;
    position: absolute;
    top: 70px;
    left: 24px;
    right: 24px;
    overflow: hidden; }

  /* Dashboard Menu */
  .dashboard-menu {
    border: 1px solid var(--semantic-color-background-primary);
    border-bottom: none;
    cursor: pointer;
    display: inline-block;
    height: 26px;
    left: -6px;
    max-width: 260px;
    padding: 0 17px 0 5px;
    position: relative;
    top: -2px;
    vertical-align: middle;
    white-space: nowrap; }

  .dashboard-menu-active {
    -moz-border-radius: 3px 3px 0 0;
    -webkit-border-radius: 3px 3px 0 0;
    background: var(--semantic-color-background-primary);
    border: 1px solid #6b6d70;
    border-bottom: none;
    border-radius: 3px 3px 0 0; }

  .dashboard-menu-text {
    color: #008bc9;
    display: inline-block;
    font-size: 19px;
    font-weight: 300;
    line-height: 25px;
    max-width: 238px;
    position: relative;
    vertical-align: top;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }

  .dashboard-menu-list {
    -moz-border-radius: 3px;
    -moz-box-shadow: 1px 0 2px #333;
    -webkit-border-radius: 3px;
    -webkit-box-shadow: 1px 0 2px #333;
    background: var(--semantic-color-surface-base-primary);
    border: 1px solid #6b6d70;
    border-radius: 3px;
    box-shadow: 1px 0 2px #333;
    display: none;
    font-size: 14px;
    font-weight: 300;
    left: 0;
    max-width: 500px;
    position: fixed;
    top: 0;
    z-index: 9999; }

  .dashboard-menu-list-item {
    color: #666;
    height: 28px;
    line-height: 28px;
    padding: 0 32px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }

  .dashboard-menu-list-item:hover,
  .dashboard-menu-list-item-active {
    background: #008bc9;
    color: var(--semantic-color-surface-base-primary); }

  .dashboard-super-text {
    font-size: 10px;
    padding-left: 2px;
    position: relative;
    top: -6px;
    color: inherit; }

  .dashboard-super-title-text {
    font-size: 10px;
    padding-left: 2px;
    position: relative;
    top: -8px;
    color: inherit; }

  /* Dashboard Timerange */
  .dashboard-timerange-container {
    display: inline-block;
    vertical-align: top;
    width: 100px; }
    .dashboard-timerange-container .dropdown, .dashboard-timerange-container .reports-favorites-list {
      min-width: 0; }
      .dashboard-timerange-container .dropdown .dropdown-icon, .dashboard-timerange-container .reports-favorites-list .dropdown-icon {
        padding-left: 5px; }
      .dashboard-timerange-container .dropdown .dropdown-button, .dashboard-timerange-container .reports-favorites-list .dropdown-button {
        text-align: center; }


  .dashboard-title {
    max-width: calc(100% - 250px);
    display: inline-block;
    vertical-align: top;
    padding-right: 10px;
    font-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }

  .dashboard-edit-title {
    display: inline-block;
    vertical-align: top;
    padding-right: 10px;
    font-size: 16px;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }

  .dashboard-grid-view {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden; }

  .insights {
    height: 100%; }

  .insights-sidebar {  
    border: 1px solid var(--semantic-color-border-base-primary);
    display: inline-block;
    height: 100%;
    vertical-align: top;
    width: 420px; }

  .insights-sidebar-filters {
    height: calc(100% - 34px);
    overflow-x: hidden;
    overflow-y: auto;
    padding: 10px; }

  .insights-composite-filter-label {
    margin-bottom: 6px !important; }

  .insights-sidebar-toolbar {
    background: var(--semantic-color-background-primary);
    padding: 0 5px;
    text-align: right; }

  .insights-filter {
    margin-top: 10px; }

  .insights-filter-label span {
    cursor: inherit;
    margin-right: 0;
    width: calc(100% - 14px); }

  .insights-filter-label.inner {
    line-height: normal;
    margin-top: 5px; }

  .insights-filter-label.inner:first-child {
    padding-top: 5px; }

  .insights-filter-label.invalid {
    color: var(--semantic-color-content-status-danger-primary); }

  .insights-filter-label-text {
    display: inline-block;
    vertical-align: top;
    width: calc(100% - 14px); }
    .insights-filter-label-text.invalid {
      color: var(--semantic-color-content-status-danger-primary); }

  span[data-help-property="SERVER_IP_ADDRESS"] {
    display: inline; }

  /*
      Main Styles:

      -   Page header, footer, and main content area
      -   Page navigation
  */
  /* Logout Button */
  .logout-button {
    color: white;
    line-height: 20px;
    padding: 0 5px 0 10px; }

  /* Main */
  .main {
    height: 100%;
    position: relative;
    width: 100%;
    overflow-y: auto; }

  #main-page {
    height: 100%; }

  /* Main Content */
  .main-content {
    width: 100%;
    height: calc(100% - 60px);
    padding: 0 24px 24px;
    overflow-y: auto; }

  .main-report-content {
    width: 100%;
    height: calc(100% - 60px);
    overflow-y: auto; }

  /* Main Header */
  .main-header {
    padding: 16px 24px 13px;
    width: 100%; }
    .main-header .column {
      height: 31px;
      padding: 1px 0; }

  .dashboard-header-left {
    height: 100%;
    width: 33.33%; }

  .dashboard-header-center {
    text-align: center;
    width: 33.33%; }
    .dashboard-header-center.column {
      padding: 0; }

  .dashboard-header-right {
    text-align: right;
    width: 33.34%; }
    .dashboard-header-right.column {
      padding: 5px 0; }

  .main-header-left {
    width: 50%; }

  .main-header-right {
    width: 50%;
    text-align: right; }

  .main-header-right-icons {
    font-size: 8px;
    margin-left: 32px;
    color: var(--semantic-color-content-interactive-primary-default);
    cursor: pointer;
    display: inline-block;
    position: relative;
    vertical-align: middle; }
    .main-header-right-icons:hover {
      color: #00bce4; }
    .main-header-right-icons:first-child {
      margin: 0px; }
    .main-header-right-icons.add-note .fa-plus-circle {
      font-size: 12px;
      position: absolute;
      top: 11px;
      left: -5px; }
    .main-header-right-icons.reset-dashboard-icon {
      color:  var(--semantic-color-content-base-primary); }
      .main-header-right-icons.reset-dashboard-icon:hover {
        color: #939393; }
    .main-header-right-icons.hidden {
      display: none; }

  .main-header-title {
    /*color: $input-label-color;*/
    font-size: 20px;
    color:  var(--semantic-color-content-base-primary); }

  .dashboard-header .main-header-title {
    color: var(--semantic-color-content-base-primary);
    font-size: 20px;
    display: inline-block;
    width: auto;
    max-width: 100%;
    padding: 0px 8px 4px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
    .dashboard-header .main-header-title:active, .dashboard-header .main-header-title:focus {
      border: none;
      outline: none;
      border-bottom: 1px solid #007ea8 !important;
      text-overflow: initial; }
    .dashboard-header .main-header-title:hover {
      border-bottom: 1px solid var(--semantic-color-content-interactive-primary-default); }
    .dashboard-header .main-header-title.disable-edit:active, .dashboard-header .main-header-title.disable-edit:focus, .dashboard-header .main-header-title.disable-edit:hover {
      border-bottom: 0px !important; }

  /* Page */
  .page {
    height: 100vh;
    min-height: 667px;
    min-width: 1060px;
    position: absolute;
    width: 100%; }

  /* Page Content */
  .page-content {
    background: var(--semantic-color-surface-base-primary);
    bottom: 37px;
    overflow: hidden;
    position: absolute;
    left: 88px;
    top: 0px;
    width: calc(100% - 88px); }

  .page-layout {
    height: 100%;
    width: 100%; }

  .page-sidebar {
    position: absolute;
    z-index: 1;
    top: 21px;
    left: 24px; }
    .page-sidebar .page-sidebar-back-button {
      padding: 0px; }
      .page-sidebar .page-sidebar-back-button:hover {
        text-decoration: none; }
      .page-sidebar .page-sidebar-back-button .sidebar-back-button {
        padding-left: 8px; }

  .page-main {
    bottom: 0;
    left: 0px;
    overflow: hidden;
    position: absolute;
    right: 0px;
    top: 0; }

  .page-title {
    color: var(--semantic-color-content-base-primary);
    font-size: 20px;
    padding: 18px 24px 18px 24px; }
    .page-title.back-sidebar-present {
      padding-left: 100px; }

  .page-main-content {
    bottom: 0;
    left: 0;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0 24px 24px 24px;
    right: 0;
    top: 56px; }

  .page-sub-header {
    color: var(--semantic-color-content-interactive-primary-default);
    font-size: 16px;
    height: 28px;
    line-height: 28px;
    margin-top: 10px;
    text-align: left;
    width: 100%; }
    .page-sub-header:first-child {
      margin-top: 0; }

  /* Page Footer */
  .page-footer {
    background: #1e1f22;
    border-top: 1px solid #1e1f22;
    bottom: 0;
    color: #939393;
    font-size: 11px;
    height: 37px;
    padding: 10px 24px;
    position: absolute;
    width: 100%; }

  .page-footer-left, .page-footer-right {
    display: inline-block;
    height: 100%;
    text-align: left;
    vertical-align: top;
    width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
    .page-footer-left .copyright-container, .page-footer-right .copyright-container {
      display: inline-block;
      vertical-align: middle;
      padding-right: 8px; }
    .page-footer-left .cloud-version-container, .page-footer-right .cloud-version-container {
      display: none;
      vertical-align: middle;
      border-left: 1px solid #505050;
      padding-left: 8px;
      padding-right: 8px; }
    .page-footer-left .patents-container, .page-footer-right .patents-container {
      display: inline-block;
      vertical-align: middle;
      border-left: 1px solid #505050;
      padding-left: 8px; }
      .page-footer-left .patents-container .patents-link-container, .page-footer-right .patents-container .patents-link-container {
        color: var(--semantic-color-content-interactive-primary-default); }
        .page-footer-left .patents-container .patents-link-container:hover, .page-footer-right .patents-container .patents-link-container:hover {
          color: #00bce4; }
    .page-footer-left .powered-by, .page-footer-right .powered-by {
      display: inline-block;
      vertical-align: middle;
      margin-right: 5px; }
    .page-footer-left .zscaler-logo, .page-footer-right .zscaler-logo {
      max-height: 14px;
      display: inline-block;
      vertical-align: middle; }

  .page-footer-right {
    text-align: right; }
    .page-footer-right .weblog-time-container {
      color: var(--semantic-color-surface-base-primary);
      padding-right: 8px; }
    .page-footer-right .last-updated-time-container {
      padding-left: 8px;
      border-left: 1px solid #505050; }

  /* Page Header */
  .page-header {
    background: var(--semantic-color-content-base-secondary);
    bottom: 37px;
    position: absolute;
    top: 0px;
    width: 88px; }

  .page-header-logo-container {
    background: var(--semantic-color-content-base-secondary);
    display: inline-block;
    height: 52px;
    padding: 12px 4px;
    text-align: center;
    width: 100%;
    vertical-align: middle; }

  .login-page-header-logo-container {
    margin: auto; }

  .page-header-logo {
    // background-image: url('../img/zscaler-short-logo.png');
      background-repeat: no-repeat;
      width: auto; /*or your image's width*/
      height: auto; /*or your image's height*/
      margin: 0;
      padding: 0;
    height: 100%;
    max-width: 100%;
    width: auto; }
    .page-header-logo.animate {
      transition: all .1s ease-in-out; }
    .page-header-logo:active {
      transform: scale(.95); }

  /* Navigation Menus */
  .page-header-navbar {
    height: 100%;
    width: 100%; }

  .page-header-contents {
    height: 100%;
    width: 100%; }

  body .ecui-block-eusa-notification {
    position: absolute;
    left: calc(50% - 136px);
    top: 8px;
    z-index: 10; }
    body .ecui-block-eusa-notification > span {
      display: inline-block;
      cursor: pointer;
      color: #f4aa00;
      font-size: 11px; }
      body .ecui-block-eusa-notification > span i {
        font-family: FontAwesome; }
      body .ecui-block-eusa-notification > span span {
        padding-left: 5px;
        font-weight: 600;
      }

  .tooltip.bottom-middle-pending-eusa::before {
    border-bottom-color: var(--semantic-color-border-base-primary);
    border-top: none;
    right: 250px;
    top: -6px; }

  .tooltip.bottom-middle-pending-eusa::after {
    border-bottom-color: var(--semantic-color-border-base-primary);
    border-top: none;
    right: 251px;
    top: -5px; }

  .POCAccount .page-header-navbar-top {
    width: calc(100% - 290px); }

  .POCAccount .page-header-navbar-down {
    width: 290px; }

  .page-header-navbar-top {
    display: inline-block;
    height: calc(100% - 114px);
    text-align: left;
    vertical-align: top;
    width: 100%; }

  .page-header-navbar-down {
    display: block;
    height: 114px;
    vertical-align: top;
    width: 100%; }
    .page-header-navbar-down .fa {
      font-size: 16px !important; }
    .page-header-navbar-down .user-logout-menu {
      margin-bottom: 6px; }
    .page-header-navbar-down .user-logout-position {
      width: 90px;
      margin-left: -6px; }

  .nav-menus {
    height: calc(100% - 52px);
    display: block;
    vertical-align: middle; }

  .nav-menu, .org-edit-nav-menu {
    display: block;
    font-size: 11px;
    height: 80px;
    line-height: 16px;
    vertical-align: top;
    margin-bottom: 1px; }

  .nav-menu-header, .org-edit-nav-menu-header {
    color: var(--semantic-color-surface-base-primary);
    opacity: .6;
    cursor: pointer;
    display: block;
    height: 100%;
    padding-top: 18px;
    text-align: center;
    text-decoration: none;
    white-space: nowrap;
    width: 100%;
    border-left: 4px solid var(--semantic-color-content-base-secondary);
    border-right: 4px solid var(--semantic-color-content-base-secondary); }
    .nav-menu-header:hover, .org-edit-nav-menu-header:hover {
      text-decoration: none;
      border-left: 4px solid #009CDA;
      border-right: 4px solid #35373B;
      color: var(--semantic-color-surface-base-primary); }
    .nav-menu-header.selected, .selected.org-edit-nav-menu-header {
      opacity: 1;
      border: none !important;
      background: #009CDA !important;
      color: var(--semantic-color-surface-base-primary) !important; }
    .nav-menu-header.active, .active.org-edit-nav-menu-header, .org-edit-nav-menu-header.reports-tab.reports-tab-selected, .nav-menu-header.reports-tab.reports-tab-selected {
      border-left: 4px solid #009CDA;
      border-right: 4px solid #35373B;
      background: #35373B;
      opacity: 1;
      color: var(--semantic-color-surface-base-primary); }

  @media (max-height: 825px) {
    .nav-menu-panel.large.policy-panel .nav-menu-panel-item, .nav-menu-panel.large.admin-panel .nav-menu-panel-item, .large.admin-panel.down-nav-menu-panel .nav-menu-panel-item, .large.admin-panel.search-nav-menu-panel .nav-menu-panel-item, .large.admin-panel.org-edit-nav-menu-panel .nav-menu-panel-item, .large.policy-panel.down-nav-menu-panel .nav-menu-panel-item, .large.policy-panel.search-nav-menu-panel .nav-menu-panel-item, .large.policy-panel.org-edit-nav-menu-panel .nav-menu-panel-item, .nav-menu-panel.large.admin-panel .nav-menu-panel-item, .large.admin-panel.down-nav-menu-panel .nav-menu-panel-item, .large.admin-panel.search-nav-menu-panel .nav-menu-panel-item, .large.admin-panel.org-edit-nav-menu-panel .nav-menu-panel-item {
      padding: 16px 0px 8px 0px; }
      .nav-menu-panel.large.policy-panel .nav-menu-panel-item:first-child, .nav-menu-panel.large.admin-panel .nav-menu-panel-item:first-child, .large.admin-panel.down-nav-menu-panel .nav-menu-panel-item:first-child, .large.admin-panel.search-nav-menu-panel .nav-menu-panel-item:first-child, .large.admin-panel.org-edit-nav-menu-panel .nav-menu-panel-item:first-child, .large.policy-panel.down-nav-menu-panel .nav-menu-panel-item:first-child, .large.policy-panel.search-nav-menu-panel .nav-menu-panel-item:first-child, .large.policy-panel.org-edit-nav-menu-panel .nav-menu-panel-item:first-child, .nav-menu-panel.large.admin-panel .nav-menu-panel-item:first-child, .large.admin-panel.down-nav-menu-panel .nav-menu-panel-item:first-child, .large.admin-panel.search-nav-menu-panel .nav-menu-panel-item:first-child, .large.admin-panel.org-edit-nav-menu-panel .nav-menu-panel-item:first-child {
        padding-top: 24px; }
    .nav-menu-panel.large.policy-panel .nav-menu-list-item, .nav-menu-panel.large.admin-panel .nav-menu-list-item, .large.admin-panel.down-nav-menu-panel .nav-menu-list-item, .large.admin-panel.search-nav-menu-panel .nav-menu-list-item, .large.admin-panel.org-edit-nav-menu-panel .nav-menu-list-item, .large.policy-panel.down-nav-menu-panel .nav-menu-list-item, .large.policy-panel.search-nav-menu-panel .nav-menu-list-item, .large.policy-panel.org-edit-nav-menu-panel .nav-menu-list-item, .nav-menu-panel.large.admin-panel .nav-menu-list-item, .large.admin-panel.down-nav-menu-panel .nav-menu-list-item, .large.admin-panel.search-nav-menu-panel .nav-menu-list-item, .large.admin-panel.org-edit-nav-menu-panel .nav-menu-list-item {
      padding: 3px 16px 3px 16px;
      height: 19px;
      line-height: 13px;
      font-size: 12px; }
    .nav-menu-panel.large.policy-panel .nav-menu-section-header, .nav-menu-panel.large.admin-panel .nav-menu-section-header, .large.admin-panel.down-nav-menu-panel .nav-menu-section-header, .large.admin-panel.search-nav-menu-panel .nav-menu-section-header, .large.admin-panel.org-edit-nav-menu-panel .nav-menu-section-header, .large.policy-panel.down-nav-menu-panel .nav-menu-section-header, .large.policy-panel.search-nav-menu-panel .nav-menu-section-header, .large.policy-panel.org-edit-nav-menu-panel .nav-menu-section-header, .nav-menu-panel.large.admin-panel .nav-menu-section-header, .large.admin-panel.down-nav-menu-panel .nav-menu-section-header, .large.admin-panel.search-nav-menu-panel .nav-menu-section-header, .large.admin-panel.org-edit-nav-menu-panel .nav-menu-section-header {
      padding: 8px 0px 5px 16px; } }

  @media (max-height: 705px) {
    .nav-menu, .org-edit-nav-menu {
      height: 76px; } }

  .nav-menu-header-label, .org-edit-nav-menu-header-label {
    position: relative;
    padding-bottom: 15px;
    padding-top: 8px; }

  .nav-menu-icon {
    font-size: 28px !important;
    display: block; }

  .nav-menu-panel, .down-nav-menu-panel, .search-nav-menu-panel, .org-edit-nav-menu-panel {
    background: #35373B;
    border-top: 0px;
    -webkit-box-shadow: 0px 0px 4px 0px rgba(42, 44, 48, .5);
    -moz-box-shadow: 0px 0px 4px 0px rgba(42, 44, 48, .5);
    box-shadow: 0px 0px 4px 0px rgba(42, 44, 48, .5);
    display: none;
    margin: 0px;
    width: 250px;
    list-style: none;
    opacity: .98;
    padding: 24px 0px 24px 0px;
    position: absolute;
    top: 0px;
    bottom: 0px;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    text-align: left;
    z-index: 9998; }
    .nav-menu-panel.large, .large.down-nav-menu-panel, .large.search-nav-menu-panel, .large.org-edit-nav-menu-panel {
      width: 410px; }
    .nav-menu-panel.dashboard-panel, .dashboard-panel.down-nav-menu-panel, .dashboard-panel.search-nav-menu-panel, .dashboard-panel.org-edit-nav-menu-panel {
      padding-top: 16px; }
    .nav-menu-panel.analytics-panel .nav-menu-section-header:first-child, .analytics-panel.down-nav-menu-panel .nav-menu-section-header:first-child, .analytics-panel.search-nav-menu-panel .nav-menu-section-header:first-child, .analytics-panel.org-edit-nav-menu-panel .nav-menu-section-header:first-child {
      padding-top: 0px; }
    .nav-menu-panel.analytics-panel .nav-menu-list-item:last-child, .analytics-panel.down-nav-menu-panel .nav-menu-list-item:last-child, .analytics-panel.search-nav-menu-panel .nav-menu-list-item:last-child, .analytics-panel.org-edit-nav-menu-panel .nav-menu-list-item:last-child {
      margin-bottom: 20px; }
    .nav-menu-panel.policy-panel, .nav-menu-panel.admin-panel, .admin-panel.down-nav-menu-panel, .admin-panel.search-nav-menu-panel, .admin-panel.org-edit-nav-menu-panel, .policy-panel.down-nav-menu-panel, .policy-panel.search-nav-menu-panel, .policy-panel.org-edit-nav-menu-panel {
      padding: 0px; }
      .nav-menu-panel.policy-panel li.nav-menu-section, .nav-menu-panel.admin-panel li.nav-menu-section, .admin-panel.down-nav-menu-panel li.nav-menu-section, .admin-panel.search-nav-menu-panel li.nav-menu-section, .admin-panel.org-edit-nav-menu-panel li.nav-menu-section, .policy-panel.down-nav-menu-panel li.nav-menu-section, .policy-panel.search-nav-menu-panel li.nav-menu-section, .policy-panel.org-edit-nav-menu-panel li.nav-menu-section {
        width: 50%;
        display: inline-block;
        vertical-align: top; }
      .nav-menu-panel.policy-panel .nav-menu-panel-item, .nav-menu-panel.admin-panel .nav-menu-panel-item, .admin-panel.down-nav-menu-panel .nav-menu-panel-item, .admin-panel.search-nav-menu-panel .nav-menu-panel-item, .admin-panel.org-edit-nav-menu-panel .nav-menu-panel-item, .policy-panel.down-nav-menu-panel .nav-menu-panel-item, .policy-panel.search-nav-menu-panel .nav-menu-panel-item, .policy-panel.org-edit-nav-menu-panel .nav-menu-panel-item {
        padding: 28px 0px 16px 0px;
        border-bottom: 1px solid #4c4f54; }
        .nav-menu-panel.policy-panel .nav-menu-panel-item:first-child, .nav-menu-panel.admin-panel .nav-menu-panel-item:first-child, .admin-panel.down-nav-menu-panel .nav-menu-panel-item:first-child, .admin-panel.search-nav-menu-panel .nav-menu-panel-item:first-child, .admin-panel.org-edit-nav-menu-panel .nav-menu-panel-item:first-child, .policy-panel.down-nav-menu-panel .nav-menu-panel-item:first-child, .policy-panel.search-nav-menu-panel .nav-menu-panel-item:first-child, .policy-panel.org-edit-nav-menu-panel .nav-menu-panel-item:first-child {
          padding-top: 24px; }
        .nav-menu-panel.policy-panel .nav-menu-panel-item:last-child, .nav-menu-panel.admin-panel .nav-menu-panel-item:last-child, .admin-panel.down-nav-menu-panel .nav-menu-panel-item:last-child, .admin-panel.search-nav-menu-panel .nav-menu-panel-item:last-child, .admin-panel.org-edit-nav-menu-panel .nav-menu-panel-item:last-child, .policy-panel.down-nav-menu-panel .nav-menu-panel-item:last-child, .policy-panel.search-nav-menu-panel .nav-menu-panel-item:last-child, .policy-panel.org-edit-nav-menu-panel .nav-menu-panel-item:last-child {
          border-bottom: none; }
      .nav-menu-panel.policy-panel .nav-menu-section-header, .nav-menu-panel.admin-panel .nav-menu-section-header, .admin-panel.down-nav-menu-panel .nav-menu-section-header, .admin-panel.search-nav-menu-panel .nav-menu-section-header, .admin-panel.org-edit-nav-menu-panel .nav-menu-section-header, .policy-panel.down-nav-menu-panel .nav-menu-section-header, .policy-panel.search-nav-menu-panel .nav-menu-section-header, .policy-panel.org-edit-nav-menu-panel .nav-menu-section-header {
        padding: 12px 0px 7px 16px; }
      .nav-menu-panel.policy-panel .nav-menu-panel-item-header, .nav-menu-panel.admin-panel .nav-menu-panel-item-header, .admin-panel.down-nav-menu-panel .nav-menu-panel-item-header, .admin-panel.search-nav-menu-panel .nav-menu-panel-item-header, .admin-panel.org-edit-nav-menu-panel .nav-menu-panel-item-header, .policy-panel.down-nav-menu-panel .nav-menu-panel-item-header, .policy-panel.search-nav-menu-panel .nav-menu-panel-item-header, .policy-panel.org-edit-nav-menu-panel .nav-menu-panel-item-header, .nav-menu-panel.policy-panel .accordion-nav-menu-panel-item-header, .nav-menu-panel.admin-panel .accordion-nav-menu-panel-item-header, .admin-panel.down-nav-menu-panel .accordion-nav-menu-panel-item-header, .admin-panel.search-nav-menu-panel .accordion-nav-menu-panel-item-header, .admin-panel.org-edit-nav-menu-panel .accordion-nav-menu-panel-item-header, .policy-panel.down-nav-menu-panel .accordion-nav-menu-panel-item-header, .policy-panel.search-nav-menu-panel .accordion-nav-menu-panel-item-header, .policy-panel.org-edit-nav-menu-panel .accordion-nav-menu-panel-item-header {
        width: 100%;
        padding-left: 16px; }
      .nav-menu-panel.policy-panel .nav-menu-list-item, .nav-menu-panel.admin-panel .nav-menu-list-item, .admin-panel.down-nav-menu-panel .nav-menu-list-item, .admin-panel.search-nav-menu-panel .nav-menu-list-item, .admin-panel.org-edit-nav-menu-panel .nav-menu-list-item, .policy-panel.down-nav-menu-panel .nav-menu-list-item, .policy-panel.search-nav-menu-panel .nav-menu-list-item, .policy-panel.org-edit-nav-menu-panel .nav-menu-list-item {
        height: 26px;
        margin-bottom: 2px;
        padding: 5px 16px 5px 16px; }
        .nav-menu-panel.policy-panel .nav-menu-list-item:last-child, .nav-menu-panel.admin-panel .nav-menu-list-item:last-child, .admin-panel.down-nav-menu-panel .nav-menu-list-item:last-child, .admin-panel.search-nav-menu-panel .nav-menu-list-item:last-child, .admin-panel.org-edit-nav-menu-panel .nav-menu-list-item:last-child, .policy-panel.down-nav-menu-panel .nav-menu-list-item:last-child, .policy-panel.search-nav-menu-panel .nav-menu-list-item:last-child, .policy-panel.org-edit-nav-menu-panel .nav-menu-list-item:last-child {
          margin-bottom: 7px; }
    .nav-menu-panel.help-panel li.nav-menu-section, .help-panel.down-nav-menu-panel li.nav-menu-section, .help-panel.search-nav-menu-panel li.nav-menu-section, .help-panel.org-edit-nav-menu-panel li.nav-menu-section {
      padding-bottom: 26px; }
    .nav-menu-panel.help-panel .nav-menu-list-item, .help-panel.down-nav-menu-panel .nav-menu-list-item, .help-panel.search-nav-menu-panel .nav-menu-list-item, .help-panel.org-edit-nav-menu-panel .nav-menu-list-item {
      height: 26px;
      margin-bottom: 2px;
      padding: 5px 16px 5px 16px; }
    .nav-menu-panel.help-panel .nav-menu-section-header, .help-panel.down-nav-menu-panel .nav-menu-section-header, .help-panel.search-nav-menu-panel .nav-menu-section-header, .help-panel.org-edit-nav-menu-panel .nav-menu-section-header {
      padding: 12px 0px 7px 16px; }
      .nav-menu-panel.help-panel .nav-menu-section-header:first-child, .help-panel.down-nav-menu-panel .nav-menu-section-header:first-child, .help-panel.search-nav-menu-panel .nav-menu-section-header:first-child, .help-panel.org-edit-nav-menu-panel .nav-menu-section-header:first-child {
        padding-top: 0px; }

  .nav-menu-section, .nav-menu-list, .nav-menu-list-item {
    display: block; }

  .nav-menu-section.hidden {
    display: none; }

  .nav-menu-error-message {
    margin-right: 5px; }

  .nav-menu-panel-item {
    display: inline-block;
    vertical-align: top;
    width: 100%; }

  .nav-menu-panel-item-header, .accordion-nav-menu-panel-item-header {
    color: #747272;
    font-size: 13px;
    font-weight: 600;
    line-height: 16px;
    opacity: 1;
    padding-left: 16px;
    white-space: nowrap;
    margin: 0px;
    width: 100%; }
    .nav-menu-panel-item-header i.fa-angle, .accordion-nav-menu-panel-item-header i.fa-angle {
      display: none; }
    .nav-menu-panel-item-header i.fa, .accordion-nav-menu-panel-item-header i.fa {
      font-size: 13px;
      line-height: 13px; }

  .nav-menu-panel-item-header-label {
    margin-left: 8px;
    vertical-align: top; }

  .nav-menu-list {
    list-style: none;
    margin: 0;
    padding: 0; }

  .nav-menu-section-header {
    color: #939393;
    font-size: 11px;
    text-transform: uppercase;
    font-weight: normal;
    line-height: 16px;
    padding: 0px 16px;
    opacity: 1;
    width: 100%;
    margin: 0px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
    .nav-menu-section-header .nav-menu-section-header-icon {
      margin-right: 5px;
      font-size: 16px; }
    .nav-menu-section-header.auth-config-overflow {
      cursor: default; }

  .nav-menu-list-item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--semantic-color-surface-base-primary);
    cursor: pointer;
    display: block;
    font-size: 13px;
    text-align: left;
    width: 100%;
    padding: 8px 16px 8px 16px;
    height: 32px;
    line-height: 16px;
    margin-bottom: 4px;
    opacity: 1; }
    .nav-menu-list-item .nav-menu-list-item-text {
      display: inline-block;
      vertical-align: middle;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap; }
    .nav-menu-list-item:hover {
      color: #009cda; }
    .nav-menu-list-item.active, .nav-menu-list-item.reports-tab.reports-tab-selected {
      color: #009cda;
      background-color: #2b2e33; }
    .nav-menu-list-item.no-indent {
      padding: 0 10px 0 15px; }
    .nav-menu-list-item.first-no-header {
      margin-top: 20px; }
    .nav-menu-list-item .nav-menu-link {
      color: var(--semantic-color-surface-base-primary); }
      .nav-menu-list-item .nav-menu-link .fa-external-link {
        font-size: 12px;
        line-height: 12px;
        padding-left: 4px;
        opacity: 1; }
    .nav-menu-list-item .nav-menu-link:hover {
      color: #009cda; }

  .nav-menu-list-item-content {
    color: var(--semantic-color-surface-base-primary);
    display: block;
    font-size: 13px;
    text-align: left;
    width: 100%;
    padding: 8px 16px; }
    .nav-menu-list-item-content:last-child {
      margin-bottom: 12px; }
    .nav-menu-list-item-content .fa {
      font-size: 13px !important; }
    .nav-menu-list-item-content.hidden {
      display: none; }

  .nav-menu-list-item-error {
    color: #FD4239; }

  .nav-menu-panel-item-icon {
    color: var(--semantic-color-surface-base-primary);
    font-size: 21px;
    margin-right: 10px; }

  .nav-menu-super-label {
    background: #CC0800 !important;
    border: 1px solid var(--semantic-color-surface-base-primary) !important;
    border-radius: 12px;
    color: var(--semantic-color-surface-base-primary) !important;
    font-size: 13px;
    height: 20px;
    line-height: 12px;
    min-width: 20px;
    padding-top: 4px;
    position: absolute;
    left: 47px;
    text-align: center;
    top: 13px;
    z-index: 10; }

  .nav-menu-link {
    text-decoration: none;
    color: #00bce4;
    display: inline-block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
    .nav-menu-link:hover {
      text-decoration: none;
      color: var(--semantic-color-surface-base-primary); }

  /*Top Menu*/
  .page-header-right-menu {
    display: inline-block;
    float: right;
    height: 100%;
    position: relative;
    color: var(--semantic-color-surface-base-primary);
    font-size: 12px; }

  /*Support Menu*/
  .help-dialog-mask {
    z-index: 9999; }
    .help-dialog-mask.submit-ticket-dialog {
      z-index: 999999; }

  /* Down Nav Menu */
  .down-nav-menu {
    display: block;
    font-size: 16px;
    height: 36px;
    width: 100%;
    vertical-align: top;
    cursor: pointer;
    border-left: 4px solid var(--semantic-color-content-base-secondary);
    border-right: 4px solid var(--semantic-color-content-base-secondary); }
    .down-nav-menu:hover {
      border-left: 4px solid #009CDA;
      border-right: 4px solid #35373B;
      background: #35373B;
      opacity: 1; }
      .down-nav-menu:hover .down-nav-menu-header {
        opacity: 1;
        color: var(--semantic-color-surface-base-primary); }
        .down-nav-menu:hover .down-nav-menu-header i.check-bars-icon {
          text-shadow: -1px -1px 0 #35373B, 1px -1px 0 #35373B, -1px 1px 0 #35373B, 1px 1px 0 #35373B; }
    .down-nav-menu:active {
      border-left: 4px solid #009CDA;
      border-right: 4px solid #35373B;
      background: #35373B;
      opacity: 1; }
      .down-nav-menu:active .down-nav-menu-header {
        opacity: 1;
        color: var(--semantic-color-surface-base-primary); }
        .down-nav-menu:active .down-nav-menu-header i.check-bars-icon {
          text-shadow: -1px -1px 0 #35373B, 1px -1px 0 #35373B, -1px 1px 0 #35373B, 1px 1px 0 #35373B; }

  .down-nav-menu-header {
    color: var(--semantic-color-surface-base-primary);
    opacity: .6;
    display: block;
    padding: 10px 0;
    text-align: center;
    font-size: 0px;
    height: 100%;
    white-space: nowrap;
    width: 100%; }
    .down-nav-menu-header span.down-nav-menu-header-label {
      display: block;
      font-size: 11px;
      left: 8px;
      line-height: 22px;
      position: absolute; }
      .down-nav-menu-header span.down-nav-menu-header-label.activation-label {
        left: 5px; }
      .down-nav-menu-header span.down-nav-menu-header-label.help-label {
        left: 21px; }
      .down-nav-menu-header span.down-nav-menu-header-label.profile-label {
        left: 6px; }
    .down-nav-menu-header i.check-bars-icon {
      font-size: 10px !important;
      left: 40px;
      position: absolute;
      top: 15px;
      text-shadow: -1px -1px 0 #1e1f22, 1px -1px 0 #1e1f22, -1px 1px 0 #1e1f22, 1px 1px 0 #1e1f22; }
    .down-nav-menu-header.poc-header {
      color: #00bce4;
      font-size: 12px;
      line-height: 36px;
      opacity: 1.0; }
      .down-nav-menu-header.poc-header i.fa.fa-bell {
        text-shadow: 0 0 5px #00bce4; }
      .down-nav-menu-header.poc-header.poc-steps-completed {
        color: var(--semantic-color-surface-base-primary); }
        .down-nav-menu-header.poc-header.poc-steps-completed i.fa.fa-bell {
          text-shadow: none; }
        .down-nav-menu-header.poc-header.poc-steps-completed.active, .down-nav-menu-header.poc-header.poc-steps-completed.reports-tab.reports-tab-selected {
          color: var(--semantic-color-surface-base-primary); }

  .down-nav-menu-panel, .search-nav-menu-panel {
    cursor: default; }

  .table-section {
    width: 100%;
    position: relative;
    overflow: hidden; }
    .table-section:last-child .table-section-body {
      padding-bottom: 0px; }
    .table-section .table-section-header {
      width: 100%;
      height: 44px;
      padding: 12px 16px;
      font-weight: 600;
      color:  var(--semantic-color-content-base-primary);
      background: #f3f3f3;
      box-shadow: 0px 0px 18px #cccccc; }
      .table-section .table-section-header .section-title {
        font-size: 16px;
        display: inline-block;
        vertical-align: middle;
        width: 50%; }
      .table-section .table-section-header .section-units {
        font-size: 13px;
        display: inline-block;
        vertical-align: middle;
        width: 50%;
        text-align: right; }
        .table-section .table-section-header .section-units .section-unit-icon {
          width: 16px;
          height: 16px;
          border-radius: 8px;
          font-size: 11px;
          background: var(--semantic-color-surface-base-primary);
          margin-right: 4px;
          text-align: center;
          background: #6b6d70;
          color: #f3f3f3;
          padding: 1px 0;
          display: inline-block;
          vertical-align: middle; }
        .table-section .table-section-header .section-units .section-unit-title {
          display: inline-block;
          vertical-align: middle; }
    .table-section .table-section-body {
      width: 100%;
      height: 100%;
      padding-top: 8px; }
      .table-section .table-section-body .table-rows-container {
        display: inline-block;
        vertical-align: middle; }
        .table-section .table-section-body .table-rows-container .table-row, .table-section .table-section-body .table-rows-container .multi-data-column-table-row {
          height: 92px;
          position: static;
          background: var(--semantic-color-surface-base-primary);
          line-height: 60px; }
          .table-section .table-section-body .table-rows-container .table-row:nth-child(2), .table-section .table-section-body .table-rows-container .multi-data-column-table-row:nth-child(2) {
            background: var(--semantic-color-content-interactive-primary-default); }
          .table-section .table-section-body .table-rows-container .table-row.threats, .table-section .table-section-body .table-rows-container .threats.multi-data-column-table-row {
            height: 204px;
            line-height: 172px; }
          .table-section .table-section-body .table-rows-container .table-row .table-row-cell, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell {
            display: inline-block;
            vertical-align: middle;
            height: 100%;
            border-right: 1px solid #f3f3f3;
            padding: 16px; }
            .table-section .table-section-body .table-rows-container .table-row .table-row-cell span, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell span {
              font-size: 13px; }
            .table-section .table-section-body .table-rows-container .table-row .table-row-cell.text, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell.text {
              color: var(--semantic-color-content-base-primary);
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap; }
            .table-section .table-section-body .table-rows-container .table-row .table-row-cell.percentage, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell.percentage {
              text-align: center; }
            .table-section .table-section-body .table-rows-container .table-row .table-row-cell.pieChart, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell.pieChart {
              padding: 0px;
              text-align: center; }
            .table-section .table-section-body .table-rows-container .table-row .table-row-cell.verticalBar, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell.verticalBar {
              text-align: center; }
            .table-section .table-section-body .table-rows-container .table-row .table-row-cell.orgAvg, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell.orgAvg {
              color: #009cda; }
            .table-section .table-section-body .table-rows-container .table-row .table-row-cell.peerAvg, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell.peerAvg {
              color: #9962c0; }
            .table-section .table-section-body .table-rows-container .table-row .table-row-cell.cloudAvg, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell.cloudAvg {
              color: #00539f; }
            .table-section .table-section-body .table-rows-container .table-row .table-row-cell .viz-type-container, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell .viz-type-container {
              width: 100%;
              height: 100%; }
              .table-section .table-section-body .table-rows-container .table-row .table-row-cell .viz-type-container.percentage, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell .viz-type-container.percentage {
                text-align: center;
                font-size: 36px;
              }
              .table-section .table-section-body .table-rows-container .table-row .table-row-cell .viz-type-container.pieChart, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell .viz-type-container.pieChart {
                width: 204px;
                height: 204px;
                margin: 0 auto; }
              .table-section .table-section-body .table-rows-container .table-row .table-row-cell .viz-type-container.legends, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell .viz-type-container.legends {
                width: 100%;
                height: 100%;
                line-height: normal; }
                .table-section .table-section-body .table-rows-container .table-row .table-row-cell .viz-type-container.legends .legend, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell .viz-type-container.legends .legend {
                  height: 33%;
                  padding: 20px 0; }
                  .table-section .table-section-body .table-rows-container .table-row .table-row-cell .viz-type-container.legends .legend .legend-icon, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell .viz-type-container.legends .legend .legend-icon {
                    width: 12px;
                    height: 12px;
                    border-radius: 6px;
                    margin-right: 8px;
                    display: inline-block;
                    vertical-align: middle; }
                  .table-section .table-section-body .table-rows-container .table-row .table-row-cell .viz-type-container.legends .legend .legend-text, .table-section .table-section-body .table-rows-container .multi-data-column-table-row .table-row-cell .viz-type-container.legends .legend .legend-text {
                    display: inline-block;
                    vertical-align: middle; }
        .table-section .table-section-body .table-rows-container.table-rowspan-container {
          display: inline-block;
          vertical-align: middle;
          height: 100%;
          padding: 16px; }
          .table-section .table-section-body .table-rows-container.table-rowspan-container .table-row, .table-section .table-section-body .table-rows-container.table-rowspan-container .multi-data-column-table-row {
            white-space: normal;
            height: 100%;
            line-height: normal; }
            .table-section .table-section-body .table-rows-container.table-rowspan-container .table-row .table-rowspan-cell, .table-section .table-section-body .table-rows-container.table-rowspan-container .multi-data-column-table-row .table-rowspan-cell {
              padding: 12px;
              border: 1px solid var(--semantic-color-border-base-primary);
              border-radius: 5px;
              color:  var(--semantic-color-content-base-primary);
              font-style: italic;
              position: relative; }
              .table-section .table-section-body .table-rows-container.table-rowspan-container .table-row .table-rowspan-cell span, .table-section .table-section-body .table-rows-container.table-rowspan-container .multi-data-column-table-row .table-rowspan-cell span {
                text-transform: none;
                line-height: 20px; }
              .table-section .table-section-body .table-rows-container.table-rowspan-container .table-row .table-rowspan-cell::after, .table-section .table-section-body .table-rows-container.table-rowspan-container .multi-data-column-table-row .table-rowspan-cell::after {
                content: '';
                position: absolute;
                left: -5px;
                top: calc(50% - 5px);
                width: 0;
                height: 0;
                border-top: 5px solid transparent;
                border-bottom: 5px solid transparent;
                border-right: 5px solid var(--semantic-color-surface-base-primary); }
              .table-section .table-section-body .table-rows-container.table-rowspan-container .table-row .table-rowspan-cell::before, .table-section .table-section-body .table-rows-container.table-rowspan-container .multi-data-column-table-row .table-rowspan-cell::before {
                content: '';
                position: absolute;
                left: -6px;
                top: calc(50% - 6px);
                width: 0;
                height: 0;
                border-top: 6px solid transparent;
                border-bottom: 6px solid transparent;
                border-right: 6px solid #e0e1e3; }

  .toggleTooltip:hover .rTooltip{
      display: block;
  }

  .rTooltip-container {
    max-height: 300px;
    max-width: 550px;
    overflow: auto;
    padding: 12px 16px;
    word-wrap: break-word;
  }

  .rTooltip {
    display: none;
    transform: translateX(5%) translateY(-105%);
    right: auto;
    bottom: auto;
    background: var(--semantic-color-background-primary);
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 3px;
    // box-shadow: 0 7px 7px -5px #939393;
    color: var(--semantic-color-content-base-primary);
    font-size: 13px;
    position: absolute;
    text-align: left;
    z-index: 10000;
  }

  .rTooltip.top-right::after {
    border-top-color: var(--semantic-color-surface-base-primary);
    border-bottom: none;
    bottom: -5px;
    left: 12px;
  }

  .rTooltip::after {
    content: "";
    border: 5px solid transparent;
    height: 0;
    position: absolute;
    width: 0;
  }
  .cursorHelp {
    cursor: help;
  }

  .rdg-wrapper .react-grid-Container{  
    overflow-y: auto!important;
    .react-grid-Main{       
      background: transparent!important; 
      .react-grid-Grid {
        background: transparent!important;
        border-radius: 0;
        border: 0;
        // min-height: 40em;
        .react-grid-Header {
          // background: transparent!important;
          background-color: var(--semantic-color-surface-table-header-default);
        }
        .react-grid-Canvas {
          // background: transparent!important;
          overflow-y: auto!important;       
        }
        .react-grid-HeaderRow {
          overflow: hidden;
          background: transparent;
          border: 0;
          .react-grid-HeaderCell {
            // border-top: 1px solid #ddd;
            border: none;
            display: flex;
            align-content: center;
            &:not(:first-of-type) {
              // border-right: 1px solid #ddd;
              border: none;
              border-top-right-radius: 5px;
              right: 0 !important;
              left: auto !important;
            }
            &:not(:last-of-type) {
              // border-left: 1px solid #ddd;
              border: none;
              border-top-left-radius: 5px;
            }
          }  
        }
      }
    }
    .react-grid-Cell {
      -webkit-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
      user-select: text;
      border-right: none;
      border-bottom: 1px solid var(--semantic-color-border-base-primary);
      background-color: var(--semantic-color-surface-table-row-default);
      color: var(--semantic-color-content-base-primary);
      &:first-child {
        // border-left: 1px solid #ddd;
        border-bottom: 1px solid var(--semantic-color-border-base-primary);
      }
      &:last-child {
        // border-right: 1px solid #ddd;
        border-bottom: 1px solid var(--semantic-color-border-base-primary);
      }
    } 
  }

  .rdg-wrapper .react-grid-HeaderCell{
    color: var(--semantic-color-content-base-primary);
    width: 100%;
    // border: .10px solid #e0e1e3;
    background-color: var(--semantic-color-surface-table-header-default);

    // color: #1E1F22;
    font-size: 13px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 16px;      
  }
  .rdg-wrapper  .line-chart-field-tooltip-container {
    max-width: 550px;
    min-width: 260px;
    overflow: hidden;
    padding: 12px 16px;
    word-wrap: break-word;
    text-transform: none;
    height: 240px;
    width:320px;
    left: calc(50% - 160px);
    top: calc(50% - 110px);
    position: absolute;
  }

  .rdg-wrapper .react-grid-HeaderCell .table-column-selector {
    border: 0px;
    width: 48px;
    .table-column-selector-button {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 50px;
    }
  }

  .rdg-wrapper .react-grid-Canvas {
    background-color: var(--semantic-color-surface-table-row-default);
  }

  .rdg-wrapper .react-grid-Row--odd > .react-grid-Cell{
    background-color: var(--semantic-color-surface-table-row-default);
    // min-height: 40px;
    outline: none;
  }

  .react-grid-Cell:focus {
    outline: none;
  }

  .react-grid-Cell  .cell-table-action {
    color: $anchor-color;
    cursor: pointer;
    text-align: center;
    &:hover {
      color: var(--semantic-color-content-accent-blue-secondary);
    }
    .disable-icon {
      color: $grey4;
    }
    .eye-icon, .pencil-icon, .delete-icon {
      color: var(--semantic-color-content-accent-blue-secondary);
      cursor: pointer;
    }
    .delete-icon{
      margin-left: 7px,
    }
  }

  .rdg-wrapper .rdg-cell-expand {
    float: left;
    display: table;
    height: 100%;
  }

  .rdg-wrapper .rdg-cell-expand span {
    border: solid var(--semantic-color-content-interactive-primary-default);
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 3px;
    margin-top: 12px;
    margin-right: 5px;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    font-size: 0px;
  }

  .rdg-wrapper .rdg-cell-expanded {
    float: left;
    display: table;
    height: 100%;
  }
  .rdg-wrapper .rdg-cell-expanded span {
    border: solid var(--semantic-color-content-interactive-primary-default);
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 3px;
    margin-top: 12px;
    margin-right: 5px;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    font-size: 0px;
  }

  .rdg-wrapper .rdg-cell-action{
    float: left;
  }

  .rdg-wrapper .rdg-cell-action .rdg-cell-action-button{
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: $anchor-color;
  }

  .subRows {
    background: var(--semantic-color-surface-table-row-default);
  }
  .subRows .react-grid-Cell .rdg-child-cell{
    background: var(--semantic-color-surface-table-row-default);
  }
  .rdg-wrapper .subRows .react-grid-Row--odd > .react-grid-Cell{
    background: var(--semantic-color-surface-table-row-default);
    outline: none;
  }
  .rdg-wrapper .subRows .react-grid-Row--even > .react-grid-Cell{
    background: var(--semantic-color-surface-table-row-default);
    outline: none;
  }

  .rdg-wrapper .rdg-child-row-action-cross {
    background: var(--semantic-color-surface-table-row-default);
  }
  .rdg-wrapper .rdg-child-row-action-cross::before{
    content: "\f148";
    font-family: 'FontAwesome';
    transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
    width: 0px; 
    height: 70%;
  }
  .rdg-wrapper .rdg-child-row-action-cross::after{
    width: 0px; 
    height: 70%;
  }
  .react-grid-HeaderCell-sortable {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .react-grid-HeaderCell-sortable.react-grid-HeaderCell-sortable--ascending > .pull-right {
    font-size:0px;
    float: right !important;
  }
  .react-grid-HeaderCell-sortable.react-grid-HeaderCell-sortable--ascending > .pull-right::after {
    font-size:11px;
    border-radius: 50%;
    border: 2px solid var(--semantic-color-content-interactive-primary-default) !important;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 11px;
    height: 11px;
    content: "\276f" !important;
    float: right !important;
    transform: rotate(270deg);
    color: var(--semantic-color-content-status-info-primary);
  }
  .react-grid-HeaderCell-sortable.react-grid-HeaderCell-sortable--descending > .pull-right {
    font-size:0px;
    float: right !important;
  }

  .react-grid-HeaderCell-sortable.react-grid-HeaderCell-sortable--descending > .pull-right::after {
    font-size:11px;
    border-radius: 50%;
    border: 2px solid var(--semantic-color-content-interactive-primary-default) !important;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 11px;
    height: 11px;
    content: "\276e" !important;
    float: right !important;
    color: var(--semantic-color-content-status-info-primary);
    transform: rotate(270deg);
  }

  .react-grid-HeaderCell-sortable > .pull-right::before {
    font-size:12px;
  }

  .react-grid-HeaderCell-sortable > .pull-right::after {
    font-size:9px;
    border-radius: 50%;
    border: 1px solid var(--semantic-color-border-base-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 13px;
    height: 13px;
    content: "\276e \276f" !important;
    float: right !important;
    color: #656666;
    transform: rotate(270deg);
  }

  .react-grid-HeaderCell-sortable--ascending:after {
    margin-left: 0.5em;
  }

  .rdg-menu-editor{
    display: none;
    position: absolute;
    background-color: var(--semantic-color-background-primary);
    // width: 17.8em;
    max-height: 50em;
    top: 0;
    right: 15px;
    margin-top: 3em;
    margin-right: 0em;
    border-radius: .3125rem;
    overflow-y: scroll;
    min-width: 15.625rem;
    .ui-sortable {
      padding-top: 3em;
    }
    .svg-inline--fa {

      color: var(--semantic-color-content-interactive-primary-default);
          vertical-align: middle;
          cursor: pointer; 
    }
  }

  .react-grid-HeaderRow {  
    border: 1px solid $grey12;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    div > .react-grid-HeaderCell--frozen {
      left: 0 !important;
    }  
  }
  .rdg-menu-editor .react-grid-HeaderCell{
    font-weight: normal;
    font-size: 10px;
    text-transform: uppercase;
  }

  .rdg-menu-editor .header {
    font-size: 11px;
      padding: 8px;
      cursor: default;
      background: var(--brand-default);
      color: var(--semantic-color-background-primary);
      border-radius: .3125rem .3125rem 0 0;
  }

  .rdg-menu-editor .table-form{
    padding: 10.5px 4px;
    left: 0;
    position: relative;
    cursor: move;
    &:hover {
      background-color: var(--semantic-color-surface-fields-hover);
    }
  }
  .table-form{
    white-space: nowrap; 
    .bars {
      float: right;
      margin-right: 1em;
    }
  }
  .rdg-menu-editor .react-grid-HeaderCell .react-grid-HeaderCell--frozen .rdg-row-actions-cell{
  width: none;
  }

  .rdg-menu-editor .rdg-row-index{
    color: var(--semantic-color-surface-base-primary);
  }
  .r-check-box{
    color: var(--semantic-color-content-interactive-primary-default);
    background: var(--semantic-color-background-primary);
    border-radius: 1px;  
    border: 1px solid var(--semantic-color-border-base-primary);
    opacity: 1;
  }
  .rdg-menu-editor .form-input {
    color: var(--semantic-color-content-base-primary);
    max-height: 28px;
    text-align: left;
    overflow: visible;
  }

  .rdg-menu-editor i.fa.fa-bars {
    position: absolute;
      right: 8px;
      top: 12px;
      font-size: 10px;
      color: #747272;
  }
  .noLi {
    list-style-type: none;
    white-space: nowrap; 
    overflow: hidden;
    text-overflow: ellipsis;
    width: fit-content;
    margin-right: 25px;
  }

  .showComponent {
    display: inline;
    z-index: 48;
  }
  .ecui-sortable-handle {
    z-index: 99;
  }

  .hideMe{
    display: none;
  }

  .rdg-menu-editor .table-column-menu-header-text {
    color: var(--semantic-color-background-primary);
    font-size: 13px;
    width: calc(100% - 13px);
    display: inline-block;
    vertical-align: middle;
    text-align: left;
    min-width: 15.625rem;
      padding: .8rem .8rem .5rem;
    &.control-button-reports {
      color: var(--semantic-color-content-interactive-primary-default);
      cursor: pointer;
      font-size: 13px;
      margin-right: 32px;
      position: relative;
      // top: 5px;
      // overflow: hidden;    
      text-overflow: ellipsis;
      white-space: nowrap;
      border: none;
      background: none;
      &:hover {
        color: var(--semantic-color-content-interactive-primary-hover);
      }
  }
  }

  .rdg-menu-editor .table-column-menu-header i.fa {
    color: var(--semantic-color-content-interactive-primary-default);
    vertical-align: middle;
    cursor: pointer;
  }
  .rdg-menu-editor .table-column-menu-body{
    background-color: var(--semantic-color-background-pale);
  }
  .rdg-menu-editor .table-column-menu-footer {
    display: inline-flex;
    &.actions {
      background: var(--brand-default);
      border-radius: .3125rem .3125rem 0 0;
      font-size: 11px;
      padding: 8px;
      cursor: default;        
      display: flex;
      &.table-column-menu-footer {
        border-radius: 0 0 .3125rem .3125rem;
        box-shadow: var(--semantic-color-border-base-primary) 0 .125rem .325rem;
        background: var(--semantic-color-surface-base-primary);          
        padding: .3125rem;
        .table-column-menu-footer-buttons {
          width: 100%;
          display: flex;
          justify-content: space-between;
        }
        button.cancel {
          background-color: transparent;
          border: none;
          border-radius: .3125rem;
          color: var(--semantic-color-content-interactive-primary-default);
          font-size: .8125rem;
          padding: .375rem .9375rem;
        }
      }
      &.action {
        color: var(--semantic-color-content-status-info-primary);
        cursor: pointer;
        font-size: 13px;
        margin-right: 25px;
        position: static;
        top: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:last-child {
          margin-right: 0;
        }

        & .action-icon {
          font-size: 16px;
          margin-right: 5px;
          position: relative;
          top: 1px;
        }
      }
    }
  }

  .noselect {
    -webkit-touch-callout: none; /* iOS Safari */
      -webkit-user-select: none; /* Safari */
      -khtml-user-select: none; /* Konqueror HTML */
        -moz-user-select: none; /* Firefox */
          -ms-user-select: none; /* Internet Explorer/Edge */
              user-select: none; /* Non-prefixed version, currently
                                    supported by Chrome and Opera */
                  outline: none;
  }

  .preventEvent {
    pointer-events: none;
  }

  .table-header-row-search-button {
    position: relative;
    float: right;
    margin-right: 4px;  
  }
  .table-header-row-search-button-selected {
    position: relative;
    float: right;
    margin-right: 4px;  
    color: var(--semantic-color-content-interactive-primary-default);
  }
  .table-header-row-search-input{
    position: absolute;
    z-index: 9999;
  }

  .table-header-row-box-container {
    width: 350px;
    z-index: 2;
    position: fixed;
    // box-shadow: 0 0 8px 0 rgba(42,44,48,0.25);
    border-radius: 5px;
    .search-container {
      // display: inline-block;
      vertical-align: middle;
      padding: 8px 12px;
    }
  }


  .insights-container .__react_component_tooltip {
    padding: 0px;
  }
  .table-expand-cell {
    padding: 0;
    width: 19px;
    top:0 !important;
  }

  .fa-sliders-up {
  font-size: 13px;
  font-weight: 900;
  line-height: 20px;
  letter-spacing: 0em;
  text-align: center;
  }
}
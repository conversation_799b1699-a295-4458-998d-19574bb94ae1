@import 'scss/colors.scss';
@import 'scss/mixins.scss';

.ec-root-page {
.multi-select-footer {
  background: var(--semantic-color-content-inverted-base-secondary);
  height: 40px;
  line-height: 40px;
  font-size: 12px;
  padding: 0 10px;
  position: absolute;
  width: 100%;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  button {
    padding: 6px 7px;
    line-height: 12px;
  }

  .left-side {
    float: left;

    .done-button {
      font-size: inherit;
    }
  }

  .right-side {
    float: right;

    button {
      border: none;
      color: var(--semantic-color-content-interactive-primary-default);
      background: inherit;
      padding: 0 7px 0;

      &:nth-child(1) {
        border-radius: 1px solid var(--semantic-color-border-base-primary);
        padding-left: 0;
      }
      &:nth-child(2) {
        padding-right: 0;
      }
    }
  }
}

.multi-select-drop-down-container {
  width: 100%;
  .multi-select-drop-down {
    width: 100%;
    position: relative;
    min-width: 150px;
    .multi-select-drop-down-selection {
      @include DisplayFlex;
      cursor: pointer;
      justify-content: space-between;
      align-items: center;
      min-height: 30px;
      min-width: 150px;
      border-bottom: 1px solid var(--semantic-color-border-base-primary);

      .selection-icon, .selected-label {
        color: var(--semantic-color-content-interactive-primary-default);
      }

      .selection-icon {
        .fa-angle-down, .fa-angle-up {
          font-size: 19px;
        }
      }
    }

    .multi-select-drop-down-modal-parent {
      border: 1px solid var(--semantic-color-border-base-primary);
      border-radius: 3px;
      position: absolute;
      width: 100%;
      min-width: 100%;
      resize: both;
      overflow: auto;
      min-height: 180px;
      height: 180px;
      background-color: var(--semantic-color-background-primary);
      z-index: 9;
      .multi-select-drop-down-modal-cont {
        .no-data{
          text-align: center;
          padding-top: 20px;
          color: $grey1;
        }
        & {
          position: relative;
          width: 100%;
          height: 100%;
        }

        .modal-header {
          background-color: var(--semantic-color-background-primary);
          color: var(--semantic-color-content-base-primary);
          padding-bottom: 15px;
          line-height: 20px;
          font-size: 12px;

          .dropdown-search {
            @include DisplayFlex;
            margin: 0 10px;
            border-bottom: 1px solid var(--semantic-color-border-base-primary);
            justify-content: space-between;
            padding: 5px 0;
            position: relative;
            input {
              border: unset;
              outline: none;
              background: var(--semantic-color-content-inverted-base-secondary);
              width: 100%;
            }
            span {
              position: absolute;
              right: 0;
            }
            .remove-button,
            .clear-button {
              border: none;
              background: unset;
              outline: none;
              color: var(--semantic-color-content-interactive-primary-default);
            }
            .clear-button {
              margin-right: 5px;
            }
          }

        }
        .no-data {
          text-align: center;
        }
        .show-more-container {
          padding: 10px;
          .show-more {
            font-size: inherit;
          }
        }
        .load-more {
          color: var(--semantic-color-content-base-primary);
          line-height: 16px;
          margin: 0 auto;
          border: none;
          background: none;
          display: block;
          padding: 5px;
          font-size: 12px;
        }
      }
    }

    &.error-container {
      color: var(--semantic-color-content-status-danger-primary);
      padding-top: 2px;
    }

    &.error {
      input {
        border-color: var(--semantic-color-border-status-danger-active);
      }
    }
  }

  &.error {
    .multi-select-drop-down-selection {
      border-color: var(--semantic-color-border-status-danger-active);

      .fa-angle-down, .fa-angle-up {
        color: var(--semantic-color-content-status-danger-primary);
      }
    }
  }
}

.modal-body-cont {
  height: calc(100% - 80px);
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 40px;

  .multi-select-drop-down-modal {
    @include DisplayFlex;
    min-height: 90px;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding-bottom: 10px;

    .option-item-wrapper {
      width: 100%;
      word-break: break-all;
    }

    .option-item {
      padding: 2.5px 10px;

      .modal-item {
        display: block;
        position: relative;
        padding-left: 25px;
        cursor: pointer;
        font-size: 13px;
        color: var(--semantic-color-content-interactive-primary-default);
        min-width: 200px;
        max-width: 100%;
        line-height: 20px;

        input {
          position: absolute;
          opacity: 0;
          cursor: pointer;
          height: 0;
          width: 0;

          &:checked~.checkmark {
            background-color: var(--semantic-color-content-interactive-primary-default);
           span {
              display: block;           
            }
          }
        }

        .checkmark {
          position: absolute;
          top: 2.5px;
          left: 0;
          height: 19px;
          width: 19px;
          border: 1px solid var(--semantic-color-content-base-primary);
          border-radius: 3px;

          span {
            position: absolute;
            display: none;
            left: 4px;
            top: 0;
            width: 8px;
            height: 13px;
            border: solid var(--semantic-color-content-base-primary);
            border-width: 0 2.5px 2.5px 0;
            transform: rotate(45deg);
          }
        }
      }

      &.item-disabled {
        span {
          color: $grey10;
          &.checkmark {
            border-color: $grey10;
          }
        }
      }
    }

    &.hierarchy-mode {
      display: block;
      padding: 0;

      .option-item.parent {
        background: $grey12;

        label {
          width: 100%;

          span.label-text {
            color: $grey4;
            font-weight: 700;
          }
        }
      }

      .children {
        padding-left: 20px;
      }
    }
  }
}

.selected-values {
  @include DisplayFlex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  align-items: center;

  .selected-value {
    @include DisplayFlex;
    margin-right: 3px;
    cursor: unset;
    color: $white;
    border: none;
    padding: 5px 8px;
    border-radius: 5px;
    background-color: var(--semantic-color-content-interactive-primary-default);
    box-shadow: 0 2px 10px 0 $blueShadow1;
    line-height: 10px;

    .fa-times {
      cursor: pointer;
      margin-left: 10px;
      display: block;
    }
  }
}
}
@import "scss/colors.scss";

.ec-root-page {
.common-dropdown-wrapper {
  .common-dropdown {
    padding: 8px 8px 4.8px 8px;
    color: #3a587c;
    font-weight: 500;
    border-radius: 5px;
    cursor: pointer;
  }

  .dropdown-closed {
    text-align: start;
    border: 1px solid var(--semantic-color-border-base-primary);
    background: var(--semantic-color-surface-base-primary);
  }

  .all-values-sub-title, .selected-sub-title {
    color:  var(--semantic-color-content-base-primary);
  }
  .text-muted {
    color: #73818f;
  }

  .errorBorder {
    border: 1px solid var(--semantic-color-border-status-danger-active) !important;
  }

  .disabledBorder {
    border: none !important;
    cursor: not-allowed;
  }

  .text-disabled {
    color: #a59f9f !important;
  }

  .dropdown-opened {
    border: 1px solid  var(--semantic-color-border-base-primary);
    text-align: start;
    background-color: var(--semantic-color-surface-base-primary);
  }

  .waiting-loader .waiting-spinner {
    height: 16px !important;
    width: 16px !important;
    border: 4.8px solid var(--semantic-color-border-base-primary);
    border-top-color: #5c76ab;
    margin-top: 8px !important;
    margin-bottom: 4.8px !important;
  }

  .dropdown-text-overflow {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 95%;
    display: inherit;
  }

  .dropdown-multiselect-wrapper {
    position: absolute;
    display: flex;
    flex-direction: column;
    border-radius: 5px;
    background-color: var(--semantic-color-surface-base-primary);
    box-shadow: 0 4px 18px 0 rgba(176, 186, 197, 0.5);
    z-index: 1000;
  }

  .dropdown-select-all-container {
    border-bottom: solid 1px #e3e5e6;
  }

  .selected-value {
    background-color: #e6f7ff !important;
    font-weight: 600;
  }

  .option-row-dropdown {
    display: flex;
    margin: 4.8px 6.4px;
    align-items: center;
    color: var(--semantic-color-content-status-info-primary);
    padding: 1.6px 4px;
    font-size: small;

    .dropdown-radiobox {
      margin-right: 10px !important;
      accent-color: var(--semantic-color-content-interactive-primary-default) !important;
    }

    input[type='checkbox'] {
      margin: 8px;
      margin-right: 11.2px;
      accent-color: var(--semantic-color-content-interactive-primary-default);
      outline: 1.4px solid var(--semantic-color-content-interactive-primary-default);
      outline-offset: -1px;
      &:not(:checked) {
        appearance: none;
        margin-bottom: 0;
        width: 1em;
        height: 1em;
        border: 1px solid var(--semantic-color-content-interactive-primary-default);
        background-color: var(--semantic-color-background-primary);
      }
    }
    input[type='checkbox'] {
      &:indeterminate {
        content: '';
        display: block;
        color: var(--semantic-color-content-interactive-primary-default);
        width: 13px;
        height: 13px;
        background-color: var(--semantic-color-surface-base-primary);
        &::before {
          content: '';
          display: block;
          width: 13px;
          height: 13px;
          background-color: var(--semantic-color-surface-base-primary);
        }
        &::after {
          content: '';
          display: block;
          width: 9px;
          height: 10px;
          border: solid var(--semantic-color-border-base-primary);
          border-width: 2px 0 0 0;
          position: absolute;
          margin: -7px 2px;
        }
      }
    }
    label {
      flex-grow: 1;
    }
  }

  .option-text {
    font-weight: 600;
  }

  .dropdown-noOptions {
    text-align: center;
    margin-bottom: 4%;
    font-size: small;
    margin-top: 4%;
  }

  .dropdown-options-container {
    max-height: 250px;
    overflow-y: scroll;
  }

  .dropdown-search-input {
    background: var(--semantic-color-background-primary);
    border-radius: 5px;
    margin: 8px 8px 0px 8px;
    max-width: 368px;
    padding: 4.8px;

    ::placeholder {
      /* Chrome, Firefox, Opera, Safari 10.1+ */
      color: #73808f;
      opacity: 1; /* Firefox */
    }
    ::-ms-input-placeholder {
      /* Microsoft Edge */
      color: #73808f;
    }

    .dropdown-search-inputer {
      background: var(--semantic-color-background-primary);
      outline: none;
      border: none;
    }

    .dropdown-search-icon {
      color: var(--semantic-color-content-base-secondary);
      margin-right: 4px;
      margin-left: 4px;
    }
  }

  .dropdown-footer {
    display: flex;
    align-items: center;
    background-color: var(--semantic-color-background-pale);
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    position: relative;

    button {
      padding: 8px 12px;
      font-weight: 500;
      background-color: transparent;
      border: none;
      outline: none;
      color: var(--semantic-color-content-status-info-primary);
      cursor: pointer;
    }
  }

  // filter

  .filter-dropdown {
    line-height: initial;
    margin: 0 2.5px;
    padding: 6px 13px;
    color: var(--semantic-color-content-status-info-primary);
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 999px;
    background-color: #f2faff;
    font-weight: 600;
    cursor: pointer;
    margin-right: 5px;
    margin-bottom: 5px;
  }
  .filter-dropdown-selected {
    margin: 0 2.5px;
    padding: 6px 13px;
    color: white;
    border: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 999px;
    background-color: var(--semantic-color-content-status-info-primary);
    font-weight: 500;
    cursor: pointer;
    margin-right: 5px;
    margin-bottom: 5px;
  }
  .key-text {
    color: var(--semantic-color-content-base-primary);
  }
  .key-text-selected {
    color: var(--semantic-color-content-base-primary);
  }

  .pill-selected {
    background-color: var(--semantic-color-content-interactive-primary-default) !important;
    font-weight: 500;
    .key-text-selected {
      color: var(--semantic-color-content-base-primary) !important;
    }
  }

  .pill-with-values {
    border: 1px solid var(--semantic-color-content-interactive-primary-default) !important;
    background-color: var(--semantic-color-background-primary) !important;
    color: var(--semantic-color-content-immutable-white) !important;
    .key-text {
      color: var(--semantic-color-content-base-primary);
    }
  }

  .default-pill {
    line-height: initial;
    color: var(--semantic-color-content-base-primary);
    background-color: var(--semantic-color-surface-base-primary);
    font-weight: 400;

    &:active, &.focus {
      background-color: var(--semantic-color-content-interactive-primary-default);
      color: var(--semantic-color-content-immutable-white);
      border: 1px solid var(--semantic-color-border-base-primary) !important;
      .key-text {
        color: var(--semantic-color-content-base-primary);
      }
    }
    svg {
      color: var(--brand-strong);
    }
  }

  .common-pill {
    color: var(--semantic-color-content-accent-blue-default) !important;
    border: 1px solid var(--semantic-color-content-interactive-primary-default);
    margin: 0 2.5px;
    padding: 6px 13px;
    border-radius: 999px;
    cursor: pointer;
    margin-right: 5px;
    margin-bottom: 5px;
  }
}

.default-new-filter-dropdown-Wrapper {
  position: absolute;
  display: flex;
  flex-direction: column;
  border-radius: 0.5rem;
  max-width: 521px;
  background-color: var(--semantic-color-surface-base-primary);
  border: 1px solid var(--semantic-color-border-base-primary);
  z-index: 1000;
  min-width: 521px;

  &.wrapper-height {
    height: 484px !important;
  }

  &.width-no-options {
    height: 90px !important;
  }

  .default-filter-dropdown-container {
    display: flex;
    border-top: 1px solid var(--semantic-color-border-base-primary);
    border-radius: 0.5rem;

    &.no-options-wrapper {
      justify-content: center;
      padding-top: 13px;
    }
  }

  .default-filter-dropdown-all {
    width: 258px !important;
    height: 433px !important;
    border-right: 1px solid var(--semantic-color-border-base-primary);
  }

  .display-flex {
    display: flex;
    flex-wrap: wrap;
  }

  .default-filter-dropdown-selected {
    width: 258px !important;
    height: 433px !important;
    background-color: var(--semantic-color-surface-base-primary) !important;
  }

  .calendar-container::before,
  .single-date-picker::before {
    left: 84px !important;
  }

  .horizontal_line_filter {
    border: 0.5px solid var(--semantic-color-border-base-primary);
    padding-left: 0px !important;
  }

  .waiting-loader .waiting-spinner {
    height: 20px !important;
    width: 20px !important;
    border: 3px solid var(--semantic-color-border-base-primary);
    border-top-color: var(--semantic-color-border-base-primary);
  }

  .waiting-loader {
    left: 0% !important;
  }

  .loading-class {
    display: flex !important;
    justify-content: center !important;
  }

  .filters-width {
    width: 258px !important;
  }

  .search-input {
    background-color: var(--semantic-color-background-pale);
    border-radius: 0.5rem;
    padding: 0;
    margin: 7.5px;
    max-width: 5050px;
    height: 32px;
    padding: 7.5px 5px;
    border: 1px solid var(--semantic-color-border-base-primary);


    ::placeholder {
      /* Chrome, Firefox, Opera, Safari 10.1+ */
      color: #73808f;
      opacity: 1; /* Firefox */
    }
    ::-ms-input-placeholder {
      /* Microsoft Edge */
      color: #73808f;
    }

    .search-inputer {
      background-color: transparent;
      // line-height: 1.875rem;
      color: var(--semantic-color-content-base-primary);
      outline: none;
      border: none ;
      margin-left: 25px;
      width: 470px !important;
    }

    .search-icons {
      color: var(--semantic-color-content-base-secondary);
      // margin-top: 2px;
      margin-left: 5px;
      position: absolute;
    }
  }

  .select-all-container {
    border-bottom: solid 1px var(--semantic-color-border-base-primary);
    padding: 4px;
  }

  .options-container {
    padding-top: 5px;
    padding-bottom: 5px;
    height: 393px;
    overflow-y: auto;

    .no-matching-options {
      padding-left: 50px;
      padding-top: 10px;
      font-style: italic;
      color: var(--semantic-color-content-status-info-primary);
    }
  }

  .selected {
    background-color:  var(--semantic-color-surface-base-primary) !important;
    font-weight: 600;
  }

  .single-select {
    padding-left: 5px;
    padding-bottom: 7.5px;
  }

  .option-row {
    display: flex;
    margin: 1px 5px;
    align-items: center;
    padding: 2px;
    color: var(--semantic-color-content-interactive-primary-default);
    padding: 7.5px 5px;

    input[type='checkbox'] {
      margin: 5px;
      margin-right: 8px;
      accent-color: var(--semantic-color-content-interactive-primary-default);
      &:not(:checked) {
        appearance: none;
        margin-bottom: 0;
        width: 1em;
        height: 1em;
        border: 1px solid var(--semantic-color-content-interactive-primary-default);
        background-color: var(--semantic-color-background-primary);
      }
      // outline: 0.5px solid #1D5F99;
    }
    input[type='checkbox'] {
      &:indeterminate {
        content: '';
        display: block;
        color: var(--semantic-color-content-base-primary);
        width: 13px;
        height: 13px;
        background-color:var(--semantic-color-surface-base-primary);
        &::before {
          content: '';
          display: block;
          width: 13px;
          height: 13px;
          background-color: var(--semantic-color-surface-base-primary);
        }
        &::after {
          content: '';
          display: block;
          width: 9px;
          height: 10px;
          border: solid var(--semantic-color-border-base-primary);
          border-width: 2px 0 0 0;
          position: absolute;
          margin: -7px 2px;
        }
      }
    }
    label {
      flex-grow: 1;
      overflow-x: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .default-filter-footer {
    display: flex;
    align-items: center;
    background-color: var(--semantic-color-background-primary);
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    position: relative;
    border-top: 1px solid var(--semantic-color-border-base-primary);
    position: absolute;
    width: 258px;
    left: 261px;
    height: 40px;

.default-new-filter-dropdown-Wrapper .default-filter-footer {
    display: flex;
    align-items: center;
    background-color: var(--semantic-color-background-primary);
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    /* padding-bottom: 3%; */
    /* padding-top: 3%; */
    position: relative;
    border-top: 1px solid var(--semantic-color-border-base-primary);
    /* box-shadow: 0px -1px 4px rgba(0, 0, 0, 0.1) !important; */
    position: absolute;
    width: 260px;
    left: 260px;
    height: 40px;
}




    button {
      padding: 5px 7.5px;
      font-weight: 500;
      background-color: transparent;
      border: none;
      outline: none;
      color: var(--semantic-color-content-interactive-primary-default);
      cursor: pointer;
      &:hover {
        color: var(--semantic-color-content-interactive-primary-hover);
      }
    }
    .submit {
      color: var(--semantic-color-content-inverted-base-primary);
      background-color: var(--semantic-color-content-interactive-primary-default);
    }
  }

  .justify-content-between {
    justify-content: space-between;
  }

  .justify-content-center {
    justify-content: center;
  }

  .show-more {
    font-weight: 500;
    background-color: transparent;
    color: var(--semantic-color-content-interactive-primary-default);
    border: none;
    margin-left: 33%;
  }

  .option-text {
    font-weight: 500;
  }

  .noOptions {
    text-align: center;
    margin-bottom: 2%;
    margin-top: 2%;
  }

  .tabs {
    display: flex;
    padding: 8px 8px 0  8px;
    min-height: 40px;

    button {
      flex-grow: 1;
      background: var(--semantic-color-background-primary);
      border-radius: 5px;
      color:  var(--semantic-color-content-base-primary);
      outline: none;
      border: none;
      margin: 0 0;

      &.active {
        background-color: var(--semantic-color-content-status-info-primary);
        color: var(--semantic-color-content-immutable-white);
      }
    }
  }
}
}
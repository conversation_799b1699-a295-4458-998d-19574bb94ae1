.ec-root-page {
.time-filter {
  display: inline-block;
  height: 55px;
  width: 100%;   

  .time-filter-label {
    line-height: 26px;
  }

  .time-filter-dropdown {
    width: 100%;
    .drop-down-selected-value {
      box-shadow: 0 0px 0 var(--semantic-color-content-interactive-primary-default);
      .dropdown-icon {
        float: right;
      }
    }
  }
  
}
div.time-filter-dropdown {
  div.drop-down-container {
    position: relative;
    width: 100%;
    display: inline-block;
    color: var(--semantic-color-content-interactive-primary-default);
    button {
      color: inherit;
      background: inherit;
      border: none;
      border-radius: inherit;
    }
    button.drop-down-selected-value {
      display: -webkit-box; /* OLD - iOS 6-, Safari 3.1-6 */
      display: -moz-box; /* OLD - Firefox 19- (buggy but mostly works) */
      display: -ms-flexbox; /* TWEENER - IE 10 */
      display: -webkit-flex; /* NEW - Chrome */
      display: flex;
      width: 100%;
      align-items: center;
      // box-shadow: 0 1px 0 var(--semantic-color-content-interactive-primary-default);
      color:  var(--semantic-color-content-base-primary);
      cursor: pointer;
      // display: block;
      height: 28px;
      padding: 5px 8px 6px 0px;
      text-align: left;
      font-size: 13px;
      .drop-down-selected-value-label {
        text-overflow: ellipsis;
        overflow: hidden;
        min-width: 96%;
        padding: 0 8px;
      }
      .label-selected-items {
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 110px;
        overflow: hidden;
        display: inline-block;
      }
      .dropdown-selected-label {
        white-space: nowrap;
        width: 185px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
      }
      .dropdown-icon {
        float: right;
      }
      span {
        flex: 0.9;
      }
      img {
        flex: 0.1;
        width: 20px;
      }
      .startDate,
      .endDate {
        display: inline-block;
      }
    }
    button.dropdown-read-only {
      color: gray;
      box-shadow: none;
      cursor: default;
    }
    button.add-filter-value {
      box-shadow: none;
      .fa-plus-circle {
        padding-right: 5px;
        color:  var(--semantic-color-content-base-primary);
        font-size: 14px;
      }
      .dropdown-icon {
        float: none;
      }
    }
    .dropdown-box {
      .drop-down-list {
        position: absolute;
        margin-top: -1px;
        background-color: var(--semantic-color-surface-base-primary);
        min-width: 100%;
        max-width: calc(100% + 100px);
        // height: 430px;
        border: 1px solid var(--semantic-color-content-interactive-primary-default);
        visibility: hidden;
        opacity: 0;
        transition: visibility 0s, opacity 0.1s linear;
        z-index: 9;
        .dropdown-list-header {
          height: 40px;
          background: #f3f3f3;
          .dropdown-search {
            border-bottom: 1px solid var(--semantic-color-content-interactive-primary-default);
            width: 90%;
            padding: 11px 0 4px 0;
            margin: 0 2px 0 6px;
            .dropdown-input-search {
              width: 77%;
              font-size: 13px;
              border: 0px;
              background: transparent;
            }
            .search-icon {
              cursor: pointer;
              font-size: 12px !important;
              float: right;
            }
          }
        }
        .dropdown-list-content {
          // max-height: 200px;
          overflow-y: auto;
          .dropdown-list-item {
            border-radius: inherit;
            width: calc(100% - 1px);
            margin: 0 auto;
            padding: 0px;
            button {
              padding: 8px;
              width: 100%;
              cursor: pointer;
              text-align: left;
            }
          }
          .dropdown-list-item-parent {
            color: #85878a;
            font-size: 12px;
          }
          .dropdown-list-item-parent:hover {
            background: transparent;
            color: var(--semantic-color-background-pale);
          }
          .dropdown-selected-value {
            background-color: var(--semantic-color-content-interactive-primary-default);
            color: var(--semantic-color-background-primary);
          }
          .dropdown-list-item.selected {
            background: var(--semantic-color-content-interactive-primary-default);
          }
        }
        .dropdown-list-footer {
          height: 46px;
          border-top: 1px solid #f3f3f3;
          padding-top: 10px;
          background: #f3f3f3;
          font-size: 13px;
          .dropdown-action-done {
            float: left;
            margin-left: 6px;
            border: 1px solid var(--semantic-color-content-interactive-primary-default);
            border-radius: 4px;
            padding: 4px 16px;
            color: white;
            background: var(--semantic-color-content-interactive-primary-default);
            cursor: pointer;
          }
          .dropdown-action-cancel {
            float: right;
            margin-right: 6px;
            color: var(--semantic-color-content-interactive-primary-default);
            margin-top: 5px;
            font-size: 12px;
            cursor: pointer;
          }
        }
        .multiselect-dropdown-list-content {
          padding: 8px;
          max-height: 200px;
          overflow-y: auto;
          .multiselect-dropdown-list-item {
            height: 28px;
            line-height: 28px;
            white-space: nowrap;
            .multi-select-list-item-checkbox {
              font-size: 16px;
            }
            .multi-select-list-item-value {
              padding: 0 10px;
              font-size: 13px;
            }
          }
        }
      }
      .add-filter-list {
        border: 1px solid var(--semantic-color-border-base-primary);
      }
    }
    .dropdown-box .drop-down-list.open {
      visibility: visible;
      opacity: 1;
      z-index: 1000;
    }
    .dropdown-box .drop-down-list.open-custom {
      visibility: visible;
      opacity: 1;
      z-index: 1000;
    }
  }
  .dropdown-input-filter {
    display: inline-block;
    width: 115px;
    font-size: 13px;
    border: 0px;
    border-bottom: 1px solid var(--semantic-color-content-interactive-primary-default);
    padding-bottom: 5px;
    background: transparent;
    margin-left: 6px;
  }
  .multi-select-filters {
    width: 200px;
    display: inline-block;
    width: 140px;
    margin-left: 6px;
  }
  div.dropdown-half-width {
    width: 160px;
  }
  .remove-dropdown-filter-icon {
    margin-left: 10px;
    font-size: 14px !important;
    color: #939393;
    cursor: pointer;
  }
  .error-input-icon {
    margin-left: 5px;
    cursor: pointer;
    color: var(--semantic-color-content-status-danger-primary);
    font-size: 14px !important;
  }
  

  .dropdown-icon {
    display: inline-block;
    font-size: 16px !important;
    text-align: center;
    padding-left: 10px;
    vertical-align: top;
    width: 20px;
  }

}
}

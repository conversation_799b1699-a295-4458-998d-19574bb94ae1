.table-filter-container {
  width: 25px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;

  .icon-container {
    position: relative;

    color: #747272;
    font-size: 13px !important;
    font-weight: bold;

    cursor: pointer;

    & .active {
      color: var(--semantic-color-content-status-info-primary);
      font-weight: bold;
    }

    & .applied-indicator {
      position: absolute;
      left: 4px;
      top: 3px;
      font-size: 10px;
      color: #D13932;
    }
  }

  .table-search-container {
    position: fixed;
    z-index: 99;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    background-color: var(--semantic-color-background-pale);

    cursor: auto;


    & .input-container {
      background: var(--semantic-color-background-pale);
      margin-right: 0;
      display: flex;

      & .table-search-input {
        display: inline-block;
        font-size: 13px;
        height: 28px;
        padding: 6px 26px 6px 0;
        vertical-align: middle;
        width: 180px;
      }
    }

    & .clear-container{
      margin-left: 4px;
    }
  }
}

@import "scss/colors.scss";
@import 'scss/mixins.scss';

.ec-root-page {
.app-table-container {
  margin-top: 30px;
  .title {
    color: $grey4;
    font-size: 16px;
    letter-spacing: .5px;
    margin-bottom: 12px;
  }

  .ReactTable.app-table {
    border-radius: 5px 5px 0 0;
    border: 1px solid $grey12;

    .rt-thead {
      border-bottom: 1px solid $grey12;
      border-radius: 5px 5px 0 0;
      box-shadow: none;
      background-color: var(--semantic-color-content-inverted-base-secondary);
      min-height:  39px;

      .rt-th {
        padding: 6px 12px;
        box-shadow: none;
        line-height: 28px;
        .rt-resizer {
          width: 10px;
          right:  -7px;
        }
      }

      .header-simple {
        position: relative;
        text-align: left;
        display: block;
        background: inherit;
        width: 100%;
        height: 100%;
        line-height: inherit;
        border: none;
        outline: 0;
        font-size: 14px;
        .sorting-icon-cont {
          position: absolute;
          right: 0;
          top: 5px;
          font-size: 10px;

          .sorting-arrow-up,
          .sorting-arrow-down {
            color: $grey10;
            line-height: 1px;
          }
        }
      }
      .header-title {
        position: relative;
        text-align: left;
        display: block;
        background: inherit;
        width: 100%;
        height: 100%;
        line-height: inherit;
        border: none;
        outline: 0;

        .sorting-icon-cont {
          position: absolute;
          right: 0;
          top: 5px;
          font-size: 10px;

          .sorting-arrow-up,
          .sorting-arrow-down {
            color: $grey10;
            line-height: 1px;
          }
        }
      }

      .rt-th.-sort-asc .sorting-arrow-up {
        color: var(--semantic-color-content-interactive-primary-default);
      }

      .rt-th.-sort-desc .sorting-arrow-down {
        color: var(--semantic-color-content-interactive-primary-default);
      }
    }

    .rt-tbody {
      .rt-tr-group {
        .rt-tr {
          @include DisplayFlex;

          &.-odd {
            background: $white;
          }

          &.-even {
            background: var(--semantic-color-content-inverted-base-secondary);
          }

          .rt-td {
            min-height: 40px;
            padding: 0 18px;
            align-self: center;
            border-right: .5px solid $white;
            align-items: center;
            @include DisplayFlex;

            &.rt-expandable {
              justify-content: center;
            }

            &.rt-pivot,
            &:last-child {
              border-right: none;
            }

            &.rt-expandable {
              width: 100%;
              padding: 0;
            }
          }
        }
      }
    }

    .pagination-bottom {
      .-pagination {
        box-shadow: none;
        border-top: 1px solid $grey12;
      }
    }

    &.has-nested-true .rt-thead {
      .rt-th:nth-child(1) {
        display: none;
      }

      .rt-th:nth-child(2) {
        width: 130px !important;
      }

    }

    .rt-noData {
      padding: 0;
      top:  calc(50% + 2rem);
    }
  }
}

.ReactTable.app-table {
  .cell-table-expander {
    cursor: inherit;
    padding: 0;
    text-align: center;
    user-select: none;
    font-size: 15px;
    font-weight: 600;
    color: var(--semantic-color-content-interactive-primary-default);

    .icon-cont {
      height: 17px;
      width: 17px;
      background: $blue9;
      border-radius: 50%;
      margin: 0 auto;

      .svg-inline--fa {
        bottom: 2px;
        position: relative;
        text-align: center;
        vertical-align: middle;

        &.fa-angle-right {
          left: 1px;
        }
      }
    }
  }

  .cell-table-link {
    color: var(--semantic-color-content-interactive-primary-default);
    .svg-inline--fa {
      margin-right: 7px;
    }
    a {
      color: inherit;
      text-decoration: none;
    }
  }

  .cell-table-text-list {
    max-width: 100%;

    .icon-cont.elipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: var(--semantic-color-content-interactive-primary-default);
      font-size: 20px;
    }
    .icon-cont.elipsis>span {
      max-width: 100%;
      color: $black;
      font-size: 13px;
    }
  }

  .cell-table-ux-score {
    @include DisplayFlex;
    align-items: center;
    .score-number {
      margin-right: 5px;
      color: $grey15;
    }

    .score-bar-small {
      align-self: center;
      flex-grow: 1;
      margin: 0 10px;
      min-width: 110px;
    }
  }

  .cell-table-action {
    .disable-icon {
      color: $grey4;
    }
    .eye-icon, .pencil-icon, .delete-icon {
      color: var(--semantic-color-content-interactive-primary-default);
      cursor: pointer;
    }
    .delete-icon{
      margin-left: 7px,
    }
  }

  .predifined-cell {
    margin: 0 auto;
  }

  .cell-status > div {
    display: inline-block;
    padding: 4px;
    border-radius: 5px;
    svg {
      margin-right: 4px;
    }
    &.enabled {
      background: $green6;
      color: $green2;
    }
    &.disabled {
      background: var(—surface-fields-disabled);
      color: var(--semantic-color-content-interactive-primary-disabled);
    }
  }
}
}
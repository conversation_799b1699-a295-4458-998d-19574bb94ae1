@import 'scss/colors.scss';

.activation-panel {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 90px;
  background-color: var(--semantic-color-surface-nav-sideBar-hover);
  height: 100vh;
  z-index: 999;
  width: 250px;
  padding: 20px 10px;

  .activation-status-container {
    color: $grey20;
    margin-bottom: 16px;
    .activation-status {
      margin-top: 5px;
      color: #d0d1d3;
      padding-left: 6px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &.edit {
        color: $red4;
      }
    }
  }
  
  .activate-button {
    width: 100%;
  }
  .force-activate-parent {
    padding-left: 4px;
    input {
      margin-right: 8px;
      cursor: pointer;
    }
    label {
      color: var(--semantic-color-surface-base-primary);
      &:active,
      &:hover {
        color: var(--semantic-color-surface-base-primary);
      }
      span {
        float: right;
        left: 10px;
        width: 110px;
        position: absolute;
        z-index: 1;
        padding-left: 20px;
      }
      input {
        position: absolute;
        z-index: 0;
      }
    }
    span {
      cursor: default;
      font-size: 13px !important;
    }
  }
}

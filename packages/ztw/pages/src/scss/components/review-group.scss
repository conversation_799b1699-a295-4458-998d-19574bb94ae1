
@import 'scss/colors.scss';

.ec-root-page {
.review-container {
    display: flex;
    flex-direction: column;
    margin-bottom: 24px;
    font-style: normal;
    font-weight: 500;
    font-size: 13px;
    line-height: 20px;
    color: #000000;
    button {
        box-sizing: border-box;
        height: 32px;
        padding: var(--Spacing-60, 6px) var(--Spacing-120, 12px);
        border: 1px solid var(--semantic-color-content-interactive-primary-default);
        border-radius: var(--Corner-Radius-1, 4px);
        background-color: var(--semantic-color-background-primary, #FFF);
        margin: 5px 0;
    }
    &.no-margin-bottom {
        margin-bottom: 0;
    }
    &.sub-review-group {
        margin-left: 20px;
        margin-top: 12px;
        margin-bottom: 0;
    }
    &.no-margin-bottom {
        margin-bottom: 0;
    }
    .review-title-container {
        display: flex;
        align-items: center;
    }
    .review-title {
        font-style: normal;
        font-weight: 500;
        font-size: 15px;
        line-height: 20px;
        color:  var(--semantic-color-content-base-primary);
        margin: 0 8px;
    }
    .review-item-number {
        color: var(--semantic-color-content-base-secondary);
        font-size: 15px;
        font-weight: 400;
    }
    .review-section-title {
        margin-top: 12px;
        font-style: normal;
        font-weight: 400;
        font-size: 13px;
        line-height: 20px;
        letter-spacing: 0.08em;
        text-transform: uppercase;
        color:  var(--semantic-color-content-base-primary);
      }
    .review-sub-section-title {
        margin-top: 4px;
        // font-style: normal;
        // font-weight: 400;
        // font-size: 13px;
        // line-height: 20px;
        // letter-spacing: 0.08em;
        // text-transform: uppercase;
        color:  var(--semantic-color-content-base-primary);
        // color: var(--DeepBlue-Content-Default-Tertiary, #69696A);
        font-size: 13px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px; /* 153.846% */
      }
    .review-title-icon {
        font-size: 13px;
        font-weight: 400;
        line-height: 20px;
        letter-spacing: 0em;
        text-align: center;
        color: var(--semantic-color-content-status-info-primary);
    }
    .review-container-items {
        display: flex;
        flex-direction: column;
        margin-top: 12px;        
        .input-container.review.half-width {
            display: flex;
            width: 90% !important;
            .form-field-label-wrapper .form-field-label {
                margin-left: 0px;
                margin-right: 8px;
                width: 230px;
                color:  var(--semantic-color-content-base-primary);
                font-weight: 400;            
                &.padding-left-20px {
                  padding-left: 20px;
                }
                &.padding-left-40px {
                  padding-left: 40px;
                }
            }
            p.disabled-input {
                font-style: normal;
                font-weight: 400;
                font-size: 13px;
                line-height: 20px;
                color:  var(--semantic-color-content-base-primary);
                &.description{
                    white-space: break-spaces;
                    max-height: 120px;
                    min-width: 400px;
                    overflow-y: auto;
                }
            }
        }
        .form-field-label-wrapper {
            display: flex; 
        }
    }
}
}
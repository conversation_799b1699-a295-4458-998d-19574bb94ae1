@import "scss/colors.scss";

.ec-root-page {
  .world-map-tooltip {
      box-shadow: 0 1px 8px 0 $grey16;
      background: $white;
      opacity: 1;
      padding: 12px;
      line-height: 19px;
      .location-name {
        padding-bottom: 3px;
        font-size: 10px;
        color: $grey4;
        opacity: 0.5;
        > span {
          padding-left: 5px;
          font-size: 11px;
          font-weight: bold;
          letter-spacing: 0.06px;
          line-height: 12px;
        }
      }
      span.good {
        color: $green2;
      }
      span.okay {
        color: $orange2;
      }
      span.poor {
        color: $red3;
      }
    }


    .tooltip-body-value-reason{
      height: 100px;
      width: 48%;
      color:  var(--semantic-color-content-base-primary);
      font-size: 12px;
      letter-spacing: 1px;
      line-height: 18px;
      float: right;
      white-space: nowrap;
      // overflow: hidden;
      text-overflow: ellipsis;
    }

    .extra-height {
      min-height: 310px;
      min-width: 290px;
    }
    .auto-height {
      height: auto;
    }
    .reason-area {
      height: 50px;
    }
    .reason-key {
      overflow: revert;
      height: 100px;
    }
    .reason-content {
      display: inline-block;
      word-break: break-word;
      word-wrap: break-word;
      width: 141px;
      text-overflow: revert;
      hyphens: auto;
      white-space: pre-wrap;
      height: 100px;
      overflow: auto;
    }

  .fontStyle {
    height: 14px;	width: 13px;	color: var(--semantic-color-content-interactive-primary-default);	font-size: 14px;	line-height: 14px;
  }

  .__react_component_tooltip { 
    padding: 0;
    z-index: 49;
  }

  .mask {
    height: 138px;
    width: 150px;
    border-radius: 4px;
    background-color: $white; // var(--semantic-color-surface-base-primary);
    box-shadow: 0 3px 8px 0 $blackShadow2;// rgba(176,186,197,0.6);
  }

  .line {	height: 0.05em; border: 0.5px solid $grey26; } // #DDDDDD; }

  // bule2 var(--semantic-color-content-interactive-primary-default)
  .tooltipHeader {	height: 15.36px;	width: 70px;	color: var(--semantic-color-content-interactive-primary-default);font-size: 13px;	line-height: 15px; padding-left: 0.5em;}

  .tooltip-content {
    padding: 0.1em;
    height: 12em;
    > span {
      display: block;
      padding-bottom: 0.5em;
    }
    .info {	height: 20px;	width: 125px;	color: #1E1F22;	font-size: 11px;	font-weight: 500;	letter-spacing: 1px;	line-height: 20px;}
  }
}
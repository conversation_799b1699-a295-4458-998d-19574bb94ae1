@import "scss/colors.scss";
@import "scss/mixins.scss";

.ec-root-page {
.toggle-inline-check-box-container {
  padding: 10px 0 0 0;
  label {
    display: flex;
    align-items: center;
    width: fit-content;
  }

  p {
    color: $grey1;
    font-weight: 500;
    margin: 15px 0 10px;
    text-transform: capitalize;
  }
  .check-box-container{
    input {
      display: none;
    }
  }
  
  .toggle-switch { 
    color: var(--semantic-color-content-interactive-primary-default);
    font-size: 20px;
  }
  .toggle-label { 
    .label {
      font-size: 13px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px; /* 153.846% */
      color: #000000
    }
    .sub-label {
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 153.846% */
      color: #767676
    }
  }
  .toggle-icon {
    size: 15em;
  }
  .toggle-medium-icon {
    size: 15em;
    width: 3.75em;
    height: 3.75em;
  }
  input[type=checkbox] {
    display: none;
  }
}

.top-right {
  position: absolute;
  &.rTooltip {
    transform: none;
    .has-info-text {
      color: rgb(36, 117, 209);
    }
    &::after {
      content: "";
      border: 5px solid transparent;
      height: 0;
      position: absolute;
      width: 0;
      border-bottom: 5px solid $white;
      bottom: 55px;
      left: 12px;
    }
    .rTooltip-container {
      max-width: 600px;
    }
  }
}

.top-left {
  .rTooltip{
    transform: none;
    .has-info-text {
      color: rgb(36, 117, 209);
    }
  }
  .rTooltip::after {
    content: "";
    border: 5px solid transparent;
    height: 0;
    position: absolute;
    width: 0;
    border-bottom: 5px solid $white;
    bottom: 55px;
    right: 12px;
    left: unset;
  }
}
}

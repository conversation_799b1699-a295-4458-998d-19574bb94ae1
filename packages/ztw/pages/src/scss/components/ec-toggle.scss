@import "scss/colors.scss";

.ec-root-page {
.check-box-container {
  input {
    display: none;
  }
  label span {
    margin-left: 0;
  }
  .toggle {
    display: inline-block;
    position: relative;
    margin: 6px 0;
    .toggle-label {
      color: $white;
      cursor: pointer;
      display: inline-block;
      font-size: 16px;
      height: 28px;
      line-height: 26px;
      text-align: center;
      width: 30px;
      vertical-align: middle;
      &.off {
        background: var(--semantic-color-content-status-danger-secondary);
        border: 1px solid var(--semantic-color-content-status-danger-secondary);
      }
      &.on {
        background: var(--semantic-color-content-status-success-secondary);
        border: 1px solid var(--semantic-color-content-status-success-secondary);
      }
      &.left {
        border-radius: 5px 0 0 5px;
        border-right: none;
      }
      &.right {
        border-radius: 0 5px 5px 0;
        border-left: none;
      }
    }
    .toggle-button {
      background: $white;
      border-radius: 5px;
      color: $black;
      cursor: pointer;
      display: inline-block;
      height: 24px;
      text-align: center;
      min-width: 0;
      position: absolute;
      right: 4px;
      top: 4px;
      width: 22px;
      height: 20px;
      -webkit-box-shadow: 0px 1px 0px 0px $grey16;
      -moz-box-shadow: 0px 1px 0px 0px $grey16;
      box-shadow: 0px 1px 0px 0px $grey16;
      &.off {
        left: 4px;
        right: auto;
        background: $white;
      }
    }
    &.disabled {
      .toggle-label {
        &.off,
        &.on {
          background: var(—surface-fields-disabled);
          color: var(--semantic-color-content-interactive-primary-disabled);
          border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
          cursor: default;
        }
      }
    }
  }
}
}
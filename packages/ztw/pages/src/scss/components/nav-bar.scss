@import 'scss/pages.scss';
@import 'scss/colors.scss';
@import 'scss/mixins.scss';

.ec-root-page {
.ec-sidebar{
	background-color: var(--semantic-color-surface-nav-sideBar-default);
	height: 100%;
	width: 90px;
	padding-bottom: 2em;
	.zlogo {
		height:  80px;
		pointer-events: none;
	}
	ul {
		@include DisplayFlex;
		flex-direction: column;
		height: 100%;
		li {
			justify-content: center;
			@include DisplayFlex;
			align-items: center;
			height:  80px;
			a {
				justify-content: center;
				flex-direction: column;
				text-decoration: none;
				@include DisplayFlex;
				align-items: center;
				flex: 1;
				color: #d0d1d3;
				span {
					font-size: 11px;
					color: var(--semantic-color-content-base-secondary);
					line-height: 16px;
				}
			}
			.active {
				background-color: var(--semantic-color-surface-nav-sideBar-active); // background color for side panel selected current page
			}
			.active-menu-item {
				border-radius: 5px;
				// color: var(--semantic-color-content-base-secondary);
				color: var(--semantic-color-content-interactive-primary-default);
				.nav-menu-list-item-text {
					// color: var(--semantic-color-content-base-secondary);
					color: var(--semantic-color-content-interactive-primary-default);
				}
			}
			.logout-full-width {
				width: 100%;
				display: flex;
				justify-content: center;
			}
	
			.custom-nav {
				cursor: pointer;
				height: 100%;
				width: 100%;
				justify-content: center;
				flex-direction: column;
				text-decoration: none;
				display: -webkit-box;
				display: -moz-box;
				display: -ms-flexbox;
				display: -webkit-flex;
				display: flex;
				align-items: center;
				flex: 1;
				.new {					
					color: #61BBF2;
					font-size: 11px;
					letter-spacing: 0;
					line-height: 20px;
				}
				span {
					font-size: 11px;
					color: var(--semantic-color-content-base-secondary);
					line-height: 16px;
				}
				.admin-panel,.help-panel {
					position: absolute;
					overflow: auto;	
					top: 0;
					bottom: 0;
					left: 90px;
					background: var(--semantic-color-surface-nav-sideBar-hover);
					opacity: .98;
					height: 100%;
					z-index: 9999;
					width: 300px;
					padding: 20px 10px;
					&.logs {
						width: 250px
					}
					&.administration {
						width:  500px;	
						.nav-menu-section {
							width: 50%;
						}						
					}
					&.help {
						width: 300px;	
						a {		
							background-color: transparent;			
						}
					}
					&.policy {
						width: 250px
					}
					ul {
						display: -webkit-box;
						display: -moz-box;
						display: -ms-flexbox;
						display: -webkit-flex;
						display: flex;
						flex-direction: row;
						height: min-content;
						flex-wrap: wrap;
					}
					.nav-menu-section {						
						margin: 0 0 .5em 0;
						ul {
							height: auto;
						}
						&:hover {
						 	background-color: transparent;
						}
						&:active {
							background-color: transparent;
						}
					}
					.nav-menu-section,
					.nav-menu-list,
					.nav-menu-list-item {
						display: block;
						height: auto;
    					margin-bottom: 20px;						
						width:fit-content;
						width: -moz-fit-content;
					}
					.nav-menu-section-header {
						color: var(--semantic-color-content-base-primary); // side pane details menu title
						font-size: 1em;
						text-transform: uppercase;
						font-weight: normal;
						line-height: 16px;
						padding: 0px 16px;
						opacity: 1;
						width: 100%;
						margin: 0px;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
					.nav-menu-list {
						list-style: none;
						margin: 0;
						padding: 0;
					}
					.nav-menu-list-item {
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						display: block;
						text-align: left;
						width: 100%;
						padding: 0;
						height: auto;
						line-height: 16px;
						margin: 1px 0;
						opacity: 1;
						.nav-menu-list-item-text {
							color: var(--semantic-color-content-base-secondary);
							display: inline-block;
							vertical-align: middle;
							width: 100%;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							font-size: 1em;
						}
						> span {
							padding: 6px 16px 6px 16px;
						}
						a {							
							padding: 6px 16px 6px 16px;
							&:hover {
								border-radius: 5px;
								.nav-menu-list-item-text {
									&:hover { 
										background-color: var(--semantic-color-surface-nav-sideBar-active);
										color: var(--semantic-color-content-interactive-primary-hover);
									}
								}
							}
						}
						&:hover {
							border-radius: 5px;
							.nav-menu-list-item-text {
								background-color: var(--semantic-color-surface-nav-sideBar-active);
								color: var(--semantic-color-content-interactive-primary-hover);
							}
							.fa-external-link {
								color: var(--semantic-color-content-interactive-primary-hover);
							}
						}
						&:active {
							border-radius: 5px;
							.nav-menu-list-item-text {
								color: var(--semantic-color-content-base-secondary);
							}
							.fa-external-link {
								color: var(--semantic-color-content-base-secondary);
							}
						}
						&.hidden {
							display: none;
						}
					}
				}

				.admin-panel .nav-menu-section .active {
					border-radius: 5px;
					.nav-menu-list-item-text {
						// active selected sidepane details menu;	
						color: var(--semantic-color-content-interactive-primary-hover);	
					}
				}
				.help-panel .nav-menu-section .active {
					.nav-menu-list-item-text {
						color: var(--semantic-color-content-base-secondary);		
					}
					&:hover {
						color: var(--semantic-color-content-interactive-primary-hover);
						border-radius: 5px;
						.nav-menu-list-item-text {
							color: var(--semantic-color-content-base-secondary);
						}
						.fa-external-link {
							color: var(--semantic-color-content-base-secondary);
						}
					}
				}
			}

			.activation-nav {
				margin-top: 12px;
				position: relative;
				.fa-circle.fa-stack-2x {
					margin-left:  -4px;
				}

				.fa-circle.fa-stack-1x {
					color: $red2;
					font-size: 15px;
					margin-bottom:  22px;
					margin-left: 18px;
				}
				
				.fa-upload {
					margin-bottom: 13px;
					margin-left: 5px;
				}

				.nav-super-label {
					background: var(--semantic-color-background-primary) !important;
					border: 1px solid var(--semantic-color-border-base-primary) !important;
					border-radius: 12px;
					color: var(--semantic-color-content-base-primary) !important;
					font-size: 13px;
					height: 20px;
					line-height: 12px;
					min-width: 20px;
					padding-top: 4px;
					position: absolute;
					left: 50px;
					text-align: center;
					top: 5px;
					z-index: 10;
					&.no-value {
						display: none;
					}
				}
			}		
			&:nth-last-child(1),
			&:nth-last-child(2) {
				margin-top: 0;
			}
			&:nth-last-child(3) {
				margin-top: 0;
			}
			&:nth-last-child(3) {
				margin-top: auto;
			}
			&:hover {
				background-color: var(--semantic-color-surface-nav-sideBar-active);
				a, span, .svg-inline--fa {
					color: var(--semantic-color-content-base-secondary);						
				}
			}
			&:active {
				background-color: var(--semantic-color-surface-nav-sideBar-active);
			}
			&.selected {
				background-color: var(--semantic-color-surface-nav-sideBar-active);
			}
			// &:first-child:hover {
			// 	background-color: $transparent;
			// }
			
		}
		li.sidemenu a:active {
			background-color: var(--semantic-color-surface-nav-sideBar-active);
		}
		li.active {
			background-color: var(--semantic-color-surface-nav-sideBar-active);

			span, .svg-inline--fa {
				color: var(--semantic-color-content-interactive-primary-default);
				// color:  var(--semantic-color-content-base-primary);
			}
		}
	}
	.svg-inline--fa {
		color: var(--semantic-color-content-base-secondary);
		margin-bottom: 6px;
	}
	.logo {
		width: 48px;
	}
}

.hide {
	display: none !important;
}

.hideMe {
	display: none;
}

.sidebar {
	.sign-out-button {
		transform: rotate(180deg);
		.fa-sign-out-alt {
			margin-bottom: 0;
    		margin-top: 5px;
		}
	}
	.normal {
		min-height: 6em;
		height: 6em;
	}
	.small {
		height: 40px;
		min-height: 2.5em;
		cursor: pointer;
	}
}


.sidebar .menu-nav-search-input .search-container {
	.search-icon-button {
		color: #D0D1D3;
		margin: 0;
		margin-left: 6px;		
	}
	.search-input {
		width:170px;
		background: var(--semantic-color-background-primary);
		padding: 6px 0;		
	}
}

#ulRoutes,#ulResults {
	flex-direction: column;
	flex-wrap: nowrap;
	height: calc(100% - 100px);
}

.sidebar #ulRoutes,#ulResults li:nth-last-child(3) {
    margin-top: 0;
}
.sidebar .search-result-routes {
	margin-top: 300px;
	overflow-y: auto;
}
.sidebar .search-result-panel {
    border-bottom: 12px solid #656666;
    border-radius: 5px;
    -webkit-box-shadow: 4px 0px 4px 0px rgba(42, 44, 48, 0.5);
    -moz-box-shadow: 4px 0px 4px 0px rgba(42, 44, 48, 0.5);
    box-shadow: 4px 0px 4px 0px rgba(42, 44, 48, 0.5);
    display: block;	
    list-style: none;
    overflow-y: auto;
    margin: 0px;
    margin-top: 16px;
    position: absolute;
    padding-top: 6px;
    text-align: left;
    top: 52px;
    width: 100%;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    z-index: 9998;
}

.sidebar .search-result-panel-item {
    overflow: hidden;	
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--semantic-color-content-status-info-primary);
    cursor: pointer;
    display: flex;
	justify-content: center;
    font-size: 13px;
    height: auto;
	min-height: 60px;
    line-height: 16px;
    padding: 6px 16px 6px 16px;
    text-align: left;
    width: 100%;
	&:first-child {
		min-height: 8px;
		cursor: default;
	}

}

.sidebar .search-options-text {
    display: inline-block;
    white-space: normal;
    width: calc(100% - 24px);
    word-wrap: break-word;
}

.sidebar .search-result-panel-item .active {
	background: none;
}

.remote-assistance-form {
	.form-sections-container {
			padding: 19px 25px;
			background: var(--semantic-color-background-primary);
			border: 1px solid var(--semantic-color-border-base-primary);
			padding: 16px 24px;
			width: 100%;
			max-height: 630px;
			overflow: auto;
			.form-section-label {
			padding: 0;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			color: var(--semantic-color-content-base-primary);
			font-weight: 400;
			font-size: 13px;
			letter-spacing: 0.5px;
			margin-bottom: 8px;
			text-transform: uppercase;
			width: 100%;
			}
			.form-section {
			flex-direction: column;
			background-color: var(--semantic-color-surface-base-primary);
			border: 1px solid var(--semantic-color-border-base-primary);        
			border-radius: 5px;
			width: 100%;
			margin-bottom: 24px;
			}
	}

	.g-row {  
			display: flex;
			justify-content: space-between;
			align-items: flex-end;
			width: 100%;
	
			&:first-child {
				.g-right{
					padding-left: 0;
				}
				.g-left {
					padding-right: 0;
				}
			}
			
			.g-fullwidth{
				padding: 12px 16px;
				width: 100%;
			}
			.g-left,.g-right {
				width: 50%;      
				padding: 12px 16px;
			}

			.input-container,.input-password-container {
				padding: 0;
				input {
					border: 1px solid var(--semantic-color-border-base-primary);
					background: var(--semantic-color-background-pale);
					border-radius: 8px;   
					color: #656666;
					cursor: text;
					display: block;
					height: 32px;
					padding: 9px 8px;
					text-align: left;
					min-width: 220px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					&:active,&:focus {
						background-color: var(--semantic-color-surface-fields-active);
					}
					&:hover {
						background-color: var(--semantic-color-surface-fields-hover);
					}
				}
			}        
			.disabled-input {
				color: var(--semantic-color-content-interactive-primary-disabled);
				padding: 14px 0;
			}
	}
	.tooltip-navlink {
		text-decoration: none;
		color: $anchor-color;
	}
	.remote-assistance-permission-required {
		// position: relative;
		background: var(--semantic-color-background-primary);
		min-height: 300px;
	}
}
}

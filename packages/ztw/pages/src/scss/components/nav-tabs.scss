@import "scss/colors.scss";
@import 'scss/mixins.scss';

.ec-root-page {
.tabP {
  margin-bottom: .6em;
  color: var(--semantic-color-content-interactive-primary-default);
  white-space: nowrap;
}
.tabPactive {
  margin-bottom: .6em;
  color: var(--semantic-color-content-base-primary);
  white-space: nowrap;
}
.tabP_XL {
  margin-bottom: .6em;
  color: var(--semantic-color-content-interactive-primary-default);
  font-size: 24px;
  font-weight: 500;
  line-height: 24px;
  white-space: nowrap;
}
.tabPactive_XL {
  margin-bottom: .6em;
  color: var(--semantic-color-content-base-primary);
  font-size: 24px;
  font-weight: 500;
  line-height: 24px;
  white-space: nowrap;
}
.tabs {
  display: inline-block;
  position: relative;
  // margin-bottom: 12px;
  &-items {
    @include DisplayFlex;
    div {
      margin-right: 25px;
      margin-bottom: 1em;
      color: var(--semantic-color-content-interactive-primary-default);
      &:last-child {
        margin-right: 0;
      }
      a {
        border: none;
        background: $transparent;
        font-size: 16px;
        // font-weight: 340;
        padding-bottom: 10px;
        text-decoration: none;
        color: inherit;
      }
      div.highlighter {
        height: 2px;
        width: 100%;
        color:  var(--semantic-color-border-base-primary);
        border-radius: 2.5px;
        padding-right: 10px;
        &.active {
          background-color: var(--semantic-color-content-interactive-primary-default);
          font-weight: 350;
          transition: background-color 1s ease-out;
        }
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  &-highlrighte {
    margin-bottom: 2em;
    height: 2px;
    border-radius:  5.5px;
    background: $grey49;
    position: absolute;
    width: 100%;
    bottom: 0;
    z-index: -1;
  }
}


.folder-tabs {
  .tabP {
    margin-bottom: .6em;
    color: var(--semantic-color-content-interactive-primary-default);
    white-space: nowrap;
    padding: 3px;
    padding-bottom: 0;
    &.disabled-input {
      color: $grey1;
    }
  }
  .tabPactive {
    margin-bottom: 0;
    color: var(--semantic-color-content-base-primary);
    white-space: nowrap;
    border: 2px solid var(--semantic-color-border-base-primary);
    border-bottom: 3.2px solid #F7F9FA;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    padding: 3px;
    padding-bottom: 0;
    top: -1px;
    position: relative;
    &.disabled-input {
      color: $grey1;
    }
  }
  .tabP_XL {
    margin-bottom: .6em;
    color: var(--semantic-color-content-interactive-primary-default);
    font-size: 24px;
    font-weight: 500;
    line-height: 24px;
    white-space: nowrap;
  }
  .tabPactive_XL {
    margin-bottom: .6em;
    color: var(--semantic-color-content-base-primary);
    font-size: 24px;
    font-weight: 500;
    line-height: 24px;
    white-space: nowrap;
  }
  &.tabs {
    display: inline-block;
    position: relative;
    // margin-bottom: 12px;
    .tabs-items {
      @include DisplayFlex;
      position: relative;
      div {
        margin-right: 25px;
        margin-bottom: 1em;
        // color:#0b74b1;
        &:last-child {
          margin-right: 0;
        }
        a {
          border: none;
          background: $transparent;
          font-size: 16px;
          // font-weight: 340;
          padding-bottom: 10px;
          text-decoration: none;
          color: inherit;
        }
        div.highlighter {
          height: 2px;
          width: 100%;
          color: var(--semantic-color-border-base-primary);
          border-radius: 2.5px;
          padding-right: 10px;
          &.active {
            background-color: transparent;
            font-weight: 350;
            transition: background-color 1s ease-out;
            position: relative;
            z-index: 2;
          }
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
    .tabs-highlrighte {
      margin-bottom: 0;
      height: 2px;
      border-radius: 0 5.5px;
      background: var(--semantic-color-border-base-primary);
      position: absolute;
      width: 100%;
      top:  24px;
      z-index: -1;
    }
  }
}
}
// @import 'scss/colors.scss';

// .view-empty-badge {
//   display: inline-block;
//   vertical-align: middle;
// }

// .view-empty-badge-icon {
//   margin-bottom: 2px;
//   font-size: 46px !important;
//   color: var(--semantic-color-content-base-primary);
// }

// .view-empty-text {
//   color: var(--semantic-color-content-base-primary);
//   font-size: 13px;
//   margin-top: 5px;
//   max-width: 200px;
// }

.ec-root-page {
.insights-widget {
    border: 1px solid var(--semantic-color-border-base-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 800px;
    border-radius: 5px;
}

.insights-widget-view {
    width: 100%;
    height: calc(100% - 52px);
    position: relative;
}

.insights-widget-view-no-header {
    margin: 0;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.insights_empty_lowercase {
    text-transform: lowercase;
}

.insights_empty_bold_text {
    font-weight: bold;
}
.insights-fas-fa-download {
    display: flex;
    justify-content: center;
}
}
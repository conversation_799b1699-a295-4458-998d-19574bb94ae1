@import 'scss/colors.scss';
@import 'scss/mixins.scss';

.ec-root-page {
.page-filters-box {
  align-self: stretch;
  border-radius: 5px;
  background: var(--semantic-color-surface-base-primary);
  border: 1px solid var(--semantic-color-border-base-primary);
  padding: 12px 15px 0 15px;
  min-height:  80px;

  .page-filter-form {
    @include DisplayFlex;
    width: 100%;

    position: relative;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    padding-right: 320px;

    .entity-dropdown-container {
      margin: 0 12px 12px 12px;

      .dropdown {
        width: 200px;
      }
    }
  }

  .filter-actions {
    @include DisplayFlex;
    position: absolute;
    right: 25px;
    align-items: center;
    justify-content: center;
    height: 45px;
    min-width: 200px;
    margin: 0 12px;
    flex-shrink: 0;
    .submit,
    .cancel {
      margin: 0 5px;
    }
  }
}
}
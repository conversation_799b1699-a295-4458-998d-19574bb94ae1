@import "scss/colors.scss";
@import "scss/mixins.scss";

.ec-root-page {
.map-container {
    width: 100%;

    svg {
        width: 100%;

        .rsm-marker,
        .rsm-marker--hover,
        .rsm-marker--press {
            outline: 0;
        }
    }
}

.zoom-btn-container {
    padding-top: 15px;

    button {
        display: block;
        background: var(--semantic-color-content-interactive-primary-default);
        border: none;
        width: 20px;
        height: 20px;
        color: $white;
        font-size: 11px;
        margin-bottom: 5px;
        border-radius: 5px;
    }
}

.map-legend {
    height: 30px;
    bottom: 15px;
    padding: 5px 10px;
    border-radius: 5px;
    box-shadow: 0 1px 6px 0 $grey18;
    border: 1px solid $grey8;
    background-color: $grey17;
    position: absolute;
    font-size: 11px;
    color: $grey4;
    .container {
      line-height: 20px;
      height: 100%;
      @include DisplayFlex;
      align-items: center;
      background: inherit;
      .statuses {
        @include DisplayFlex;
        margin-left: 3px;
        div {
          margin-left: 5px;
          @include DisplayFlex;
          align-items: center;
          span {
            margin-left: 5px;
          }
          div.status-line {
            height: 2px;
            width: 12px;
            border-radius: 3px;
            &.good {
              background-color: $green2;
            }
            &.okay {
              background-color: $orange3;
            }
            &.poor {
              background-color: $red3;
            }
          }
        }
      }
      .separatore {
        height: 100%;
        border-right: 1px solid var(--semantic-color-border-base-primary);
        margin: 0 10px;
      }
    }
  }

  .map-legend-numeric {
    height: 50px;
    width: 101.5%;
    bottom: 0;
    padding: 5px 10px;
    border-top: 1px solid $grey8;
    background-color: $grey17;
    position: absolute;
    font-size: 11px;
    color: $grey4;
    .container {
      line-height: 20px;
      height: 100%;
      @include DisplayFlex;
      justify-content: space-between;
      align-items: center;
      background: inherit;
      .data {
        height: 24px;	width: 20px;	color: var(--semantic-color-content-base-primary); 	
        font-size: 16px;	
        line-height: 24px;  
        margin-right: 10px;
      }
      .legend{
        height: 15px;	
        width: 77px;	
        color:  var(--semantic-color-content-base-primary);	
        font-size: 13px;	
        line-height: 15px;
      }
      .info{
        margin-left: 10px;
        .info-dc{
          float: left;
          margin-right: 3em;
        }
        .info-ec{
          float: right;
        }
      }
      .legend-detail{
        height: 15px;	
        width: 77px;	
        color:  var(--semantic-color-content-base-primary);	
        font-size: 13px;	
        line-height: 15px;
      }
      .statuses {
        @include DisplayFlex;
        margin-left: 10px;
        div {
          margin-left: 5px;
          @include DisplayFlex;
          align-items: center;
          .legend {
            margin-right: 20px;
          }
        }
      }
    }
  }

  .world-map-tooltip {
    box-shadow: 0 1px 8px 0 $grey16;
    background: $white;
    opacity: 1;
    padding: 12px;
    line-height: 19px;
    .location-name {
      padding-bottom: 3px;
      font-size: 10px;
      color: $grey4;
      opacity: .5;
      > span {
        padding-left: 5px;
        font-size: 11px;
        font-weight: bold;
        letter-spacing:  0.06px;
        line-height: 12px;
      }
    }
    span {
      color: $grey4;
      font-size: 12px;
      letter-spacing:  0.07px;
      line-height: 14px;
    }
  }
.box-map-container {
    height:  700px;
    position: relative;
    border-top: 2px solid $blue16;
    border-bottom: 2px solid $blue16;

    .left-boxes,
    .right-boxes {
        position: absolute;
        top: 0;
        bottom: 0;
        z-index: 9;
        @include DisplayFlex;
        flex-direction: column;
        align-items: flex-start;
        flex: 1;

        .map-box:last-child {
            border-bottom: none;

            .separator {
                display: none;
            }
        }
    }

    .right-boxes {
        right: 0;
        border-left: 1px solid var(--semantic-color-border-base-primary);
        border-right: 1px solid var(--semantic-color-border-base-primary);
    }

    .left-boxes {
        left: 0;
        border-right: 1px solid var(--semantic-color-border-base-primary);
        border-left: 1px solid var(--semantic-color-border-base-primary);
    }

    .container {
        background: radial-gradient(circle, $white 0%, $white2 100%);
        position: relative;
        justify-content: center;
        height: 100%;

        .title {
            padding: 24px 0;
            text-align: center;

            p:nth-child(1) {
                color: $blue18;
                font-size: 20px;
                font-weight: bold;
                line-height: 30px;
            }

            p:nth-child(2) {

                color: $grey10;
                font-size: 12px;
                line-height: 30px;
            }
        }

        svg {
            fill: $grey24;
        }

        .map-legend {
            width: 230px;
            margin: 0 auto;
            left: 0;
            right: 0;
        }
    }
}

.map-box {
    @include DisplayFlex;
    flex-direction: column;
    border-bottom: 1px solid $grey25;
    background: $blue17;
    width: 200px;
    position: relative;

    .content {
        padding: 10px;
        height: 100%;

        .title {
            @include DisplayFlex;
            align-items: center;

            p {
                color: var(--semantic-color-content-interactive-primary-default);
                font-size: 15px;
                margin-left: 12px;
            }
        }
    }

    .separator {
        height: 3px;
        flex: .8;
        border-radius: 1.5px;
        background-color: $grey25;
        width: 80%;
        margin: 0 auto;
        position: absolute;
        right: 0;
        left: 0;
        bottom:  -2px;
        z-index: 9999;
    }

}

.world-map-container {
   @include DisplayFlex;
    position: relative;
  }
  
.fontStyle {
  height: 14px;	width: 13px;	color: var(--semantic-color-content-interactive-primary-default);	font-size: 14px;	line-height: 14px;
}

.world-map-tooltip {
    box-shadow: 0 1px 8px 0 $grey16;
    background: $white;
    opacity: 1;
    padding: 12px;
    line-height: 19px;
    .location-name {
      padding-bottom: 3px;
      font-size: 10px;
      color: $grey4;
      opacity: .5;
      > span {
        padding-left: 5px;
        font-size: 11px;
        font-weight: bold;
        letter-spacing:  0.06px;
        line-height: 12px;
      }
    }
    span.good {
      color: $green2;
    }
    span.okay {
      color: $orange2;
    }
    span.poor {
      color: $red3;
    }
  }


.fontStyle {
  height: 14px;	width: 13px;	color: var(--semantic-color-content-interactive-primary-default);	font-size: 14px;	line-height: 14px;
}

.__react_component_tooltip { 
  padding: 0;
}

.mask {
  height: 138px;
  width: 150px;
  border-radius: 4px;
  background-color: $white; // var(--semantic-color-surface-base-primary);
  box-shadow: 0 3px 8px 0 $blackShadow2;// rgba(176,186,197,0.6);
 }

.line {	height: .05em; border: .5px solid $grey26; } // #DDDDDD; }

// bule2 var(--semantic-color-content-interactive-primary-default)
.tooltipHeader {	
  height: 15.36px;	
  width: 70px;	
  color: var(--semantic-color-content-interactive-primary-default);	
  font-size: 13px;	
  line-height: 15px;
  padding-left: .5em;
}
.tooltip-content {
  padding: 0.1em;
  > span {
    display: block;
    padding-bottom: .5em;
  }
  .info {	
    height: 20px;	
    width: 125px;	
    color: var(--semantic-color-content-base-primary); 	
    font-size: 11px;	
    font-weight: 500;	
    letter-spacing: 1px;	
    line-height: 20px;
  }
}

.selected-circle{
    stroke: $blue20;
}
  
}
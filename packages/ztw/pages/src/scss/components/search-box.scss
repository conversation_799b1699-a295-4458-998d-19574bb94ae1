@import 'scss/colors.scss';

.ec-root-page {
.search-box {
	position: relative;
	background-color: var(--semantic-color-surface-base-secondary	);
	border: 1px solid var(--semantic-color-border-base-primary);
	border-radius: .25rem;	 
	display: flex; 
	width: 13.75rem;
	height: 2rem;

	.input-search-box {
		outline: none;
		background-color: var(--semantic-color-surface-base-secondary	);
		color: var(--semantic-color-content-base-primary);
		border: none;
		font-size: 13px;	
		line-height: 15px;
		&:focus {
			outline: none;
		  }
	}

	.icon-wrapper {
		width: 2.1875rem;
		height: 1.875rem;
		line-height: 1.875rem;
		text-align: center;
		justify-content: center;
		background-color: transparent;
		&.left {
		  border-top-left-radius: 0.3125rem;
		  border-bottom-left-radius: 0.3125rem;
		}
	
		&.right {
		  border-top-right-radius: 0.3125rem;
		  border-bottom-right-radius: 0.3125rem;
		}
	
		.search-icon {
		  color: var(--semantic-color-content-base-secondary);
		}
		.remove-icon {
		  color: var(--semantic-color-content-base-primary);
	
		  &.disabled {
			color: var(--semantic-color-content-interactive-primary-disabled );
		  }
		}
	  }

	// .fa-search,.fa-magnifying-glass {
	// 	position: absolute;
	// 	right: 10px;
	// 	top: 8px;
	// 	color: $blue2;
	// 	cursor: pointer;
	// }
	.fa-times,.fa-xmark {
		position: absolute;
		right: 30px;
		top: 8px;
		color: var(--semantic-color-content-interactive-primary-default);
		cursor: pointer;
	}
}
}
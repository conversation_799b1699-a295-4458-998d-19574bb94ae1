@import '~react-datepicker/dist/react-datepicker.css';
@import 'src/scss/colors.scss';

.ec-root-page {
div.time-filter-dropdown {
  .closed, .single-date-picker.closed {
   display: none;
  }

  .calendar-container {
    justify-content: center;
    align-items: center;
    display: flex;
    position: absolute;
    width: 550px;
    height: 430px;
    z-index: 1000;
    background-color: var(--semantic-color-surface-base-primary);
    border-radius: 2px;
    border: 2px solid var(--semantic-color-border-base-primary);
  }
  .single-date-picker {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    position: absolute;
    width: 250px;
    height: 260px;
    z-index: 1000;
    background-color: var(--semantic-color-background-primary);
  }

  .calendar-container, .single-date-picker {
    .react-datepicker-wrapper {
      opacity: 0;
    }

    .react-datepicker__triangle {
      display: none;
    }

    .react-datepicker-time__caption {
      display: none;
    }

    .react-datepicker {
      font-size: 14px;
    }

    .react-datepicker__header {
      padding-top: 20px;
    }

    .react-datepicker__month {
      margin: 4px 13px;
      height: 195px;
    }

    .react-datepicker__day-names {
      font-size: 12px;
      margin-top: 12px;
      background-color: var(--semantic-color-surface-base-primary); 
    }

    .react-datepicker__day-name {
      color:  var(--semantic-color-content-base-primary);
      outline: none;
      width: 32px;
      height: 32px;
      line-height: 32px;
      margin: 0;
    }
    .react-datepicker__day {
      background: var(--semantic-color-background-primary);
      // color: var(--semantic-color-content-interactive-primary-default);
      outline: none;
      width: 32px;
      height: 32px;
      line-height: 32px;
      margin: 0;
      color: var(--semantic-color-content-base-primary);
    }

    .react-datepicker__current-month {
      font-size: 16px;
      color:  var(--semantic-color-content-base-primary);
    }

    .react-datepicker__navigation--previous {
      border: 0;
      height: 20px;
      color: var(--semantic-color-border-base-secondaryest);
      background: var(--semantic-color-surface-base-primary);
      // color: $white;
      // border-right-color: $grey8;
      // left: 1em;
    }

    .react-datepicker__navigation--next {
      border: 0;
      height: 20px;
      color: var(--semantic-color-border-base-secondaryest);
      background-color: var(--semantic-color-surface-base-primary);
      // color: $white;
      // border-left-color: $grey8;
      // right: 1em;
    }

    .start, .end {
      position: relative;
      width: 50%;
      height: 100%;
    }
    .singleDate {
      position: relative;
      width: 100%;
      height: 100%;
    }


    .start, .end, .singleDate {
      .date-type-heading {
        color: var(--semantic-color-content-base-primary);
        font-size: 13px;
        margin-bottom: 8px;
        text-transform: uppercase;
        text-align: left;
      }

      .start-date-heading {
        margin-top: 5px;
        margin-left: 21px;
      }

      .end-date-heading {
        margin-top: 5px;
        margin-left: 16px;
      }

      .react-datepicker__header {
        border: none;
        padding-top: 0;
        background-color: var(--semantic-color-surface-base-primary);
      }

      .react-datepicker__day--disabled {
        color: var(--semantic-color-content-base-primary) !important;
        font-weight: normal;
      }

      .react-datepicker-popper {
        transform: translate3d(0px, 20px, 0px) !important;
      }

      .react-datepicker__day--range-end,
      .react-datepicker__day--range-start {
        @at-root .react-datepicker__week {
          background-color: var(--semantic-color-surface-base-primary);

          .react-datepicker__day:not(.react-datepicker__day--in-range) {
            color: $anchor-color;
            font-weight: bold;
            background-color: var(--semantic-color-background-primary);
          }
        }
      }

      .react-datepicker__month-container {
        border: 1px solid var(--semantic-color-border-base-primary);
      }

      .react-datepicker__day--keyboard-selected {
        background-color: var(--semantic-color-background-primary);
        color: $black;
      }

      .react-datepicker__input-time-container {
        margin: 8px 0 6px;
        text-align: center;

        .react-datepicker-time__input-container {
          border: none;
          .react-datepicker-time__input {
            margin-left: 0;
          }
        }
      }

      .react-datepicker__day--in-range {
        background-color: var(--semantic-color-surface-base-primary);
        margin: 0;
        border-radius: 0;
        color: $dayInRangeColor;
      }

      .react-datepicker__day--outside-month {
        color:  var(--semantic-color-content-base-primary) !important;
        cursor: default;
        pointer-events: none;
        background: var(--semantic-color-background-primary);
      }

      input.react-datepicker-time__input {
        border-bottom: 2px solid var(--semantic-color-border-base-primary);
      }

      .react-datepicker__navigation-icon::before {
        border-color: var(--semantic-color-content-base-primary);
      }
      .react-datepicker__day-name {
        text-transform: uppercase;
        margin-right: 0px;
        color: var(--semantic-color-content-interactive-primary-default);
      }

      input.customTimeInput {
        width: 50px !important;
        text-align: center;
        border: none;
        border-bottom: 1px solid var(--semantic-color-border-base-primary);
        font-size: 1.1em;
      }

      input.customTimeInput:focus {
        border: none;
        border-bottom: 1px solid var(--semantic-color-content-interactive-primary-default);
        outline: none;
      }
    }

    .singleDate { 
      .react-datepicker__day--selected{
        background: var(--semantic-color-content-interactive-primary-active);
        border-radius: 50%;
        color: var(--semantic-color-content-inverted-base-primary);
        font-weight: 700;
      }
      .react-datepicker-popper {
        padding: 0;
        inset: 0px auto auto 0px !important;
        transform: none !important;
        top: 0px
      }
    }

    .start {
      .react-datepicker {
        background: var(--semantic-color-surface-base-primary);
        margin-left: 20px;
        border: none;
        .react-datepicker__day--range-start {
          background: var(--semantic-color-content-interactive-primary-active);
          border-radius: 50%;
          color: var(--semantic-color-content-inverted-base-primary);
          font-weight: 700;
        }
      }
    }

    .time-container {
      text-align: center;
    }

    span.end-time,
    span.start-time {
      display: inline-flex;
      margin-top: 5px;
      font-weight: bold;
      font-size: 14px;
      color: var(--semantic-color-content-interactive-primary-default);
    }

    .vertical-border {
      border-left: 1px solid $grey10;
      position: absolute;
      left: 50%;
      top: 100px;
      bottom: 105px;
      z-index: 12;
    }

    .closeIcon {
      position: absolute;
      top: 16px;
      right: 20px;
      z-index: 13;
      color: black;
    }

    .end {
      .react-datepicker {
        background: var(--semantic-color-surface-base-primary);
        border: none;
        margin-left: 15px;
        .react-datepicker__day--range-end {
          background: var(--semantic-color-content-interactive-primary-active);
          border-radius: 50%;
          color: var(--semantic-color-content-inverted-base-primary);
          font-weight: 700;
        }
      }
    }

    .buttons-row {
      position: absolute;
      bottom: 0px;
      width: 100%;
      background: var(--semantic-color-surface-base-primary);
      border-top: 1px solid var(--semantic-color-border-base-primary);
      height: 50px;
      display: flex;
      padding: 8px;
      .apply {
        background: var(--semantic-color-content-interactive-primary-default);
        border: .0625rem solid var(--semantic-color-content-interactive-primary-default);
        color: var(--semantic-color-content-inverted-base-primary);

        margin-right: 15px;
        margin-left: 12px;
        border: none;
        padding: 6px 20px;
        font-size: 14px;
        border-radius: .3125rem;
        cursor: pointer;
      }

      .close {
        box-shadow: none;
        color: var(--semantic-color-content-interactive-primary-default);
        border: none;
        font-size: 14px;
        padding: 6px 20px;
        border-radius: .3125rem;
        background-color: transparent;
        cursor: pointer;
      }
    }

    &::before {
      content: '';
      border: 6px solid transparent;
      // border-bottom-color: $white;
      border-top: none;
      height: 0;
      position: absolute;
      left: 287px;
      top: -6px;
      width: 0;
    }
  }

  select.meridian-selector {
    border: none;
    margin-left: 5px;
    color: var(--semantic-color-content-interactive-primary-default);
    background: var(--semantic-color-surface-status-neutral-active	);
    width: 40px;
    outline: none;
    font-size: 13px;
    border-radius: .3rem;
    cursor: pointer;
    height: 2rem;
    text-align: center;

  }

  .meridian-selector-option {  
    background: var(--semantic-color-surface-status-neutral-active	);
  }

  select * {  
    background: var(--semantic-color-surface-status-neutral-active	);
  }

  .rc-time-picker-panel {
    z-index: 1070;
    width: 170px;
    position: absolute;
    box-sizing: border-box;
  }

  .rc-time-picker-panel-input-wrap {
    width: 100px;
    text-align: center;
  }

  .rc-time-picker-panel-select:last-child {
    display: none;
    width: 0;
  }

  input.rc-time-picker-input {
    border: none;
    // border-bottom: 1px solid $blue2;
    background-color: var(--semantic-color-surface-status-neutral-active	);
    color: var(--semantic-color-content-interactive-primary-default);
    padding: 0;
    text-align: center;
    font-size: 13px;
    width: 85px;
    margin-right: 6px;
    height: 19px;
    outline: none;
    border-radius: .3rem;
    cursor: pointer;
    height: 2rem;
    padding: .5625rem .5rem;

  }

  .rc-time-picker-clear {
    display: none;
  }
}

.rc-time-picker-panel-inner {
  color: var(--semantic-color-content-interactive-primary-default) !important;
  background: var(--semantic-color-surface-status-neutral-active	) !important;
}
.rc-time-picker-panel-input {
  color: var(--semantic-color-content-interactive-primary-default) !important;
  background-color: var(--semantic-color-surface-status-neutral-active	) !important;
}
li.rc-time-picker-panel-select-option-selected {
  background: var(--semantic-color-content-interactive-primary-default) !important;
  color: var(--semantic-color-content-inverted-base-primary) !important;
}
.rc-time-picker-panel-select li:hover {
  background: var(--semantic-color-content-interactive-primary-hover) !important;
  color: var(--semantic-color-content-inverted-base-primary) !important;
}
}
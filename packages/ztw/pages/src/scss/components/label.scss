@import 'scss/colors.scss';
@import "scss/mixins.scss";

.ec-root-page {
h3 {
  color: $grey1;
  font-size: 16px;
  font-weight: 500;
  line-height: 21px;
}

h1 {
  color: $grey1;
  font-size:  24px;
  font-weight: 500;
  line-height: 30px;
}

.box-label {
  text-transform: uppercase;
  color: $grey1;
  font-size: 16px;	
  font-weight: 500;	
  letter-spacing: 1px;	
  line-height: 19px;
  padding: 5px 10px 0 10px;
  .form-field-tooltip-container {

    color:  red;
    border: 1px solid red;
    // background: var(--semantic-color-background-primary);
    // color:  var(--semantic-color-content-base-primary);
    // border: 1px solid var(--semantic-color-border-base-primary);
		max-width: 550px;
		min-width: 260px;
		overflow: hidden;
		padding: 12px 16px;
		word-wrap: break-word;
		white-space: break-spaces;
		text-transform: none;
	}
}

.date-sub-header {
    @include DisplayFlex;
    text-transform: none;
    color: $grey1;
    align-items: center;
    color: $grey1;
    font-size: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid $grey14;

    h2 {
        color: inherit;
        font-size: inherit;
        flex: .3;
        text-transform: inherit;
    }

    p {
        flex: .4;
        color: $grey4;
        font-size: inherit;
        text-align: center;
        text-transform: inherit;
  
    }

    div {
        flex: .3;
    }
}

.error-label {
  color: $red2;
  line-height:  22px;
  margin-top: 12px;
}

.form-field-label-wrapper {
	display: block;
	.form-field-label {
		color: var(--semantic-color-content-base-secondary);
		font-weight: 500;
		line-height: 20px;
		display: inline-block;
		&.invalid {
			color: var(--semantic-color-content-status-danger-primary);
		  }
	}
	.form-field-label:hover + .rTooltip{
		display: block;
	}
	.top-right.rTooltip {
		top: auto;
		left: auto;
		max-width:  550px;
		min-width:  450px;
	}
	.form-field-tooltip-container {
		background: var(--semantic-color-background-primary);
		color:  var(--semantic-color-content-base-primary);
		border: 1px solid var(--semantic-color-border-base-primary);
		max-width: 550px;
		min-width: 260px;
		overflow: hidden;
		padding: 12px 16px;
		word-wrap: break-word;
		white-space: break-spaces;
		text-transform: none;
	}
}

.form-field-label-same-line-wrapper {
	display: flex;
    align-items: center;
	.form-field-label {
		color: $grey1;
		font-weight: 500;
		line-height: 20px;
		display: inline-block;
		min-width: max-content;
	}
	.form-field-label:hover + .rTooltip{
		display: block;
	}
	.top-right.rTooltip {
		top: auto;
		left: auto;
		max-width:  550px;
		min-width:  450px;
	}
	.form-field-tooltip-container {
		background: var(--semantic-color-background-primary);
		color:  var(--semantic-color-content-base-primary);
		border: 1px solid var(--semantic-color-border-base-primary);
		max-width: 550px;
		min-width: 260px;
		overflow: hidden;
		padding: 12px 16px;
		word-wrap: break-word;
		white-space: break-spaces;
		text-transform: none;
	}
}

.form-section-label {
	color: var(--semantic-color-content-base-primary);
	font-weight: 500;
	text-transform: uppercase;
	padding: 22px 24px 8px  24px;
	.form-field-label {
		color: var(--semantic-color-content-base-primary);
	}
}

.form-value-label {
	color: #939393;
	margin-top: 12px;
}

.sub-header {
    text-transform: none;
    color: $grey1;
    font-size: 16px;	
    font-weight: 500;	
    letter-spacing: 1px;	
    line-height: 19px;
    background: var(--semantic-color-surface-base-primary);
    border-top: 1px solid var(--semantic-color-border-base-primary);
    // line-height: 30px;
    // font-size: 16px;
    // font-weight: normal;
    padding: 30px;
    // border-bottom: 1px solid $grey14;
}

.sub-title {
    text-transform: uppercase;
    color: $grey4;
    letter-spacing: .5px;
    line-height: 16px;
    box-sizing: border-box;	
    padding: 7px 0;
    border-bottom: 1px solid $grey14;
    font-size: 14px;
}

}
@import 'scss/colors.scss';

.ec-root-page{
.table-container .content .cell-container {
  color: grey1;
  padding: 12px 8px;
  height: 49px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 28px;
  // border-right: 1px solid #e0e1e3;
  border-right: none;

  &.no-ellipsis {
    text-overflow: initial;
    white-space: initial;
  }

  & > * {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &.tooltip-cue:hover {
    cursor: pointer;
    color: var(--semantic-color-content-interactive-primary-default);
  }

  .error-tooltip {
    margin-left: 4px;
    .fa-info-circle-icon {
      font-size: 13px;
      font-weight: 400;
      line-height: 20px;
      letter-spacing: 0em;
      text-align: center;
      color: var(--semantic-color-content-status-info-primary);
    }
  }
  .cca-cell-container{
    cursor: default;
    display: flex;
    justify-content: center;
    .cca-download-file {
      width: fit-content;
      cursor: pointer;
      .disabled-input.fa-external-link {
        color: var(--semantic-color-content-interactive-primary-disabled);
        cursor: not-allowed;
      }
    }
  }
  .permission-status-cell {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 4px;
    border-radius: var(--cornerRadius-40, 4px);
    border: 1px solid var(--semantic-color-border-status-info-default, #A2C1FF);
    background: var(--semantic-color-surface-status-info-default, #F2F7FF);

    color: var(--semantic-color-content-status-info-primary, #0E3896);

    /* Paragraph/P1 */
    font-size: var(--semantic-fontSize-small, 14px);
    font-style: normal;
    font-weight: var(--semantic-fontWeight-regular, 400);
    line-height: var(--font-line-height-small, 20px); /* 142.857% */
    svg {
      margin-right: 4px;

    }
    &.enabled {
      border: 1px solid var(--semantic-color-border-status-info-default, #A2C1FF);
      background: var(--semantic-color-surface-status-info-default, #F2F7FF);
    }    
    &.disabled {
      border: 1px solid var(--semantic-color-border-status-neutral-default, #BAC2CF);
      background: var(--semantic-color-surface-status-neutral-default, #F2F7FF);
      color: var(--semantic-color-content-status-neutral-primary, #0E3896);
      svg {
        color: var(--semantic-color-content-status-neutral-secondary, #0E3896);
      }  
    }    
  }
  .partner-permission-cell {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 4px;
    border-radius: var(--cornerRadius-40, 4px);
    border: 1px solid var(--semantic-color-border-status-info-default, #A2C1FF);
    background: var(--semantic-color-surface-status-info-default, #F2F7FF);

    color: var(--semantic-color-content-status-info-primary, #0E3896);

    /* Paragraph/P1 */
    font-size: var(--semantic-fontSize-small, 14px);
    font-style: normal;
    font-weight: var(--semantic-fontWeight-regular, 400);
    line-height: var(--font-line-height-small, 20px); /* 142.857% */
    svg {
      margin-right: 4px;

    }
    &.enabled { //allowed
      border: 1px solid var(--semantic-color-border-status-success-default);
      background: var(--semantic-color-surface-accent-green-default);
      color: var(--semantic-color-content-status-success-primary);
      svg {
        color: var(--semantic-color-content-status-success-secondary);
      }      
    }    
    &.na { //
      border: 1px solid var(--semantic-color-border-status-neutral-default, #BAC2CF);
      background: var(--semantic-color-surface-status-neutral-default, #F2F7FF);
      color: var(--semantic-color-content-status-neutral-primary, #0E3896);
      svg {
        color: var(--semantic-color-content-status-neutral-secondary, #0E3896);
      }  
    }  
    &.denied { //
      border: 1px solid var(--semantic-color-border-severity-high-default);
      background: var(--semantic-color-surface-severity-high-default);
      color: var(--semantic-color-content-status-danger-primary);
      svg {
        color: var(--semantic-color-surface-interactive-danger-default);
      }  
    }  
    &.pending { //
      border: 1px solid var(--semantic-color-border-accent-deepOrange-default);
      background: var(--semantic-color-surface-status-warning-default);
      color: var(--semantic-color-content-status-warning-primary, #8B3F17);
      svg {
        color: var(--semantic-color-content-status-warning-secondary);
      }  
    }    
  }
  .policy-action {
    width: 100%;
    height: 25px;
    border-radius: 5px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 4px;

    background-color: rgba(253, 66, 57, 0.1);
    color: #d13932;

    &.allowed {
      background-color: rgba(115, 161, 60, 0.1);
      color: #73a13c;
    }
  }
}

.multi-attribute-cell-fixed-height {
  min-height: 300px;
  height: auto;
  // span {
  //   white-space: normal;
  //   word-break: break-all;
  //   word-wrap: break-word;
  // }
}
.multi-attribute-cell-content-header {
  height: 12px;
  width: 67px;
  color: var(--semantic-color-border-base-subdued);
  font-size: 11px;
  letter-spacing: 0.5px;
  line-height: 12px;
}
.multi-attribute-cell-content-value {
  height: 15px;
  width: auto;
  color: var(--semantic-color-content-base-tertiary);
  font-size: 13px;
  letter-spacing: 0;
  line-height: 15px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.download-nss-server {
  //height: 15px;
  // width: 59px;
  color: var(--semantic-color-content-interactive-primary-default);
  font-size: 13px;
  // letter-spacing: 0;
  // line-height: 15px;
}

.parent-checkbox {
  padding-left: 8px;
  &.class-checkbox-version {
    display: flex;
    justify-content: center;
  }
}

.child-checkbox {
  padding-left: 64px;
}

.child-checkbox-input {
  margin-right: 8px;
}

.child-checkbox-label {
  display: flex;
  align-items: center;
}

.container-row-cc-group {
  .failed-scheduled {
    position: relative;
  }
  .scheduled-icon {
    color: $yellow7;
    margin-right: 4px;
    // cursor: pointer;
  }
  .failed-icon {
    color: $slider-color-high;
    margin-right: 4px;
    cursor: pointer;
  }
  .success-icon {
    color: var(--semantic-color-border-severity-lowest-active);
    margin-right: 4px;
    // cursor: pointer;
  }
  .disabled-icon, .disabling-icon {
    color: $button-disabled-text-color;
    margin-right: 4px;
  }
  .inactive-icon {
    color: $red2;
    margin-right: 4px;
  }
  .os-green-circle,
  .os-red-circle,
  .os-gray-circle
   {
    width: 50px;
    padding-left: 10px;
    position: relative;
    text-align: left;
    display: inline-block;
    &::before {
      content: '';
      width: 8px;
      height: 8px;
      position: absolute;
      top: 11px;
      left: 0px;
      border-radius: 4px;
    }
  }
  .os-green-circle {
    &::before {
      background: var(--semantic-color-border-severity-lowest-active);
    }
  }
  .os-red-circle {
    &::before {
      background: $red2;
    }
  }
  .os-gray-circle {
    &::before {
      background: $button-disabled-text-color;
    }
  }
}

.appliance-table {
  .failed-icon {
    color: $slider-color-high;
  }
  .success-icon {
    color: var(--semantic-color-border-severity-lowest-active);
  }
  .disabled-icon, .disabling-icon {
    color: $button-disabled-text-color;
  }
  .inactive-icon {
    color: $button-disabled-text-color;
  }
}

.cc-group-tooltip-container {
  width: 260px;
  min-width: 260px;
  overflow: hidden;
  padding: 6px 20px !important;
  word-wrap: break-word;
  text-transform: none;
  font-size: 13px!important;
  letter-spacing: 0!important;
  line-height: 18px!important;
  white-space: break-spaces;
  background: var(--semantic-color-surface-base-primary);
  border: 1px solid var(--semantic-color-border-base-primary);
  box-shadow: 0 7px 7px -5px var(--semantic-color-border-base-primary);
  color: var(--semantic-color-content-base-primary);
}

.operational-staus {
  .operational-staus-text {
    margin-right: 16px;
    .operational-staus-dot {
      height: 8px;
      width: 8px;
      margin-right: 4px;
      background-color: var(--semantic-color-surface-base-primary);
      border-radius: 50%;
      display: inline-block;  
      &.active{
        background-color: var(--semantic-color-border-severity-lowest-active);
      }
      &.inactive{
        background-color: var(--semantic-color-border-severity-high-active);
      }
      &.disabled{
        background-color: $button-disabled-text-color;
      }
    }
  }
  .operational-staus-icon{
    background-color: transparent;    
    .active{
      color: var(--semantic-color-border-severity-lowest-active);
      margin: 0 4px 0 7px;
    }
    .inactive{
      color: var(--semantic-color-border-severity-high-active);
      margin: 0 4px 0 7px;
    }
    .disabled{
      color: $button-disabled-text-color;
      margin: 0 4px 0 7px;
    }
  }
}

.account-column {
  img {
    width: 25px;
    vertical-align: middle;
  }
  .aws-text,.azure-text,.gcp-text {
    display: inline-block;
    padding-left: 10px;
    text-align: left;
    color: var(--semantic-color-content-base-primary);
  }
}

.fa-clock { 
  color:#F19325;
  margin-right: 4px;
}
.fa-circle-x-mark-icon {
  color:#CE4035;
  margin-right: 4px;
  width: 14px;
  height: 14px;
}
.prov-status {
  width: max-content;
  .radio-button-container {
    padding: 0;
    .radio-group-container {
      .radio-buttons {
        min-width: auto;
        width: 100%;
        // background: #c8c8c8;
        background: var(--semantic-color-background-primary);
  
        .radio-button {
          width: auto;
          label {
            padding: 0 8px;
          }
          .check-circle {
            margin-right: 3px;
          }
          &.checked-false label {
            border: none;
            transition: background .8s;
            padding-left: 15px;
            border-color: var(--semantic-color-content-interactive-primary-disabled);
            color: var(--semantic-color-content-interactive-primary-disabled);
            // cursor: not-allowed;
          }
          &.checked-false label {
            background: var(--semantic-color-surface-interactive-primary-default);
            color: var(--semantic-color-content-inverted-base-primary);
            // border: 1px solid var(--semantic-color-content-base-secondary);
          }
          &.checked-true label {
            background: var(--semantic-color-surface-interactive-primary-default);
            color: var(--semantic-color-content-inverted-base-primary);
            border: 1px solid var(--semantic-color-border-base-secondary);
          }
          &.checked-true,
          &.checked-false {
            &.disabled label {
              background: var(--semantic-color-surface-fields-disabled);
              color: var(--semantic-color-content-base-secondary);
              border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
            }
          }
        }
      }
    }
  }
  .deployed-button {
    border: none;
    background: var(--semantic-color-content-interactive-primary-hover);
    outline: none;
    color: var(--semantic-color-surface-base-primary);
    font-size: 13px;
    line-height: 16px;
    padding: 4px 8px;
    height: 32px;
    border-radius: 5px;
    margin: 0;
    min-width: max-content;
    width: auto;
    cursor: auto;
    opacity: .7;
    .svg-inline--fa {
      margin-right: 5px;
    }
  }
}

.account-column.cloud-name {
  display: flex;
  line-height: 30px;
  align-items: center;
}
.__react_component_tooltip.react-tooltip {
  padding: 2px 12px;
}
}
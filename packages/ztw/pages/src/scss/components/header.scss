@import "scss/mixins.scss";
@import "scss/colors.scss";


.ec-root-page {
    .app-header-container {
        @include DisplayFlex;
        padding: 10px 0 35px;

        .left-container .svg-inline--fa {
            font-size:  22px;
            margin-right: 8px;
            color: $grey4;
        }

        .right-container,
        .left-container {
            @include DisplayFlex;
            align-items: center;
            flex: .4;
        }

        .center-container {
            flex: .2;
            justify-content: center;
            align-items: center;
            @include DisplayFlex;

            .header-drop-down {
                width:  95px;
            }
        }
    }

    .drilldown-header-container {
        margin: 6px 0 45px 0;
        @include DisplayFlex;
        .drilldown-name {
            line-height: 30px;
            padding-top: 4px;
            .svg-inline--fa {
            height: 21px;
            color: $grey4;
            font-size: 20px;
            }
            .page-title {
            color: $grey1;
            font-size:  24px;
            margin-left: 8px;
            }
        }
    }
}
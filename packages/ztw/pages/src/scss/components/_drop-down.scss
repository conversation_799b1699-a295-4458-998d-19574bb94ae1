@import 'scss/colors.scss';
//Fonts
.ec-root-page {
.table-filter {
  div.drop-down-container {
    position: relative;
    width: 100%;
    display: inline-block;
    color: var(--semantic-color-content-interactive-primary-default);
    background-color: $dropdown-bkgrd-color;
    border: 1px solid $table-body-border-color;
    border-radius: 5px;
    box-shadow: 0 0px 2px 1px $default-box-shadow;
    button {
      color: inherit;
      background: inherit;
      border: none;
      border-radius: inherit;
    }
    button.drop-down-selected-value {
      width: 100%;
      align-items: center;
      box-shadow: none;
      // color: $dropdown-text-color;
      cursor: pointer;
      display: block;
      height: 28px;
      padding: 5px;
      text-align: left;
      font-size: 13px;
      .label-selected-items {
        text-overflow: ellipsis;
        white-space: nowrap;
        // width: 110px;
        width: calc(100% - 20px);
        overflow: hidden;
        display: inline-block;
      }
      .dropdown-selected-label {
        white-space: nowrap;
        width: calc(100% - 20px);
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
      }
      .label-selected-value {
        color: $input-label-color;
      }
      .dropdown-selected-sub-label {
        font-size: 12px;
        color: var(--semantic-color-content-base-primary); 
      }
      .dropdown-icon {
        float: right;
      }
      /* span {
      //  flex: 0.9;
      } */
      img {
        flex: 0.1;
        width: 20px;
      }
      .startDate, .endDate, .custom-date {
        display: inline-block;
      }
    }
    button.dropdown-read-only {
      color: gray;
      box-shadow: none;
      cursor: default;
    }
    button.add-filter-value {
      box-shadow: none;
      .fa-plus-circle {
        padding-right: 5px;
        color:  $anchor-color;
        font-size: 14px;
      }
      .dropdown-icon {
        float: none;
      }
    }
    .dropdown-box {
      .drop-down-list {
        position: absolute;
        margin-top: -1px;
        background-color: var(--semantic-color-surface-base-primary);
        min-width: 100%;
        max-width: calc(100% + 300px);
        border: 1px solid var(--semantic-color-border-base-primary);
        display: none;
        opacity: 0;
        transition: visibility 0s, opacity 0.1s linear;
        z-index: 9;
        .dropdown-list-header {
          height: 40px;
          background:$dropdown-bkgrd-color;
          .dropdown-search {
            border-bottom: 1px solid #2089c4;
            width: 90%;
            padding: 11px 0 4px 0;
            margin: 0 2px 0 6px;
            .dropdown-input-search {
              width: 77%;
              font-size: 13px;
              border: 0px;
              background: transparent;
              color: $form-input-label-color;
            }
            .search-icon {
              cursor: pointer;
              font-size: 12px !important;
              float: right;
              .dropdown-search-clear-icon {
                margin-left: -15px;
                margin-right: 10px;
                color: #999;
                cursor: pointer;
                font-size: 14px !important;
                vertical-align: middle;
              }
            }
          }
        }
        .dropdown-list-content {
          max-height: 200px;
          overflow-y: auto;
          @media only screen and (min-height: 731px) and (max-height: 790px) {
            max-height: 140px;
          }
          @media only screen and (max-height: 730px) {
            max-height: 90px;
          }
          .dropdown-list-item {
            border-radius: inherit;
            width: calc(100% - 1px);
            margin: 0 auto;
            padding: 0px;
            button {
              padding: 8px;
              width: 100%;
              cursor: pointer;
              text-align: left;
            }
          }
          .dropdown-list-item-parent {
            color: #85878a;
            font-size: 12px;
          }
          .dropdown-list-item-parent:hover {
            background: transparent;
            color: #85878a;
          }
          .dropdown-selected-value {
            background-color:$dropdown-list-item-bkgrd-active-color;
            color: $dropdown-list-item-text-active-color;
          }
          .dropdown-list-item.selected {
            background: #fafafa;
          }
          .dropdown-list-empty {
            @extend .dropdown-list-item;
            margin-top: 5px;
            margin-bottom: 10px;
            font-style: italic;
            text-align: center;
            cursor: default;
            &:hover{
                background: none;
            }
          }
          .dropdown-message-item {
            cursor: pointer;
            @extend .dropdown-list-empty;
          }
        }
        .dropdown-list-footer {
          height: 46px;
          border-top: 1px solid #f3f3f3;
          padding-top: 10px;
          background: $dropdown-bkgrd-color;
          font-size: 13px;
          .dropdown-action-done {
              float: left;
              margin-left: 6px;
              border: 1px solid #2089c4;
              border-radius: 4px;
              padding: 4px 16px;
              color: white;
              background: #2089c4;
              cursor: pointer;
          }
          .dropdown-action-cancel {
              float: right;
              margin-right: 6px;
              color: #2089c4;
              margin-top: 5px;
              font-size: 12px;
              cursor: pointer;
          }
        }
        .list-item-select-all {
          height: 32px;
          white-space: nowrap;
          padding: 8px;
          border-bottom: 1px solid var(--semantic-color-border-base-primary);
          .multi-select-list-item-value {
            padding: 0 10px;
            font-size: 13px;
          }
          .multi-select-list-item-checkbox {
            font-size: 16px;
          }
        }
        .multiselect-dropdown-list-content {
          padding: 8px;
          max-height: 200px;
          overflow-y: auto;
          @media only screen and (min-height: 731px) and (max-height: 790px) {
            max-height: 140px;
          }
          @media only screen and (max-height: 730px) {
            max-height: 90px;
          }
          .multiselect-dropdown-list-item {
              height: 28px;
              line-height: 28px;
              white-space: nowrap;
              .multi-select-list-item-checkbox {
                  font-size: 16px;
              }
              .item-deleted {
                  text-decoration: line-through;
              }
              .multi-select-list-item-value {
                  padding: 0 10px;
                  font-size: 13px;
              }
          }

          .parent {
            span {
              font-weight: bold;
            }
          }

          .child {
            margin-left: 15px;
          }
        }
      }
      .add-filter-list {
        border: 1px solid $default-border-color;
      }
    }
    .dropdown-box .drop-down-list.open {
      display: block;
      opacity: 1;
      z-index: 1000;
    }
    .drop-down-list.expand{
      min-width: 450px;
      max-width: 200%;
      .multiselect-dropdown-list-content-container{
        display: flex;
        .dropdown-left-panel,
        .multiselect-dropdown-selected-list-container {
          width: 50%;
          .dropdown-list-header{
            height: auto;
          }
          .dropdown-list-header-title {
            background: var(--semantic-color-surface-base-primary);
            color:  var(--semantic-color-content-base-primary);
            font-weight: 500;
            letter-spacing: 0;
            line-height: 20px;
            padding: 6px 10px;
          }
          .dropdown-search, .multiselect-dropdown-selected-list-search{
            background: $dropdown-bkgrd-color;
            margin: 0;
            padding: 11px 2px 4px 6px;
            border-bottom: none;
            width: 100%;
          }
          .multiselect-dropdown-list-item.child {
            padding-left: 15px;
          }
        }
        .multiselect-dropdown-selected-list-container {
          border-left: 1px solid $default-border-color;
          .multiselect-dropdown-selected-list{
            overflow-y: auto;
            padding: 8px;
            max-height: 200px;
            @media only screen and (min-height: 731px) and (max-height: 790px) {
              max-height: 140px;
            }
            @media only screen and (max-height: 730px) {
              max-height: 90px;
            }
            .multiselect-dropdown-selected-list-item {
              font-size: 13px;
              height: 28px;
              line-height: 28px;
              position: relative;
              .multiselect-dropdown-list-item-value{
                max-width: calc(100% - 25px);
                display: inline-block;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
              }
              .multiselect-remove-icon{
                top: 8px;
                right: 0;
              }
            }
          }
        }
      }
      .dropdown-deselect-all {
        display: inline-block;
        margin-left: 20px;
        padding: 5px 10px;
        cursor: pointer;
        color: var(--semantic-color-content-interactive-primary-default);
      }
    }
    .drop-down-list.left {
      right: 0;
    }
    &.invalid {
      border-color: var(--semantic-color-content-status-danger-primary);
      .drop-down-selected-value {
        box-shadow: 0 1px 0 #e74c3c;
      }
    }
  }
  .dropdown-input-filter {
    color: $text-input-color;
    display: inline-block;
    width: 115px;
    font-size: 13px;
    border: 0px;
    border-bottom: 1px solid #2089c4;
    padding-bottom: 5px;
    background: transparent;
    margin-left: 6px;
  }
  .multi-select-filters {
    // width: 200px;
    display: inline-block;
    width: 145px;
    margin-left: 6px;
  }
  div.dropdown-half-width {
    width: 160px;
  }
  .remove-dropdown-filter-icon {
    margin-left: 10px;
    font-size: 14px !important;
    color: #939393;
    cursor: pointer;
  }
}
}
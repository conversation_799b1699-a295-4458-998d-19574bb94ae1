@import "scss/colors.scss";
@import "scss/mixins.scss";

.ec-root-page {
.radio-button-container {
  padding: 0 15px;

  .charts {
    margin: 0  0 6px;
  }

  p {
    color: $grey1;
    font-weight: 500;
    margin: 15px 0 10px;
    text-transform: capitalize;
  }

  .radio-buttons {
    @include DisplayFlex;
    width: 100%;
    &.disabled {
      .radio-button {
        label {
          background: var(—surface-fields-disabled);
          color: var(--semantic-color-content-interactive-primary-disabled);
          border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
          cursor: not-allowed;
        }        
      }
    }

    .radio-button {
      width: 50%;
      &.disabled {
        label {
          background: var(—surface-fields-disabled);
          color: var(--semantic-color-content-interactive-primary-disabled);
          border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
          cursor: not-allowed;
        }
      }
      &:first-child label {
        border-radius: 5px 0 0 5px;
      }

      &:last-child label {
        border-radius: 0 5px 5px 0;
      }
      
      .chart-label {
        width: 18px;
        font-size: 13px;
      }

      label {
        @include DisplayFlex;
        cursor: pointer;
        position: relative;
        padding: 4px 20px 4px 5px;
        border: 1px solid var(--semantic-color-content-interactive-primary-disabled);
        color: var(--semantic-color-content-interactive-primary-disabled);
        align-items: center;
        justify-content: center;

        .check-circle {
          // width: 13px;
          font-size: 13px;
        }

        input {
          display: none;
        }
      }

      .check-circle {
        margin-right: .5rem
      }
    }
  }
}

.option-button-container.option-group-container {
  padding: 0 15px;

  .charts {
    margin: 0  0 6px;
  }

  p {
    color: $grey1;
    font-weight: 500;
    margin: 15px 0 10px;
    text-transform: capitalize;
  }

  .option-buttons {
    @include DisplayFlex;
    position: relative;
    width: fit-content;
    border-radius: 5px;
    background-color: transparent;
    border: none;
    max-height: 33px;
    min-width: 260px;

    &.disabled {
      .option-button {
        label {
          background: var(—surface-fields-disabled);
          color: var(--semantic-color-content-interactive-primary-disabled);
          border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
          cursor: not-allowed;
        }        
      }
    }

    .option-button {
      width: 50%;
      &.disabled {
        label {
          background: var(—surface-fields-disabled);
          color: var(--semantic-color-content-interactive-primary-disabled);
          border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
          cursor: not-allowed;
        }
      }    

      label {
        background: var(--semantic-color-background-primary);
        color:  var(--semantic-color-content-base-primary);
        border: none;
        height: 100%;
        min-height: 32px;
      }

      &:first-child label {
        left:  0; //1px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
      &:last-child label {
        left:  0; //-1px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
      &:nth-child(2):not(:last-child)  label {
        left: 0;
        border-radius: 0;
        border-left: none;
        border-right: none; 
      }
      &:nth-child(3):not(:last-child)  label {
        left: 0;
        border-radius: 0;
        // border-left: none;
        border-right: none; 
      }
      
      
      .chart-label {
        width: 18px;
        font-size: 13px;
      }

      label {
        @include DisplayFlex;
        cursor: pointer;
        position: relative;
        padding: 4px 20px 4px 5px;
        background-color: transparent;
        color:  var(--semantic-color-content-base-primary);
        align-items: center;
        justify-content: center;

        .check-circle {
          // width: 13px;
          font-size: 13px;
          color: var(--semantic-color-content-interactive-primary-default) ;
        }

        input {
          display: none;
        }
      }

      .check-circle {
        margin-right: .5rem
      }
    }
  }  
}

.option-button-container.option-group-container.single-option {
  margin-bottom: 11px;
  .option-buttons {
    min-width: fit-content;
    .option-button {
      width: 100%;
    }
  }
}

}
@import "scss/colors.scss";

.ec-root-page {
.checkbox-container {
    padding: 5px 0;

    .container {
        display: block;
        position: relative;
        padding-left: 25px;
        cursor: pointer;
        font-size: 13px;
        color: $blue2;

        input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;

            &:checked~.checkmark span {
                display: block;
            }
        }

        .checkmark {
            border: 1px solid;
            border-radius: 5px;
            position: absolute;
            top: 0;
            left: 0;
            height: 19px;
            width: 19px;
            border: 1px solid var(--semantic-color-border-base-primary);
            border-radius: 3px;

            span {
                position: absolute;
                display: none;
                left: 4px;
                top: 0;
                width: 8px;
                height: 13px;
                border: solid var(--semantic-color-border-base-primary);
                border-width: 0 2.5px 2.5px 0;
                transform: rotate(45deg);
            }
        }

    }

    .disabled {
        color: var(--semantic-color-border-interactive-primary-disabled);
        cursor: default;

        .checkmark {
            border: 1px solid var(--semantic-color-border-interactive-primary-disabled);
        }
    }
}
}
@import 'scss/colors.scss';

.ec-root-page {
.oneui-banner {
	margin-top: 3.5rem;
}

.ec-notification-container {
    z-index: 10000000;
    min-width: 41.875rem;
    max-width: 41.875rem;
    min-height: 2.5rem;
    max-height: 22.5rem;
    overflow: hidden;
    display: flex;
    --top: 0.375rem;
    position: fixed;
    top: var(--top);
    left: 50%;
    animation: animation-open ease 200ms;
    animation-fill-mode: forwards;
    will-change: top;
    &.persistent {
        top: 0px
    }

    &.poc-class {
        left: 0;
        top: 97px;
        width: 100%;
        padding: 0 24px;
        margin: 0px;

        &.pocnotice {
            width: 100%;
            background: #fffced;
            border: 1px solid var(--semantic-color-border-base-primary);

            .notification-message-content {
                display: block;
                color: var(--semantic-color-content-base-primary);

                .form-link-text {
                    display: inline-block;
                    line-height: normal;
                    vertical-align: inherit;
                    padding: 0;
                    margin: 0px;
                }
            }
        }
    }


    .notification-icon {
        width: 2.5rem;
        font-size: 1.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--theme);
        color: var(--semantic-color-surface-base-primary);
    }
    & {
        .notification-icon {
            border-top-left-radius: 0.25rem;
            border-bottom-left-radius: 0.25rem;
        }

        .notification-content {
            border: 0.078125rem solid var(--theme); // ---  BUG-123695 (0.078125rem instead of 0.0625rem)
            border-right: none;
            overflow-y: auto;
        }

        .notification-content.active {
            overflow-y: hidden;
        }

        .notification-icon-close {
            border: 0.078125rem solid var(--theme); // --- BUG-123695 (0.078125rem instead of 0.0625rem)
            border-left: none;
            border-top-right-radius: 0.25rem;
            border-bottom-right-radius: 0.25rem;
        }
    }
}

.notification {
    border: 1px solid var(--theme);
    border-top-left-radius: 0rem;
    border-bottom-left-radius: 0rem;
    border-radius: 3px;
    color: #3498db;
    font-size: 13px;
    line-height: 25px;
    margin: 0 auto;
    padding: 5px 5px 5px 10px;
    text-align: left;
    width: 720px;
    max-height: 220px;
    overflow-y: auto;
    &.persistent {
        background: $yellow1;
        border-color: $yellow1;
    }

    &.warning {
        background: #fef7ec;
        border-color: #fce7c5;
        color: #d5880b;
    }

    &.error {
        border-color: var(--semantic-color-border-status-danger-active);
        border-top-left-radius: 0rem;
        border-bottom-left-radius: 0rem;
    }

    & + & {
        margin-top: 5px;
    }

    &.pocnotice {
        width: 100%;
        background: #fffced;
        border: 1px solid var(--semantic-color-border-base-primary);
        position: absolute;
        top: 87px;
        width: 100%;
        padding: 8px 8px 8px 16px;
        box-shadow: 0 2px 4px -1px rgba(0,0,0,.7);
        border-radius: 0px;

        .notification-message-content {
            display: block;
            color: var(--semantic-color-content-base-primary);
            line-height: 20px;

            &:first-child {
                line-height: inherit;
            }

            .form-link-text {
                display: inline-block;
                line-height: normal;
                vertical-align: inherit;
                padding: 0;
                margin: 0px;
            }
        }
    }


}

.notification-message {
    display: inline-block;
    overflow-y: auto;
    vertical-align: top;
    width: calc(100% - 25px);
    word-break: normal;
    border-top-left-radius: 0rem;
    border-bottom-left-radius: 0rem;
    &.persistent {
        text-align: center;
    }
}

.notification-close-icon {
    border: none;
    align-items: center;
    justify-content: center;
    color: var(--theme);
    background-color: var(--semantic-color-surface-base-primary);
    cursor: pointer;

    svg {
        will-change: transform;
        transition: transform ease 0.1s;
    }

    &:hover {
        svg {
            transform: scale(1.07);
        }
    }
}

.ec-notification-long-message-content {
    white-space: break-spaces;
    max-width: 720px;
    max-height: 500px;
    overflow: auto;
}
}
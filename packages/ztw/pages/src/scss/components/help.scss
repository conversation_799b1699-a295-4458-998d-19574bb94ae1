@import 'scss/mixins.scss';
@import 'scss/colors.scss';

.ec-root-page {
.turn-on {
  visibility: visible;
}

.inline-help-container {
  width: calc(100% - 10px);
  height: 100%;
  overflow: hidden;
  /* should hide until the help pages are ready*/
  /** to TURN ON in runtime **/
  /* document.getElementsByClassName('inline-help-container')[0].style.visibility = 'visible'; */
  // visibility: hidden;

  .inline-help-icons-container {
    color: $white;
    position: absolute;
    right: 10px;
    bottom: 50px;
    z-index: 99;

    &:focus {
      outline: none;
    }

    .inline-help-text-icon {
      width: auto;
      text-align: center;
      height:  43px;
      background: var(--semantic-color-content-interactive-primary-default);
      border-radius: 1.25rem;
      box-shadow: 0 1px 5px $blueShadow2;

      .inline-help-logo {
        @include inline-block(middle);
      }

      .inline-help-text {
        color: var(--semantic-color-content-immutable-white);
        display: inline-block;
        font-size: .8125rem;
        margin: -.375rem 0 0;
        vertical-align: middle;
      }
    }

    .text-help-container {
      padding: 10px;

      &:hover {
        cursor: pointer;
        opacity: 1;
      }
    }
  }

  .inline-help-iframes-container-outer {
    font-size: 13px;
    position: fixed;
    min-width:  351px;
    min-height:  469px;
    border: 0.7px solid $white1;
    border-radius: 4px 4px 0 0;
    box-shadow: 0 1px 5px $grey31;
    resize: both;
    overflow: auto;
    &::-webkit-resizer {
      background: transparent;
    }

    .inline-help-iframes-container-inner {
      width: 100%;
      height: 100%;
      overflow: hidden;
      .inline-block {
        display: inline-block;
        vertical-align: middle;
      }

      .half-width {
        width: 50%;
      }

      .inline-help-iframes-header {
        padding: 10px 15px 30px;
        color: $grey4;
        background: $white1;
        cursor: move;
        position: relative;

        .iframe-header-icon {
          position: absolute;
          top:  0.7px;
          left:  0.7px;
          width: 10px;
          height: 10px;
        }

        .iframe-controls {
          text-align: right;

          .control-icon {
            font-size: 15px;
            cursor: pointer;
          }
        }
      }

      .inline-help-iframes-content-body {
        height: calc(100% - 40px);
        background: $white;
        border-top: 0.7px solid var(--semantic-color-border-base-primary);
        border-bottom: 0.7px solid var(--semantic-color-border-base-primary);
        position: relative;

        .iframes-content {
          width: 100%;
          height: 100%;
        }

        iframe {
          width: 100%;
        }
      }

      .inline-help-iframes-footer {
        color: $blue20;
        border: 0.7px solid var(--semantic-color-border-base-primary);
        // box-shadow: 0 1px 1px $grey31;
        background-color: var(--semantic-color-background-primary);
        display: none;
        .iframes-footer-options {
          padding: 10px 15px;
          text-align: center;
          border-right: 0.7px solid var(--semantic-color-border-base-primary);
          cursor: pointer;
          position: relative;

          &:last-child {
            border-right: 0;
          }

          &:hover {
            color: var(--semantic-color-content-interactive-primary-hover);
          }

          &.disabled {
            cursor: default;
            background: var(—surface-fields-disabled);
            color: var(--semantic-color-content-interactive-primary-disabled);            
          }
        }
      }
    }
  }
}
}
.ec-root-page {
.subscription-required{
    background: var(--semantic-color-background-primary);
    border-radius:3px;
    height:100%;
    text-align:
    center;
    width:100%;
    &.background-white {
        background: var(--semantic-color-background-primary);
    }
}
.subscription-required:before{content:"";display:inline-block;height:100%;vertical-align:middle;width:0;}
.subscription-required-splash{
    background: var(--semantic-color-background-primary);
    border:1px solid var(--semantic-color-border-base-primary);border-radius:3px;display:inline-block;max-width:480px;padding:20px;text-align:left;vertical-align:middle;}
.subscription-required-splash-title{color: var(--semantic-color-content-interactive-primary-default);font-size:18px;margin-bottom:20px;}
.subscription-required-splash-message{color:var(--semantic-color-content-interactive-primary-disabled);font-size:14px;list-style:1.4em;}
.page-content{
    background: var(--semantic-color-background-primary);
    bottom:37px;overflow:hidden;position:absolute;left:88px;top:0px;width:calc(100% - 88px);}
.page-layout{height:100%;width:100%;}
.page-sidebar{position:absolute;z-index:1;top:21px;left:24px;}
.page-main{bottom:0;left:0px;overflow:hidden;position:absolute;right:0px;top:0;}
.page-title{color:var(--semantic-color-content-base-secondary);font-size:20px;padding:18px 24px 18px 24px;}
.page-main-content{
    bottom:0;
    left:0;
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 20%;
    right:0;
    top:56px;
}
}
@import 'scss/mixins.scss';
@import 'scss/colors.scss';
@import 'scss/defaults.scss';

#root{
    height: 100%;
    width: 100%;
	min-width: 628px;
}

#r-app {
	height: 100%;
	width: 100%;
	overflow-y: auto;
  .ec-root-page {
    height: 100%;
  }
}

.ec-root-page {
	.modal-overlay {
		overflow-y: auto;
	}
}
.ec-root-page {
	.modal-overlay {
		overflow-y: auto;
	}
	.page{
		@include DisplayFlex;
		align-items: stretch;
		align-content: stretch;
		width: 100%;
		min-height: 100vh;
		overflow: hidden;

		.content {
			flex: 1 1 auto;
			overflow-y: auto;
			position: relative; //change reference for content
		}
	}
	// body {
//     margin: 0;
//     padding: 0;
//     box-sizing: border-box;
// }

}

.ec-root-page {
.modal-overlay {
  .modal-body.splash-screen-modal {
    width: 884px;
    h1 {
      display: block;
      font-size: 2em;
      margin-block-start: 0.67em;
      margin-block-end: 0.67em;
      margin-inline-start: 0px;
      margin-inline-end: 0px;
      font-weight: bold;
    }
    h2 {
      color: var(--semantic-color-content-base-primary);
    }
    p {
      margin: 1em 0;
      color: var(--semantic-color-content-base-primary);
    }
    .dialog-body-splash {
      background: var(--semantic-color-background-pale);
      border: 1px solid var(--semantic-color-border-base-primary);
      overflow-x: hidden;
      overflow-y: auto;
      padding: 16px 24px;
      width: 100%;
    }
    .modal-header{
      p {
        background-color: var(--semantic-color-background-primary);
      }
    }
    .modal-content {
      position: relative;
      .content {
        background: var(--semantic-color-background-primary);
        border: 1px solid var(--semantic-color-border-base-primary);
        overflow-x: hidden;
        overflow-y: auto;
        padding: 16px 24px;
        width: 100%;
      }
      a {
        color: var(--semantic-color-content-status-info-primary);
        text-decoration: none;
      }
      span {
        color: var(--semantic-color-content-base-primary);
      }
      #dontshow span {
        color:  var(--semantic-color-content-base-primary);
      }
      .modal-footer {
        background: var(--semantic-color-background-primary);
        border-top: 1px solid var(--semantic-color-border-base-primary);
        border-radius: 0 0 5px 5px;
        padding: 16px;
        width: 100%;
        text-align: right;
        margin-top: 0;
        display: block;
        position: sticky;
        bottom: 0;
        top: auto;
        .primary-button {
          margin: 0px 16px;
        }
      }
    }

  }
}


.eusa-banner {
  @include DisplayFlex;
  cursor: pointer;
  background-color: $yellow1;
  // background-color: var(--semantic-color-surface-status-warning-default); yet to be supported
  color: var(--semantic-color-content-status-warning-primary);
  padding: 4px 16px;
  position: absolute;
  top: 0;
  width: auto;
  border-radius: 3px;
  left: 40%;
  z-index: 1;

  .fa-exclamation-triangle{
    color: var(--semantic-color-content-status-warning-primary);
    font-size: 14px;
    margin-right: 5px;
  }

  .text-label {
    font-size: 12px;
    cursor: pointer;
    margin-bottom: 0;
    margin-left: 5px;
  }

  .toggle-label-button {
    border: none;
    background: $transparent;
    color: var(--semantic-color-content-interactive-primary-default);
    font-size: 10px;
    font-weight: bold;
    margin-left: 5px;
  }
}


.eusa-modal-content {

  overflow: auto;
  min-height: 30vh;
  max-height: 70vh;

  .ecui-waiting-overlay  {
    align-self: center;
  }

  .container-eusa {
    padding: 20px;
    text-align: justify;
    p {
      color: var(--semantic-color-content-base-secondary) !important;
    }


    &-fields {
      @include DisplayFlex;
      background: $white;
      border-radius: 2px;
      background-color: $white;
      box-shadow: 0 0 11px 0 $grey14;

      >div {
        flex: 0.5;
      }
    }
  }

}
}
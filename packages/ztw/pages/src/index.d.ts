// Type definitions for @ztw/pages library
// Auto-generated during build process

declare module '@ztw/pages' {
  import * as React from 'react';
  
  // Main library export
  export interface EdgeUXLibrary {
    [key: string]: any;
  }
  
  // Component exports
  export const AppProvider: React.ComponentType<any>;
  export const store: any;
  export const reducers: any;
  export const sagas: any;
  
  // Re-export everything from lib-entry
  export * from './lib-entry';
  
  const library: EdgeUXLibrary;
  export default library;
}

// UMD global export
declare global {
  interface Window {
    EdgeUXLibrary: any;
  }
}

export = EdgeUXLibrary;
declare const EdgeUXLibrary: any;

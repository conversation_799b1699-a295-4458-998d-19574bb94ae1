import axios from 'axios';
import { GET_TIMESTAMP_BLACKLIST, BUILD_TARGETS } from 'config';
import { handleError } from './handleError';

let baseURL = (sessionStorage.getItem('NEXT_PUBLIC_API_ENDPOINT')?.trim()) ? sessionStorage.getItem('NEXT_PUBLIC_API_ENDPOINT') : process.env.REACT_APP_API_BASE_URL;

// Since Prod and other envs have a diff API Key we need both of them at this moment.
if (sessionStorage.getItem('api-endpoint')?.trim()) {
  baseURL = sessionStorage.getItem('api-endpoint');
}

const headerValues = {
  'Content-Type': 'application/json',
  'Cache-Control': 'must-understand, no-store',
};

const http = axios.create({
  baseURL,
  timeout: 86400000, // increased to support image download (usually 1 - 2 GB)
  withCredentials: true,
  headers: headerValues,
});

// let environment;
// let getBearerToken;

// export function initializeHttpClient(env, tokenGetter) {
//   environment = env;
//   getBearerToken = tokenGetter;
// }

// if (process.env.REACT_APP_BUILD_TARGET == BUILD_TARGETS.ONEUI) {
//   http.interceptors.request.use((config) => {
//     if (!environment || !getBearerToken) {
//       throw new Error(
//         'HTTP client is not initialized. Call initializeHttpClient before making requests.',
//       );
//     }
//     const publicAPIEndpoint = environment.endpoint();
//     const accessToken = getBearerToken();

//     config.headers.Authorization = `Bearer ${accessToken}`;
//     config.baseURL = `${publicAPIEndpoint}/private/ziam`;

//     return config;
//   });
// }

http.interceptors.response.use(
  (response) => response,
  handleError,
);

http.interceptors.request.use(
  (config) => {
    const configWithHeader = config;

    const cookies = document && (document.cookie || '');
    let ecCookie = cookies;
    if (cookies.indexOf(';') > -1) {
      const cookieList = cookies.split(';');
      cookieList.filter((e) => e.trim().startsWith('ZS_SESSION_CODE'));
      // eslint-disable-next-line prefer-destructuring
      ecCookie = cookieList[0];
    }

    const cookie = ecCookie.split('=');
    // eslint-disable-next-line prefer-destructuring
    const zsSessionCode = cookie[1];

    if (process.env.REACT_APP_BUILD_TARGET !== BUILD_TARGETS.ONEUI) {
      configWithHeader.headers.ZS_CUSTOM_CODE = zsSessionCode;
    }

    if (window.ZtwUxpApp) {
      configWithHeader.headers['x-zscaler-cloud'] = window.ZtwUxpApp?.cloudName || '';
      configWithHeader.headers['x-zscaler-tenant'] = window.ZtwUxpApp?.tenantId || '';
    } else {
      configWithHeader.headers['x-zscaler-cloud'] = '';
      configWithHeader.headers['x-zscaler-tenant'] = '';
    }
    // To alliviate caching in get call. All  get calls will have
    // timestamp sent as query param
    
    if (configWithHeader.method === 'get') {
      const { url } = configWithHeader;
      let confURL = url;
      if (confURL.indexOf('ec/') > -1) {
        confURL = confURL.substring(confURL.indexOf('ec/') + 2, confURL.length);
      }
      if (!GET_TIMESTAMP_BLACKLIST.some((x) => confURL.includes(x))) {
        // eslint-disable-next-line
        configWithHeader.url = `${configWithHeader.url}${configWithHeader.url.includes('?')?'&':'?'}timestamp=${Date.now()}`;
      }
    }
    return configWithHeader;
  },
  handleError,
);

export default http;

import PersistentStorage, { LS_CSRF_TOKEN } from 'utils/persistentStorage';
import { BUILD_TARGETS } from 'config';

import http from './http';

let environment;
let getBearerToken;

export function initializeHttpClient(env, tokenGetter) {
  environment = env;
  getBearerToken = tokenGetter;
}

const getUpdatedOptions = (options) => {
  if (process.env.NODE_ENV === 'production' || (process && process.env && process.env.REACT_APP_CSRF_ENABLED)) {
    return {
      ...options,
      headers: {
        'X-CSRF-Token': PersistentStorage.getItem(LS_CSRF_TOKEN) || 'Fetch',
      },
    };
  }

  if (process.env.REACT_APP_BUILD_TARGET === BUILD_TARGETS.ONEUI) {
    // const publicAPIEndpoint = environment.endpoint();
    const accessToken =  getBearerToken ? getBearerToken() : (() => sessionStorage.getItem('bearer-token'))();
    return {
      ...options,
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    };
  }

  return options;
};

export const genericInterface = (RESOURCE) => ({
  create: async (obj, opts) => http.post(RESOURCE, obj, getUpdatedOptions(opts)),
  read: async (id, opts) => {
    const url = id ? `${RESOURCE}/${id}` : RESOURCE;
    return http.get(url, getUpdatedOptions(opts));
  },
  update: async (obj, opts) => http.put(`${RESOURCE}${obj && obj.id ? `/${obj.id}` : ''}`, obj, getUpdatedOptions(opts)),
  del: async (obj, opts) => http.delete(RESOURCE, getUpdatedOptions(opts)),
});

genericInterface.post = genericInterface.create;

export default genericInterface;

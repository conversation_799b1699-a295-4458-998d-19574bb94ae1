// @flow
import React from 'react';
import { reduxForm } from 'redux-form';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
 
export function BasicCustomAppForm({
  valid,
  t,
  modalLoading,
  isCCPage,
  enableType,
  handleCancel,
  handleEnable,
}) {
  const { rows } = enableType || {};
  const { id } = enableType || {};

  const onSubmitHandler = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    handleEnable(rows);
  };

  return (
    <form onSubmit={onSubmitHandler} className="add-custom-app-form delete-confirm-form cc-group">
      <Loading loading={modalLoading}>
        <div className="form-sections-container">
          <div className="form-section">
            <p className="are-you-sure-you-want">
              {isCCPage && id === 'ONE_CC' && t('ENABLE_CLOUD_CONNECTOR_CONFIRMATION')}
              {isCCPage && id === 'ONE_CC_GROUP' && (t('ENABLE_CLOUD_CONNECTOR_GROUP_CONFIRMATION')).replace('{0}', rows && rows.length)}
              {isCCPage && id === 'SELECTED_CC' && (t('ENABLE_CLOUD_CONNECTOR_SELECTED_CONFIRMATION')).replace('{0}', rows && rows.length)}
              {!isCCPage && id === 'ONE_CC' && t('ENABLE_BRANCH_CONNECTOR_CONFIRMATION')}
              {!isCCPage && id === 'ONE_CC_GROUP' && (t('ENABLE_BRANCH_CONNECTOR_GROUP_CONFIRMATION')).replace('{0}', rows && rows.length)}
              {!isCCPage && id === 'SELECTED_CC' && (t('ENABLE_BRANCH_CONNECTOR_SELECTED_CONFIRMATION')).replace('{0}', rows && rows.length)}
            </p>
          </div>
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="submit" className={`submit ${!valid ? 'disabled' : ''}`}>{t('ENABLE')}</button>
            <button type="button" className="cancel" onClick={() => handleCancel(false)}>{t('CANCEL')}</button>
          </div>
        </div>
      </Loading>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  handleCancel: PropTypes.func,
  handleEnable: PropTypes.func,
  t: PropTypes.func,
  modalLoading: PropTypes.bool,
  isCCPage: PropTypes.bool,
  enableType: PropTypes.shape({}),
  valid: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  handleCancel: null,
  handleEnable: null,
  t: (str) => str,
  modalLoading: false,
  isCCPage: false,
  enableType: {},
  valid: true,
};

const DeleteConfirmationForm = reduxForm({
  form: 'deleteConfirmationForm',
})(BasicCustomAppForm);

export default (withTranslation()(DeleteConfirmationForm));

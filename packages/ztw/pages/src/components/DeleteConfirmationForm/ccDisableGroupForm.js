// @flow
import React from 'react';
import { reduxForm } from 'redux-form';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
 
export function BasicCustomAppForm({
  valid,
  t,
  modalLoading,
  isCCPage,
  disableType,
  handleCancel,
  handleDisable,
}) {
  const { rows } = disableType || {};
  const { id } = disableType || {};

  const onSubmitHandler = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    handleDisable(rows);
  };

  return (
    <form onSubmit={onSubmitHandler} className="add-custom-app-form delete-confirm-form cc-group">
      <Loading loading={modalLoading}>
        <div className="form-sections-container">
          <div className="form-section">
            <p className="are-you-sure-you-want">
              {isCCPage && id === 'ONE_CC' && t('DISABLE_CLOUD_CONNECTOR_CONFIRMATION')}
              {isCCPage && id === 'ONE_CC_GROUP' && (t('DISABLE_CLOUD_CONNECTOR_GROUP_CONFIRMATION')).replace('{0}', rows && rows.length)}
              {isCCPage && id === 'SELECTED_CC' && (t('DISABLE_CLOUD_CONNECTOR_SELECTED_CONFIRMATION')).replace('{0}', rows && rows.length)}
              {!isCCPage && id === 'ONE_CC' && t('DISABLE_BRANCH_CONNECTOR_CONFIRMATION')}
              {!isCCPage && id === 'ONE_CC_GROUP' && (t('DISABLE_BRANCH_CONNECTOR_GROUP_CONFIRMATION')).replace('{0}', rows && rows.length)}
              {!isCCPage && id === 'SELECTED_CC' && (t('DISABLE_BRANCH_CONNECTOR_SELECTED_CONFIRMATION')).replace('{0}', rows && rows.length)}
            </p>
          </div>
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="submit" className={`submit ${!valid ? 'disabled' : ''}`}>{t('DISABLE')}</button>
            <button type="button" className="cancel" onClick={() => handleCancel(false)}>{t('CANCEL')}</button>
          </div>
        </div>
      </Loading>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  handleCancel: PropTypes.func,
  handleDisable: PropTypes.func,
  t: PropTypes.func,
  isCCPage: PropTypes.bool,
  modalLoading: PropTypes.bool,
  disableType: PropTypes.shape({}),
  valid: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  handleCancel: null,
  handleDisable: null,
  t: (str) => str,
  isCCPage: false,
  modalLoading: false,
  disableType: {},
  valid: true,
};

const DeleteConfirmationForm = reduxForm({
  form: 'deleteConfirmationForm',
})(BasicCustomAppForm);

export default (withTranslation()(DeleteConfirmationForm));

// @flow
import React from 'react';
import { reduxForm } from 'redux-form';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
 
export function BasicCustomAppForm(props) {
  const {
    valid,
    t,
    modalLoading,
    handleCancel,
    handleUpdate,
    selectedRowID,
    selectedStatus,
    provisioningTemplatesData,
  } = props;
  const onSubmitHandler = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    handleUpdate(selectedRowID);
  };
  
  const selectedRow = provisioningTemplatesData.find((x) => x.id === selectedRowID);
  const isGateway = selectedRow?.provUrlData?.deviceTemplate?.deployAsGateway;

  return (
    <form onSubmit={onSubmitHandler} className="add-custom-app-form delete-confirm-form cc-group">
      <Loading loading={modalLoading}>
        <div className="form-sections-container">
          <div className="form-section">
            <p className="are-you-sure-you-want">

              {isGateway
                && (
                  <>
                    {selectedStatus !== 'STAGED'
                      ? t('STATUS_UPDATE_CONFIRMATION_MESSAGE_STAGED_MVP1')
                      : t('STATUS_UPDATE_CONFIRMATION_MESSAGE_NOT_DEPLOYED')}
                  </>
                )}

              {!isGateway
                && (
                  <>
                    {(selectedStatus !== 'STAGED')
                      ? t('STATUS_UPDATE_CONFIRMATION_MESSAGE_STAGED')
                      : t('STATUS_UPDATE_CONFIRMATION_MESSAGE_NOT_DEPLOYED')}
                  </>
                )}
            </p>
            <br />
            {!isGateway
              ? t('STATUS_UPDATE_CONFIRMATION_MESSAGE')
              : t('STATUS_UPDATE_CONFIRMATION_MESSAGE_MVP1')}

          </div>
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="submit" className={`submit ${!valid ? 'disabled' : ''}`}>{t('UPDATE')}</button>
            <button type="button" className="cancel" onClick={() => handleCancel(false)}>{t('CANCEL')}</button>
          </div>
        </div>
      </Loading>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  handleCancel: PropTypes.func,
  handleUpdate: PropTypes.func,
  modalLoading: PropTypes.bool,
  provisioningTemplatesData: PropTypes.arrayOf(PropTypes.shape()),
  selectedRowID: PropTypes.string,
  selectedStatus: PropTypes.string,
  t: PropTypes.func,
  valid: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  provisioningTemplatesData: null,
  handleCancel: null,
  handleUpdate: null,
  modalLoading: false,
  selectedRowID: null,
  selectedStatus: '',
  t: (str) => str,
  valid: true,
};

const provInfoStatusUpdateForm = reduxForm({
  form: 'provInfoStatusUpdateForm',
})(BasicCustomAppForm);

export default (withTranslation()(provInfoStatusUpdateForm));

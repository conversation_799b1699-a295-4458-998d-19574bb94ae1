// @flow
import React, { useState } from 'react';
import { reduxForm } from 'redux-form';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
 
export function BasicCustomAppForm(props) {
  const {
    t,
    message,
    modalLoading,
    selectedRowID,
    handleCancel,
    handleEnableDisable,
    enable,
  } = props;
  const onSubmitHandler = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    handleEnableDisable(selectedRowID, enable);
  };
  const [valid, setValid] = useState(false);

  return (
    <form onSubmit={onSubmitHandler} className="add-custom-app-form delete-confirm-form">
      <Loading loading={modalLoading}>
        <div className="form-sections-container">
          <div className="form-section">
            {message || (
              <p className="modal-text">
                {enable ? t('ENABLE_DATA_COLLECTION_DESCRIPTION') : t('DISABLE_DATA_COLLECTION_DESCRIPTION')}
              </p>
            )}
          </div>
          <div className="form-section">
            {message || (
              <>
                <p className="are-you-sure-text">
                  {t('ARE_YOU_SURE')}
                </p>
                <span className="are-you-sure">
                  <input
                    id="areYouSure"
                    type="checkbox"
                    onChange={(x) => setValid(x.target.checked)} />
                  <span className="i-uderstand-the-consequence">
                    {t('I_UNDERSTAND_THE_CONSEQUENCE')}
                  </span>
                </span>
              </>
            )}
          </div>
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="submit" disabled={!valid} className={`submit ${!valid ? 'disabled' : ''}`}>{enable ? t('ENABLE') : t('DISABLE')}</button>
            <button type="button" className="cancel" onClick={() => handleCancel(false)}>{t('CANCEL')}</button>
          </div>
        </div>
      </Loading>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  handleCancel: PropTypes.func,
  handleEnableDisable: PropTypes.func,
  t: PropTypes.func,
  message: PropTypes.string,
  modalLoading: PropTypes.bool,
  selectedRowID: PropTypes.string,
  enable: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  handleCancel: noop,
  handleEnableDisable: noop,
  t: (str) => str,
  message: null,
  modalLoading: false,
  selectedRowID: null,
  enable: true,
};

const DeleteConfirmationForm = reduxForm({
  form: 'deleteConfirmationForm',
})(BasicCustomAppForm);

export default (withTranslation()(DeleteConfirmationForm));

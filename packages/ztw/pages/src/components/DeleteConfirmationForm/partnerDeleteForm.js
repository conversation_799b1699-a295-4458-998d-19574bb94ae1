// @flow
import React, { useState } from 'react';
import { reduxForm } from 'redux-form';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
 
export function BasicCustomAppForm({
  t,
  message,
  bottomMessage,
  modalLoading,
  selectedRowID,
  handleCancel,
  handleDelete,
  subItems,
}) {
  const onSubmitHandler = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    handleDelete(selectedRowID, subItems);
  };
  const [valid, setValid] = useState();

  return (
    <form onSubmit={onSubmitHandler} className="add-custom-app-form delete-confirm-form">
      <Loading loading={modalLoading}>
        <div className="form-sections-container">
          <div className="form-section">
            {message || (
              <p className="modal-text">
                {/* {subItems && subItems.length > 1 ?
                    t('DELETE_CONFIRMATION_MESSAGE2') :
                    t('DELETE_CONFIRMATION_MESSAGE1')} */}
                { t('DELETE_ACCOUNT_DESCRIPTION')}
              </p>
            )}
          </div>
          <div className="form-section">
            {bottomMessage || (
              <>
                <p className="are-you-sure-text">
                  {t('ARE_YOU_SURE')}
                </p>
                <span className="are-you-sure">
                  <input
                    id="areYouSure"
                    type="checkbox"
                    onChange={(x) => setValid(x.target.checked)} />
                  <span className="i-uderstand-the-consequence">
                    {t('I_UNDERSTAND_THE_CONSEQUENCE')}
                  </span>
                </span>
              </>
            )}
          </div>
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="submit" disabled={!valid} className={`submit ${!valid ? 'disabled' : ''}`}>{t('DELETE')}</button>
            <button type="button" className="cancel" onClick={() => handleCancel(false)}>{t('CANCEL')}</button>
          </div>
        </div>
      </Loading>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  handleCancel: PropTypes.func,
  handleDelete: PropTypes.func,
  t: PropTypes.func,
  message: PropTypes.string,
  bottomMessage: PropTypes.string,
  modalLoading: PropTypes.bool,
  selectedRowID: PropTypes.string,
  subItems: PropTypes.shape([]),
};

BasicCustomAppForm.defaultProps = {
  handleCancel: noop,
  handleDelete: noop,
  t: (str) => str,
  message: null,
  bottomMessage: null,
  modalLoading: false,
  selectedRowID: null,
  subItems: [],
};

const DeleteConfirmationForm = reduxForm({
  form: 'deleteConfirmationForm',
})(BasicCustomAppForm);

export default (withTranslation()(DeleteConfirmationForm));

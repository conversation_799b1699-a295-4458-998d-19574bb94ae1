// @flow
import React from 'react';
import { reduxForm } from 'redux-form';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
 
export function BasicCustomAppForm({
  valid,
  t,
  modalLoading,
  handleCancel,
  handleDelete,
  parentItem,
}) {
  const onSubmitHandler = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    handleDelete(parentItem[0], true);
  };

  return (
    <form onSubmit={onSubmitHandler} className="add-custom-app-form delete-confirm-form cc-group">
      <Loading loading={modalLoading}>
        <div className="form-sections-container">
          <div className="form-section">
            <p className="are-you-sure-you-want">
              {t('DELETE_CC_GROUP_CONFIRMATION')}
            </p>
          </div>
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="submit" className={`submit ${!valid ? 'disabled' : ''}`}>{t('CONFIRM')}</button>
            <button type="button" className="cancel" onClick={() => handleCancel(false)}>{t('CANCEL')}</button>
          </div>
        </div>
      </Loading>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  handleCancel: PropTypes.func,
  handleDelete: PropTypes.func,
  t: PropTypes.func,
  modalLoading: PropTypes.bool,
  valid: PropTypes.bool,
  parentItem: PropTypes.arrayOf({}),
};

BasicCustomAppForm.defaultProps = {
  handleCancel: noop,
  handleDelete: noop,
  t: (str) => str,
  modalLoading: false,
  valid: true,
  parentItem: [],
};

const DeleteConfirmationForm = reduxForm({
  form: 'deleteConfirmationForm',
})(BasicCustomAppForm);

export default (withTranslation()(DeleteConfirmationForm));

import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { Trans } from 'react-i18next';
import ReactTooltip from 'react-tooltip';
import { noop } from 'utils/lodash';

import optionButton from '../OptionButton';

function optionGroup(props = {}) {
  const {
    name,
    options,
    label,
    disabled,
    styleClass,
    styleClassLabel,
    id,
    onChange,
  } = props;
  
  return (
    <div className={`option-button-container option-group-container ${styleClass}`}>
      <ReactTooltip place="top" type="info" effect="solid" offset={{ left: 115 }} />
      {label && <p data-tip={`Tooltip for ${label}`} className={`${styleClassLabel}`}><Trans>{label}</Trans></p>}
      <div className={`option-buttons ${disabled ? 'disabled' : ''}`} id={id}>
        {options.map((item) => (
          <Field
            key={item.value}
            disabled={item.disabled || disabled}
            name={name}
            component={optionButton}
            onChange={onChange}
            type="radio"
            checked={item.checked}
            value={item.value}
            label={item.label} />
        ))}
      </div>
    </div>
  );
}

optionGroup.propTypes = {
  label: PropTypes.string,
  name: PropTypes.string.isRequired,
  options: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    checked: PropTypes.bool,
  })).isRequired,
  disabled: PropTypes.bool,
  styleClass: PropTypes.string,
  styleClassLabel: PropTypes.string,
  id: PropTypes.string,
  onChange: PropTypes.func,
};

optionGroup.defaultProps = {
  label: null,
  disabled: false,
  styleClass: '',
  styleClassLabel: '',
  id: null,
  onChange: noop,
};

export default optionGroup;

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDownload } from '@fortawesome/pro-solid-svg-icons';

const infoApplyFilterFirst = () => (
  <div className="insights-widget">
    <div className="insights-widget-view insights-widget-view-no-header">
      {/* <div className="insights-refresh-overlay hidden -js-refresh-overlay"> */}
      <div className="view-empty-badge">
        <div className="view-empty-badge-icon insights-fas-fa-download">
          <FontAwesomeIcon icon={faDownload} className="info-circle" />
        </div>
        <div className="view-empty-text">
          <span className="insights_empty_text">Set the options on the left and </span>
          <span className="insights_empty_text insights_empty_lowercase">Click </span>
          <span className="insights_empty_text insights_empty_bold_text">Apply Filters </span>
          <span>to view logs. </span>
        </div>
      </div>
      {/* </div> */}
    </div>
  </div>
);

export default infoApplyFilterFirst;

import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faToggleOn, faToggleOff } from '@fortawesome/pro-solid-svg-icons';

function ToggleInline(props = {}) {
  const {
    input,
    disabled,
    label,
    styleClass,
    subLabel,
  } = props;
  return (
    <div className="toggle-inline-check-box-container">
      <label htmlFor="e">
        <input
          id="e"
          type="checkbox"
          disabled={disabled}
          className="hide"
          {...input} />
        <div className="toggle-switch">
          {
            input.value
              ? <FontAwesomeIcon icon={faToggleOn} className={styleClass} />
              : <FontAwesomeIcon icon={faToggleOff} className={styleClass} />
          }
        </div>
        <div className="toggle-label">
          <span className="label">{label}</span>
          <span className="sub-label">{subLabel}</span>
        </div>
      </label>
    </div>
  );
}

ToggleInline.defaultProps = {
  disabled: false,
  label: null,
  styleClass: '',
  subLabel: null,
};

ToggleInline.propTypes = {
  disabled: PropTypes.bool,
  label: PropTypes.string,
  styleClass: PropTypes.string,
  subLabel: PropTypes.string,
  input: PropTypes.shape({
    name: PropTypes.string,
    value: PropTypes.any,
    checked: PropTypes.bool,
  }).isRequired,
};

export default ToggleInline;

import { noop } from 'utils/lodash';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faTrash, faPencilAlt } from '@fortawesome/pro-regular-svg-icons';

function Drawer({
  isDeletable,
  isEditable,
  handleEdit,
  handleDelete,
  isOpen,
  onClose,
  position,
  showBackdrop,
  customHeader,
  customFooter,
  children,
  styleClass,
}) {
  const [open, setOpen] = useState(isOpen);

  useEffect(() => {
    setOpen(() => isOpen);
  }, [isOpen]);

  const handleClose = () => {
    setOpen(false);
    onClose();
  };

  if (!isOpen) return <></>;
  
  return (
    <div className={`drawer-container ${styleClass}`}>
      <div className={`drawer ${open ? 'active' : ''} ${position}`}>
        {customHeader ? (
          <div className="drawer-header">
            {customHeader}
            <div className="drawer-actions">
              { isEditable && (
                <FontAwesomeIcon
                  className="drawer-close-icon fas fa-times"
                  onClick={handleEdit}
                  role="presentation"
                  icon={faPencilAlt} />
              )}
              { isDeletable && (
                <FontAwesomeIcon
                  className="drawer-close-icon fas fa-times"
                  onClick={handleDelete}
                  role="presentation"
                  icon={faTrash} />
              )}
              <FontAwesomeIcon
                className="drawer-close-icon fas fa-times"
                onClick={handleClose}
                role="presentation"
                icon={faTimes} />
            </div>
          </div>
        ) : (
          <div className="drawer-header--default">
            <div className="drawer-actions">
              { isEditable && (
                <FontAwesomeIcon
                  className="drawer-close-icon fas fa-times"
                  onClick={handleEdit}
                  role="presentation"
                  icon={faPencilAlt} />
              )}
              { isDeletable && (
                <FontAwesomeIcon
                  className="drawer-close-icon fas fa-times"
                  onClick={handleClose}
                  role="presentation"
                  icon={faTrash} />
              )}
              <FontAwesomeIcon
                className="drawer-close-icon fas fa-times"
                onClick={handleClose}
                role="presentation"
                icon={faTimes} />
            </div>
          </div>
        )}
        <div className="drawer-body">{children}</div>
        {customFooter ? <div className="drawer-footer">{customFooter}</div> : null}
      </div>
      {showBackdrop ? <div className="dialog-mask" role="presentation" onClick={handleClose} /> : null}
    </div>
  );
}

Drawer.propTypes = {
  children: PropTypes.element.isRequired,
  customFooter: PropTypes.element,
  customHeader: PropTypes.element,
  handleDelete: PropTypes.func,
  handleEdit: PropTypes.func,
  isDeletable: PropTypes.bool,
  isEditable: PropTypes.bool,
  isOpen: PropTypes.bool,
  onClose: PropTypes.func,
  position: PropTypes.oneOf(['left', 'right', 'top', 'bottom']),
  showBackdrop: PropTypes.bool,
  styleClass: PropTypes.string,
};

Drawer.defaultProps = {
  handleDelete: noop,
  handleEdit: noop,
  isDeletable: false,
  isEditable: false,
  isOpen: false,
  onClose: noop,
  position: 'right',
  showBackdrop: false,
  styleClass: '',
};

export default Drawer;

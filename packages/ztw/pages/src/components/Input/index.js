/* eslint-disable jsx-a11y/label-has-for */
import React from 'react';
import PropTypes from 'prop-types';
import { Trans, withTranslation } from 'react-i18next';
import ReactTooltip from 'react-tooltip';
import {
  setToolTipPosition,
} from 'utils/helpers';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCopy } from '@fortawesome/pro-regular-svg-icons';

const handleHover = (event) => {
  setToolTipPosition(event, '.modal-body');
};

function Input(props = {}) {
  const {
    meta,
    input,
    label,
    id,
    placeholder,
    styleClass,
    inputStyleClass,
    t,
    isDisabled,
    unit,
    type,
    tooltip,
    customErrorText,
    hasCopyButton,
  } = props;
  const hasError = meta.touched && !!meta.error;
  const handleCopy = () => {
    navigator.clipboard.writeText(input.value);
  };
  
  return (
    <div className={`input-container ${hasError ? 'error' : ''} ${styleClass}`}>
      <div>
        <label className="form-field-label-wrapper" htmlFor={id}>
          
          {label && (
            <span className={`form-field-label  ${hasError ? 'invalid' : ''}`} data-tip data-for={label} onMouseEnter={handleHover}>
              {/* <p className={`input-label  ${hasError ? 'error' : ''} `}>
                {t(label)}
              </p> */}
              <Trans>{t(label)}</Trans>
              <ReactTooltip
                id={label}
                clickable
                place="top"
                type="light"
                offset={{ top: -10 }}
                effect="solid"
                disable={(tooltip.length === 0)}
                delayHide={30}
                border
                borderColor="#939393"
                className="form-field-tooltip-container">
                <div id="tooltip-top" className="tooltip-top tooltip-top-text">
                  {hasError && <div className="help-error-text has-info-text">{customErrorText ? t(customErrorText) : t(meta.error)}</div>}
                  <div className="help-text">{tooltip}</div>
                </div>
              </ReactTooltip>
            </span>
          )}
          
          <div className={`input-wrapper position-relative ${hasCopyButton ? 'inline-border-default' : ''} ${inputStyleClass} ${type}`}>
            {type && type === 'textarea' && (
              <textarea
                {...input}
                id={id}
                type={type}
                className="form-textarea"
                disabled={isDisabled ? 'disabled' : ''}
                placeholder={placeholder ? t(placeholder) : ''} />
            )}
            {type && type !== 'textarea'
            && (
              <input
                {...input}
                id={id}
                type={type}
                disabled={isDisabled ? 'disabled' : ''}
                placeholder={placeholder ? t(placeholder) : ''} />
            )}
            {unit && <span className="unit">{unit}</span>}
            {hasCopyButton && (
              <button
                type="button"
                className="has-copy-button"
                onClick={handleCopy}>
                <FontAwesomeIcon icon={faCopy} className="fa-copy-icon" />
              </button>
            )}
          </div>
        </label>
      </div>

      {/* <div className="error-container">
        {hasError && customErrorText && <p>{t(customErrorText)}</p>}
        {hasError && !customErrorText && <p>{t(meta.error)}</p>}
      </div> */}
    </div>
  );
}

Input.defaultProps = {
  type: 'text',
  label: null,
  unit: null,
  placeholder: '',
  isDisabled: false,
  inputStyleClass: '',
  styleClass: '',
  tooltip: '',
  t: (str) => str,
  hasCopyButton: false,
};

Input.propTypes = {
  type: PropTypes.string,
  label: PropTypes.string,
  unit: PropTypes.string,
  isDisabled: PropTypes.bool,
  id: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
  inputStyleClass: PropTypes.string,
  styleClass: PropTypes.string,
  tooltip: PropTypes.string,
  t: PropTypes.func,
  meta: PropTypes.shape({
    active: PropTypes.bool,
    touched: PropTypes.bool,
    error: PropTypes.string,
  }).isRequired,
  input: PropTypes.shape({
    onFocus: PropTypes.func.isRequired,
    onBlur: PropTypes.func.isRequired,
    onChange: PropTypes.func.isRequired,
    name: PropTypes.string,
    value: PropTypes.any,
  }).isRequired,
  customErrorText: PropTypes.string,
  hasCopyButton: PropTypes.bool,
};

export default withTranslation()(Input);
export { Input };

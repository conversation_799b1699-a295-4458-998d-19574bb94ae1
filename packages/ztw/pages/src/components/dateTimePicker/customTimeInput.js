import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import TimePicker from 'rc-time-picker';
import 'rc-time-picker/assets/index.css';

const options = [
  {
    id: 'AM', value: 'AM', label: 'AM', className: 'meridian-selector-option',
  },
  {
    id: 'PM', value: 'PM', label: 'PM', className: 'meridian-selector-option',
  },
];

class CustomTimeInput extends Component {
  static getDerivedStateFromProps(nextProps) {
    const { time } = nextProps;
    const {
      meridian,
      timeValue,
      dateValue,
    } = CustomTimeInput.extractDateAndTime(time);
    return {
      meridian,
      timeValue,
      dateValue,
    };
  }

  static extractDateAndTime(time) {
    const timeMomentInst = moment(time, 'MM/DD/YYYY hh:mm:ss A');
    const meridian = timeMomentInst.format('A');
    const timeValue = timeMomentInst.format('h:mm:ss');
    const dateValue = timeMomentInst.format('MM/DD/YYYY');
    return {
      meridian,
      timeValue,
      dateValue,
    };
  }

  static propTypes = {
    onTimeInputChange: PropTypes.func.isRequired,
    type: PropTypes.string.isRequired,
    onMeridianChange: PropTypes.func.isRequired,
    showMinute: PropTypes.bool,
    showSecond: PropTypes.bool,
  };

  constructor(props) {
    super(props);
    this.state = {
      format: 'h:mm:ss',
      meridian: null,
      timeValue: null,
      dateValue: null,
    };
  }

  handleChangeInputValue = (value) => {
    const { type, onTimeInputChange } = this.props;
    const { meridian } = this.state;
    let hours = Number(value.format('hh'));
    if (meridian === 'PM') {
      hours = hours === 12 ? hours : hours + 12;
    }
    const minutes = value.format('mm');
    const seconds = value.format('ss');
    const date = `${hours}:${minutes}:${seconds}`;
    onTimeInputChange(date, type);
  };
  
  handleMeridianChange = async (meridian) => {
    const { onMeridianChange, type, onTimeInputChange } = this.props;
    const { dateValue, timeValue } = this.state;
    const newDate = moment(
      `${dateValue} ${timeValue} ${meridian}`,
      'MM/DD/YYYY hh:mm:ss A',
    );
    this.setState({
      meridian,
    });
    await onMeridianChange(newDate, type);
    await onTimeInputChange(newDate, type);
  };

  renderMeridianSelector = () => options.map((option) => {
    const { value, id, label } = option;

    return (
      <option key={id} value={value}>
        {label}
      </option>
    );
  });

  render() {
    const { meridian, timeValue, format } = this.state;
    const { showMinute, showSecond } = this.props;
    let currentValue;

    if (showMinute && showSecond) {
      currentValue = moment(`${timeValue} ${meridian}`, 'hh:mm:ss A');
    } else if (showMinute) {
      currentValue = moment(`${timeValue} ${meridian}`, 'hh:mm A');
    } else {
      currentValue = moment(`${timeValue} ${meridian}`, 'hh:00:00 A');
    }

    return (
      <>
        <TimePicker
          showSecond={showSecond}
          showMinute={showMinute}
          value={currentValue}
          onChange={this.handleChangeInputValue}
          onAmPmChange={this.handleMeridianChange}
          format={format}
          use12Hours
          inputReadOnly />
        <select
          className="meridian-selector"
          id="selectTimePicker"
          value={meridian}
          onChange={(e) => this.handleMeridianChange(e.target.value)}>
          {this.renderMeridianSelector()}
        </select>
      </>
    );
  }
}

export default CustomTimeInput;

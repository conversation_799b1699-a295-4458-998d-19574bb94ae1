import React, { useState } from 'react';
import { connect } from 'react-redux';
import { faAngleUp, faAngleDown } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import DatePicker from 'react-datepicker';
import moment from 'moment-timezone';
import PropTypes from 'prop-types';

function singleDatePicker(props) {
  const today = new Date();
  const { input } = props;
  const [startDate, setStartDate] = useState(input.value || new Date());
  const [isOpened, setIsOpened] = useState(false);
  const defaultCN = 'single-date-picker';
  const containerCN = isOpened ? `${defaultCN}` : `${defaultCN} closed`;

  return (
    
    <div className="drop-down-container">
      <div className="time-filter-dropdown">
       
        <button
          className="drop-down-selected-value"
          onClick={() => setIsOpened(!isOpened)}
          type="button">
          <span>
            {moment(input.value).format('MMM. DD, YYYY')}
          </span>
          <FontAwesomeIcon icon={isOpened ? faAngleUp : faAngleDown} pull="right" />
        </button>
         
        <div className={containerCN}>
          <div className="singleDate">
            <DatePicker
              className="singleDate"
              open={isOpened}
              selected={startDate}
              shouldCloseOnSelect
              minDate={today}
              onChange={(date) => {
                setStartDate(date);
                input.onChange(date);
                setIsOpened(false);
              }}
              useWeekdaysShort />
          </div>
        </div>

      </div>
    </div>
  );
}

singleDatePicker.propTypes = {
  input: PropTypes.shape({}),
};

singleDatePicker.defaultProps = {
  input: {},
};

const mapStateToProps = (state) => {
  const { duration } = state;
  const { startTime, endTime } = duration;
  return {
    startTime,
    endTime,
  };
};

export default connect(mapStateToProps)(singleDatePicker);

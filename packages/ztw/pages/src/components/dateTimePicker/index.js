import React, { Component } from 'react';
import { connect } from 'react-redux';
import DatePicker from 'react-datepicker';
import PropTypes from 'prop-types';
import moment from 'moment';
import { withTranslation } from 'react-i18next';
import CustomTimeInput from './customTimeInput';

// import 'react-datepicker/dist/react-datepicker-cssmodules.css';

class DateTimePicker extends Component {
  static propTypes = {
    isOpened: PropTypes.bool.isRequired,
    applyRage: PropTypes.func.isRequired,
    onClose: PropTypes.func.isRequired,
    t: PropTypes.func,
    showMinute: PropTypes.bool,
    showSecond: PropTypes.bool,
    dateRange: PropTypes.number,
    maxDateSelectable: PropTypes.number,
    currentValue: PropTypes.shape(),
  };

  static defaultProps = {
    t: (str) => str,
    showMinute: true,
    showSecond: true,
    dateRange: 90,
    maxDateSelectable: 90,
    currentValue: {},
  };

  constructor(props) {
    super(props);
    const { isOpened, showMinute, currentValue } = this.props;
    const dateFormat = 'mm/dd/yyyy h:mm:ss a';

    const currentStartTime = currentValue?.startTime || new Date();
    const currentEndTime = currentValue?.endTime || new Date();
    const substractHour = currentValue.label === 'CUSTOM' ? 0 : 2;

    const startDate = showMinute ? moment(currentStartTime).subtract(substractHour, 'h') : moment(currentStartTime).startOf('hour').subtract(substractHour, 'h');
    const endDate = showMinute ? moment(currentEndTime) : moment(currentEndTime).startOf('hour');

    this.state = {
      dateFormat,
      startDate,
      endDate,
      isOpened,
      today: moment(),
    };
  }

  handleTimeInputChange = (date, type) => {
    // eslint-disable-next-line prefer-const
    let { startDate, endDate } = this.state;
    const { showMinute, showSecond, dateRange } = this.props;
    const currentTime = showMinute ? moment() : moment().startOf('hour');
    let time;
    const dateRangeInHour = dateRange ? (dateRange * 24) : 2160;

    if (showMinute && showSecond) {
      time = moment(date, 'HH:mm:ss');
    } else if (showMinute) {
      time = moment(date, 'HH:mm');
    } else {
      time = moment(date, 'HH:mm').startOf('hour');
    }

    if (type === 'start') {
      startDate = moment(startDate).set({
        hour: time.get('hour'),
        minute: showMinute ? time.get('minute') : '00',
        second: showSecond ? time.get('second') : '00',
      });
      if (startDate.isAfter(currentTime)) {
        startDate = currentTime.clone().subtract(2, 'h');
      }
    }

    if (type === 'end') {
      endDate = moment(endDate).set({
        hour: time.get('hour'),
        minute: showMinute ? time.get('minute') : '00',
        second: showSecond ? time.get('second') : '00',
      });
      if (startDate.isAfter(currentTime)) {
        endDate = currentTime;
      }
    }

    const diff = endDate.diff(startDate, 'minutes') / 60;
    if (startDate.isAfter(endDate) || startDate.isSame(endDate) || diff < 2) {
      startDate = endDate.clone().subtract(2, 'h');
    }

    if (diff > dateRangeInHour) {
      startDate = endDate.clone().subtract(dateRangeInHour, 'h');
    }

    this.setState({
      startDate,
      endDate,
    });
  };

  handleMeridianChange = (date, type) => {
    let { startDate, endDate } = this.state;
    const { dateRange } = this.props;
    const currentTime = moment();
    const dateRangeInHour = dateRange ? (dateRange * 24) : 2160;

    if (type === 'start') {
      startDate = date;
    }

    if (type === 'end') {
      endDate = date;
      if (endDate.isAfter(currentTime)) {
        endDate = currentTime;
      }
    }

    const diff = endDate.diff(startDate, 'minutes') / 60;
    if (diff < 2) {
      startDate = endDate.clone().subtract(2, 'h');
    }
    if (diff > dateRangeInHour) {
      startDate = endDate.clone().subtract(dateRangeInHour, 'h');
    }

    this.setState({
      startDate,
      endDate,
    });
  };

  handleChange = (date, type) => {
    let { startDate, endDate } = this.state;
    const { today } = this.state;
    startDate = date.startDate ? moment(date.startDate) : startDate;
    endDate = date.endDate ? moment(date.endDate) : endDate;
    if (
      moment(startDate).isAfter(endDate)
      || moment(startDate).isSame(endDate)
    ) {
      if (moment(startDate).isAfter(today)) {
        endDate = today;
        startDate = moment(today).clone().subtract(2, 'h');
      } else {
        if (type === 'start' && moment(startDate).isAfter(endDate)) {
          endDate = startDate;
        }
        startDate = moment(endDate).clone().subtract(2, 'h');
      }
    }

    if (moment(endDate).isAfter(today)) {
      endDate = today;
    }
    if (moment(startDate).isAfter(today) || moment(startDate).isSame(today)) {
      startDate = moment(today).clone().subtract(2, 'h');
    }

    this.setState({ startDate, endDate });
    this.handleRangeSelected(startDate, endDate, type);
  };

  handleChangeStart = (startDate) => this.handleChange({ startDate }, 'start');

  handleChangeEnd = (endDate) => this.handleChange({ endDate }, 'end');

  handleRangeSelected = (startDate, endDate, type) => {
    const { dateRange, maxDateSelectable } = this.props;
    const dateRangeInHour = dateRange ? (dateRange * 24) : 2160;
    const rangeLimit = dateRangeInHour;
    const differenceInDays = moment(endDate).diff(startDate, 'hours');
    let newEndDate = null;
    let newStartDate = null;
    if (differenceInDays > rangeLimit) {
      if (type === 'start') {
        newEndDate = startDate.clone().add('hours', rangeLimit);
      } else {
        newStartDate = endDate.clone().subtract(rangeLimit, 'hours');
      }
    }
    if (differenceInDays > (maxDateSelectable * 24)) {
      newEndDate = moment(startDate).add((maxDateSelectable * 24), 'h');
    }
    this.setState({
      endDate: newEndDate || endDate,
      startDate: newStartDate || startDate,
    });
  };

  handleApplyRange = () => {
    const { applyRage } = this.props;
    const { startDate, endDate } = this.state;
    const duration = moment.duration(endDate.diff(startDate));
    const days = parseInt(duration.asDays(), 10);
    const hours = parseInt(duration.asHours(), 10);
    const timeShift = `${hours}h`;
    applyRage({
      timeShift,
      days,
      hours,
      startDate,
      endDate,
    });
  };

  handleClosePicker = () => {
    const { onClose } = this.props;
    this.setState({ isOpened: false });
    onClose();
  };

  clear = () => {
    this.setState({
      startDate: moment().subtract(2, 'h'),
      endDate: moment(),
    });
  };

  render() {
    let { endDate, startDate, today } = this.state;
    const {
      t,
      showMinute,
      showSecond,
      dateRange,
      maxDateSelectable,
    } = this.props;
    const { dateFormat, isOpened } = this.state;
    endDate = endDate.toDate();
    startDate = startDate.toDate();
    today = today.toDate();
    const defaultCN = 'calendar-container';
    const containerCN = isOpened ? `${defaultCN}` : `${defaultCN} closed`;
    const minDateRange = dateRange || 91;
    let maxEndDate = today;

    if (moment(startDate).add((maxDateSelectable * 24), 'h') < moment(today)) {
      maxEndDate = moment(startDate).add((maxDateSelectable * 24), 'h');
    }

    return (
      <div className={containerCN}>
        <div className="start">
          <div className="date-type-heading start-date-heading">
            {t('START_TIME')}
          </div>
          <DatePicker
            className="start"
            open={isOpened}
            dateFormat={dateFormat}
            shouldCloseOnSelect={false}
            selected={Date.parse(startDate)}
            selectsStart
            startDate={startDate}
            endDate={endDate}
            minDate={Date.parse(moment().subtract(minDateRange, 'days'))}
            maxDate={today}
            onChange={this.handleChangeStart}
            showTimeInput
            useWeekdaysShort
            customTimeInput={(
              <CustomTimeInput
                time={startDate}
                type="start"
                showMinute={showMinute}
                showSecond={showSecond}
                onTimeInputChange={this.handleTimeInputChange}
                onMeridianChange={this.handleMeridianChange} />
            )} />
        </div>
        {/* <div className="vertical-border" /> */}
        <div className="end">
          <div className="date-type-heading end-date-heading">
            {t('END_TIME')}
          </div>
          <DatePicker
            className="end"
            open={isOpened}
            dateFormat={dateFormat}
            selected={Date.parse(endDate)}
            selectsEnd
            startDate={startDate}
            endDate={endDate}
            minDate={startDate}
            maxDate={Date.parse(maxEndDate)}
            onChange={this.handleChangeEnd}
            showTimeInput
            useWeekdaysShort
            customTimeInput={(
              <CustomTimeInput
                time={endDate}
                showMinute={showMinute}
                showSecond={showSecond}
                onTimeInputChange={this.handleTimeInputChange}
                onMeridianChange={this.handleMeridianChange}
                type="end" />
            )} />
        </div>
        <div className="buttons-row">
          <button type="submit" className="apply" onClick={this.handleApplyRange}>
            {t('APPLY')}
          </button>
          <button type="submit" className="close" onClick={this.handleClosePicker}>
            {t('CLOSE')}
          </button>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => {
  const { duration } = state;
  const { startTime, endTime } = duration;
  return {
    startTime,
    endTime,
  };
};

export default connect(mapStateToProps)(withTranslation()(DateTimePicker));

// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

function SubscriptionRequired(props) {
  const { t } = props;
  return (
    <div className="page-main-content">
      <div className="subscription-required">
        <div className="subscription-required-splash">
          <div className="subscription-required-splash-title">{t('WORKLOAD_SERVICE_REQUIRED')}</div>
          <div className="subscription-required-splash-message">{t('WORKLOAD_SERVICE_REQUIRED_MESSAGE')}</div>
        </div>
      </div>
    </div>
  );
}

SubscriptionRequired.propTypes = {
  t: PropTypes.func,
};

SubscriptionRequired.defaultProps = {
  t: (str) => str,
};

export default withTranslation()(SubscriptionRequired);
export { SubscriptionRequired };

import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import React from 'react';
import { withTranslation } from 'react-i18next';
import DrilldownSearch from './DrilldownSearch.js';

class ListDropdownWithSearchInsights extends React.Component {
  static propTypes = {
    items: PropTypes.arrayOf(PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    })),
    setValue: PropTypes.func,
    selectedValue: PropTypes.oneOfType([
      PropTypes.shape({
        value: PropTypes.string,
        label: PropTypes.string,
      }),
      PropTypes.string,
    ]),
    defaultValue: PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    }),
    onClickCb: PropTypes.func,
    t: PropTypes.func,
  };

  static defaultProps = {
    items: [],
    selectedValue: {},
    setValue: noop,
    defaultValue: {},
    onClickCb: null,
    t: (str) => str,
  };

  constructor(props) {
    super(props);
    this.state = {
      openList: true,
    };
    this.wrapperRef = null;
  }

  componentDidMount() {
    document.addEventListener('mousedown', this.handleMousedown);
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMousedown);
  }

  setWrapperRef = (node) => {
    this.wrapperRef = node;
  };

  handleChangeValue = (item) => {
    const { setValue, onClickCb } = this.props;
    onClickCb(item);
    setValue(item);
    this.setState({
      openList: false,
    });
  };

  handleMousedown = (e) => {
    const containRef = this.wrapperRef && this.wrapperRef.contains(e.target);
    if (!containRef) {
      this.setState({ openList: false });
      // this.setState({ openList: false, selectedValue: defaultValue });
    }
  };

  handleOpenList = () => {
    const { openList } = this.state;
    this.setState({ openList: !openList });
  };

  render() {
    const { items, t, selectedValue: selectedValueProps } = this.props;

    return (
      <div className="drill-down-container" ref={this.setWrapperRef}>
        <div className="drill-down-header">
          <span className="drill-down-fixed-item drill-down-header-text">{selectedValueProps}</span>
        </div>
        <div className="drill-down-search">
          <DrilldownSearch {...this.props} />
        </div>
        <div className="drill-down-body">
          <div className="drill-down-body-content ">
            {items.map((item) => (
              <div
                role="button"
                aria-label="drill down "
                onKeyPress={null}
                tabIndex={0}
                key={item.value}
                onClick={() => { this.handleChangeValue(item); }}
                className="drill-down-item drill-down-content">
                {t(item.label)}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
}

export default withTranslation()(ListDropdownWithSearchInsights);

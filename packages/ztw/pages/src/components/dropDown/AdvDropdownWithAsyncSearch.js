import { faAngleUp, faAngleDown } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import React from 'react';
import { withTranslation } from 'react-i18next';
import DropdownAsyncSearch from './DropdownAsyncSearch';

export const changeHandler = (func) => (value) => func(value);

class AdvDropdownWithAsyncSearch extends React.Component {
  static propTypes = {
    items: PropTypes.arrayOf(PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    })),
    setValue: PropTypes.func,
    selectedValue: PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    }),
    defaultValue: PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    }),
    actions: PropTypes.shape({
      setSearchString: PropTypes.func,
      searchData: PropTypes.func,
    }),
    searchParamName: PropTypes.string,
    searchString: PropTypes.string,
    onBlur: PropTypes.func,
    onChange: PropTypes.func,
    onClickCb: PropTypes.func,
    onPreload: PropTypes.func,
    t: PropTypes.func,
    onSearchStringChange: PropTypes.func,
    placeholder: PropTypes.string,
    meta: PropTypes.shape({
      touched: PropTypes.bool,
      error: PropTypes.string,
    }),
    input: PropTypes.shape({}),
    loading: PropTypes.bool,
    hasNextPage: PropTypes.bool,
    disabled: PropTypes.bool,
    displayFilterLabel: PropTypes.string,
    styleClass: PropTypes.string,
  };

  static defaultProps = {
    items: [],
    selectedValue: {},
    setValue: noop,
    defaultValue: {},
    onBlur: null,
    onChange: null,
    onClickCb: null,
    onPreload: (str) => str,
    t: (str) => str,
    onSearchStringChange: null,
    placeholder: 'SELECT',
    meta: {},
    input: {},
    loading: false,
    hasNextPage: false,
    disabled: false,
    styleClass: '',
    searchParamName: 'search',
    actions: {
      setSearchString: noop,
      searchData: noop,
    },
  };

  constructor(props) {
    super(props);
    const { defaultValue } = props;
    this.state = {
      selectedValue: defaultValue,
      openList: false,
    };
    this.wrapperRef = null;
  }

  componentDidMount() {
    const { onClickCb, onSearchStringChange, input = {} } = this.props;
    document.addEventListener('mousedown', this.handleMousedown);
    onClickCb(input.value);
    onSearchStringChange('');
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMousedown);
  }

  setWrapperRef = (node) => {
    this.wrapperRef = node;
  };

  handleChangeValue = (item) => {
    const {
      onClickCb, onChange,
    } = this.props;
    this.setState({
      selectedValue: item,
      openList: false,
    });
    onClickCb(item);
    onChange(item);
  };

  handleMousedown = (e) => {
    const { defaultValue } = this.props;
    const containRef = this.wrapperRef && this.wrapperRef.contains(e.target);
    if (!containRef) {
      this.setState({ openList: false, selectedValue: defaultValue });
    }
  };

  handleOpenList = () => {
    const {
      defaultValue, onBlur, onPreload,
      onSearchStringChange, searchParamName,
      actions: {
        setSearchString,
      } = {},
    } = this.props;
    onSearchStringChange('');
    setSearchString('', searchParamName);

    onPreload();
    const { openList } = this.state;
    this.setState({ openList: !openList, selectedValue: defaultValue });
    onBlur();
  };

  render() {
    const {
      items, t, meta, placeholder, loading, hasNextPage, disabled, displayFilterLabel, styleClass,
      searchString,
    } = this.props;
    const { selectedValue, openList } = this.state;
    const hasError = meta && meta.touched && !!meta.error;

    if (loading) return <></>;

    return (
      <div className={`adv-dropdown-with-async-search ${styleClass}`}>
        <div className={`drop-down-container ${hasError ? 'error' : ''}`} ref={this.setWrapperRef}>
          <button
            disabled={disabled}
            className="drop-down-selected-value"
            onClick={this.handleOpenList}
            type="button">
            {
              displayFilterLabel && <span className="table-filter-label">{`${displayFilterLabel} = `}</span>
            }
            <span className="selected-value">{t(selectedValue.name) || t(placeholder)}</span>
            <FontAwesomeIcon icon={openList ? faAngleUp : faAngleDown} pull="right" />
          </button>
          <ul className={`drop-down-list ${openList ? 'open' : ''}`}>
            <li className="list-item-search">
              <DropdownAsyncSearch {...this.props} searchString={searchString} placeholder="SEARCH" />
            </li>
            <div className="items-container">
              {items.map((item) => (
                <li key={item.id} className="list-item">
                  <button onClick={() => { this.handleChangeValue(item); }} type="button"><span>{t(item.name)}</span></button>
                </li>
              ))}
            </div>
            { hasNextPage && (
              <li className="search-to-see-more">
                <center>
                  {t('SEARCH_TO_SEE_MORE')}
                </center>
              </li>
            )}
          </ul>
        </div>
        <div className="error-container">{hasError && <p>{t(meta.error)}</p>}</div>
      </div>
    );
  }
}

export default withTranslation()(AdvDropdownWithAsyncSearch);

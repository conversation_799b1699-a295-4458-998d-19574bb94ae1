import React from 'react';
import Select from 'react-select';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';

class SelectWithSearch extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      openList: false,
      search: '',
    };
    this.wrapperRef = null;
  }

  componentDidMount() {
    document.addEventListener('mousedown', this.handleMousedown);
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMousedown);
  }

  setWrapperRef = (node) => {
    this.wrapperRef = node;
  };

  handleChangeValue = (item) => {
    const {
      setValue, onCallBack, name, input,
    } = this.props;
    input.onChange(item);
    setValue(item);
    this.setState({
      openList: false,
    });
    onCallBack(name, item);
  };

  handleMousedown = (e) => {
    const containRef = this.wrapperRef && this.wrapperRef.contains(e.target);
    if (!containRef) {
      this.setState({ openList: false });
    }
  };

  handleOpenList = () => {
    const { openList } = this.state;
    this.setState({ openList: !openList });
  };

  handleSearchFilter = (event, value) => {
    this.setState({ search: value });
  };

  render() {
    const {
      items, placeholder, title, input: { value: { value } },
    } = this.props;
    const { search } = this.state;
    const selectedItem = items.find((x) => x.value === value);

    return (
      <div className="select-container 22" ref={this.setWrapperRef}>

        <div className="select-container-with-search">

          <Select
            name="simpleSearch"
            classNamePrefix="my-select-with-search"
            menuPlacement="auto"
            minMenuHeight={450}
            value={title || selectedItem}
            placeholder={placeholder}
            onChange={this.handleChangeValue}
            options={items.filter((x) => x.label.toUpperCase().includes(search.toUpperCase()))} />
        </div>

      </div>
    );
  }
}

SelectWithSearch.propTypes = {
  items: PropTypes.arrayOf(PropTypes.shape({
    value: PropTypes.string,
    label: PropTypes.string,
  })),
  setValue: PropTypes.func,
  selectedValue: PropTypes.shape({
    value: PropTypes.string,
    label: PropTypes.string,
  }),
  input: PropTypes.shape({
    value: PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    }),
  }),
  onCallBack: PropTypes.func,
  name: PropTypes.string,
  placeholder: PropTypes.string,
  title: PropTypes.string,
};

SelectWithSearch.defaultProps = {
  items: [],
  selectedValue: {},
  setValue: noop,
  input: {},
  onCallBack: (str) => str,
  name: '',
  placeholder: '',
  title: null,
};

export default withTranslation()(SelectWithSearch);

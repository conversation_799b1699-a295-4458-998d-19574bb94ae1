import { noop, isEqual } from 'lodash-es';
import PropTypes from 'prop-types';
// import Localized from 'utils/i18n';
import { withTranslation } from 'react-i18next';

import React from 'react';
import { SectionLabel } from '../../label';

class SingleDropdown extends React.Component {
  constructor(props) {
    super(props);
    const { defaultValue } = props;
    let value = defaultValue;
    if (typeof defaultValue === 'string') {
      value = {
        id: defaultValue,
        value: defaultValue,
        label: defaultValue,
      };
    }
    this.state = {
      search: '',
      selectedValue: value,
      openList: false,
    };
    this.wrapperRef = null;
  }

  componentDidMount() {
    document.addEventListener('mousedown', this.handleMousedown);
  }

  componentDidUpdate(prevProps) {
    const { defaultValue, selectedValue } = this.props;
    if (prevProps.defaultValue !== defaultValue) {
      let value = defaultValue;
      if (typeof defaultValue === 'string') {
        value = {
          id: defaultValue,
          value: defaultValue,
          label: defaultValue,
        };
      }
      // eslint-disable-next-line
      this.setState({ selectedValue: value });
    }

    if (prevProps.selectedValue !== selectedValue) {
      let value = selectedValue;
      if (typeof selectedValue === 'string') {
        value = {
          id: selectedValue,
          value: selectedValue,
          label: selectedValue,
        };
      }
      // eslint-disable-next-line
      this.setState({ selectedValue: value });
    }
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMousedown);
  }

  setWrapperRef = (node) => {
    this.wrapperRef = node;
  };

  handleChangeValue = (item) => {
    if (item.isParent) {
      return;
    }
    if (item.isDisabled) {
      return;
    }
    const { setValue } = this.props;
    this.setState({
      selectedValue: item,
      openList: false,
    });
    setValue(item);
  };

  handleMousedown = (e) => {
    const containRef = this.wrapperRef.contains(e.target);
    if (!containRef) {
      this.setState({ openList: false });
    }
  };

  handleOpenList = () => {
    const { readOnlyDropdown, setValue } = this.props;
    const { openList } = this.state;
    if (!readOnlyDropdown) {
      this.setState({
        selectedValue: [],
        openList: !openList,
      });
      setValue([]);
    }
  };

  handleChangeInput = (event) => {
    this.setState({ search: event.target.value });
  };

  renderListOption = (item, selectedValue) => {
    const { t } = this.props;
    const itemLabel = item.custom ? item.label : t(item.label);
    return (
      <div key={item.label + item.id} className={`dropdown-list-item ${isEqual(item.value, selectedValue.value) ? 'dropdown-selected-value' : ''} ${item.isDisabled ? 'dropdown-selected-value-disabled' : ''}`}>
        <button
          onClick={() => { this.handleChangeValue(item); }}
          type="button">
          {item.hasHtmlContent ? (item.htmlContent) : (<span title={itemLabel}>{itemLabel}</span>)}
        </button>
      </div>
    );
  };

  render() {
    // eslint-disable-next-line
    let { items, dropdownContainerClass, numericLabelSort, hasLocalSort, label, tooltip } = this.props;
    const {
      hasSearch, t, parentItems, showParent, readOnlyDropdown,
      showLabelInSelectedValue, labelSelectedValue,
      throwError, loadMore, showClickMoreMessage, showNoMoreResults,
      cclass, searchByLabel, staticLabelSelectedValue,
    } = this.props;
    
    if (hasLocalSort) {
      items.sort((a, b) => {
        if (numericLabelSort) {
          return a.label.localeCompare(b.label, undefined, { numeric: true });
        }
        return t(a.label).localeCompare(t(b.label));
      });
    }
    const { search, selectedValue, openList } = this.state;
    if (hasSearch && search.trim().length) {
      items = items.filter((item) => {
        if (searchByLabel) {
          return (t(item.label) || '').toString().toLowerCase().indexOf(search.trim().toLowerCase()) !== -1;
        }
        return (t(item.value) || '').toString().toLowerCase().indexOf(search.trim().toLowerCase()) !== -1;
      });
    }
    if (readOnlyDropdown) {
      return (
        <span className="form-input-text disabled read-only" ref={this.setWrapperRef}>
          <span className="form-read-only-input-text">
            <div className="dropdown-selected-label">
              {showLabelInSelectedValue && (
                <span className="label-selected-value">
                  {t(labelSelectedValue)}
                  {' '}
                  =
                  {' '}
                </span>
              )}
              {selectedValue.hasHtmlContent
                ? selectedValue.htmlContent
                : (
                  <span>
                    {selectedValue.custom
                      ? selectedValue.label
                      : t(selectedValue.label)}
                  </span>
                )}
            </div>
          </span>
        </span>
      );
    }
    let dropdownOptions = (
      <div className="dropdown-list-content">
        {items.map((item) => (
          this.renderListOption(item, selectedValue)
        ))}
        {((search.trim().length) && items.length === 0)
          ? <div className="dropdown-list-empty">{t('NO_DATA_AVAILABLE')}</div>
          : null}
        {(!search.trim().length && showClickMoreMessage) ? (
          <div role="button" tabIndex="0" className="dropdown-message-item" onClick={loadMore} onKeyPress={loadMore}>{t('CLICK_TO_SEE_MORE')}</div>
        ) : null}
        {(!search.trim().length && showNoMoreResults) ? (
          <div className="dropdown-list-empty">{t('NO_MORE_DATA')}</div>
        ) : null}
      </div>
    );
    if (showParent) {
      const elementsList = [];
      parentItems.map((parent) => {
        elementsList.push(parent);
        items.map((item) => {
          if (item.parent === parent.value) {
            elementsList.push(item);
          }
          return item;
        });
        return parent;
      });
      dropdownOptions = (
        <div className="dropdown-list-content">
          {elementsList.map((item) => (
            <div key={item.label + item.id} className={`dropdown-list-item ${isEqual(item.isParent, true) ? 'dropdown-list-item-parent' : ''} ${isEqual(item.value, selectedValue.value) ? 'dropdown-selected-value' : ''} ${item.isDisabled ? 'dropdown-selected-value-disabled' : ''}`}>
              <button onClick={() => { this.handleChangeValue(item); }} type="button"><span title={t(item.label)}>{t(item.label)}</span></button>
            </div>
          ))}
        </div>
      );
    }

    return (
      <div>
        {label && (
          <SectionLabel
            label={label}
            tooltip={tooltip} />
        )}
        <div className={`drop-down-container ${dropdownContainerClass} ${throwError ? 'invalid' : ''}`} ref={this.setWrapperRef}>
          <button
            className={`drop-down-selected-value ${readOnlyDropdown ? 'dropdown-read-only' : ''}`}
            onClick={this.handleOpenList}
            type="button">
            {!staticLabelSelectedValue && (
              <div className="dropdown-selected-label">
                {showLabelInSelectedValue && (
                  <span className="label-selected-value">
                    {t(labelSelectedValue)}
                    {' '}
                    =
                    {' '}
                  </span>
                )}
                {selectedValue.hasHtmlContent
                  ? selectedValue.htmlContent
                  : (
                    <span>
                      {selectedValue.custom
                        ? selectedValue.label
                        : t(selectedValue.label)}
                    </span>
                  )}
              </div>
            )}
            {staticLabelSelectedValue && (
              <div className="dropdown-selected-label">
                <span className="label-selected-value">{t(labelSelectedValue)}</span>
              </div>
            )}
            {!readOnlyDropdown && (
              <span
                className={`dropdown-icon fa ${openList ? 'fa-caret-up' : 'fa-caret-down'}`}>
              </span>
            )}
          </button>
          <div className="dropdown-box">
            <div className={`drop-down-list ${openList ? 'open' : ''} ${cclass || ''}`}>
              {hasSearch && (
                <div className="dropdown-list-header">
                  <div className="dropdown-search">
                    <input
                      className="dropdown-input-search"
                      type="text"
                      value={search}
                      placeholder={t('DROPDOWN_SEARCH')}
                      onChange={this.handleChangeInput} />
                    <span className="search-icon fa fa-search"></span>
                  </div>
                </div>
              )}
              {dropdownOptions}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

SingleDropdown.propTypes = {
  hasSearch: PropTypes.bool,
  hasLocalSort: PropTypes.bool,
  t: PropTypes.func,
  items: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number,
        PropTypes.arrayOf(PropTypes.string),
        PropTypes.shape({}),
      ]),
      label: PropTypes.string,
      isDisabled: PropTypes.bool,
    }),
  ),
  numericLabelSort: PropTypes.bool,
  parentItems: PropTypes.arrayOf(PropTypes.shape({})),
  readOnlyDropdown: PropTypes.bool,
  dropdownContainerClass: PropTypes.string,
  setValue: PropTypes.func,
  loadMore: PropTypes.func,
  showClickMoreMessage: PropTypes.bool,
  showNoMoreResults: PropTypes.bool,
  searchByLabel: PropTypes.bool,
  showParent: PropTypes.bool,
  throwError: PropTypes.bool,
  defaultValue: PropTypes.oneOfType([PropTypes.shape({
    value: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.number,
      PropTypes.arrayOf(PropTypes.string),
      PropTypes.shape({}),
    ]),
    label: PropTypes.string,
  }), PropTypes.string]),
  labelSelectedValue: PropTypes.string,
  showLabelInSelectedValue: PropTypes.bool,
  cclass: PropTypes.string,
  label: PropTypes.string,
  tooltip: PropTypes.string,
  staticLabelSelectedValue: PropTypes.bool,
  selectedValue: PropTypes.shape(),
};

SingleDropdown.defaultProps = {
  dropdownContainerClass: '',
  hasSearch: false,
  hasLocalSort: true,
  t: (str) => str,
  items: [],
  numericLabelSort: false,
  parentItems: [],
  readOnlyDropdown: false,
  setValue: noop,
  loadMore: noop,
  showClickMoreMessage: false,
  searchByLabel: false,
  showLabelInSelectedValue: false,
  showNoMoreResults: false,
  showParent: false,
  throwError: false,
  defaultValue: {
    id: 1,
    value: 'ANY',
    label: 'ANY',
  },
  label: '',
  tooltip: '',
  staticLabelSelectedValue: false,
};

export default withTranslation()(SingleDropdown);

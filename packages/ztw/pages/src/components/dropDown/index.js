import { faAngleUp, faAngleDown } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import React from 'react';

class DropDown extends React.Component {
  static propTypes = {
    items: PropTypes.arrayOf(PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    })),
    setValue: PropTypes.func,
    selectedValue: PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    }),
    defaultValue: PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    }),
    onCallBack: PropTypes.func,
    name: PropTypes.string,
    t: PropTypes.func,
  };

  static defaultProps = {
    items: [],
    selectedValue: {},
    setValue: noop,
    defaultValue: {},
    onCallBack: (str) => str,
    name: '',
    t: (str) => str,
  };

  constructor(props) {
    super(props);
    const { defaultValue } = props;
    this.state = {
      selectedValue: defaultValue,
      openList: false,
    };
    this.wrapperRef = null;
  }

  componentDidMount() {
    document.addEventListener('mousedown', this.handleMousedown);
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMousedown);
  }

  setWrapperRef = (node) => {
    this.wrapperRef = node;
  };

  handleChangeValue = (item) => {
    const { setValue, onCallBack, name } = this.props;
    setValue(item);
    this.setState({
      selectedValue: item,
      openList: false,
    });
    onCallBack(name, item);
  };

  handleMousedown = (e) => {
    const containRef = this.wrapperRef && this.wrapperRef.contains(e.target);
    if (!containRef) {
      this.setState({ openList: false });
    }
  };

  handleOpenList = () => {
    const { openList } = this.state;
    this.setState({ openList: !openList });
  };

  render() {
    const { items, t } = this.props;
    const { selectedValue, openList } = this.state;
    return (
      <div className="drop-down-container" ref={this.setWrapperRef}>
        <button
          className="drop-down-selected-value"
          onClick={this.handleOpenList}
          type="button">
          <span>{t(selectedValue.label)}</span>
          <FontAwesomeIcon icon={openList ? faAngleUp : faAngleDown} pull="right" />

        </button>
        <ul className={`drop-down-list ${openList ? 'open' : ''}`}>
          {items.filter((item) => item.value !== selectedValue.value).map((item) => (
            <li key={item.value}>
              <button onClick={() => { this.handleChangeValue(item); }} type="button"><span>{t(item.label)}</span></button>
            </li>
          ))}
        </ul>
      </div>
    );
  }
}

export default withTranslation()(DropDown);

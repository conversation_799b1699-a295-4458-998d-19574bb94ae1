// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import ReactTooltip from 'react-tooltip';

function Footer(props) {
  const { t } = props;
  const hiddenInfo = () => {
    // eslint-disable-next-line no-undef
    const version = VERSION;
    // eslint-disable-next-line no-undef
    const branch = BRANCH;
    // eslint-disable-next-line no-undef
    const lastCommitDate = LASTCOMMITDATETIME;
    return (
      <>
        <input type="hidden" name="VR" value={`${version}`} />
        <input type="hidden" name="BR" value={`${branch}`} />
        <input type="hidden" name="UTC" value={`${lastCommitDate}`} />
      </>
    );
  };

  return (
    <div className="footer">
      <div className="left-section">

        <div className="copyright">
          {t('COPYRIGHT')}
          <span className="copyright-symbol">&copy;</span>
          <span className="year">{`2007-${new Date().getFullYear()}`}</span>
          {t('ZSCALER_INC_ALL_RIGHTS_RESERVED')}
        </div>

        {hiddenInfo()}
        <div className="version">
          {`${t('VERSION')} 6.2`}
        </div>

        <div className="patents">
          <ReactTooltip place="top" type="light" effect="solid" data-multiline="true" id="patents-tooltip" className="react-tooltip">
            <p className="patents-tooltip-text">{t('FOOTER_PATENTS_TOOLTIP')}</p>
          </ReactTooltip>

          <a
            href="https://www.zscaler.com/patents"
            target="_blank"
            rel="noopener noreferrer"
            data-tip
            data-for="patents-tooltip">
            {t('PATENTS')}
          </a>
        </div>

      </div>
    </div>
  );
}

Footer.propTypes = {
  t: PropTypes.func,
};

Footer.defaultProps = {
  t: (str) => str,
};

export default withTranslation()(Footer);

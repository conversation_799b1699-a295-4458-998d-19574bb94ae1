import React from 'react';
import PropTypes from 'prop-types';
import { Header as Title } from 'components/label';
import DropDown from 'components/dropDown';
import { setTime } from 'ducks/duration';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { noop } from 'utils/lodash';
import * as constants from 'ducks/duration/constants';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

function AppHeader(props) {
  const {
    defaultDuration,
    title,
    icon,
    actions: { setDropdownTime },
  } = props;

  return (
    <div className="app-header-container">
      <div className="left-container">
        {icon && <FontAwesomeIcon icon={icon} />}
        <Title title={title} />
      </div>
      <div className="center-container">
        <div className="header-drop-down">
          <DropDown
            items={constants.DURATION_DATA}
            setValue={setDropdownTime}
            defaultValue={defaultDuration} />
        </div>
      </div>
      <div className="right-container" />
    </div>
  );
}

const mapStateToProps = (state) => ({
  ...state.duration,
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    setDropdownTime: setTime,
  }, dispatch);

  return {
    actions,
  };
};

AppHeader.propTypes = {
  title: PropTypes.node,
  actions: PropTypes.shape({ setDropdownTime: PropTypes.func }),
  icon: PropTypes.shape(),
  defaultDuration: PropTypes.shape(),
};

AppHeader.defaultProps = {
  title: {},
  actions: { setDropdownTime: noop },
  icon: null,
  defaultDuration: {},
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(AppHeader);

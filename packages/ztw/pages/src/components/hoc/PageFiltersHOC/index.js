import React from 'react';
import PropTypes from 'prop-types';

import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm } from 'redux-form';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import {
  resetFilterValues,
  applyFilterValues,
  addFilterValues,
} from 'ducks/filters';
import * as constants from 'ducks/filters/constants';
import * as filterSelector from 'ducks/filters/selectors';

export class PageFiltersForm extends React.PureComponent {
  static propTypes = {
    actions: PropTypes.shape(),
    handleSubmit: PropTypes.func,
    WrappedComponent: PropTypes.func.isRequired,
    t: PropTypes.func,
  };

  static defaultProps = {
    actions: {
      saveCustomApp: noop,
      resetHandle: noop,
    },
    handleSubmit: noop,
    t: (str) => str,
  };

  render() {
    const {
      actions,
      handleSubmit,
      t,
      WrappedComponent,
    } = this.props;

    const { resetHandle } = actions;
    
    const handleReset = async () => {
      await resetHandle();
      handleSubmit();
    };

    return (
      <div className="page-filters-box">
        <form onSubmit={() => handleSubmit()} className="page-filter-form">
          <WrappedComponent {...this.props} t={t} />
          <div className="filter-actions">
            <div>
              <button type="button" className="cancel" onClick={handleReset}>{t('RESET')}</button>
              <button type="button" className="submit" onClick={() => handleSubmit()}>{t('APPLY')}</button>
            </div>
          </div>
        </form>
      </div>
    );
  }
}

const translatedComponent = withTranslation()(PageFiltersForm);

const FilterForm = reduxForm({
  form: constants.FORM_NAME,
  destroyOnUnmount: false,
  // forceUnregisterOnUnmount: true,
  touchOnBlur: true,
  enableReinitialize: false,
})(translatedComponent);

export const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    applyHandle: applyFilterValues,
    resetHandle: resetFilterValues,
    addFilterHandler: addFilterValues,
  }, dispatch);

  return {
    actions,
  };
};

export const mapStateToProps = (state) => ({
  initialValues: filterSelector.filtersData(state),
});

const ConnectedFilterForm = connect(
  mapStateToProps,
  mapDispatchToProps,
)(FilterForm);

export function PageFiltersHOC(WrappedComponent) {
  // eslint-disable-next-line func-names
  return function (props) {
    return (
      <ConnectedFilterForm
        {...props}
        WrappedComponent={WrappedComponent} />
    );
  };
}

export default PageFiltersHOC;

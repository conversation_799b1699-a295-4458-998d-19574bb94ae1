// @flow

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

import { BoxLabel } from 'components/label';
import { Pie } from '../Pie';

export class Donut extends PureComponent {
  static propTypes = {
    displayName: PropTypes.string,
    tooltip: PropTypes.string,
    data: PropTypes.shape({
      pieData: PropTypes.arrayOf(PropTypes.shape({})),
      colors: PropTypes.arrayOf(PropTypes.string),
      innerText: PropTypes.string,
    }),
    t: PropTypes.func,
    legendsPos: PropTypes.string,
  };
    
  static defaultProps = {
    displayName: '',
    tooltip: '',
    data: {},
    t: (str) => str,
    legendsPos: 'left',
  };

  render() {
    const {
      t,
      displayName,
      tooltip,
      data,
      legendsPos,
    } = this.props;
    const { pieData, colors, innerText } = data;
    return (
      <div className="content-box ux-donut">
        <BoxLabel text={t(displayName)} tooltip={t(tooltip)} styleClasses="uppercase ux-donut-title" />
        <Pie
          data={pieData}
          colors={colors}
          innerText={innerText}
          legendsPos={legendsPos}
          isInteractive
          // enableArcLinkLabels
          activeOuterRadiusOffset={0} />
      </div>
    );
  }
}

export default (withTranslation()(Donut));

import React from 'react';
import ReactTooltip from 'react-tooltip';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { withTranslation } from 'react-i18next';

const DATA_TIP = 'map-tooltip';
function MapTooltip({
  content, header, icon, t,
}) {
  return (
    <ReactTooltip place="right" type="light" className="mask">
      <div>
        <div style={{ padding: '1em', color: 'var(--semantic-color-content-interactive-primary-default)' }}>
          <div className="location-name">
            <FontAwesomeIcon icon={icon} className="fontStyle" />
            <span className="tooltipHeader">{header}</span>
          </div>
        </div>
        <hr className="line" />
        <div>
          <div className="tooltip-content">
            {content && content.length > 0
              ? (content.map((item) => (
                <React.Fragment key={item.name}>
                  <span className="info">{t(item.name)}</span>
                  <span>{item.value}</span>
                </React.Fragment>
              )))
              : ''}
          </div>
        </div>
      </div>
    </ReactTooltip>
  );
}

MapTooltip.propTypes = {
  header: PropTypes.string,
  content: PropTypes.shape({}),
  icon: PropTypes.string,
  t: PropTypes.func,
};

MapTooltip.defaultProps = {
  header: '',
  content: {},
  icon: '',
  t: (str) => str,
};

const WrappedMapTooltip = withTranslation()(MapTooltip);

export { WrappedMapTooltip, DATA_TIP };
export default MapTooltip;

// @flow
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from 'react-simple-maps';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { WrappedMapTooltip, DATA_TIP } from './MapTooltip';
import Map from '../Map';
import MapLegendNumeric from '../MapLegendNumeric';

// const renderLines = () => {
//   flows.map(flow => (
//     <Line
//       key={flow.geoId}
//       className="world-map-arc"
//       line={{
//         ...flow,
//         coordinates: {
//           start: flow.coordinates,
//           end: originCoordinates
//         }
//       }}
//       buildPath={buildCurves}
//       strokeWidth={flow.strokeWidth}
//       {...mouseInteractionProps}
//     />
//   ));
// }

class MapWithTooltip extends Component {
  static buildCurves(start, end, arc) {
    const x0 = start[0];
    const x1 = end[0];
    const y0 = start[1];
    const y1 = end[1];
    const curve = {
      forceUp: `${x1} ${y0}`,
      forceDown: `${x0} ${y1}`,
    }[arc.curveStyle];

    return `M ${start.join(' ')} Q ${curve} ${end.join(' ')}`;
  }
  
  static propTypes = {
    data: PropTypes.arrayOf(PropTypes.object),
    legendData: PropTypes.shape({}),
    pinIcon: PropTypes.shape({}),
    actions: PropTypes.shape({}),
  };

  static defaultProps = {
    data: [],
    legendData: {},
    pinIcon: {},
    actions: {},
  };

  state = {
    tooltipData: {},
    // zoom: 1,
  };

  handleMouseMove = ({ data }) => {
    this.setState({
      tooltipData: {
        ...data,
      },
    });
  };

  render() {
    const { tooltipData } = this.state;
    const { data, legendData, actions } = this.props;
   
    return (
      <div className="world-map-container">
        <Map zooming>
          <Markers tabable={false}>
            {data.map((item) => {
              // const circleSize = CIRCLE_SIZE[coordinate.volume] || 0;
              // const circleColor = CIRCLE_COLOR[coordinate.uxScore] || colors.red3;
              const {
                id,
                pinSize,
                pinColor,
                geolocation,
                pinIcon,
                color,
              } = item;

              return (
                <Marker
                  key={`${id}`}
                  marker={{
                    coordinates: [
                      geolocation.longitude,
                      geolocation.latitude],
                    data: item,
                  }}
                  onMouseMove={this.handleMouseMove}>
                  { pinIcon
                    ? (
                      <g transform="scale(0.03)">
                        <FontAwesomeIcon icon={pinIcon} className="fontStyle" style={`${color ? 'color:' + color : ''}`} data-tip={DATA_TIP} />
                      </g>
                    )
                    : (
                      <>
                        <circle
                          cx="0"
                          cy="0"
                          r="5"
                          fill={pinColor}
                          style={{
                            pressed: { outline: 0 },
                          }} />
                        <circle
                          cx="0"
                          cy="0"
                          r={pinSize}
                          fill={pinColor}
                          // stroke={pinColor}
                          // strokeWidth="2"
                          data-tip={DATA_TIP}
                          fillOpacity="0.15"
                          style={{
                            pressed: { outline: 0 },
                          }} />
                      </>
                      
                    )}
                 
                </Marker>
              );
            })}
          </Markers>
        </Map>
        <WrappedMapTooltip
          {...tooltipData} />
        <MapLegendNumeric legendData={legendData} actions={actions} />
      </div>
    );
  }
}

export default MapWithTooltip;

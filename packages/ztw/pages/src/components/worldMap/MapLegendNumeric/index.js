import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBuilding, faCodeBranch } from '@fortawesome/pro-solid-svg-icons';

import colors from 'scss/colors.scss';

function MapLegendNumeric({ lengendData, t, actions }) {
  return (
    <div className="map-legend-numeric">
      <div className="container">
        <div className="statuses">
          <div>
            <span className="data">{lengendData.datacenters}</span>
            <span className="legend">{t('DATA_CENTERS')}</span>
          </div>
          <div>
            <span className="data">{lengendData.clusters}</span>
            <span className="legend">{t('CLUSTERS')}</span>
          </div>
          <div>
            <span className="data">{lengendData.edgeconnectors}</span>
            <span className="legend">{t('CLOUD_CONNECTORS')}</span>
          </div>
          <div
            onClick={() => actions.handleTrafficFlow()}
            onKeyPress={() => actions.handleTrafficFlow()}
            type="button"
            role="presentation">
            <span className="data">|</span>
            <span className="legend">{t('TRAFFIC_FLOW')}</span>
          </div>
        </div>
        <div className="info">
          <div className="info-dc">
            <FontAwesomeIcon icon={faBuilding} className="fontStyle" style={{ color: colors.green9, marginRight: '1em' }} />
            <span className="legend">{t('DATA_CENTERS')}</span>
          </div>
          <div className="info-ec">
            <FontAwesomeIcon icon={faCodeBranch} className="fontStyle" style={{ color: colors.blue2, marginRight: '1em' }} />
            <span className="legend">{t('CLOUD_CONNECTORS')}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

MapLegendNumeric.propTypes = {
  lengendData: PropTypes.shape({}),
  t: PropTypes.func,
  actions: PropTypes.shape({}),
};

MapLegendNumeric.defaultProps = {
  lengendData: {
    datacenters: 6,
    clusters: 3,
    edgeconnectors: 24,
  },
  t: (str) => str,
  actions: {},
};

export default withTranslation()(MapLegendNumeric);
export { MapLegendNumeric };

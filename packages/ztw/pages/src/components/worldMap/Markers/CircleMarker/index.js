// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { DATA_TIP } from '../../MapTooltip';

function CircleMarker({
  isSelected, onMouseMove, onClick, pinSize, pinColor, setMarkerRef,
}) {
  const STROKE_WIDTH = 2;
  const circleSize = 2 * (pinSize + STROKE_WIDTH);

  const selectedCircle = isSelected
    ? (
      <circle
        cx={circleSize / 2}
        cy={circleSize / 2}
        r={pinSize}
        className="selected-circle"
        strokeWidth="3"
        strokeOpacity="0.5"
        fillOpacity="0"
        style={{
          pressed: { outline: 0 },
        }} />
    ) : null;
  return (
    <svg
      onMouseMove={onMouseMove}
      onClick={onClick}
      height={circleSize}
      width={circleSize}
      data-tip={DATA_TIP}>
      <circle
        cx={circleSize / 2}
        cy={circleSize / 2}
        r="3"
        fill={pinColor}
        ref={(node) => setMarkerRef(node)}
        style={{
          pressed: { outline: 0 },
        }} />
      <circle
        cx={circleSize / 2}
        cy={circleSize / 2}
        r={pinSize}
        fill={pinColor}
        stroke={pinColor}
        strokeWidth={STROKE_WIDTH}
        fillOpacity="0.4"
        style={{
          pressed: { outline: 0 },
        }} />
      {selectedCircle}
    </svg>
  );
}

CircleMarker.propTypes = {
  isSelected: PropTypes.bool,
  onMouseMove: PropTypes.func,
  onClick: PropTypes.func,
  pinSize: PropTypes.number,
  pinColor: PropTypes.string,
  setMarkerRef: PropTypes.func,
};

CircleMarker.defaultProps = {
  isSelected: false,
  onMouseMove: null,
  onClick: null,
  pinSize: 0,
  pinColor: '',
  setMarkerRef: () => {},
};

export default CircleMarker;

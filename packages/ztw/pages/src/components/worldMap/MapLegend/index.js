import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

function MapLegend({ title, description, t }) {
  return (
    <div className="map-legend">
      <div className="container">
        <p className="title">{t(title)}</p>
        <div className="statuses">
          <div>
            <div className="status-line good" />
            <span>{t('GOOD')}</span>
          </div>
          <div>
            <div className="status-line okay" />
            <span>{t('OKAY')}</span>
          </div>
          <div>
            <div className="status-line poor" />
            <span>{t('POOR')}</span>
          </div>
        </div>
        {description && (
          <>
            <div className="separatore" />
            <p className="description">{t(description)}</p>
          </>
        )}
      </div>
    </div>
  );
}

MapLegend.propTypes = {
  title: PropTypes.string,
  description: PropTypes.string,
  t: PropTypes.func,
};

MapLegend.defaultProps = {
  title: null,
  description: null,
  t: (str) => str,
};

export default withTranslation()(MapLegend);
export { MapLegend };

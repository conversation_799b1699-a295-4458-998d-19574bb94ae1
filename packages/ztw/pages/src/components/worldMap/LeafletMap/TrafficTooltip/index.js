import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMapMarkerAlt } from '@fortawesome/pro-regular-svg-icons';
import { withTranslation } from 'react-i18next';
import { isDirect, isZPA } from 'utils/helpers';

const getThroSessionData = (entry) => {
  let session = 0;
  let bytes = 0;
  entry.forEach((i) => {
    if (i.name === 'SESSIONS') {
      session = i.total;
    } else if (i.name === 'BYTES') {
      // bytes = i.total;
      // Bits to kbps
      bytes = (i.total && i.total > 0) ? ((i.total * 8) / (24 * 60 * 60 * 1024)).toFixed(3) : 0;
    }
  });
  return bytes + '/' + session;
};

const getData = (entries) => {
  let zia = '';
  let zpa = '';
  let direct = '';
  let proxy = '';
  entries.forEach((i) => {
    if (i.name === 'ZIA') {
      zia = getThroSessionData(i.entries);
    } else if (isZPA(i.name)) {
      zpa = getThroSessionData(i.entries);
    } else if (isDirect(i.name)) {
      direct = getThroSessionData(i.entries);
    } else if (i.name === 'ECSELF') {
      proxy = getThroSessionData(i.entries);
    }
  });
  return {
    zia,
    zpa,
    direct,
    proxy,
  };
};

const DATA_TIP = 'map-tooltip';
function MapTooltip(props) {
  const {
    showTrafficTooltip,
    group,
    location,
    name,
    entries,
    t,
  } = props;

  if (!showTrafficTooltip) return null;
  const { zia, zpa, direct } = getData(entries);
  return (
    <div style={{ height: 'auto', paddingBottom: '0.75em' }}>
      <div style={{ padding: '0.2em' }}>
        <div className="location-name">
          <span className="tooltip-header">{name}</span>
          <span className="tooltip-header-info">{group}</span>
          <span className="tooltip-header-info">{location}</span>
          <div className="location">
            <FontAwesomeIcon icon={faMapMarkerAlt} className="fontStyle" />
            <span className="tooltip-header-loc">{location}</span>
          </div>
        </div>
      </div>
      <hr className="line" />
      <div className="tooltip-content">
        <div className="tooltip-body-title">
          {t('THROUGHPUT_KBPS_PER_SESSION')}
        </div>
        <div className="tooltip-body-key">
          {t('ZIA')}
        </div>
        <div className="tooltip-body-value">
          {zia}
        </div>
        <div className="tooltip-body-key">
          {t('ZPA')}
        </div>
        <div className="tooltip-body-value">
          {zpa}
        </div>
        <div className="tooltip-body-key">
          {t('DIRECT')}
        </div>
        <div className="tooltip-body-value">
          {direct}
        </div>
      </div>
    </div>
  );
}

MapTooltip.propTypes = {
  showTrafficTooltip: PropTypes.bool,
  name: PropTypes.string,
  group: PropTypes.string,
  location: PropTypes.string,
  content: PropTypes.shape({}),
  entries: PropTypes.arrayOf(PropTypes.shape({})),
  t: PropTypes.func,
};

MapTooltip.defaultProps = {
  showTrafficTooltip: false,
  name: '',
  group: '',
  location: '',
  content: {},
  entries: [],
  t: (str) => str,
};

const WrappedTrafficTooltip = withTranslation()(MapTooltip);

export { WrappedTrafficTooltip, DATA_TIP };
export default MapTooltip;

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { faTimes } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { isEqual, isNil } from 'utils/lodash';
import { getTextWidth } from 'utils/helpers';
import { withTranslation } from 'react-i18next';

class SelectedValues extends Component {
  state = {
    valuesSelected: [],
    hiddenValueCount: null,
  };

  componentDidMount() {
    const { values } = this.props;
    if (values && values.length) {
      this.setElements();
    }
  }

  shouldComponentUpdate() {
    const { valuesSelected, hiddenValueCount } = this.state;
    if (valuesSelected.length || !isNil(hiddenValueCount)) {
      // reset state, will render with values, and will get exact width of elements
      this.setState({ valuesSelected: [], hiddenValueCount: null });
    }
    return true;
  }

  componentDidUpdate() {
    // after rendering will get exact widths of elements,
    // and will re-render using setState
    this.setElements();
  }

  valuesContainerRef = null;

  getButtonsRefs = () => {
    const { values } = this.props;
    const valuesId = values.map((val) => val.id.toString());
    return Array.prototype.filter.call(
      this.valuesContainerRef.children,
      ((button) => valuesId.indexOf(button.id.toString()) !== -1),
    );
  };

  getShownHiddenValues = (moreItemsWidth) => {
    const { values } = this.props;
    const buttons = this.getButtonsRefs();
    const shownValues = [];
    const containerWidth = this.valuesContainerRef.offsetWidth;
    let shownValue;
    const hiddenButtonsRefs = [];
    let containerWidthWithLabel = containerWidth - moreItemsWidth;

    Array.prototype.forEach.call(buttons, (button) => {
      containerWidthWithLabel -= (button.offsetWidth + parseInt(3, 10));
      if (containerWidthWithLabel < 0) {
        hiddenButtonsRefs.push(button);
      } else {
        shownValue = values.find((val) => val.id.toString() === button.id.toString());
        shownValues.push(shownValue);
      }
    });

    return {
      hiddenButtonsRefs,
      shownValues,
    };
  };

  getHiddenValuesCount = (moreItemsWidth, hiddenButtonsRefs, shownValues) => {
    const { values } = this.props;
    const containerWidth = this.valuesContainerRef.offsetWidth;
    let moreItemsTextWidth = !shownValues.length ? containerWidth : moreItemsWidth;
    let cantTakePlace;

    // check if hidden values can take place of show more label
    Array.prototype.some.call(hiddenButtonsRefs, (hiddenButton) => {
      moreItemsTextWidth -= (hiddenButton.offsetWidth + parseInt(3, 10));
      if (moreItemsTextWidth < 0) {
        cantTakePlace = true;
        return true;
      }
      return false;
    });
    return cantTakePlace ? values.length - shownValues.length : 0;
  };

  addHiddenValuesInsteadOfShowMoreLabel = (hiddenButtonsRefs, shownValues) => {
    const { values } = this.props;

    Array.prototype.forEach.call(hiddenButtonsRefs, (hiddenButton) => {
      const showValue = values.find((val) => val.id.toString() === hiddenButton.id.toString());
      shownValues.push(showValue);
    });
    return shownValues;
  };

  setElements = () => {
    const { t } = this.props;
    const moreItemsWidth = getTextWidth(t('MORE_ITEMS_SELECTED', { count: 0 }));
    const { valuesSelected, hiddenValueCount } = this.state;

    const { shownValues, hiddenButtonsRefs } = this.getShownHiddenValues(moreItemsWidth);
    const leftValueCount = this.getHiddenValuesCount(
      moreItemsWidth,
      hiddenButtonsRefs,
      shownValues,
    );

    if (!leftValueCount) {
      this.addHiddenValuesInsteadOfShowMoreLabel(hiddenButtonsRefs, shownValues);
    }

    if ((shownValues.length && !isEqual(valuesSelected, shownValues))
      || (leftValueCount && hiddenValueCount !== leftValueCount)) {
      this.setState({ valuesSelected: shownValues, hiddenValueCount: leftValueCount });
    }
  };

  setValuesContainerRef = (element) => {
    this.valuesContainerRef = element;
  };

  handleSelectedItemClick = (item, e) => {
    const { handelItemClick } = this.props;
    e.stopPropagation();
    handelItemClick(item);
  };

  getList = () => {
    const { values } = this.props;
    const { valuesSelected, hiddenValueCount } = this.state;

    const data = isNil(hiddenValueCount) ? values : valuesSelected;
    if (data.length) {
      return data.map((item) => (
        <button
          type="button"
          key={`selected-${item.id}`}
          className="selected-value"
          id={item.id}
          onClick={(e) => this.handleSelectedItemClick(item, e)}>
          <p>
            {item.name}
            {' '}
          </p>
          <FontAwesomeIcon icon={faTimes} />
        </button>
      ));
    }
    return null;
  };

  getLabel = () => {
    const { t, label } = this.props;
    const { valuesSelected, hiddenValueCount } = this.state;
    let selectLabel;
    if (hiddenValueCount && !valuesSelected.length) {
      selectLabel = label;
    }
    if (hiddenValueCount && valuesSelected.length) {
      selectLabel = t('MORE_ITEMS_SELECTED', { count: hiddenValueCount });
    }

    return selectLabel ? <span className="selected-label">{selectLabel}</span> : null;
  };

  render() {
    return (
      <div className="selected-values" ref={this.setValuesContainerRef}>
        {this.getList()}
        {this.getLabel()}
      </div>
    );
  }
}

SelectedValues.propTypes = {
  t: PropTypes.func,
  values: PropTypes.arrayOf(PropTypes.shape({
    value: PropTypes.any,
    label: PropTypes.string,
  })),
  handelItemClick: PropTypes.func,
  label: PropTypes.string,
};

SelectedValues.defaultProps = {
  t: (str) => (str),
  values: [],
  handelItemClick: null,
  label: '',
};

export default withTranslation()(SelectedValues);
export { SelectedValues };

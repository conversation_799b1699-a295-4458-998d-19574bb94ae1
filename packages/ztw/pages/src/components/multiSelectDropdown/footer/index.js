import React from 'react';
import PropTypes from 'prop-types';

function Footer(props) {
  const {
    doneLabel,
    allLabel,
    noneLabel,
    done,
    selectAll,
    unselect,
    isSingleSelect,
  } = props;

  return (
    <div className="multi-select-footer">
      <div className="left-side">
        <button type="button" onClick={done} className="done-button submit">{doneLabel}</button>
      </div>
      <div className="right-side">
        {!isSingleSelect && <button type="button" className="select-all" onClick={() => selectAll()}>{allLabel}</button>}
        <button type="button" className="select-none" onClick={unselect}>{noneLabel}</button>
      </div>
    </div>
  );
}

Footer.propTypes = {
  selectAll: PropTypes.func,
  unselect: PropTypes.func,
  done: PropTypes.func,
  noneLabel: PropTypes.string,
  allLabel: PropTypes.string,
  doneLabel: PropTypes.string,
  isSingleSelect: PropTypes.bool,
};

Footer.defaultProps = {
  selectAll: () => { },
  unselect: () => { },
  done: () => { },
  noneLabel: null,
  allLabel: null,
  doneLabel: null,
  isSingleSelect: false,
};

export default Footer;

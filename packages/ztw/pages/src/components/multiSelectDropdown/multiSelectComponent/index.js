/* eslint-disable react/jsx-handler-names */
// @flow

import React, { Component } from 'react';
import { faAngleUp, faAngleDown } from '@fortawesome/pro-regular-svg-icons';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import DropdownSearch from 'components/dropDown/DropdownSearch';
import OptionsList from '../optionsList';
import SelectedValues from '../selectedValues';
import Footer from '../footer';

class MultiSelectDropDown extends Component {
  static propTypes = {
    label: PropTypes.string,
    disabledTooltip: PropTypes.string,
    displayLabel: PropTypes.string,
    noDataLabel: PropTypes.string,
    hasMorePages: PropTypes.bool,
    loadMoreLabel: PropTypes.string,
    actions: PropTypes.shape(),
    isOpen: PropTypes.bool,
    values: PropTypes.arrayOf(PropTypes.shape({})),
    selectedValues: PropTypes.arrayOf(PropTypes.shape({})),
    data: PropTypes.arrayOf(PropTypes.shape({})),
    searchString: PropTypes.string,
    showValuesCount: PropTypes.bool,
    isSingleSelect: PropTypes.bool,
    searchParamName: PropTypes.string,
  };

  static defaultProps = {
    label: null,
    disabledTooltip: null,
    noDataLabel: null,
    loadMoreLabel: null,
    displayLabel: null,
    hasMorePages: null,
    actions: {},
    isOpen: false,
    selectedValues: [],
    values: [],
    data: [],
    searchString: '',
    showValuesCount: false,
    isSingleSelect: false,
    searchParamName: '',
  };

  componentDidMount() {
    document.addEventListener('mousedown', this.handleMouseDown);
  }

  shouldComponentUpdate(nextProps) {
    const {
      actions: { load }, isOpen,
    } = this.props;

    if (nextProps.isOpen && nextProps.isOpen !== isOpen) {
      load();
    }
    return true;
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleMouseDown);
  }

  handleMouseDown = (e) => {
    const { isOpen } = this.props;
    if (isOpen && !this.multiSelectRef.contains(e.target)) {
      this.handleDone();
    }
  };

  loadNextPage = () => {
    const {
      actions: {
        nextPage,
        load,
      },
    } = this.props;
    return Promise.resolve()
      .then(() => {
        nextPage();
      })
      .then(() => {
        load();
      });
  };

  handelListToggle = () => {
    const { actions: { cancel, open }, isOpen } = this.props;
    if (isOpen) {
      cancel();
      return;
    }
    open();
  };

  handleDone = () => {
    const {
      actions: { setSelectedValues, cancel },
      selectedValues,
    } = this.props;
    setSelectedValues(selectedValues);
    cancel();
  };

  getSelectedValues = () => {
    const {
      actions: { removeValue },
      values, showValuesCount, label, displayLabel,
    } = this.props;

    if (!values.length) {
      return <span className="selected-label">{label}</span>;
    }

    if (showValuesCount) {
      return (
        <span className="selected-label">
          {displayLabel}
        </span>
      );
    }

    return (
      <SelectedValues
        values={values}
        handelItemClick={removeValue}
        label={displayLabel} />
    );
  };

  getShowMoreBtn = () => {
    const {
      hasMorePages,
      loadMoreLabel,
    } = this.props;
    if (hasMorePages) {
      return (
        <button
          type="button"
          onClick={this.loadNextPage}
          className="load-more">
          {loadMoreLabel}
        </button>
      );
    }
    return null;
  };

  getOptionsList = () => {
    const {
      actions: {
        onChange,
      },
      searchString,
      disabledTooltip,
      noDataLabel,
      data,
      isSingleSelect,
    } = this.props;

    if (!data.length) {
      return <div className="no-data">{noDataLabel}</div>;
    }

    return (
      <OptionsList
        data={data}
        onChange={onChange}
        searchString={searchString}
        disabledTooltip={disabledTooltip}
        isSingleSelect={isSingleSelect} />
    );
  };

  getModal = () => {
    const {
      actions: {
        setSearchString, selectAll, unselect,
        searchData,
      },
      isOpen,
      searchParamName,
      isSingleSelect,
    } = this.props;

    if (isOpen) {
      return (
        <div className="multi-select-drop-down-modal-parent">
          <div className="multi-select-drop-down-modal-cont">
            <div className="modal-header">
              <DropdownSearch
                actions={{
                  setSearchString,
                  searchData,
                }}
                searchParamName={searchParamName} />
            </div>
            <Loading {...this.props}>
              <ServerError {...this.props}>
                <>
                  <div className="modal-body-cont">
                    {this.getOptionsList()}
                    {this.getShowMoreBtn()}
                  </div>
                  <Footer
                    done={this.handleDone}
                    selectAll={selectAll}
                    unselect={unselect}
                    isSingleSelect={isSingleSelect}
                    {...this.props} />
                </>
              </ServerError>
            </Loading>
          </div>
        </div>
      );
    }
    return null;
  };

  render() {
    const { isOpen } = this.props;

    return (
      <div
        className="multi-select-drop-down"
        ref={(r) => { this.multiSelectRef = r; }}>
        <div
          className="multi-select-drop-down-selection"
          onClick={this.handelListToggle}
          role="presentation">
          {this.getSelectedValues()}
          <div className="selection-icon">
            <FontAwesomeIcon
              icon={isOpen ? faAngleUp : faAngleDown} />
          </div>
        </div>
        {this.getModal()}
      </div>
    );
  }
}

export default MultiSelectDropDown;

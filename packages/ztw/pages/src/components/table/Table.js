/* eslint-disable react/jsx-handler-names */
// @flow
import React from 'react';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';
import { Trans } from 'react-i18next';

const DEFAULT_PAGE_SIZE = 150;
class Table extends React.Component {
  getPageSize = () => {
    const { data } = this.props;
    const dataLength = data.length;
    if (!dataLength) {
      return 1;
    }

    return dataLength <= DEFAULT_PAGE_SIZE ? dataLength : DEFAULT_PAGE_SIZE;
  };

  ifShowPagination = () => {
    const { data } = this.props;
    if (!data.length) {
      return false;
    }
    return !(data.length <= DEFAULT_PAGE_SIZE);
  };

  onExpandedChangeHandler = (expendedData, item, classData, itemData) => {
    const { handelExpendedRow } = this.props;
    handelExpendedRow(itemData.original.id);
  };

  getDefaultExpanded = () => {
    const { data, expendedRowIds } = this.props;
    const defaultExpandedRows = {};

    data.forEach((item, index) => {
      defaultExpandedRows[index] = (expendedRowIds || []).indexOf(item.id) !== -1;
    });
    return defaultExpandedRows;
  };

  render() {
    const {
      columns, data, defaultSorted, title, SubComponent,
    } = this.props;
    return (
      <div className="app-table-container">
        {title && <div className="title"><Trans>{title}</Trans></div>}
        <ReactTable
          defaultExpanded={this.getDefaultExpanded()}
          onExpandedChange={this.onExpandedChangeHandler}
          className={`app-table has-nested-${!!SubComponent}`}
          onPageSizeChange={this.setPreferredPageSize}
          data={data}
          defaultSorted={defaultSorted}
          columns={columns}
          defaultPageSize={this.getPageSize()}
          showPagination={this.ifShowPagination()}
          resizable
          noDataText={<Trans>NO_DATA</Trans>}
          SubComponent={SubComponent ? (row) => <SubComponent row={row} /> : null} />
      </div>
    );
  }
}

Table.propTypes = {
  title: PropTypes.node,
  columns: PropTypes.arrayOf(PropTypes.object),
  data: PropTypes.arrayOf(PropTypes.object),
  defaultSorted: PropTypes.arrayOf(PropTypes.object),
  SubComponent: PropTypes.func,
  expendedRowIds: PropTypes.arrayOf(PropTypes.number),
  handelExpendedRow: PropTypes.func,
};

Table.defaultProps = {
  title: null,
  columns: [],
  data: [],
  defaultSorted: [],
  SubComponent: null,
  expendedRowIds: [],
  handelExpendedRow: () => { },
};

export default Table;

// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { Trans } from 'react-i18next';

function LinkCell({
  value,
  to,
  icon,
  original,
  hash,
}) {
  return (
    <div className="cell-table-link">
      {icon}
      <Link
        to={{
          pathname: to,
          hash: hash ? `#${hash}` : null,
          state: original,
        }}>
        <Trans>{value}</Trans>
      </Link>
    </div>
  );
}

LinkCell.propTypes = {
  value: PropTypes.string,
  to: PropTypes.string,
  icon: PropTypes.shape(),
  original: PropTypes.shape(),
  hash: PropTypes.string,
};

LinkCell.defaultProps = {
  value: null,
  to: null,
  icon: null,
  original: {},
  hash: null,
};

export default LinkCell;

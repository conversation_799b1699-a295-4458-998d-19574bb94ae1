/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
// @flow

import React from 'react';
import { withTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faDownload, faTriangleExclamation, faEye,
} from '@fortawesome/pro-solid-svg-icons';
import {
  faRedo, faTimes, faCode, faPencil,
} from '@fortawesome/pro-regular-svg-icons';
import PropTypes from 'prop-types';
import diffIcon from 'images/diff_view_icon.png';

function ActionCell({
  isDisabled, isEdit, isPredefined, isDownload, isDownloadDisable, iconType, handleRegenerateAction,
  handleDownloadAction, handleEditAction, handleDeleteAction, handleViewAction, t,
}) {
  return (
    <div className={`cell-table-action ${isPredefined ? 'predifined-cell' : null}`}>
      {!isDisabled && isDownloadDisable && <FontAwesomeIcon icon={faDownload} className="disable-icon" />}
      {!isDisabled && isDownload && <FontAwesomeIcon icon={faDownload} className="eye-icon" onClick={handleDownloadAction} />}
      {!isEdit && !isDownload && !isDownloadDisable && iconType && (iconType === 'diffIcon'
        ? <img className="eye-icon" src={diffIcon} onClick={handleViewAction} alt="view" />
        : <FontAwesomeIcon icon={faCode} className="eye-icon" onClick={handleViewAction} />)}
      {!isDisabled && !isEdit && !isDownload && !isDownloadDisable && !iconType && <FontAwesomeIcon icon={faEye} className="eye-icon" onClick={handleViewAction} />}
      {!isDisabled && isEdit && <FontAwesomeIcon icon={faPencil} className="pencil-icon" onClick={handleEditAction} />}
      {!isDisabled && !isPredefined && handleRegenerateAction && <FontAwesomeIcon className="delete-icon" icon={faRedo} title={t('REGENERATE_API_KEY_TOOLTIP')} onClick={handleRegenerateAction} />}
      {!isDisabled && !isPredefined && <FontAwesomeIcon className="delete-icon" icon={faTimes} title={t('DELETE_THIS_ITEM')} onClick={handleDeleteAction} />}
      {isDisabled && (
        <span className="disabled-icon api-disabled">
          <FontAwesomeIcon icon={faTriangleExclamation} className="disable-icon" />
          {' '}
          {t('DISABLED')}
        </span>
      )}

    </div>
  );
}

ActionCell.propTypes = {
  isDisabled: PropTypes.bool,
  isEdit: PropTypes.bool,
  isPredefined: PropTypes.bool,
  isDownload: PropTypes.bool,
  isDownloadDisable: PropTypes.bool,
  handleEditAction: PropTypes.func,
  handleDeleteAction: PropTypes.func,
  handleViewAction: PropTypes.func,
  handleDownloadAction: PropTypes.func,
  handleRegenerateAction: PropTypes.func,
  iconType: PropTypes.string,
  t: PropTypes.func,
};

ActionCell.defaultProps = {
  isDisabled: false,
  isEdit: null,
  isPredefined: false,
  isDownload: false,
  isDownloadDisable: false,
  handleEditAction: null,
  handleDeleteAction: null,
  handleViewAction: null,
  handleDownloadAction: null,
  handleRegenerateAction: null,
  iconType: '',
  t: null,
};

export default withTranslation()(ActionCell);

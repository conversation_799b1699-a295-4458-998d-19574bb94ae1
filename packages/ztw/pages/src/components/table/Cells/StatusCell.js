// @flow

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle, faTimesCircle } from '@fortawesome/pro-solid-svg-icons';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';

function StatusCell({ isEnabled }) {
  return (
    <div className="cell-status">
      {isEnabled && (
        <div className="enabled">
          <FontAwesomeIcon icon={faCheckCircle} className="info-circle" />
          <span><Trans>ENABLED</Trans></span>
        </div>
      )}
      {!isEnabled && (
        <div className="disabled">
          <FontAwesomeIcon icon={faTimesCircle} className="info-circle" />
          <span><Trans>DISABLED</Trans></span>
        </div>
      )}
    </div>
  );
}

StatusCell.propTypes = {
  isEnabled: PropTypes.bool,
};

StatusCell.defaultProps = {
  isEnabled: null,
};

export default StatusCell;

// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAngleDown, faAngleRight } from '@fortawesome/pro-solid-svg-icons';

function Expander({ isExpanded }) {
  return (
    <div className="cell-table-expander">
      <div className="icon-cont">
        <FontAwesomeIcon icon={isExpanded ? faAngleDown : faAngleRight} />
      </div>
    </div>
  );
}

Expander.propTypes = {
  isExpanded: PropTypes.bool,
};

Expander.defaultProps = {
  isExpanded: false,
};

export default Expander;

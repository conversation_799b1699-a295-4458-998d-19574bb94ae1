// @flow

import React from 'react';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { isEmpty } from 'utils/lodash';
import Loading from 'components/spinner/Loading';

function SubscriptionRequired({ accessSubscriptions }) {
  const { t } = useTranslation();
  if (isEmpty(accessSubscriptions)) return <Loading loading />;

  return (
    <div className="page-main-content">
      <div className="subscription-required">
        <div className="subscription-required-splash">
          <div className="subscription-required-splash-title">{t('SUBSCRIPTION_REQUIRED')}</div>
          <div className="subscription-required-splash-message">{t('SUBSCRIPTION_REQUIRED_MESSAGE')}</div>
        </div>
      </div>
    </div>
  );
}
SubscriptionRequired.propTypes = {
  accessSubscriptions: PropTypes.shape({}),
};

SubscriptionRequired.defaultProps = {
  accessSubscriptions: {},
};

export default (SubscriptionRequired);
export { SubscriptionRequired };

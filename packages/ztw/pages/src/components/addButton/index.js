// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus } from '@fortawesome/pro-solid-svg-icons';

function AddNewButton(props) {
  const { label, clickCallback, icon = null } = props;

  return (
    <button onClick={clickCallback} type="button" className="add-button">
      <FontAwesomeIcon icon={icon || faPlus} />
      {' '}
      {label}
    </button>
  );
}

AddNewButton.propTypes = {
  icon: PropTypes.node,
  label: PropTypes.node,
  clickCallback: PropTypes.func,
};

AddNewButton.defaultProps = {
  icon: null,
  label: null,
  clickCallback: null,
};

export default AddNewButton;

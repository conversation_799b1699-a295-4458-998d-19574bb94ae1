// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Trans, withTranslation } from 'react-i18next';
import { formValueSelector, isValid } from 'redux-form';
import { faChevronRight, faChevronDown } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const formValidity = {};
const selector = formValueSelector('provisioningTemplateWizard');
let type;

function MainNav(props) {
  const {
    activePage,
    typeFromState,
    valid,
    selected,
    mainNavConfig,
    subItemName,
    subItem2Name,
  } = props;

  // track form validity for each sub-form
  formValidity[activePage] = valid;

  // if a sub-form is invalid, mark all next sub-forms as invalid
  if (!valid) {
    mainNavConfig.forEach((item, index) => {
      if (index > activePage) {
        formValidity[index] = false;
      }
    });
  }

  // if the entire wizard form is filled out and user comes back to 1st screen and
  // changes the type, unset validity for all other pages to reflect change
  // in Additional params
  if (activePage === 0 && type !== typeFromState) {
    for (let i = 1, l = mainNavConfig.length; i < l; i += 1) {
      formValidity[i] = false;
    }

    type = typeFromState;
  }

  const treeLineLvl1 = (
    <svg className="tree-line" width="20%" height="100%" viewBox="0 0 20 100">
      <line x1="0" y1="00" x2="00" y2="100" stroke="#000" strokeWidth="1" />
      <line x1="0" y1="50" x2="40" y2="50" stroke="#000" strokeWidth="1" />
    </svg>
  );

  const treeLineLvl1LastItem = (
    <svg className="tree-line" width="20%" height="100%" viewBox="0 0 20 100">
      <line x1="0" y1="00" x2="00" y2="50" stroke="#000" strokeWidth="1" />
      <line x1="0" y1="50" x2="40" y2="50" stroke="#000" strokeWidth="1" />
    </svg>
  );

  const treeLineLvl1OnLvl2 = (
    <svg className="tree-line" width="20%" height="100%" viewBox="0 0 20 100">
      <line x1="0" y1="00" x2="00" y2="100" stroke="#000" strokeWidth="1" />
    </svg>
  );
  
  const treeLineLvl2 = (
    <svg className="tree-line" width="30%" height="100%" viewBox="0 0 30 100">
      <line x1="60" y1="00" x2="60" y2="100" stroke="#000" strokeWidth="1" />
      <line x1="60" y1="50" x2="80" y2="50" stroke="#000" strokeWidth="1" />
    </svg>
  );

  const treeLineLvl2LastIte = (
    <svg className="tree-line" width="30%" height="100%" viewBox="0 0 30 100">
      <line x1="60" y1="00" x2="60" y2="50" stroke="#000" strokeWidth="1" />
      <line x1="60" y1="50" x2="80" y2="50" stroke="#000" strokeWidth="1" />
    </svg>
  );

  return (
    <div className="main-nav">
      <ul>
        {mainNavConfig.map((item, index) => {
          const { subOptions } = item || {};
          const { subOptions2 } = subOptions || {};
          const widgetTitle = <span className="name"><Trans>{item.id}</Trans></span>;

          return (
            <>
              <li
                className={`${index === activePage && !subOptions ? 'active' : ''}`}
                key={item?.id}>
                <button
                  type="button"
                  onClick={item?.onHandleClick}
                  disabled={item?.disabled}>
                  <span className="fa-layers fa-fw">
                    <FontAwesomeIcon icon={item.icon} size="lg" />
                  </span>
                  <div className="main-nav-line">
                    {widgetTitle}
                    <div className="main-nav-chevron">
                      {subOptions && index !== activePage && <FontAwesomeIcon icon={faChevronRight} size="lg" />}
                      {subOptions && index === activePage && <FontAwesomeIcon icon={faChevronDown} size="lg" />}
                    </div>
                  </div>
                </button>
              </li>
              {subOptions && index === activePage
              && (
                subOptions.map((subItem, lvl1idx) => (
                  <>
                    <li
                      className={`sub-level-1 ${subItem?.name === subItemName && !subItem2Name ? 'active' : ''}`}
                      key={subItem?.id}>
                      {lvl1idx === subOptions.length - 1 ? treeLineLvl1LastItem : treeLineLvl1}
                      <button
                        type="button"
                        onClick={subItem?.onHandleClick}
                        disabled={subItem?.disabled}>
                        <span className="fa-layers fa-fw">
                          {/* <FontAwesomeIcon icon={subItem.icon} size="lg" /> */}
                        </span>
                        <div className="main-nav-line">
                          {/* <span className="tree-view">{'\u251C'}</span> */}
                          <span className="name"><Trans>{subItem.name}</Trans></span>
                          <div className="main-nav-chevron">
                            {subOptions && subItem?.name !== subItemName && <FontAwesomeIcon icon={faChevronRight} size="lg" />}
                            {subOptions && subItem?.name === subItemName && <FontAwesomeIcon icon={faChevronDown} size="lg" />}
                          </div>
                        </div>
                      </button>
                    </li>
                    {subItem?.subOptions2 && subItem?.name === subItemName
                && (
                  subItem?.subOptions2?.map((subItem2, lvl2idx) => (
                    <li
                      className={`sub-level-1 sub-level-2 ${subItem2?.externalId === subItem2Name ? 'active' : ''}`}
                      key={subItem2?.id}>
                      {lvl1idx === subOptions.length - 1 ? <></> : treeLineLvl1OnLvl2}
                      {lvl2idx === subItem?.subOptions2?.length - 1 ? treeLineLvl2LastIte : treeLineLvl2}
                      
                      <button
                        type="button"
                        onClick={subItem2?.onHandleClick}
                        disabled={subItem2?.disabled}>
                        <span className="fa-layers fa-fw">
                        </span>
                        <div className="main-nav-line">
                          <span className="name">
                            <Trans>
                              &nbsp;
                              &nbsp;
                              &nbsp;
                              {subItem2.name}
                            </Trans>
                          </span>
                          <div className="main-nav-chevron">
                            {subOptions2 && subItem2?.externalId !== subItem2Name && <FontAwesomeIcon icon={faChevronRight} size="lg" />}
                            {subOptions2 && subItem2?.externalId === subItem2Name && <FontAwesomeIcon icon={faChevronDown} size="lg" />}
                          </div>
                        </div>
                      </button>
                    </li>
                  ))
                )}

                  </>

                ))
              )}

            </>
          );
        })}
      </ul>
    </div>
  );
}

MainNav.propTypes = {
  activePage: PropTypes.number,
  typeFromState: PropTypes.string,
  valid: PropTypes.bool,
  mainNavConfig: PropTypes.arrayOf(PropTypes.string),
};

MainNav.defaultProps = {
  activePage: 0,
  typeFromState: '',
  valid: false,
  mainNavConfig: {},
};

export default connect((state) => ({
  typeFromState: selector(state, 'type'),
  valid: isValid('provisioningTemplateWizard')(state),
}))(withTranslation()(MainNav));

export { MainNav };

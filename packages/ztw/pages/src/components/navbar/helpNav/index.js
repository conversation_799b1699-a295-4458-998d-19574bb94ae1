// @flow
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import * as ssoDuck from 'ducks/sso';
import { NavLink } from 'react-router-dom';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faQuestionCircle, faExternalLink } from '@fortawesome/pro-regular-svg-icons';
import Modal from 'components/modal';

import {
  HELP_SUB_NAV_ROUTES,
} from 'config';

import {
  toggleHelpPanel, toggleShowForm, setModalTitle,
} from 'ducks/helpNav';
import * as selectors from 'ducks/helpNav/selectors';
import * as loginSelectors from 'ducks/login/selectors';

import {
  RemoteAssistanceForm,
  URLLookupForm,
  BlacklistedIPCheckForm,
} from './components';

class HelpNav extends Component {
  static propTypes = {
    actions: PropTypes.shape({}),
    data: PropTypes.shape({}),
    accessPermissions: PropTypes.shape({}),
    authType: PropTypes.string,
    toggleHelpPanel: PropTypes.bool,
    location: PropTypes.shape({ pathname: PropTypes.string }),
    t: PropTypes.func,
    modalTitle: PropTypes.string,
    showForm: PropTypes.bool,
  };

  static defaultProps = {
    actions: {},
    data: {},
    accessPermissions: {},
    authType: '',
    toggleHelpPanel: false,
    modalTitle: '',
    showForm: false,
    location: null,
    t: (str) => str,
  };

  // routes will come from top level route
  active = (prop) => {
    const { location } = this.props;
    // debugger;
    if (!location) return '';

    const { pathname } = location;

    // we will make following dynamic based on url
    if (pathname.includes(prop.path)) {
      return 'active';
    }

    return '';
  };

  getRoutes = (pageroutes) => pageroutes.map((prop) => {
    const {
      t,
      accessPermissions,
      authType,
      actions,
    } = this.props;

    return (
      <li className={`nav-menu-section ${(prop.perm && accessPermissions[prop.perm] === 'NONE') ? 'hide' : ''}`} key={prop.title}>
        <h3 className="nav-menu-section-header">{t(prop.title)}</h3>
        <ul>
          {
            prop.routes.map((route) => {
              const className = this.active(route);
              if ((route.perm && accessPermissions[route.perm] === 'NONE') || authType === 'SUPPORT_ACCESS_PARTIAL') {
                return null;
              }
              return (
                <li key={route.name} className={`${className} ${route.liclass ? route.liclass : ''}`}>
                  {(route.externalLink)
                  && (
                    <a href={route.redirectTo} target="_blank" rel="noopener noreferrer" className="external-link-button">
                      <span className="nav-menu-list-item-text">
                        {t(route.name)}
                        <FontAwesomeIcon className="fa-external-link fa-xs" icon={faExternalLink} size="xs" />
                      </span>
                    </a>
                  )}
                  {(!route.externalLink)
                  && (
                    <NavLink
                      key={route.name}
                      to="?"
                      onClick={
                        async (e) => {
                          if (route.name === 'SUBMIT_TICKET') {
                            e.preventDefault();
                            actions.submitTicket();
                          } else {
                            await actions.toggleHelpPanel(false);
                            await actions.setModalTitle(route.name);
                            await actions.toggleShowForm(true);
                          }
                        }
                      }
                      activeclassname="active-menu-item">
                      <span className="nav-menu-list-item-text">
                        {t(route.name)}
                      </span>
                    </NavLink>
                  )}
                </li>
              );
            })
          }
        </ul>
      </li>
    );
  });
 
  render() {
    const {
      modalTitle,
      showForm,
      actions: { toggleHelpPanel: toggleActivePanel, toggleShowForm: onShowForm },
      toggleHelpPanel: togglePanel,
      t,
    } = this.props;

    return (
      <>
        <span
          className="custom-nav help-nav"
          onMouseOver={() => toggleActivePanel(true)}
          onFocus={() => toggleActivePanel(true)}
          onMouseLeave={() => toggleActivePanel(false)}
          onBlur={() => toggleActivePanel(false)}>
          <span className="fa-cloud">
            <FontAwesomeIcon className="svg-inline--fa fa-user font16 fa" icon={faQuestionCircle} size="sm" />
          </span>
          {
            (togglePanel) ? (
              <div
                className="help-panel help"
                onMouseOver={() => toggleActivePanel(true)}
                onFocus={() => toggleActivePanel(true)}
                onMouseLeave={() => toggleActivePanel(false)}
                onBlur={() => toggleActivePanel(false)}>
                <ul>
                  {this.getRoutes(HELP_SUB_NAV_ROUTES)}
                </ul>
              </div>
            ) : ''
          }
        </span>
        <Modal
          title={t(modalTitle)}
          isOpen={showForm}
          closeModal={() => onShowForm(false)}>
          <div className="remote-assistance-form">
            {modalTitle === 'REMOTE_ASSISTANCE' && <RemoteAssistanceForm />}
            {modalTitle === 'URL_LOOKUP' && <URLLookupForm />}
            {modalTitle === 'BLACKLISTED_IP_CHECK' && <BlacklistedIPCheckForm />}
          </div>
        </Modal>
      </>
    );
  }
}

const mapStateToProps = (state) => ({
  ...selectors.baseSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    submitTicket: ssoDuck.submitTicket,
    toggleHelpPanel,
    toggleShowForm,
    setModalTitle,
  }, dispatch);

  return {
    actions,
  };
};

export default withTranslation()(connect(
  mapStateToProps,
  mapDispatchToProps,
)(HelpNav));

export { HelpNav };

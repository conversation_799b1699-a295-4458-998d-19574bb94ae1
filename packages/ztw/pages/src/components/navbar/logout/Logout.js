import React, { Component } from 'react';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSignOutAlt } from '@fortawesome/pro-solid-svg-icons';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { logout } from 'ducks/login';
// import { withRouter, Redirect, BrowserRouter } from 'react-router-dom';
import { redirect } from 'react-router-dom';

class Logout extends Component {
  static propTypes = {
    actions: PropTypes.shape(),
    redirectHome: PropTypes.bool,
  };

  static defaultProps = {
    actions: null,
    redirectHome: false,
  };

  handleLogout = () => {
    const { actions: { signOut } } = this.props;
    signOut();
  };

  render() {
    const { redirectHome } = this.props;
    let logoutPath = '/login';
    if (localStorage.getItem('loginType') && localStorage.getItem('loginType').indexOf('classiclogin') > -1) {
      logoutPath = '/classiclogin';
    }
    if (redirectHome) {
      return redirect(logoutPath);
    }
    // if (redirectHome) {
    //   return (
    //     <>
    //       <BrowserRouter basename={BASE_ROUTE_PATH} forceRefresh>
    //         <Redirect from="/" to={logoutPath} />
    //       </BrowserRouter>
    //       <li
    //         key="/logout"
    //         className="small sign-out-button"
    //         onClick={this.handleLogout}
    //         onKeyPress={this.handleLogout}
    //         role="menuitem"
    //         tabIndex="0">
    //         <FontAwesomeIcon
    //           className="fa"
    //           icon={faSignOutAlt}
    //           transform={{ rotate: 180 }} />
    //       </li>
    //     </>
    //   );
    // }
    return (
      <li
        key="/logout"
        className="small"
        onClick={this.handleLogout}
        onKeyPress={this.handleLogout}
        role="menuitem"
        tabIndex="0">
        <FontAwesomeIcon
          className="fa"
          icon={faSignOutAlt}
          transform={{ rotate: 180 }} />
      </li>
    );
  }
}

const mapStateToProps = (state) => ({
  ...state.login,
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    signOut: logout,
  }, dispatch);
  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)((Logout));

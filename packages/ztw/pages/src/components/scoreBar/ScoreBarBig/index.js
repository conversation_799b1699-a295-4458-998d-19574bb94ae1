// @flow

import React from 'react';
import PropTypes from 'prop-types';
import getScoreBarColor from 'utils/helpers/getScoreBarColor';

function ScoreBarBig(props) {
  const { percentage } = props;
  const color = getScoreBarColor(percentage);

  return (
    <div className="score-bar-big" style={{ background: color }}>
      <div
        className="score"
        style={{
          width: `${100 - percentage}%`,
        }} />
    </div>
  );
}

ScoreBarBig.propTypes = {
  percentage: PropTypes.number,
};

ScoreBarBig.defaultProps = {
  percentage: null,
};

export default ScoreBarBig;

import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

export function GenericErrorMessage({ size, message, t }) {
  return (
    <div className="data-error">
      <div>
        <span className={`${size} text`}>{t(message)}</span>
      </div>
    </div>
  );
}

GenericErrorMessage.propTypes = {
  message: PropTypes.string,
  size: PropTypes.string,
  t: PropTypes.func,
};

GenericErrorMessage.defaultProps = {
  message: 'NO_WIDGET_DATA',
  size: 'medium',
  t: (str) => str,
};

export default withTranslation()(GenericErrorMessage);

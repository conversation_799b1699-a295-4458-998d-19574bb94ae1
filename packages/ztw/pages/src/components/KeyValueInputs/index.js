import React, {
  useEffect, useRef, useState,
} from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import { faTimesCircle, faEye, faEyeSlash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const defaultProps = {
  keyValue: '',
  valValue: '',
  deleteKeyValuePair: noop,
  updateConnectionHeaders: noop,
  placeholderKey: '',
  placeholderValue: '',
  containerClass: '',
  t: (str) => str,
  readOnly: false,
};
  
function KeyValueInputs({
  keyValue,
  valValue,
  deleteKeyValuePair,
  index,
  updateConnectionHeaders,
  placeholderKey,
  placeholderValue,
  containerClass,
  t,
  readOnly,
}) {
  const inputKeyRef = useRef(null);
  const inputValueRef = useRef(null);
  const inputValueRefObfuscated = useRef(null);

  const [inputKeyValue, setInputKeyValue] = useState(keyValue || '');
  const [inputValValue, setInputValValue] = useState(valValue || '');
  const [obfuscated, setObfuscatedState] = useState(true);
  const [inputValValueObfuscated, setInputValValueObfuscated] = useState(valValue);

  useEffect(() => {
    setInputKeyValue(keyValue);
    setInputValValue(valValue);
    const finalKeyValPair = keyValue + ':' + valValue;
    updateConnectionHeaders(finalKeyValPair, index);
    const stringActionValueObfuscated = valValue.replace(/./g, '*');
    setInputValValueObfuscated(stringActionValueObfuscated);
    setObfuscatedState(true);
    return () => {
    };
  }, [valValue]);

  const handleDelete = () => {
    deleteKeyValuePair(index);
  };

  const onChangeKeyHandler = (evt) => {
    const { value } = evt.target;
    setInputKeyValue(value);
  };

  const onChangeValueHandler = (evt) => {
    const { value } = evt.target;
    setInputValValue(value);
  };

  const enableObfuscation = () => {
    setInputKeyValue(inputKeyValue);
    setInputValValue(inputValValue);
    const finalKeyValPair = inputKeyValue + ':' + inputValValue;
    updateConnectionHeaders(finalKeyValPair, index);
    const stringActionValueObfuscated = inputValValue.replace(/./g, '*');
    setInputValValueObfuscated(stringActionValueObfuscated);
    setObfuscatedState(true);
  };

  const disableObfuscation = () => {
    setObfuscatedState(false);
  };
    
  const handleShowValue = () => {
    setObfuscatedState(false);
  };

  const handleHideValue = () => {
    setObfuscatedState(true);
  };

  return (
    <>
      <div className="checkbox-container half-width">
        <span>{t('KEY') + ' ' + (index + 1)}</span>
        <div className={`key-value-pair-container input-container ${containerClass}`}>
          <div className="input-wrapper">
            {readOnly ? (
              <p className="disabled-input">{keyValue}</p>
            ) : (
              <input
                ref={inputKeyRef}
                key={`input-key-${index}`}
                className={`key${index}`}
                type="text"
                value={inputKeyValue}
                onChange={onChangeKeyHandler}
                onKeyUp={onChangeKeyHandler}
                placeholder={placeholderKey} />
            )}
          </div>
        </div>
      </div>
      <div className="checkbox-container half-width">
        <span className="">{t('VALUE') + ' ' + (index + 1)}</span>
        <div className={`key-value-pair-container input-container ${containerClass}`}>
          <div className="input-wrapper">
            {readOnly ? (
              <p className="disabled-input">{valValue}</p>
            ) : (
              <div>
                {!obfuscated ? (
                  <input
                    ref={inputValueRef}
                    key={`input-val-${index}`}
                    className={`value${index}`}
                    type="text"
                    value={inputValValue}
                    onChange={onChangeValueHandler}
                    onKeyUp={onChangeValueHandler}
                    placeholder={placeholderValue}
                    // onMouseLeave={() => updateConnHeadersChild()}
                    // onFocusOut={() => enableObfuscation()}
                    onBlur={() => enableObfuscation()}
                    onFocus={() => disableObfuscation()} />
                ) : (
                  <input
                    ref={inputValueRefObfuscated}
                    key={`input-val-${index}`}
                    className={`value${index}`}
                    type="text"
                    value={inputValValueObfuscated}
                    // onChange={onChangeValueHandler}
                    // onKeyUp={onChangeValueHandler}
                    placeholder={placeholderValue}
                    // onMouseLeave={() => updateConnHeadersChild()}
                    // onFocusOut={() => enableObfuscation()}
                    // onBlur={() => disableObfuscation()}
                    onFocus={() => disableObfuscation()} />
                )}
                {obfuscated ? (
                  <span className="form-link-text reveal-show-hide-icon cloud-nss-action-string-value-show">
                    <FontAwesomeIcon
                      icon={faEye}
                      className="delete-icon"
                      onClick={handleShowValue} />
                  </span>
                ) : (
                  <span className="form-link-text reveal-show-hide-icon cloud-nss-action-string-value-hide">
                    <FontAwesomeIcon
                      icon={faEyeSlash}
                      className="delete-icon"
                      onClick={handleHideValue} />
                  </span>
                )}
              </div>
            )}
            {!readOnly
              && (
                <span className="custom-delete-icon">
                  <FontAwesomeIcon
                    icon={faTimesCircle}
                    className="delete-icon"
                    onClick={handleDelete} />
                </span>
              )}
          </div>
        </div>
      </div>
    </>
  );
}
  
KeyValueInputs.defaultProps = defaultProps;
  
KeyValueInputs.propTypes = {
  index: PropTypes.number,
  keyValue: PropTypes.string,
  valValue: PropTypes.string,
  deleteKeyValuePair: PropTypes.func,
  updateConnectionHeaders: PropTypes.func,
  placeholderKey: PropTypes.string,
  placeholderValue: PropTypes.string,
  containerClass: PropTypes.string,
  t: PropTypes.func,
  readOnly: PropTypes.bool,
};
  
export default KeyValueInputs;

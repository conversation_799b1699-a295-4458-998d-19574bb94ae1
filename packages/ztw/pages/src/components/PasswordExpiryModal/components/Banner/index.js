// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';

import { faExclamationTriangle, faTimesCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

export function Banner(props) {
  Banner.propTypes = {
    message: PropTypes.string,
    notificationType: PropTypes.string,
  };

  Banner.defaultProps = {
    message: '',
    notificationType: 'INFO',
  };
  const { t } = useTranslation();
  const { message, notificationType } = props;

  const getNotificationType = () => {
    if (notificationType === 'INFO') {
      return (<FontAwesomeIcon icon={faExclamationTriangle} />);
    }
    if (notificationType === 'CRITICAL') {
      return (<FontAwesomeIcon icon={faTimesCircle} />);
    }
    return null;
  };

  return (
    <div className="password-banner">
      {getNotificationType()}
      <span className="text-label">{t(message)}</span>
    </div>
  );
}

export default Banner;

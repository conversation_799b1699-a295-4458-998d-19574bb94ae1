import PropTypes from 'prop-types';
import { noop, isEqual } from 'utils/lodash';

import React from 'react';

class TabSwitch extends React.Component {
  static propTypes = {
    items: PropTypes.arrayOf(PropTypes.shape({
      value: PropTypes.oneOfType([
        PropTypes.string,
      ]),
      label: PropTypes.string,
    })),
    t: PropTypes.func,
    setValue: PropTypes.func,
    currentValue: PropTypes.shape({
      value: PropTypes.oneOfType([
        PropTypes.string,
      ]),
      label: PropTypes.string,
    }),
    i18n: PropTypes.shape({}),
    className: PropTypes.string,
  };
  
  static defaultProps = {
    items: [],
    t: (str) => str,
    i18n: {
      localizeString: (str) => str,
    },
    currentValue: {},
    setValue: noop,
  };
  
  handleChangeValue = (item) => {
    const { setValue } = this.props;
    setValue(item);
  };
  
  render() {
    const {
      items, t, currentValue, className,
    } = this.props;
      
    const { value } = currentValue;

    return (
      <div className={`tab-switch-container ${className || ''}`}>
        {items.map((item) => (
          <div key={item.id} className="tab-list-item">
            <div
              className={`tab-list-value ${isEqual(item.value, value) ? 'tab-selected-value' : ''}`}
              aria-label="Select value"
              onClick={() => { this.handleChangeValue(item); }}
              tabIndex="0"
              onKeyPress={() => { this.handleChangeValue(item); }}
              role="button">
              {t(item.label)}
            </div>
          </div>
        ))}
      </div>
    );
  }
}
  
export default TabSwitch;

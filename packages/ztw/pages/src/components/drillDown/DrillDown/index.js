import Select from 'react-select';
import React from 'react';
import PropTypes from 'prop-types';
import { noop, isArray } from 'utils/lodash';
import DrillDownPlaceholder from '../DrillDownPlaceholder';

class DrillDown extends React.Component {
  static propTypes = {
    setValue: PropTypes.func,
    data: PropTypes.arrayOf(PropTypes.shape({
      value: PropTypes.string,
      label: PropTypes.string,
    })),
    icon: PropTypes.shape(),
    placeholder: PropTypes.string,
    isMulti: PropTypes.bool,
  };

  static defaultProps = {
    setValue: noop,
    data: null,
    icon: null,
    placeholder: null,
    isMulti: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      selectedOption: null,
    };
  }

  handleChange = (selectedOption) => {
    const { setValue } = this.props;
    const selectedId = isArray(selectedOption)
      ? selectedOption.map((item) => item.value) : [selectedOption.value];
    setValue(selectedId);
    this.setState({ selectedOption });
  };

  render() {
    const { selectedOption } = this.state;
    const {
      data, icon, placeholder, isMulti,
    } = this.props;
    return (
      <div className="select-item">
        <Select
          isMulti={isMulti}
          value={selectedOption}
          onChange={this.handleChange}
          options={data}
          placeholder={<DrillDownPlaceholder icon={icon} placeholder={placeholder} />} />
      </div>
    );
  }
}

export default DrillDown;

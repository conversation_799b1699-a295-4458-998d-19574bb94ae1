// @flow

import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment-timezone';
import { Trans, withTranslation } from 'react-i18next';

function DateSubHeader(props) {
  const {
    title, startDate, endDate, startDateFormat, endDateFormat,
  } = props;
  const date = startDate
    ? `${moment(startDate).format(startDateFormat)} - ${moment(endDate).format(endDateFormat)}`
    : null;
  return (
    <div className="date-sub-header">
      <h2><Trans>{title}</Trans></h2>
      <p>{date}</p>
      <div />
    </div>
  );
}

DateSubHeader.propTypes = {
  title: PropTypes.string,
  startDate: PropTypes.number,
  endDate: PropTypes.number,
  startDateFormat: PropTypes.string,
  endDateFormat: PropTypes.string,
};

DateSubHeader.defaultProps = {
  title: null,
  startDate: null,
  endDate: null,
  startDateFormat: 'MMMM DD, h:mm A',
  endDateFormat: 'h:mm A',
};

export default withTranslation()(DateSubHeader);

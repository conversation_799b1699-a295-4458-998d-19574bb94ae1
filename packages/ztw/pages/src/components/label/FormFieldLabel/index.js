/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable jsx-a11y/label-has-associated-control */
// @flow

import React from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';
import { withTranslation } from 'react-i18next';
import {
  setToolTipPosition,
} from 'utils/helpers';

const handleHover = (event) => {
  setToolTipPosition(event, '.modal-body');
};

function FormFieldLabel(props) {
  const {
    styleClass, text, tooltip, error, id, place,
  } = props;
  
  return (
    <label className="form-field-label-wrapper">
      <span className={`form-field-label paragraph-2-strong ${styleClass}`} data-tip data-for={id || text} onMouseEnter={handleHover}>
        {text}
        <ReactTooltip
          id={id || text}
          clickable
          place={place}
          type="light"
          offset={{ top: -10 }}
          effect="solid"
          // disable={(tooltip.length === 0)}
          disable={(tooltip.length === 0 && (!error || error?.length === 0))}
          delayHide={30}
          border
          borderColor="#939393"
          className="form-field-tooltip-container">
          <div id="tooltip-top" className="tooltip-top tooltip-top-text">
            {error && <div className="help-error-text has-info-text">{error}</div>}
            <div className="help-text">{tooltip}</div>
          </div>
        </ReactTooltip>
      </span>
    </label>
  );
}

FormFieldLabel.propTypes = {
  styleClass: PropTypes.string,
  id: PropTypes.string,
  text: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object,
  ]),
  place: PropTypes.string,
  error: PropTypes.string,
  tooltip: PropTypes.oneOfType([PropTypes.string, PropTypes.shape({})]),
};

FormFieldLabel.defaultProps = {
  styleClass: '',
  id: null,
  text: null,
  place: 'top',
  error: null,
  tooltip: '',
};

export default withTranslation()(FormFieldLabel);

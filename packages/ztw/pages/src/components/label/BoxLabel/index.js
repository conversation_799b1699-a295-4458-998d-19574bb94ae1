// @flow

import React from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';

function BoxLabel(props) {
  const { text, tooltip } = props;

  return (
    <h2 className="box-label" data-tip data-for={text}>
      {text}
      {tooltip && tooltip !== '' && (
        <ReactTooltip
          id={text}
          place="top"
          type="light"
          border
          borderColor="#939393"
          className="form-field-tooltip-container">
          {tooltip}
        </ReactTooltip>
      )}
    </h2>
  );
}

BoxLabel.propTypes = {
  text: PropTypes.string,
  tooltip: PropTypes.string,
};

BoxLabel.defaultProps = {
  text: '',
  tooltip: '',
};

export default BoxLabel;

// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { Trans, withTranslation } from 'react-i18next';

function SubHeader(props) {
  const { title } = props;

  return (
    <h2 className="sub-header"><Trans>{title}</Trans></h2>
  );
}

SubHeader.propTypes = {
  title: PropTypes.string,
};

SubHeader.defaultProps = {
  title: '',
};

export default withTranslation()(SubHeader);

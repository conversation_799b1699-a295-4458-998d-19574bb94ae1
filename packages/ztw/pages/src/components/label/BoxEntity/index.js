import React from 'react';
import PropTypes from 'prop-types';
import { Trans, withTranslation } from 'react-i18next';
import ReactTooltip from 'react-tooltip';

function BoxEntity(props) {
  const { text, tooltip } = props;
  return (
    <div>
      <h3 data-tip={tooltip}>
        <Trans>{text}</Trans>
      </h3>
      <ReactTooltip />
    </div>
  );
}

BoxEntity.propTypes = {
  text: PropTypes.string,
  tooltip: PropTypes.string,
};

BoxEntity.defaultProps = {
  text: '',
  tooltip: '',
};

export default withTranslation()(BoxEntity);

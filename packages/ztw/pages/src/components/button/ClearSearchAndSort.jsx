import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEraser } from '@fortawesome/pro-solid-svg-icons';

function ClearSearchAndSort(props) {
  const {
    label,
    onActionCb,
    tabIndex,
    tooltip,
    isVisible,
  } = props;

  return (
    <div
      className={`download-button ${isVisible ? '' : 'hidden'}`}
      tabIndex={tabIndex}
      onKeyPress={() => onActionCb()}
      onClick={() => onActionCb()}
      role="button">
      <span
        className="icon"
        data-tip={tooltip}
        data-for="TunnelLogsReactTooltip">
        <FontAwesomeIcon icon={faEraser} />
      </span>
      {label}
    </div>
  );
}

ClearSearchAndSort.propTypes = {
  label: PropTypes.string,
  onActionCb: PropTypes.func,
  tabIndex: PropTypes.number,
  tooltip: PropTypes.string,
  isVisible: PropTypes.bool,
};

ClearSearchAndSort.defaultProps = {
  label: '',
  onActionCb: null,
  tabIndex: 0,
  tooltip: '',
  isVisible: false,
};

export default ClearSearchAndSort;

import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPrint } from '@fortawesome/pro-solid-svg-icons';

function Print(props) {
  const {
    label,
    onActionCb,
    tabIndex,
  } = props;
  return (
    <div
      className="download-button"
      tabIndex={tabIndex}
      onKeyPress={() => onActionCb()}
      onClick={() => onActionCb()}
      role="button">
      <span className="icon">
        <FontAwesomeIcon icon={faPrint} />
      </span>
      {label}
    </div>
  );
}

Print.propTypes = {
  label: PropTypes.string,
  onActionCb: PropTypes.func,
  tabIndex: PropTypes.number,
};

Print.defaultProps = {
  label: '',
  onActionCb: null,
  tabIndex: 0,
};

export default Print;

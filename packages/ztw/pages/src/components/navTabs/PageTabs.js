import React from 'react';
import { change, getFormValues } from 'redux-form';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { isEmpty } from 'utils/lodash';
import PropTypes from 'prop-types';
import { NavLink } from 'react-router-dom';

function PageTabs({ meta, id }) {
  const { form } = meta;
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const {
    tabConfiguration, pageTabs, isPageTabFullwidth,
  } = useSelector((state) => getFormValues(form)(state)) || {};

  if (isEmpty(tabConfiguration)) return <></>;

  return (
    <div className="tabs">
      <div className="tabs-items">
        {tabConfiguration.map((item) => (
          <div key={`${item.value}-tab`} id={`${item.value}-tab`} className={`${isPageTabFullwidth ? 'max-width' : ''}`}>
            <NavLink
              activeclassname="active-menu-item"
              onClick={() => {
                dispatch(change(form, id, item.value));
                if (item.handleClick()) { dispatch(item.handleClick()); }
              }}
              to={item.to}>
              <p className={`${pageTabs === item.value ? 'tabPactive' : 'tabP'} ${isPageTabFullwidth ? 'max-width' : ''}`}>{t(item.title)}</p>
            </NavLink>
            <div className={`highlighter ${pageTabs === item.value ? 'active' : ''}`} />
          </div>
        ))}
      </div>
      <div className="tabs-highlrighte" />
    </div>
  );
}

PageTabs.propTypes = {
  meta: PropTypes.shape({}),
  id: PropTypes.string,
};

PageTabs.defaultProps = {
  meta: {},
  id: '',
};
export default PageTabs;

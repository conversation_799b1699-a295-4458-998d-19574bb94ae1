import React from 'react';
import { change, getFormValues } from 'redux-form';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { isEmpty } from 'utils/lodash';
import PropTypes from 'prop-types';
import { NavLink } from 'react-router-dom';

function FolderTabs({
  meta, id, folderTabs, disable,
}) {
  const { form } = meta;
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const {
    folderConfiguration, isPageTabFullwidth,
  } = useSelector((state) => getFormValues(form)(state)) || {};

  if (isEmpty(folderConfiguration)) return <></>;

  return (
    <div className="folder-tabs tabs">
      <div className="tabs-items">
        {folderConfiguration.map((item) => (
          <div key={`${item.value}-tab`} id={`${item.value}-tab`} className={`${isPageTabFullwidth ? 'max-width' : ''}`}>
            <NavLink
              activeclassname="active-menu-item"
              onClick={() => {
                dispatch(change(form, id, item.value));
                if (item.handleClick()) { dispatch(item.handleClick()); }
              }}
              to={item.to}>
              <p className={`${folderTabs === item.value ? 'tabPactive' : 'tabP'} ${disable ? 'disabled-input' : ''} ${isPageTabFullwidth ? 'max-width' : ''}`}>{t(item.title)}</p>
            </NavLink>
            <div className={`highlighter ${folderTabs === item.value ? 'active' : ''}`} />
          </div>
        ))}
      </div>
      <div className="tabs-highlrighte" />
    </div>
  );
}

FolderTabs.propTypes = {
  disable: PropTypes.bool,
  folderTabs: PropTypes.string,
  id: PropTypes.string,
  meta: PropTypes.shape({}),
};

FolderTabs.defaultProps = {
  disable: false,
  folderTabs: '',
  id: '',
  meta: {},
};
export default FolderTabs;

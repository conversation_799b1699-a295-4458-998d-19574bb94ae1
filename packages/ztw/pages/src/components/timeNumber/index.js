// @flow

import React from 'react';
import PropTypes from 'prop-types';

function TimeNumber(props) {
  const { number, timeUnit } = props;

  return (
    <>
      <span className="time-number">{number}</span>
      <span className="time-unit-text">{timeUnit}</span>
    </>
  );
}

TimeNumber.propTypes = {
  number: PropTypes.number,
  timeUnit: PropTypes.string,
};

TimeNumber.defaultProps = {
  number: null,
  timeUnit: null,
};

export default TimeNumber;

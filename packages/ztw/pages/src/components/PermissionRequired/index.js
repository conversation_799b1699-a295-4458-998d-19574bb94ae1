// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { isEmpty } from 'utils/lodash';
import Loading from 'components/spinner/Loading';
import * as loginSelectors from 'ducks/login/selectors';

function PermissionRequired({ accessPermissions }) {
  const { t } = useTranslation();
  if (isEmpty(accessPermissions)) return <Loading loading />;

  return (
    <div className="page-main-content">
      <div className="permission-required">
        <div className="permission-required-splash">
          <div className="permission-required-splash-title">{t('PERMISSION_REQUIRED')}</div>
          <div className="permission-required-splash-message">{t('PERMISSION_REQUIRED_MESSAGE')}</div>
        </div>
      </div>
    </div>
  );
}
PermissionRequired.propTypes = {
  accessPermissions: PropTypes.shape({}),
};

PermissionRequired.defaultProps = {
  accessPermissions: {},
};

export default (PermissionRequired);
export { PermissionRequired };

import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle, faExclamationTriangle } from '@fortawesome/pro-solid-svg-icons';

export function ApplianceNotification(props) {
  const {
    info,
    isAlert,
    styleClass,
  } = props;

  return (
    <div className={`info-box-notification-box ${isAlert ? 'alert' : ''} ${styleClass}`}>
      <div className="info-box-notification-title">
        <div className="left">
          {!isAlert && <FontAwesomeIcon icon={faInfoCircle} className="info-circle" />}
          {isAlert && <FontAwesomeIcon icon={faExclamationTriangle} className="info-circle" />}
          {info}
        </div>
        <div
          className="right"
          role="button"
          aria-label="Close"
          tabIndex="0"
          onKeyPress={null}
          onClick={null}>
        </div>
      </div>
    </div>
  );
}

ApplianceNotification.propTypes = {
  info: PropTypes.string,
  isAlert: PropTypes.bool,
  styleClass: PropTypes.string,
};

ApplianceNotification.defaultProps = {
  info: '',
  isAlert: false,
  styleClass: '',
};

export default ApplianceNotification;

import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch } from '@fortawesome/pro-solid-svg-icons/faSearch';

function DropdownSearch({ showSearch, value, onChange }) {
  return (
    <div>
      {showSearch && (
        <div className="dropdown-search-input">
          <i className="dropdown-search-icon">
            <FontAwesomeIcon icon={faSearch} size="sm" />
          </i>
          <input
            className="dropdown-search-inputer"
            type="text"
            placeholder="Search"
            value={value}
            onChange={(e) => onChange(e.target.value)} />
        </div>
      )}
    </div>
  );
}

DropdownSearch.propTypes = {
  showSearch: PropTypes.bool.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
};

export default DropdownSearch;

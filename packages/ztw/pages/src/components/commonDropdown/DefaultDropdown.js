/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, {
  useEffect, useMemo, useState, useRef,
} from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch } from '@fortawesome/pro-solid-svg-icons/faSearch';
import { useTranslation } from 'react-i18next';
import Loader from '../spinner/Loading';

function DefaultDropdown({
  options,
  onChange,
  selected,
  onCancel,
  loading,
  showSearch,
  initialValues,
}) {
  const [searchQuery, setSearchQuery] = useState('');
  const [localSelected, setLocalSelected] = useState(selected || []);
  const dropdownRef = useRef();
  const { t } = useTranslation();

  useEffect(() => {
    // eslint-disable-next-line no-use-before-define
    const isAllSelected = isSelectedAll || localSelected.length === 0;
    const listener = (e) => {
      if (!dropdownRef.current.contains(e.target)) {
        onChange(localSelected, options, isAllSelected);
      }
    };

    document.addEventListener('click', listener);
    // document.addEventListener('focusin', listener);
    return () => {
      document.removeEventListener('click', listener);
      // document.removeEventListener('focusin', listener);
    };
  }, [localSelected]);

  const optionsWithSelection = useMemo(() => {
    return options.map((x) => ({
      ...x,
      selected: localSelected.includes(x.value),
    }));
  }, [options, localSelected]);

  const filteredOptionsWithSelection = useMemo(() => {
    // Don't filter api response locally
    return optionsWithSelection?.filter(
      (x) => x?.label?.toLowerCase().includes(searchQuery?.toLowerCase()),
    );
  }, [searchQuery, optionsWithSelection]);

  const handleSearchQuery = (e) => {
    setSearchQuery(e.target.value);
  };

  // eslint-disable-next-line no-shadow
  const handleSelectAll = (selected) => {
    if (selected) {
      setLocalSelected(
        options.filter((obj) => !obj.disabled).map((x) => x.value),
      );
    } else {
      setLocalSelected([]);
    }
  };

  const isSelectedAll = useMemo(
    () => optionsWithSelection.filter((x) => x.selected).length
        === options.filter((obj) => !obj.disabled).length
      && options.filter((obj) => !obj.disabled).length > 0,
    [options, optionsWithSelection],
  );

  useEffect(() => {
    if (document.getElementById('chk-all')) {
      if (isSelectedAll || localSelected.length === 0) {
        document.getElementById('chk-all').indeterminate = false;
      } else {
        document.getElementById('chk-all').indeterminate = true;
      }
    }
  }, [localSelected]);

  const handleChange = (value, checked) => {
    if (checked) {
      setLocalSelected((prevValue) => Array.from(new Set([...prevValue, value])));
    } else {
      setLocalSelected((prevValue) => prevValue.filter((x) => x !== value));
    }
  };

  const renderSelectedNoMatchingContent = () => {
    if (optionsWithSelection.filter((x) => x.selected).length) {
      return <p className="no-matching-options">No matching items found</p>;
    }
    return null;
  };

  const renderMultiSelectDropdown = () => {
    return (
      <div className="display-flex">
        <div className="default-filter-dropdown-all">
          <div className="select-all-container">
            <div className="option-row justify-content-between py-3">
              <span className="all-values-sub-title">{t('ALL_VALUES')}</span>
              <span
                className="cursor-pointer"
                onClick={() => handleSelectAll(true)}>
                {`${t('SELECT_ALL')} (${
                  optionsWithSelection.filter((option) => !option.disabled)
                    .length
                })`}
              </span>
            </div>
          </div>
          <div className="options-container">
            {filteredOptionsWithSelection.length > 0 ? (
              filteredOptionsWithSelection.map((option) => (
                <Checkbox
                  key={option.value}
                  value={option.value}
                  disabled={option.disabled}
                  label={option.label}
                  selected={option.selected}
                  onChange={(val) => handleChange(option.value, val)}
                  type="all" />
              ))
            ) : (
              <p className="no-matching-options">No matching items found</p>
            )}
          </div>
        </div>
        <div className="default-filter-dropdown-selected">
          <div className="select-all-container">
            <div className="option-row justify-content-between py-3">
              <span className="selected-sub-title">{t('SELECTED')}</span>
              <span
                className="cursor-pointer"
                onClick={() => handleSelectAll(false)}>
                {`${t('CLEAR_ALL')} (${
                  optionsWithSelection.filter((x) => x.selected).length
                })`}
              </span>
            </div>
          </div>
          <div className="options-container" style={{ height: '351px' }}>
            {filteredOptionsWithSelection.filter((x) => x.selected).length > 0
              ? filteredOptionsWithSelection
                .filter((x) => x.selected)
                .map((option) => (
                  <div key={option.value}>
                    <Checkbox
                      key={option.value}
                      value={option.value}
                      disabled={option.disabled}
                      label={option.label}
                      selected={option.selected}
                      onChange={(val) => handleChange(option.value, val)}
                      type="selected" />
                  </div>
                ))
              : renderSelectedNoMatchingContent()}
          </div>
          <div className="default-filter-footer justify-content-center">
            <button type="button" className="submit" onClick={() => onCancel(localSelected.map((x) => ({ label: x, value: x })))}>
              {t('DONE')}
            </button>
            <button type="button" className="cancel" onClick={() => onCancel(initialValues)}>
              {t('CANCEL')}
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderNoOptions = () => {
    return (
      <div className="display-flex">
        <span style={{ color: 'var(--semantic-color-content-interactive-primary-default)', fontWeight: '600' }}>No Options</span>
      </div>
    );
  };

  const renderDropdown = () => {
    if (options.length > 0) {
      return renderMultiSelectDropdown();
    }
    return renderNoOptions();
  };

  return (
    <div
      ref={dropdownRef}
      className={`default-new-filter-dropdown-Wrapper ${
        // eslint-disable-next-line no-nested-ternary
        loading
          ? null
          : options.length > 0
            ? 'wrapper-height'
            : 'width-no-options'
      }`}>
      {showSearch && (
        <div className="search-input">
          <i className="search-icons">
            <FontAwesomeIcon icon={faSearch} />
          </i>
          <input
            className="search-inputer"
            type="text"
            placeholder="Search"
            value={searchQuery}
            onChange={handleSearchQuery} />
        </div>
      )}
      <div
        className={`default-filter-dropdown-container ${
          options.length > 0 ? 'loading-class' : 'no-options-wrapper'
        }`}>
        {loading ? <Loader isOverlay={false} /> : renderDropdown()}
      </div>
      {/* <CustomTooltip place="right" type="light" multiline /> */}
    </div>
  );
}

DefaultDropdown.propTypes = {
  /**
   * Array of value/label pairs that represent the options
   */
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    }),
  ).isRequired,
  initialValues: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    }),
  ),
  /**
   * Array of selected values (without label)
   */
  selected: PropTypes.arrayOf(PropTypes.string).isRequired,
  /**
   * onChange is called with the selected values whenever a selection
   * changes. You need to pass the values back to selected
   */
  onChange: PropTypes.func.isRequired,
  /**
   * onCancel is called whenever the cancel button is clicked. Use this to hide the
   * dropdown
   */
  onCancel: PropTypes.func.isRequired,
  showSearch: PropTypes.bool,
  /**
   * This will be called when Show more button is clicked
   * (pageNum, searchQuery) => void
   */
  loading: PropTypes.bool,
};

DefaultDropdown.defaultProps = {
  loading: false,
  showSearch: true,
};

function Checkbox({
  // eslint-disable-next-line react/prop-types
  value, label, selected, onChange, disabled, type,
}) {
  function css(element, property) {
    return window.getComputedStyle(element, null).getPropertyValue(property);
  }

  function textWidth(text, fontProp) {
    const tag = document.createElement('div');
    tag.style.position = 'absolute';
    tag.style.left = '-99in';
    tag.style.whiteSpace = 'nowrap';
    tag.style.font = fontProp;
    tag.innerHTML = text;

    document.body.appendChild(tag);
    const result = tag.clientWidth;
    document.body.removeChild(tag);
    return result;
  }

  const showTooltip = document.getElementById(`lbl-${value}-${type}`) !== null
    ? textWidth(
      label,
      css(document.getElementById(`lbl-${value}-${type}`), 'font-family'),
    ) > 209
    : false;

  return (
    <div className="option-row" key={value}>
      <input
        type="checkbox"
        className="cursor-pointer"
        id={`chk-${value}`}
        checked={selected}
        onChange={() => onChange(!selected)}
        disabled={disabled} />
      <label
        id={`lbl-${value}-${type}`}
        htmlFor={`chk-${value}`}
        data-tip={showTooltip ? label : ''}
        className={`cursor-pointer ${
          selected ? 'option-text' : disabled && 'text-muted'
        }`}>
        {label}
      </label>
    </div>
  );
}

export default DefaultDropdown;

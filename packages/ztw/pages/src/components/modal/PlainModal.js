import React from 'react';
import ReactModal from 'react-modal';
import PropTypes from 'prop-types';

ReactModal.setAppElement('body');

function PlainModal({
  isOpen, children, styleClass, contentClass,
}) {
  return (
    <ReactModal
      isOpen={isOpen}
      contentLabel="Example Modal"
      className={`modal-plain-body ${styleClass}`}
      portalClassName="ec-root-page"
      closeTimeoutMS={900}
      overlayClassName="modal-overlay">
      <div className={`modal-content ${contentClass}`}>
        {children}
      </div>
    </ReactModal>
  );
}

PlainModal.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.element,
    PropTypes.arrayOf(PropTypes.element),
  ]),
  isOpen: PropTypes.bool,
  styleClass: PropTypes.string,
  contentClass: PropTypes.string,
};

PlainModal.defaultProps = {
  children: null,
  isOpen: false,
  styleClass: '',
  contentClass: '',
};

export default PlainModal;

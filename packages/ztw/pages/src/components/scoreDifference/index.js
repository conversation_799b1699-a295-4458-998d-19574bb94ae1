// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronCircleUp, faChevronCircleDown } from '@fortawesome/pro-solid-svg-icons';

function ScoreDifference(props) {
  const { number } = props;
  const isPositive = number >= 0;

  return (
    <span className={`score-difference ${isPositive ? 'positive' : 'negative'}`}>
      {`${Math.abs(number)}%`}
      <FontAwesomeIcon icon={isPositive ? faChevronCircleUp : faChevronCircleDown} />
    </span>
  );
}

ScoreDifference.propTypes = {
  number: PropTypes.number,
};

ScoreDifference.defaultProps = {
  number: 0,
};

export default ScoreDifference;

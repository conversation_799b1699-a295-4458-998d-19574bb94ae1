import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

export function CheckboxInput(props = {}) {
  const {
    label,
    id,
    input,
    className,
    disable,
    hidden,
    t,
  } = props;
  const classNames = disable ? 'container disabled' : 'container';
  return (
    <div className={`checkbox-container ${className} ${hidden ? 'hidden' : ''}`}>
      <label
        className={classNames}
        htmlFor={id}>
        <input
          id={id}
          type="checkbox"
          {...input}
          disabled={disable ? 'disabled' : ''} />
        <span className="checkmark">
          <span />
        </span>
        <span className="label-text">{t(label)}</span>
      </label>
    </div>
  );
}

CheckboxInput.propTypes = {
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  label: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  className: PropTypes.string,
  input: PropTypes.shape({
    onChange: PropTypes.func,
    value: PropTypes.string,
    name: PropTypes.string,
    checked: PropTypes.bool,
  }),
  disable: PropTypes.bool,
  hidden: PropTypes.bool,
  t: PropTypes.func,
};

CheckboxInput.defaultProps = {
  id: null,
  label: null,
  input: {},
  className: '',
  disable: false,
  hidden: false,
  t: (str) => str,
};

export default withTranslation()(CheckboxInput);

import { createAction, loading } from 'ducks/generics';
import { genericInterface } from 'utils/http';
import { isEmpty, omit, get } from 'utils/lodash';
import { notify, notifyError } from 'ducks/notification';
import { checkActivation } from 'ducks/activation';
import * as constants from './constants';
import * as selectors from './selectors';
import actionTypes from './action-types';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};
export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundError = createAction(actionTypes.DATA_LOAD_ERROR);
const dataChanged = createAction(actionTypes.DATA_CHANGED);
const boundDataLoadSuccess = createAction(actionTypes.DATA_LOAD_SUCCESS);
const boundModalLoading = createAction(actionTypes.MODAL_LOADING);

export const loader = () => async (dispatch) => {
  dispatch(
    boundLoading({
      // [constants.HISTORY]: [],
      ...constants.DEFAULTS,
      loading: true,
    }),
  );
  // dispatch(reset('dnsInsightsFiltersForm'));
  // Prev Day;
  try {
    const permissionsApi = await genericInterface(constants.PERMISSIONS_API_ENDPOINT).read();
    const { data: { id: roleId } = {} } = permissionsApi || {};
    const roleManagementApi = await genericInterface(constants.ROLEMANAGEMENT_API_QUERY).read();
    let { data } = roleManagementApi || [];
    if (roleId) {
      data = data.map((x) => (x.id === roleId ? { ...x, isNonEditable: true } : x));
    }
    dispatch(boundDataLoadSuccess({
      [constants.DATA_TABLE]: data ? data.map((x, idx) => ({ ...x, idx: idx + 1 })) : [],
      loading: false,
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_LOADING_ROLE_MANAGEMENT'));
    else dispatch(notifyError('errorMsg'));
  }
};

export const getRoleManagementData = () => (dispatch) => {
  dispatch(boundDataLoadSuccess({
    [constants.DATA]: constants.DATA,
    loading: false,
  }));
};

// eslint-disable-next-line max-len
export const toggleAddForm = (toggle, appData = constants.DEFAULT_FORM_DATA, modalTitle) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SHOW_FORM]: toggle,
    [constants.APP_DATA]: appData,
    [constants.MODAL_TITLE]: modalTitle,
    [constants.ADD_ROLE]: true,
    [constants.VIEW_ONLY]: false,
  }));
};

// eslint-disable-next-line max-len
export const toggleEditForm = (toggle, appData = constants.DEFAULT_FORM_DATA, modalTitle) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SHOW_FORM]: toggle,
    [constants.APP_DATA]: appData,
    [constants.MODAL_TITLE]: modalTitle,
    [constants.ADD_ROLE]: false,
    [constants.VIEW_ONLY]: false,
  }));
};

// eslint-disable-next-line max-len
export const toggleViewForm = (toggle, appData = constants.DEFAULT_FORM_DATA, modalTitle) => (dispatch) => {
  dispatch(dataChanged({
    [constants.SHOW_FORM]: toggle,
    [constants.APP_DATA]: appData,
    [constants.MODAL_TITLE]: modalTitle,
    [constants.ADD_ROLE]: false,
    [constants.VIEW_ONLY]: true,
  }));
};

// eslint-disable-next-line max-len
export const toggleClose = () => (dispatch) => {
  dispatch(dataChanged({
    [constants.SHOW_FORM]: false,
  }));
};

export const toggleDeleteForm = (toggle, data) => (dispatch) => {
  dispatch(toggleClose());
  dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
  dispatch(dataChanged({
    [constants.SHOW_DELETE_FORM]: toggle,
    [constants.SELECTED_ROW_ID]: data ? data.id.toString() : null,
    loading: false,
  }));
};

const beforeSave = (newRole, appData, formData) => (newRole ? {
  rank: 7,
  logsLimit: 'UNRESTRICTED',
  name: formData.name,
  policyAccess: 'NONE',
  dashboardAccess: 'NONE',
  reportAccess: 'NONE',
  analysisAccess: 'NONE',
  usernameAccess: 'NONE',
  adminAcctAccess: 'READ_WRITE',
  isAuditor: false,
  isNonEditable: false,
  roleType: 'EDGE_CONNECTOR_ADMIN',
  featurePermissions: omit(formData, 'name', 'EDGE_CONNECTOR_POLICY_CONFIGURATION'),
}
  : {
    ...omit(appData, 'permissions'),
    featurePermissions: omit(formData, 'name', 'EDGE_CONNECTOR_POLICY_CONFIGURATION'),
  });

export const saveForm = (newRole) => async (dispatch, getState) => {
  dispatch(boundLoading({ loading: true }));
  const state = getState();
  const appData = selectors.appDataSelector(state);
  const { rbacFragmentSection: formData } = selectors.formValuesSelector(state);
  if (newRole && isEmpty(formData.name)) {
    dispatch(notifyError('SAVE_ERROR_MESSAGE'));
    dispatch(boundLoading({ loading: false }));

    return;
  }

  const roleManagementValues = beforeSave(newRole, appData, formData);
  const saveRoleManagement = genericInterface(constants.ROLEMANAGEMENT_API_ENDPOINT);
  const apiEndPoint = newRole ? saveRoleManagement.create : saveRoleManagement.update;

  try {
    await apiEndPoint(roleManagementValues, {});
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundLoading({ loading: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_SAVING_ROLE_MANAGEMENT'));
    else dispatch(notifyError(errorMsg));

    return;
  }

  dispatch(toggleClose());
  dispatch(notify('SUCCESSFULLY_SAVED'));
  dispatch(loader());
  dispatch(checkActivation());
};

export const deleteForm = () => async (dispatch, getState) => {
  dispatch(boundLoading({}));
  const state = getState();
  const rowId = selectors.selectedRowIDSelector(state);

  const apiRoleManagement = genericInterface(`${constants.ROLEMANAGEMENT_API_ENDPOINT}/${rowId}`);
  const apiEndPoint = apiRoleManagement.del;

  try {
    await apiEndPoint(rowId, {});
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(boundModalLoading({ [constants.MODAL_LOADING]: false }));
    dispatch(boundError(errorMsg));
    if (isEmpty(errorMsg)) dispatch(notifyError('ERROR_DELETING_ROLE_MANAGEMENT'));
    else dispatch(notifyError(errorMsg));

    return;
  }

  dispatch(toggleClose());
  dispatch(toggleDeleteForm(false));
  dispatch(notify('SUCCESSFULLY_DELETED'));
  dispatch(loader());
  dispatch(checkActivation());
};

export const updateMenu = (columns) => (dispatch) => {
  dispatch(
    dataChanged({
      [constants.COLUMNS]: [],
    }),
  );
  dispatch(
    dataChanged({
      [constants.COLUMNS]: columns,
    }),
  );
};

/* SEARCH */
export const handleOnSearchFilter = (event, searchText) => (dispatch) => {
  if (event.keyCode && event.keyCode === 13 && searchText.length > 0) {
    return dispatch(dataChanged({
      [constants.SEARCH_DATA]: searchText,
    }));
  } if (searchText.length === 0) {
    return dispatch(dataChanged({
      [constants.SEARCH_DATA]: '',
    }));
  }

  return null;
};

// export const onEdit = ({ row, rowMeta }) => (dispatch) => {
//   const clonedRow = clonedeep(row);

//   dispatch(dataChanged({
//     [constants.ORIGINAL]: row,
//     [constants.EDIT_ROW]: clonedRow,
//     [constants.EDIT_ROW_META]: rowMeta,
//     [constants.TOGGLE_FORM]: true,
//     [constants.ADD_MODAL]: false,
//   }));
// };

/* Users Table methods */
export const editRow = (rowMeta) => (dispatch, getState) => {
  const state = getState();
  const rows = selectors.usersRowSelector(state);
  const { rowIdx } = rowMeta;
  const row = rows[rowIdx];
  dispatch(dataChanged({
    formErrors: {},
  }));
  // we're invoking function is formMultiSelect duck
  // dispatch(onEdit({ row, rowMeta }));
};

// eslint-disable-next-line max-len
export const handleDashboardChange = (t, appData = constants.DEFAULT_FORM_DATA) => (dispatch) => {
  dispatch(dataChanged({
    [constants.DASHBOARD]: appData,
  }));
};

// eslint-disable-next-line max-len
export const handleCloudConnectorProvisioningChange = (t, appData = constants.DEFAULT_FORM_DATA) => (dispatch) => {
  dispatch(dataChanged({
    [constants.CLOUD_CONNECTOR_PROVISIONING]: appData,
  }));
};

// eslint-disable-next-line max-len
export const handlePolicyConfigurationChange = (t, appData = constants.DEFAULT_FORM_DATA) => (dispatch) => {
  dispatch(dataChanged({
    [constants.POLICY_CONFIGURATION]: appData,
  }));
};

// eslint-disable-next-line max-len
export const handleAdminManagementChange = (t, appData = constants.DEFAULT_FORM_DATA) => (dispatch) => {
  dispatch(dataChanged({
    [constants.ADMIN_MANAGEMENT]: appData,
  }));
};

// eslint-disable-next-line max-len
export const handleLocationManagementChange = (t, appData = constants.DEFAULT_FORM_DATA) => (dispatch) => {
  dispatch(dataChanged({
    [constants.LOCATION_MANAGEMENT]: appData,
  }));
};

// eslint-disable-next-line max-len
export const handleTrafficForwardingDNSChange = (t, appData = constants.DEFAULT_FORM_DATA) => (dispatch) => {
  dispatch(dataChanged({
    [constants.TRAFFIC_FORWARDING_DNS]: appData,
  }));
};

// eslint-disable-next-line max-len
export const handleApiKeyManagementChange = (t, appData = constants.DEFAULT_FORM_DATA) => (dispatch) => {
  dispatch(dataChanged({
    [constants.API_KEY_MANAGEMENT]: appData,
  }));
};

// eslint-disable-next-line max-len
export const handleRemoteAssistanceManagementChange = (t, appData = constants.DEFAULT_FORM_DATA) => (dispatch) => {
  dispatch(dataChanged({
    [constants.REMOTE_ASSISTANCE_MANAGEMENT]: appData,
  }));
};

// eslint-disable-next-line max-len
export const handleNssLoggingChange = (t, appData = constants.DEFAULT_FORM_DATA) => (dispatch) => {
  dispatch(dataChanged({
    [constants.REMOTE_ASSISTANCE_MANAGEMENT]: appData,
  }));
};

// eslint-disable-next-line max-len
export const handlePublicCloudChange = (t, appData = constants.DEFAULT_FORM_DATA) => (dispatch) => {
  dispatch(dataChanged({
    [constants.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT]: appData,
  }));
};

// eslint-disable-next-line max-len
export const handleTemplateChange = (t, appData = constants.DEFAULT_FORM_DATA) => (dispatch) => {
  dispatch(dataChanged({
    [constants.TEMPLATE_MANAGEMENT]: appData,
  }));
};

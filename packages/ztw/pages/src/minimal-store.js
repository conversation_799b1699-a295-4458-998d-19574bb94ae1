import {
  createStore, combineReducers, compose, applyMiddleware,
} from 'redux';
import createDebounce from 'redux-debounced';
import promise from 'redux-promise';
import thunkMiddleware from 'redux-thunk';
import { reducer as formReducer } from 'redux-form';

// Create minimal reducers without importing the full ducks
const createMinimalReducer = () => (state = {}, action) => {
  switch (action.type) {
    default:
      return state;
  }
};

// Minimal set of reducers for library mode
const minimalReducers = {
  notification: createMinimalReducer('notification'),
  login: createMinimalReducer('login'),
  profile: createMinimalReducer('profile'),
  form: formReducer,
};

export function configureMinimalStore(initialState = {}) {
  const reducer = combineReducers(minimalReducers);

  const rootReducer = (state, action) => {
    // Simple root reducer for minimal store
    return reducer(state, action);
  };

  const middleware = [
    createDebounce(),
    promise,
    thunkMiddleware,
  ];

  // eslint-disable-next-line no-underscore-dangle
  const composeEnhancers = window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose;

  const store = createStore(
    rootReducer,
    initialState,
    composeEnhancers(applyMiddleware(...middleware)),
  );

  return store;
}

export default configureMinimalStore;

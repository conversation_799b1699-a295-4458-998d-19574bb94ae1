'use client';
import React from 'react';
import PropTypes from 'prop-types';
import { Provider } from 'react-redux';
import { configureMinimalStore } from './../minimal-store.js';

export const minimalStore = configureMinimalStore();

function MinimalAppProvider({ children }) {
  return (
    <>
      <div className="ec-root-page">
        <Provider store={minimalStore}>
          {children}
        </Provider>
      </div>
    </>
  );
}

MinimalAppProvider.propTypes = {
  children: PropTypes.any,
};

export default MinimalAppProvider;

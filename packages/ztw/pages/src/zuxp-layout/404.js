import React from 'react';
import { useTranslation } from 'react-i18next';
// import { useSelector } from 'react-redux';
import { ReactComponent as NotFoundIcon } from 'images/not_found_icon.svg';
// import { isAuthenticatedSelector } from 'ducks/login/selectors';

function Page404() {
  const { t } = useTranslation();
  // const isAuthenticated = useSelector(isAuthenticatedSelector);

  return (
    <>
      <NotFoundIcon />
      {t('PAGE_NOT_FOUND')}
    </>
  );
}

export default Page404;

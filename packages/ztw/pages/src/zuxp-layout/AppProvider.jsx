'use client';
import React from 'react';
import PropTypes from 'prop-types';
import { Provider } from 'react-redux';
import { configureStore } from './../store.js';
import AppLayout from './AppLayout';

export const store = configureStore();

function AppProvider({ children }) {
  // return null;
  return (
    <>
      <div className="ec-root-page">
        <Provider store={store}>
          <AppLayout>{children}</AppLayout>
        </Provider>
      </div>
      {/* <div id={PORTAL_ROOT_ID} /> */}
    </>
  );
}

AppProvider.propTypes = {
  children: PropTypes.any,
};
export default AppProvider;

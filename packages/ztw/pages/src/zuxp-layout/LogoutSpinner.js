// @flow

import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';

import Spinner from 'components/spinner';

export class LogoutSpinner extends PureComponent {
  static propTypes = {
    loading: PropTypes.bool,
  };

  static defaultProps = {
    loading: false,
  };

  render() {
    const { loading } = this.props;
    if (!loading) return null;
    return <Spinner />;
  }
}

const mapStateToProps = (state) => {
  const logoutState = localStorage.getItem('loginType') === 'zslogin' ? 'oneIdentityLogin' : 'login';
  return {
    ...state[logoutState],
  };
};

export default connect(
  mapStateToProps,
  null,
)(withTranslation()(LogoutSpinner));

/* eslint-disable jsx-a11y/click-events-have-key-events,jsx-a11y/no-static-element-interactions */
// @flow

import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as eusaSelector from 'ducks/eusa/selectors';
import { faExclamationTriangle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { toggleEUSAModal } from 'ducks/eusa';

export function EUSABanner() {
  const {
    eusaLatestStatus, loading,
  } = useSelector(eusaSelector.infoSelector) || {};
  const { acceptedStatus } = eusaLatestStatus || {};
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const showEUSABanner = () => {
    dispatch(toggleEUSAModal(true));
  };
 
  if (acceptedStatus || loading) return null;

  return (
    <div className="eusa-banner" onClick={showEUSABanner}>
      <FontAwesomeIcon icon={faExclamationTriangle} />
      {' '}
      <span className="text-label">{t('CLICK_HERE_TO_ACCEPT_EUSA')}</span>
    </div>
  );
}

export default EUSABanner;

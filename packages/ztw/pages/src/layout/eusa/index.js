/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable react/no-danger */
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useSelector, useDispatch } from 'react-redux';
import { isEmpty, noop } from 'utils/lodash';
import { useTranslation } from 'react-i18next';
import * as eusaSelector from 'ducks/eusa/selectors';
import Modal from 'components/modal';
import {
  acceptEUSAAgreement, getEUSALatestStatus, toggleEUSAModal, // handleAcceptEUSA
} from 'ducks/eusa';
import Loading from 'components/spinner/Loading';

export function EusaContainer(props) {
  const { callToggleEUSAModalListener } = props;
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const {
    loading,
    eusaLatestInformation,
    eusaLatestStatus,
    isEUSAModalNeedToBeOpened,
  } = useSelector(eusaSelector.infoSelector);
  const { agreement } = eusaLatestInformation || {};

  useEffect(() => {
    if (isEmpty(eusaLatestStatus)) {
      dispatch(getEUSALatestStatus());
    }
  }, [eusaLatestStatus, dispatch]);

  const { acceptedStatus } = eusaLatestStatus;
  if (acceptedStatus && callToggleEUSAModalListener) {
    callToggleEUSAModalListener();
  }

  // const htmlDecode = (input) => {
  //   const doc = new DOMParser().parseFromString(input, 'text/html');
  //   return doc.documentElement.textContent;
  // };

  const acceptAgreement = (payload) => {
    dispatch(acceptEUSAAgreement(payload));
    if (callToggleEUSAModalListener) {
      callToggleEUSAModalListener();
    }
  };
  
  const toggleEUSA = (payload) => {
    dispatch(toggleEUSAModal(payload));
  };

  return (
    <Modal
      title={t('EUSA_AGREEMENT')}
      isOpen={isEUSAModalNeedToBeOpened}
      closeModal={() => toggleEUSA(false)}
      styleClass="eusa-modal-content">
      <Loading loading={loading}>
        <>
          <div className="container-eusa">
            <div
              className="content"
              dangerouslySetInnerHTML={{ __html: (agreement) }} />

          </div>
          <div className="modal-footer">
            <button
              type="submit"
              className="primary-button"
              onClick={() => acceptAgreement(eusaLatestStatus)}>
              {t('ACCEPT')}
            </button>
            <button
              type="button"
              onClick={() => toggleEUSA(false)}>
              {t('CANCEL')}

            </button>
          </div>
        </>
      </Loading>
    </Modal>
  );
}

EusaContainer.propTypes = {
  callToggleEUSAModalListener: PropTypes.func,
};

EusaContainer.defaultProps = {
  callToggleEUSAModalListener: noop,
};

export default EusaContainer;

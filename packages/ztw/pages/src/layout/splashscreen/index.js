/* eslint-disable jsx-a11y/label-has-for */
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/no-danger */
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import axios from 'axios';
import jsonpAdapter from 'axios-jsonp';
import { withTranslation } from 'react-i18next';
import get from 'lodash/get';
import PersistentStorage, { VERSION_HASH } from 'utils/persistentStorage';

import * as loginSelector from 'ducks/login/selectors';
import * as eusaSelector from 'ducks/eusa/selectors';
import Modal from 'components/modal';

export function Splashscreen(props) {
  const {
    t, isAuthenticated, eusaLoading, isEUSAModal,
  } = props;
  const isEUSAModalNeedToBeOpened = eusaLoading || isEUSAModal;
  const [show, setShow] = useState(false);
  const [content, setContent] = useState(false);
  const [title, setTitle] = useState(false);

  const config = {
    headers: {
      'Referrer-Policy': 'origin',
    },
  };
  
  const fetchData = () => {
    axios({
      url: 'https://webapi.zscaler.com/api/zbcc/admin-ui-messages/?filter=latest',
      adapter: jsonpAdapter,
    }, config).then((response) => {
      const { status, data = {} } = response;
      const getContent = get(data, '[0].content', '');
      const getTitle = get(data, '[0].title', '');

      if (status === 200 && getContent.length > 0) {
        setShow(true);
        setContent(getContent);
        setTitle(getTitle);
      }
    }).catch((error) => {
      console.log(error); // eslint-disable-line
    });
  };

  const getMeta = (metaName) => {
    const metas = document.getElementsByTagName('meta');
    for (let i = 0; i < metas.length; i += 1) {
      if (metas[i].getAttribute('name') === metaName) {
        return metas[i].getAttribute('content');
      }
    }
    return '';
  };

  const handleChange = (evt) => {
    if (evt.target.checked) {
      const newversion = getMeta('keywords');
      return PersistentStorage.setItem({ [VERSION_HASH]: newversion });
    }
    return PersistentStorage.removeItem(VERSION_HASH);
  };

  const closeModal = () => {
    setShow(false);
  };

  const htmlDecode = (input) => {
    const doc = new DOMParser().parseFromString(input, 'text/html');
    return doc.documentElement.textContent;
  };

  useEffect(() => {
    const newversion = getMeta('keywords');
    const oldversion = PersistentStorage.getItem(VERSION_HASH);
    const isNew = (newversion !== oldversion);
    if (!isNew) {
      return;
    }

    fetchData();
  }, [isAuthenticated]);
  
  if (!show) return null;

  return (
    <Modal
      title={title}
      isOpen={show && !isEUSAModalNeedToBeOpened}
      closeModal={closeModal}
      styleClass="splash-screen-modal">
      <div
        className="content"
        dangerouslySetInnerHTML={{ __html: htmlDecode(content) }} />

      <div className="modal-footer">
        <label>
          <input
            type="checkbox"
            name="dontshow"
            id="dontshow"
            onChange={handleChange} />
          <span>{t('DONT_SHOW_AGAIN')}</span>
        </label>
        <button
          type="submit"
          className="primary-button"
          onClick={closeModal}>
          {t('CLOSE')}
        </button>
      </div>
    </Modal>
  );
}

Splashscreen.propTypes = {
  isAuthenticated: PropTypes.bool,
  isEUSAModal: PropTypes.bool,
  eusaLoading: PropTypes.bool,
  t: PropTypes.func,
};

Splashscreen.defaultProps = {
  isAuthenticated: null,
  isEUSAModal: true,
  eusaLoading: true,
  t: (str) => str,
};

const mapStateToProps = (state) => ({
  isAuthenticated: loginSelector.isAuthenticatedSelector(state),
  isEUSAModal: eusaSelector.toggleEUSAModalSelector(state),
  eusaLoading: eusaSelector.loadingSelector(state),
});

const mapDispatchToProps = null;

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(Splashscreen));

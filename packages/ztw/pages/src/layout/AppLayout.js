import React, { Suspense } from 'react';
import PropTypes from 'prop-types';

import Navbar from 'components/navbar';
import Notification from 'components/notification/Notification';
import Help from 'components/help';
import Footer from 'components/footer';
import { Outlet, useLocation } from 'react-router-dom';
import PasswordExpiryModal from 'components/PasswordExpiryModal';
import Splashscreen from './splashscreen';
import LogoutSpinner from './LogoutSpinner';
import EUSAConfirmationModal from './eusa';
import EUSABanner from './eusa/EUSABanner';

function AppLayout(props) {
  const location = useLocation();

  return (
    <div className="page">
      <Notification />
      <EUSAConfirmationModal />
      <EUSABanner />
      <Splashscreen />
      <div id="noprint" className="noprint page">
        <LogoutSpinner />
        <div className="navbar">
          <Navbar {...props} location={location} />
        </div>
        <div className="content">
          <Suspense fallback={<LogoutSpinner />}>
            <Outlet />
          </Suspense>
        </div>
        <Help />
        <Footer />
      </div>
      <PasswordExpiryModal callTogglePasswordExpiryChangeListener={(str) => str} />
    </div>
  );
}

AppLayout.propTypes = {
  classes: PropTypes.shape({}),
};

AppLayout.defaultProps = {
  classes: {},
};

export default AppLayout;

// Import library-specific styles
import './scss/main.scss';

// Export main pages




// Export layout components (commented out to reduce bundle size)
// export { default as AppLayout } from './layout/AppLayout';

// Export providers (minimal bundle only)
export { default as AppProvider } from './zuxp-layout/MinimalAppProvider.jsx';
export { default as MinimalAppProvider } from './zuxp-layout/MinimalAppProvider.jsx';
export { default as Gateways } from './pages/administration/gateways';

// Export utilities and hooks (minimal versions to avoid heavy imports)
// Note: Full http utilities available but commented out to reduce bundle size
// export { http, genericInterface } from './utils/http';
// export { initializeHttpClient } from './utils/http/generics';
// export * as helpers from './utils/helpers';
// export * as validations from './utils/validations';
// export * as persistentStorage from './utils/persistentStorage';

// Export minimal store configuration
export { configureMinimalStore as store, minimalStore } from './minimal-store.js';

// Export routing
// export { default as routes } from './routes';
// export { default as AuthRouter } from './AuthRouter';
// export { default as OneUIAuthRouter } from './OneUIAuthRouter';

// Export common connected components (individual exports to avoid module resolution issues)
// export { default as Activation } from './commonConnectedComponents/Activation';
// export { default as SSO } from './commonConnectedComponents/SSO';

// Export duck modules for state management
// export * as ducks from './ducks';

// // Export configuration
// export * as config from './config';

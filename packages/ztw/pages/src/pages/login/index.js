import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import withRouter from 'layout/withRouter';
import Spinner from 'components/spinner';
import * as loginSelectors from 'ducks/login/selectors';
import Zscaler<PERSON>ogo from '../../images/cloud_connector_logo.png';
import LoginForm from './components/loginForm';
import Banner from './components/Banner';
import Footer from './components/Footer';

class Login extends React.Component {
  static propTypes = {
    locale: PropTypes.shape(),
    history: PropTypes.shape(),
    t: PropTypes.func,
    isLoggedIn: PropTypes.bool,
    accessPrivileges: PropTypes.shape({}),
    navigate: PropTypes.func,
  };

  static defaultProps = {
    locale: null,
    history: null,
    t: (str) => str,
    isLoggedIn: false,
    accessPrivileges: {},
    navigate: () => null,
  };

  state = {
    loading: true,
  };

  componentDidMount() {
    window.addEventListener('load', this.handleLoad);
  }

  handleLoad = () => {
    this.setState({ loading: false });
  };

  render() {
    const {
      locale, t,
    } = this.props;
    const { loading } = this.state;
    const localizationText = locale;
    const year = new Date().getFullYear().toString();

    if (loading) return <Spinner />;

    return (
      <div id="login-page-container" className="login-page-container">
        <div id="login-page" className="login-page">
          <div id="login-page-header" className="login-page-header-container">
            <div className="login-page-header-logo-container">
              <img className="login-page-header-logo" src={ZscalerLogo} alt="Zscaler" />
            </div>
            <LoginForm {...this.props} />
          </div>
          <div className="login-page-banner-content">
            <Banner />
            <Footer
              copyrightText={t(localizationText.COPYRIGHT)}
              copyrightStatement={t(localizationText.COPYRIGHT_STATEMENT)}
              year={year} />
          </div>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...state.login,
  ...state.passwordExpiry,
  cloudData: state.cloud,
  isLoggedIn: loginSelectors.isLoggedIn(state),
});

export default connect(mapStateToProps)(withTranslation()(withRouter(Login)));

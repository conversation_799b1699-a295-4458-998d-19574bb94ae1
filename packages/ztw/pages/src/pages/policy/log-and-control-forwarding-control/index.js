import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import { noop, isEmpty } from 'utils/lodash';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import PolicyTipDescription from 'components/PolicyTipDescription';
import { hasBsku, hasCsku } from 'utils/helpers';
import * as loginSelectors from 'ducks/login/selectors';
import * as LogAndControlFwdPoliciesSelector from 'ducks/logAndControlFwdPolicies/selectors';
import {
  loader,
  handleOnSearchFilter,
  toggleClose,
  toggleSortBy,
  toggleAddForm,
  toggleEditForm,
  toggleDeleteForm,
  toggleDuplicateForm,
  toggleViewForm,
  deletePolicy,
} from 'ducks/logAndControlFwdPolicies';
import {
  LogAndControlFwdSearch,
  LogAndControlFwdTable,
} from './components';
import LogAndControlFwdPoliciesForm from './components/LogAndControlFwdPoliciesForm';

function LogAndControlFwdPolicies(props) {
  const {
    accessPermissions,
    accessSubscriptions,
    addPolicy,
    cachedData,
    cancelHandle,
    duplicateRow,
    handleClearTable,
    handleDelete,
    handleDeleteConfirmationForm,
    handleRefresh,
    modalLoading,
    searchData,
    selectedRowID,
    showDeleteForm,
    showForm,
    t,
  } = props;

  useEffect(() => {
    if (!isEmpty(cachedData)) handleClearTable();
  }, [searchData]);

  const onHandleClose = async () => {
    cancelHandle(false);
    await handleClearTable();
    await handleRefresh();
  };
   
  const isReadOnly = accessPermissions.EDGE_CONNECTOR_FORWARDING === 'READ_ONLY';
  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);

  if (!hasBSubscription && !hasCSubscription) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }

  if (accessPermissions.EDGE_CONNECTOR_FORWARDING === 'NONE') {
    return <PermissionRequired accessPermissions={accessPermissions} />;
  }

  const getTitle = () => {
    if (isReadOnly) return t('VIEW_LOG_AND_CONTROL_FORWARDING_RULE');
    if (!addPolicy && !duplicateRow) return t('EDIT_LOG_AND_CONTROL_FORWARDING_RULE');
    return t('ADD_LOG_AND_CONTROL_FORWARDING_RULE');
  };
  
  return (
    <>
      <HelpArticle article={HELP_ARTICLES.LOG_AND_CONTROL_FWD} />
      <div className="main-container-policies">
        <div className="page-title header-3">
          {t('LOG_AND_CONTROL_FORWARDING')}
        </div>
        <PolicyTipDescription title={t('CONFIGURE_LOG_AND_CONTROL_FORWARD')} tip={t('CONFIGURE_LOG_AND_CONTROL_FORWARD_TIP')} />
        <div>
          {/* // Search for Rule Oder, Name  & Location */}
          <LogAndControlFwdSearch isReadOnly={isReadOnly} search={searchData} />
        </div>
        <div className="source-ip-wrapper">
          <ServerError {...props}>
            <LogAndControlFwdTable />
            <Modal
              title={getTitle()}
              isOpen={showForm}
              styleClass="traffic-policies-edit"
              closeModal={onHandleClose}>
              <LogAndControlFwdPoliciesForm handleClose={onHandleClose} />
            </Modal>
            <Modal
              title={t('DELETE_CONFIRMATION')}
              isOpen={showDeleteForm}
              closeModal={() => handleDeleteConfirmationForm(false)}>
              <DeleteConfirmationForm
                modalLoading={modalLoading}
                selectedRowID={selectedRowID}
                handleCancel={() => handleDeleteConfirmationForm(false)}
                handleDelete={handleDelete} />
            </Modal>
          </ServerError>
        </div>
      </div>
    </>
  );
}

const mapStateToProps = (state) => ({
  ...LogAndControlFwdPoliciesSelector.baseSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  authType: loginSelectors.authTypeSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loader,
    handleSearchFilter: handleOnSearchFilter,
    cancelHandle: toggleClose,
    handleToggleAddForm: toggleAddForm,
    handleToggleEditForm: toggleEditForm,
    handleToggleDuplicateForm: toggleDuplicateForm,
    handleToggleViewForm: toggleViewForm,
    handleDeleteConfirmationForm: toggleDeleteForm,
    handleSortBy: toggleSortBy,
    handleDelete: deletePolicy,
  }, dispatch);
  return actions;
};

LogAndControlFwdPolicies.propTypes = {
  t: PropTypes.func,
  searchData: PropTypes.string,
  logAndControlFwdTableData: PropTypes.arrayOf(PropTypes.shape()),
  handleToggleEditCopyForm: PropTypes.func,
  cancelHandle: PropTypes.func,
  addPolicy: PropTypes.bool,
  duplicateRow: PropTypes.bool,
  showForm: PropTypes.bool,
  showDeleteForm: PropTypes.bool,
  load: PropTypes.func,
  accessPermissions: PropTypes.shape({
    EDGE_CONNECTOR_FORWARDING: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  handleSortBy: PropTypes.func,
  handleClearTable: PropTypes.func,
  handleDelete: PropTypes.func,
  handleRefresh: PropTypes.func,
  handleSearchFilter: PropTypes.func,
  handleToggleEditForm: PropTypes.func,
  handleToggleViewForm: PropTypes.func,
  handleToggleDuplicateForm: PropTypes.func,
  handleToggleForm: PropTypes.func,
  handleDeleteConfirmationForm: PropTypes.func,
  sortField: PropTypes.string,
  sortDirection: PropTypes.string,
  cachedData: PropTypes.arrayOf(PropTypes.shape()),
  selectedRowID: PropTypes.number,
  modalLoading: PropTypes.bool,
};

LogAndControlFwdPolicies.defaultProps = {
  t: (str) => str,
  searchData: '',
  logAndControlFwdTableData: [],
  handleToggleEditCopyForm: noop,
  cancelHandle: noop,
  addPolicy: false,
  duplicateRow: false,
  showForm: false,
  showDeleteForm: false,
  load: noop,
  accessPermissions: {},
  accessSubscriptions: [],
  authType: '',
  handleSortBy: noop,
  handleClearTable: noop,
  handleDelete: noop,
  handleRefresh: noop,
  handleSearchFilter: noop,
  handleToggleEditForm: noop,
  handleToggleViewForm: noop,
  handleToggleDuplicateForm: noop,
  handleToggleForm: noop,
  handleDeleteConfirmationForm: noop,
  sortField: '',
  sortDirection: '',
  cachedData: [],
  selectedRowID: null,
  modalLoading: false,
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(LogAndControlFwdPolicies));

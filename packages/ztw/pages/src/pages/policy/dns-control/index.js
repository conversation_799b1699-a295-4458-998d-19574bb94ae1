import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect, useDispatch } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import { noop, isEmpty } from 'utils/lodash';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import PolicyTipDescription from 'components/PolicyTipDescription';
import { hasBsku, hasCsku } from 'utils/helpers';
import { dropdownActions } from 'ducks/dropdowns/ip-dns-destination-group-with-wildcard';
import * as loginSelectors from 'ducks/login/selectors';
import * as DnsPoliciesSelector from 'ducks/dnsPolicies/selectors';
import {
  deletePolicy,
  handleOnSearchFilter,
  loader,
  toggleAddForm,
  toggleClose,
  toggleDeleteForm,
  toggleDuplicateForm,
  toggleEditForm,
  toggleSortBy,
  toggleViewForm,
} from 'ducks/dnsPolicies';
import {
  DnsControlSearch,
  DnsPoliciesTable,
} from './components';

import DnsPoliciesForm from './components/DnsPoliciesForm';

function DnsPolicies(props) {
  const dispatch = useDispatch();
  const {
    accessPermissions,
    accessSubscriptions,
    addPolicy,
    cachedData,
    cancelHandle,
    duplicateRow,
    handleClearTable,
    handleDelete,
    handleDeleteConfirmationForm,
    handleRefresh,
    modalLoading,
    searchData,
    selectedRowID,
    showDeleteForm,
    showForm,
    t,
  } = props;

  useEffect(() => {
    dispatch(dropdownActions.load());
    dispatch(dropdownActions.open());
  }, []);

  useEffect(() => {
    if (!isEmpty(cachedData)) handleClearTable();
  }, [searchData]);
   
  const onHandleClose = async () => {
    cancelHandle(false);
    await handleClearTable();
    await handleRefresh();
  };
   
  const isReadOnly = accessPermissions.EDGE_CONNECTOR_FORWARDING === 'READ_ONLY';
  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);

  if (!hasBSubscription && !hasCSubscription) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }

  if (accessPermissions.EDGE_CONNECTOR_FORWARDING === 'NONE') {
    return <PermissionRequired accessPermissions={accessPermissions} />;
  }

  const getTitle = () => {
    if (isReadOnly) return t('VIEW_DNS_POLICIES');
    if (!addPolicy && !duplicateRow) return t('EDIT_DNS_POLICIES');
    return t('ADD_DNS_POLICIES');
  };

  return (
    <>
      <HelpArticle article={HELP_ARTICLES.DNS_CONTROL_POLICY} />
      <div className="main-container-policies">
        <div className="page-title header-3">
          {t('DNS_POLICY')}
        </div>
        <PolicyTipDescription title={t('DNS_CONTROL_TIPS_TITLE')} tip={t('DNS_CONTROL_TIPS_DESC')} />
        <div>
          {/* // Search for Rule Oder, Name  & Location */}
          <DnsControlSearch isReadOnly={isReadOnly} />
        </div>
        <div className="source-ip-wrapper">
          <ServerError {...props}>
            <DnsPoliciesTable />
            <Modal
              title={getTitle()}
              isOpen={showForm}
              styleClass="traffic-policies-edit"
              closeModal={onHandleClose}>
              <DnsPoliciesForm handleClose={onHandleClose} />
            </Modal>
            <Modal
              title={t('DELETE_CONFIRMATION')}
              isOpen={showDeleteForm}
              closeModal={() => handleDeleteConfirmationForm(false)}>
              <DeleteConfirmationForm
                modalLoading={modalLoading}
                selectedRowID={selectedRowID}
                handleCancel={() => handleDeleteConfirmationForm(false)}
                handleDelete={handleDelete} />
            </Modal>
          </ServerError>
        </div>
      </div>
    </>
  );
}

const mapStateToProps = (state) => ({
  ...DnsPoliciesSelector.baseSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  authType: loginSelectors.authTypeSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loader,
    cancelHandle: toggleClose,
    handleSearchFilter: handleOnSearchFilter,
    handleToggleAddForm: toggleAddForm,
    handleToggleEditForm: toggleEditForm,
    handleToggleDuplicateForm: toggleDuplicateForm,
    handleToggleViewForm: toggleViewForm,
    handleDeleteConfirmationForm: toggleDeleteForm,
    handleSortBy: toggleSortBy,
    handleDelete: deletePolicy,
  }, dispatch);
  return actions;
};

DnsPolicies.propTypes = {
  t: PropTypes.func,
  searchData: PropTypes.string,
  dnsPoliciesTableData: PropTypes.arrayOf(PropTypes.shape()),
  cancelHandle: PropTypes.func,
  addPolicy: PropTypes.bool,
  duplicateRow: PropTypes.bool,
  showForm: PropTypes.bool,
  showDeleteForm: PropTypes.bool,
  load: PropTypes.func,
  accessPermissions: PropTypes.shape({
    EDGE_CONNECTOR_FORWARDING: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  handleClearTable: PropTypes.func,
  handleDelete: PropTypes.func,
  handleRefresh: PropTypes.func,
  handleSearchFilter: PropTypes.func,
  handleSortBy: PropTypes.func,
  handleToggleEditForm: PropTypes.func,
  handleToggleViewForm: PropTypes.func,
  handleToggleDuplicateForm: PropTypes.func,
  handleToggleForm: PropTypes.func,
  handleDeleteConfirmationForm: PropTypes.func,
  sortField: PropTypes.string,
  sortDirection: PropTypes.string,
  cachedData: PropTypes.arrayOf(PropTypes.shape()),
  selectedRowID: PropTypes.number,
  modalLoading: PropTypes.bool,
};

DnsPolicies.defaultProps = {
  t: (str) => str,
  searchData: '',
  dnsPoliciesTableData: [],
  cancelHandle: noop,
  addPolicy: false,
  duplicateRow: false,
  showForm: false,
  showDeleteForm: false,
  load: noop,
  accessPermissions: {},
  accessSubscriptions: [],
  authType: '',
  handleClearTable: noop,
  handleDelete: noop,
  handleRefresh: noop,
  handleSearchFilter: noop,
  handleSortBy: noop,
  handleToggleEditForm: noop,
  handleToggleViewForm: noop,
  handleToggleDuplicateForm: noop,
  handleToggleForm: noop,
  handleDeleteConfirmationForm: noop,
  sortField: '',
  sortDirection: '',
  cachedData: [],
  selectedRowID: null,
  modalLoading: false,
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(DnsPolicies));

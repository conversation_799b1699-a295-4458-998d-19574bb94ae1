import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect, useDispatch } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import { noop, isEmpty } from 'utils/lodash';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import Modal from 'components/modal';
import ServerError from 'components/errors/ServerError';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import PolicyTipDescription from 'components/PolicyTipDescription';
import { hasBsku, hasCsku } from 'utils/helpers';
import { dropdownActions } from 'ducks/dropdowns/ip-destination-group-with-wildcard';
import * as loginSelectors from 'ducks/login/selectors';
import * as TrafficFwdPoliciesSelector from 'ducks/trafficFwdPolicies/selectors';
import {
  deletePolicy,
  handleOnSearchFilter,
  loader,
  toggleAddForm,
  toggleClearTable,
  toggleClose,
  toggleDeleteForm,
  toggleDuplicateForm,
  toggleEditForm,
  toggleForm,
  toggleRefreshTable,
  toggleSortBy,
  toggleViewForm,
} from 'ducks/trafficFwdPolicies';
import {
  TrafficFwdPoliciesSearch,
  TrafficFwdPoliciesTable,
} from './components';
import TrafficFwdPoliciesForm from './components/TrafficFwdPoliciesForm';

function TrafficFwdPolicies(props) {
  const dispatch = useDispatch();
  const {
    accessPermissions,
    accessSubscriptions,
    addPolicy,
    cachedData,
    cancelHandle,
    duplicateRow,
    handleClearTable,
    handleDelete,
    handleDeleteConfirmationForm,
    handleRefresh,
    modalLoading,
    searchData,
    selectedRowID,
    showDeleteForm,
    showForm,
    t,
  } = props;
  
  useEffect(() => {
    dispatch(dropdownActions.load());
    dispatch(dropdownActions.open());
  }, []);

  useEffect(() => {
    if (!isEmpty(cachedData)) handleClearTable();
  }, [searchData]);
 
  const onHandleClose = async () => {
    cancelHandle(false);
    await handleClearTable();
    await handleRefresh();
  };
   
  const isReadOnly = accessPermissions.EDGE_CONNECTOR_FORWARDING === 'READ_ONLY';
  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);

  if (!hasBSubscription && !hasCSubscription) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }

  if (accessPermissions.EDGE_CONNECTOR_FORWARDING === 'NONE') {
    return <PermissionRequired accessPermissions={accessPermissions} />;
  }

  const getTitle = () => {
    if (isReadOnly) return t('VIEW_TRAFFIC_FWD_POLICIES');
    if (!addPolicy && !duplicateRow) return t('EDIT_TRAFFIC_FWD_POLICIES');
    return t('ADD_TRAFFIC_FWD_POLICIES');
  };
  
  return (
    <>
      <HelpArticle article={HELP_ARTICLES.TRAFFIC_FWD} />
      <div className="main-container-policies">
        <div className="page-title header-3">
          {t('TRAFFIC_FORWARDING')}
        </div>
        <PolicyTipDescription title={t('CONFIGURE_TRAFFIC_FORWARD')} tip={t('CONFIGURE_TRAFFIC_FORWARD_TIP')} />
        <div className="controls-container">
          <TrafficFwdPoliciesSearch isReadOnly={isReadOnly} search={searchData} />
        </div>
        <div className="source-ip-wrapper">
          <ServerError {...props}>
            <TrafficFwdPoliciesTable />
            <Modal
              title={getTitle()}
              isOpen={showForm}
              styleClass="traffic-policies-edit"
              closeModal={onHandleClose}>
              <TrafficFwdPoliciesForm handleClose={onHandleClose} />
            </Modal>
            <Modal
              title={t('DELETE_CONFIRMATION')}
              isOpen={showDeleteForm}
              closeModal={() => handleDeleteConfirmationForm(false)}>
              <DeleteConfirmationForm
                modalLoading={modalLoading}
                selectedRowID={selectedRowID}
                handleCancel={() => handleDeleteConfirmationForm(false)}
                handleDelete={handleDelete} />
            </Modal>
          </ServerError>
        </div>
      </div>
    </>
  );
}

const mapStateToProps = (state) => ({
  ...TrafficFwdPoliciesSelector.baseSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  authType: loginSelectors.authTypeSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loader,
    cancelHandle: toggleClose,
    handleClearTable: toggleClearTable,
    handleRefresh: toggleRefreshTable,
    handleSearchFilter: handleOnSearchFilter,
    handleToggleAddForm: toggleAddForm,
    handleToggleEditForm: toggleEditForm,
    handleToggleDuplicateForm: toggleDuplicateForm,
    handleToggleViewForm: toggleViewForm,
    handleDeleteConfirmationForm: toggleDeleteForm,
    handleToggleEditCopyForm: toggleForm,
    handleSortBy: toggleSortBy,
    handleDelete: deletePolicy,
  }, dispatch);
  return actions;
};

TrafficFwdPolicies.propTypes = {
  accessPermissions: PropTypes.shape({
    EDGE_CONNECTOR_FORWARDING: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  addPolicy: PropTypes.bool,
  // authType: PropTypes.string,
  cachedData: PropTypes.arrayOf(PropTypes.shape()),
  cancelHandle: PropTypes.func,
  duplicateRow: PropTypes.bool,
  load: PropTypes.func,
  handleClearTable: PropTypes.func,
  handleDelete: PropTypes.func,
  handleDeleteConfirmationForm: PropTypes.func,
  handleRefresh: PropTypes.func,
  handleSearchFilter: PropTypes.func,
  modalLoading: PropTypes.bool,
  searchData: PropTypes.string,
  selectedRowID: PropTypes.number,
  showDeleteForm: PropTypes.bool,
  showForm: PropTypes.bool,
  t: PropTypes.func,
};

TrafficFwdPolicies.defaultProps = {
  accessPermissions: {},
  accessSubscriptions: [],
  addPolicy: false,
  cachedData: [],
  cancelHandle: noop,
  duplicateRow: false,
  load: noop,
  handleClearTable: noop,
  handleDelete: noop,
  handleDeleteConfirmationForm: noop,
  handleRefresh: noop,
  handleSearchFilter: noop,
  modalLoading: false,
  searchData: '',
  selectedRowID: null,
  showDeleteForm: false,
  showForm: false,
  t: (str) => str,
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(TrafficFwdPolicies));

import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { loader } from 'ducks/profile';
import BasicCustomForm from './component/BasicCustomForm';

function Profile(props) {
  const dispatch = useDispatch();
  const { t } = props;

  useEffect(
    () => {
      dispatch(loader(props));
    },
    [],
  );

  return (
    <div className="myprofile-main-container">
      <div className="page-title ">
        <span className="source-ip-groups">
          {t('MY_PROFILE')}
        </span>
      </div>
      <BasicCustomForm {...props} />
    </div>
  );
}

Profile.propTypes = {
  classes: PropTypes.shape({}),
  t: PropTypes.func,
  handleSubmit: PropTypes.func,
};

Profile.defaultProps = {
  classes: {},
  t: (str) => str,
  handleSubmit: (str) => str,
};

export default withTranslation()(Profile);

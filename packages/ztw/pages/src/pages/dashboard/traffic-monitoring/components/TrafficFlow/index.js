import React from 'react';
import PropTypes from 'prop-types';
import { useLocation, useNavigate } from 'react-router-dom';
import { withTranslation } from 'react-i18next';
import {
  faArrowLeft,
  faSyncAlt,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { getIcon } from 'utils/helpers';
import { isEmpty } from 'utils/lodash';
import ServerError from 'components/errors/ServerError';
import NavTabs from 'components/navTabs';
import RadioTab from 'components/radioTab';
import { HELP_ARTICLES, BASE_LAYOUT } from 'config';
import HelpArticle from 'components/HelpArticle';
import { TrafficOverview } from './components/TrafficOverview';
import { DNSOverview } from './components/DNSOverview';

export function TrafficFlow(props) {
  const {
    showTrafficFlow,
    cloudConnectorData,
    showOverviewDefault,
    trafficOverviewTabs,
    backToParent,
    actions,
    deviceStatuslogTime,
    t,
  } = props;
  const navigate = useNavigate();
  const queryLocation = useLocation().search;
  const params = new URLSearchParams(queryLocation);
  const filter = params.get('filter');

  const {
    name,
    deploymentType,
    deviceType,
    status,
  } = cloudConnectorData;

  if (!showTrafficFlow) return null;
  return (
    <>
      <HelpArticle article={HELP_ARTICLES.TRAFFIC_FLOW} />
      <div className="dashboard cloudconnector-info">
        {/* navigation header */}
        <div>
          <FontAwesomeIcon
            className="far title-back"
            icon={faArrowLeft}
            size="lg"
            onClick={() => {
              if (isEmpty(filter)) {
                backToParent();
              } else {
                navigate(`${BASE_LAYOUT}/dashboard/connector-monitoring?filter=${filter}`);
              }
            }}
            role="button"
            aria-label="Back to Parent"
            tabIndex="0"
            onKeyPress={() => backToParent()} />
          <div className="title-nav">
            <span>
              {t('TRAFFIC_MONITORING')}
            </span>
          </div>
          <div className="title-nav-right">
            <span style={{ paddingRight: '1em' }}>
              {t('>')}
            </span>
            <span>
              {getIcon(deploymentType, status, deviceType)}
              {' '}
              {name}
            </span>
          </div>
        </div>
        {/* Tabs Section */}
        <div className="config-nav-tab">
          {/* Tabs */}
          <div>
            <div className="nav-tabs">
              <NavTabs
                tabConfiguration={[{
                  id: 'connector-monitoring',
                  title: t(['CENTOS', 'REDHAT_LINUX', 'MICROSOFT_HYPER_V', 'VMWARE_ESXI'].includes(deploymentType) || deviceType === 'PHYSICAL' ? 'BC_DETAILS' : 'CC_DETAILS'),
                  to: `${BASE_LAYOUT}/dashboard/connector-monitoring?filter=${filter || ''}`,
                },
                {
                  id: 'traffic-monitoring',
                  title: t('TRAFFIC_FLOW'),
                  to: `${BASE_LAYOUT}/dashboard/traffic-monitoring?filter=${filter || ''}`,
                }]} />
              {/* Status & Action */}
              <div className="status">
                <span>
                  {t('LAST_UPDATE')}
                  {' '}
                  {deviceStatuslogTime}
                </span>
                <span>
                  <FontAwesomeIcon
                    className="far refresh"
                    icon={faSyncAlt}
                    size="lg"
                    onClick={() => actions.viewTrafficFlow()}
                    onKeyPress={() => actions.viewTrafficFlow()}
                    type="button"
                    role="presentation" />
                </span>
              </div>
            </div>
          </div>
        </div>
        <ServerError {...props}>
          <div className="overview-tabs">
            <div className="logs-tab">
              <RadioTab
                optionsList={trafficOverviewTabs}
                toggler={actions.toggleTab} />
            </div>
          </div>
          <div className="overview-container">
            <TrafficOverview {...props} showOverview={showOverviewDefault} />
          </div>
          <div className="overview-container">
            <DNSOverview {...props} showOverview={showOverviewDefault} />
          </div>
        </ServerError>
      </div>
    </>
  );
}

TrafficFlow.propTypes = {
  showTrafficFlow: PropTypes.bool,
  cloudConnectorData: PropTypes.shape({}),
  showOverviewDefault: PropTypes.bool,
  trafficOverviewTabs: PropTypes.arrayOf(PropTypes.shape()),
  backToParent: PropTypes.func,
  actions: PropTypes.shape({}),
  deviceStatuslogTime: PropTypes.string,
  t: PropTypes.func,
};

TrafficFlow.defaultProps = {
  showTrafficFlow: false,
  cloudConnectorData: {
    lastUpdated: '',
  },
  showOverviewDefault: false,
  trafficOverviewTabs: [],
  backToParent: null,
  actions: null,
  deviceStatuslogTime: '',
  t: (str) => str,
};

export default withTranslation()(TrafficFlow);

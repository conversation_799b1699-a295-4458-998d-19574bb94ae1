import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import LineChartBasic from 'components/lineChartBasic';
import DropDown from 'components/dropDown';
import {
  TREND_TIME_FRAME_DATA,
  TREND_DETAIL_DATA,
} from 'config';

export function TrafficTrend(props) {
  const {
    showTotalTrafficSection,
    showTotalTrafficChartsMenu,
    trafficTrendData,
    trafficTrendChartData,
    genericTrendDataKeys,
    actions,
    t,
  } = props;

  if (!showTotalTrafficSection) return null;
  return (
    <div className="chart-container">
      <div>
        <div className="sub-header">
          <div className="content">
            <div className="content-box-flex">
              <div>
                {t('TRAFFIC_TREND')}
              </div>
              <div className="interval">
                <div className="middle-flex trendDropDown">
                  <DropDown
                    items={TREND_TIME_FRAME_DATA}
                    setValue={(str) => str}
                    name="totalTrendInterval"
                    onCallBack={actions.handleTrendInterval}
                    defaultValue={{
                      value: '24_HOURS',
                      label: '24_HOURS',
                    }} />
                </div>
              </div>
              <div>
                <div className="last-flex trendDropDown">
                  <DropDown
                    items={TREND_DETAIL_DATA}
                    setValue={(str) => str}
                    name="TrafficTrendDetail"
                    // eslint-disable-next-line react/jsx-handler-names
                    onCallBack={actions.toggleTotalTrafficChartsMenu}
                    defaultValue={{
                      value: 'SHOW_DETAILS',
                      label: 'SHOW_DETAILS',
                    }} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <hr className="sub-header-hr" />
      {trafficTrendData
        .filter((x) => (!showTotalTrafficChartsMenu || (showTotalTrafficChartsMenu && x.key === 'total')))
        .map((index) => (
          index.checkbox
            ? (
              <div className="chart-section" key={'TRAFFIC' + index.name}>
                <div className="chart-sub-header">
                  {t(index.name)}
                </div>
                <LineChartBasic
                  data={trafficTrendChartData[index.dataKey]}
                  dataKeys={genericTrendDataKeys}
                  clickMoreInfo={actions.drilldownSessionLogs} />
              </div>
            )
            : ''
        ))}
    </div>
  );
}

TrafficTrend.propTypes = {
  cloudConnectorData: PropTypes.shape({}),
  showTotalTrafficSection: PropTypes.bool,
  showTotalTrafficChartsMenu: PropTypes.bool,
  trafficTrendData: PropTypes.arrayOf(PropTypes.shape({})),
  trafficTrendChartData: PropTypes.shape({}),
  genericTrendDataKeys: PropTypes.arrayOf(PropTypes.shape({})),
  actions: PropTypes.shape({}),
  t: PropTypes.func,
};

TrafficTrend.defaultProps = {
  cloudConnectorData: {
    lastUpdated: '',
  },
  showTotalTrafficSection: false,
  showTotalTrafficChartsMenu: false,
  trafficTrendData: [{}],
  trafficTrendChartData: {},
  genericTrendDataKeys: [],
  actions: {},
  t: (str) => str,
};

export default withTranslation()(TrafficTrend);

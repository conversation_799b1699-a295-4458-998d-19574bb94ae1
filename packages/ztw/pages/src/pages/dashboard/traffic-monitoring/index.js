// @flow
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import { verifyConfigData } from 'utils/helpers';
import { withTranslation } from 'react-i18next';

import { SubHeader } from 'components/label';
import NavTabs from 'components/navTabs';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  faSyncAlt,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { handleSetLastUrl } from 'ducks/connectorMonitoring';
import {
  load, viewTrafficFlow, toggleTrafficMointoring,
  toggleTotalSessionChartsMenu, toggleTotalDNSChartsMenu,
  toggleSessionSec, toggleTotalTrafficSec, toggleTotalTrafficChartsMenu,
  applyTotalTraffic<PERSON>harts, toggleTab, handleTrendInterval, applyFilters,
  drilldownSessionLogs, drilldownDnsLogs,
} from 'ducks/trafficMonitoring';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import * as dashboardSelector from 'ducks/connectorMonitoring/selectors';
import * as trafficMonitoringSelector from 'ducks/trafficMonitoring/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import Donut from 'components/pieChart/Donut';
import { BASE_LAYOUT, HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import { TRAFFIC_MONITORING_TABLE_CONFIGS } from 'ducks/trafficMonitoring/constants';
import ConfigTable from 'components/configTable/ConfigTableWithPagination';

import GeoView from '../components/GeoView';
import { TrafficFlow } from './components/TrafficFlow';
import Filters from './Filters';

export function TrafficMonitoring(props) {
  useEffect(() => {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.BRANCH_AND_CLOUD_MONITORING });
    const { actions } = props;
    actions.load();
    actions.handleSetLastUrl('traffic-monitoring');
    return () => {
      actions.handleSetLastUrl('cleanData');
    };
  }, []);

  const getData = (row, spec) => {
    let session = 0;
    let kbps = 0;
    if (row) {
      const ele = row.find((i) => i.name === spec);
  
      if (ele && 'entries' in ele) {
        const { entries } = ele;
        entries.forEach((i) => {
          if (i.name === 'SESSIONS') {
            session = i.total;
          } else if (i.name === 'BYTES') {
            // bytes = i.total;
            // bytes = i.total / 1000;
            // bits to kbps
            kbps = (i.total && i.total > 0) ? (i.total * 8 / (24 * 60 * 60 * 1024)).toFixed(3) : 0;
          }
        });
      }
    }

    return kbps + '/' + session;
  };

  const getTableData = () => {
    const { traffictabledata } = props;
    const tableData = traffictabledata.map((row) => {
      return {
        ...row,
        name: row.name,
        ecId: row.id,
        ecName: row.name,
        group: row.group,
        deploymentType: row.deploymentType,
        deviceType: row.deviceType,
        status: row.status,
        location: row.location,
        ziaThroughput: getData(row.entries, 'ZIA'),
        zpaThroughput: getData(row.entries, 'ECZPA'),
        directThroughput: getData(row.entries, 'DIRECT'),
        isReadOnly: true,
        isDeletable: false,
        isEditable: false,
      };
    });
    return tableData;
  };

  const {
    t,
    totalEcData,
    activeHealthData,
    configData,
    showTrafficFlow,
    cloudConnectorData,
    actions,
    traffictabledata,
    weblogTime,
  } = props;
  const enableZTGateway = verifyConfigData({ configData, key: 'enableZTGateway' });

  return (
    <Loading {...props}>
      <HelpArticle article={HELP_ARTICLES.TRAFFIC_MONITORING} />
      <div className={`dashboard ${showTrafficFlow ? 'hide' : 'unhide'}`}>
        <div className="dual-container">
          <h1 className="page-title">
            {t('DASHBOARD')}
          </h1>
          <div className="left-section">
            <span>
              {t('LAST_UPDATE')}
              {' '}
              {weblogTime}
            </span>
          </div>
          <div
            className="right-section"
            onClick={() => actions.load()}
            onKeyPress={() => actions.load()}
            type="button"
            role="presentation">
            <span>
              <FontAwesomeIcon
                className="far refresh"
                icon={faSyncAlt}
                size="lg" />
              {t('REFRESH')}
            </span>
          </div>
        </div>
        <div className="main-container provisioning-template-header">
          <div className="configuration-nav-tab">
            <NavTabs
              tabConfiguration={[{
                id: 'connector-monitoring',
                title: t('BRANCH_AND_CLOUD_CONNECTOR_MONITORING'),
                to: `${BASE_LAYOUT}/dashboard/connector-monitoring`,
              },
              {
                id: 'traffic-monitoring',
                title: t('TRAFFIC_MONITORING'),
                to: `${BASE_LAYOUT}/dashboard/traffic-monitoring`,
              },
              ...(enableZTGateway ? [{
                id: 'zero-trust-gateway',
                title: t('ZERO_TRUST_GATEWAY'),
                to: `${BASE_LAYOUT}/dashboard/zero-trust-gateway`,
              }] : [])]} />
          </div>
        </div>

        <Filters {...props} handleSubmit={actions.applyFilters} />
        <ServerError {...props}>
          <div className="pie-container">
            <Donut data={totalEcData} displayName={t('THROUGHPUT_ACROSS_SERVICES')} tooltip={t('TOOLTIP_THROUGHPUT_ACROSS_SERVICES')} />
            <Donut data={activeHealthData} displayName={t('SESSIONS_ACROSS_SERVICES')} tooltip={t('TOOLTIP_SESSIONS_ACROSS_SERVICES')} />
          </div>
          {!showTrafficFlow
              && (
                <div className="dashboard-row map-content">
                  <SubHeader title={t('GEO_VIEW')} />
                  <GeoView data={traffictabledata} actions={actions} showTrafficTooltip />
                </div>
              )}
          <div className="dashboard-row table-content traffic-monitoring-table">
            <ConfigTable
              {...TRAFFIC_MONITORING_TABLE_CONFIGS}
              // eslint-disable-next-line react/jsx-handler-names
              onHandleRowView={actions.viewTrafficFlow}
              pagination
              sizesPerPage={[50, 100, 150, 200, 250]}
              data={getTableData()} />
          </div>
        </ServerError>
      </div>
      <ServerError {...props}>
        <TrafficFlow
          showTrafficFlow={showTrafficFlow}
          cloudConnectorData={cloudConnectorData}
          backToParent={actions.toggleTrafficMointoring}
          {...props} />
      </ServerError>
    </Loading>
  );
}

const mapStateToProps = (state) => ({
  ...dashboardSelector.default(state),
  ...trafficMonitoringSelector.default(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  configData: loginSelectors.configDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load,
    applyFilters,
    toggleSessionSec,
    toggleTotalTrafficSec,
    toggleTotalTrafficChartsMenu,
    toggleTotalSessionChartsMenu,
    toggleTotalDNSChartsMenu,
    applyTotalTrafficCharts,
    viewTrafficFlow,
    toggleTrafficMointoring,
    toggleTab,
    handleTrendInterval,
    drilldownSessionLogs,
    drilldownDnsLogs,
    handleSetLastUrl,
  }, dispatch);
  return { actions };
};

TrafficMonitoring.propTypes = {
  actions: PropTypes.shape({
    load: PropTypes.func,
  }),
  totalEcData: PropTypes.shape({}),
  activeHealthData: PropTypes.shape({}),
  configData: PropTypes.shape(),
  geoData: PropTypes.arrayOf(PropTypes.shape({})),
  t: PropTypes.func,
  traffictabledata: PropTypes.arrayOf(PropTypes.shape()),
  showTrafficFlow: PropTypes.bool,
  cloudConnectorData: PropTypes.shape({}),
  weblogTime: PropTypes.string,
};

TrafficMonitoring.defaultProps = {
  actions: {
    load: noop,
  },
  totalEcData: {},
  configData: {},
  activeHealthData: {},
  geoData: [],
  t: (str) => str,
  traffictabledata: null,
  showTrafficFlow: false,
  cloudConnectorData: {},
  weblogTime: '',
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(TrafficMonitoring));

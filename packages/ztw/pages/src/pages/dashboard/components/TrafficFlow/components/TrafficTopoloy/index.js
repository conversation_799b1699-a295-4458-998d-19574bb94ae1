import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { PathsConstructor } from 'components/curve';
import { Node, EdgeConnector } from './components';

export class TrafficTopoloy extends PureComponent {
  static propTypes = {
    ecTrafficData: PropTypes.shape(),
    toggleFamily: PropTypes.func,
    incomingTraffic: PropTypes.bool,
    outgoingTraffic: PropTypes.bool,
  };

  static defaultProps = {
    ecTrafficData: {},
    toggleFamily: (str) => str,
    incomingTraffic: false,
    outgoingTraffic: false,
  };

  render() {
    const {
      toggleFamily,
      ecTrafficData,
      incomingTraffic,
      outgoingTraffic,
    } = this.props;
    const {
      paths,
      source,
      subnets,
      edgeconnector,
      service,
      destination,
    } = ecTrafficData;
    return (
      <div className="traffic-content">
        <div className="nodes-holder">
          {/* Source */}
          <Node nodeClass="node-source" data={source} family={incomingTraffic} />
          {/* Subnets */}
          <Node nodeClass="node-section" data={subnets} family={incomingTraffic} leftChild />
          {/* EdgeConnector */}
          <EdgeConnector
            toggleFamily={toggleFamily}
            edgeconnector={edgeconnector}
            incomingTraffic={incomingTraffic}
            outgoingTraffic={outgoingTraffic} />
          {/* Service */}
          <Node nodeClass="node-section" data={service} family={outgoingTraffic} rightChild />
          {/* Destination */}
          <Node nodeClass="node-section" data={destination} family={outgoingTraffic} />
        </div>
        <PathsConstructor paths={paths} />
      </div>
    );
  }
}

export default TrafficTopoloy;

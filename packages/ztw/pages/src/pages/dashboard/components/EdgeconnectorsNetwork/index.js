import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { MapWithTooltip } from 'components/worldMap';
import EdgeconnectorFooter from './components/EdgeconnectorFooter';

export class EdgeconnectorsNetwork extends PureComponent {
  static propTypes = {
    data: PropTypes.arrayOf(PropTypes.shape({})),
    actions: PropTypes.shape(),
  };

  static defaultProps = {
    data: [],
    actions: {},
  };

  render() {
    const { data, actions } = this.props;
    return (
      <div className="ec-network">
        <MapWithTooltip
          data={data}
          legendTitle="Data Centers"
          legendDescription="Cloud Connectors"
          actions={actions} />
        <EdgeconnectorFooter />
      </div>
    );
  }
}

export default EdgeconnectorsNetwork;

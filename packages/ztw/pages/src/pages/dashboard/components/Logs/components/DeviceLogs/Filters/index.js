/* eslint-disable jsx-a11y/label-has-for */
// @flow
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import DropDown from 'components/dropDown';
import {
  LOGS_EVENT_TIME,
  LOGS_EC_LOCATION,
  LOGS_EC_VERSION,
  LOGS_HYPERVISOR_VERSION,
} from 'config';

import * as LogsSelector from 'ducks/logs/selectors';
import {
  toggleFilters,
} from 'ducks/logs';

export class Filters extends PureComponent {
  static propTypes = {
    t: PropTypes.func,
    showFilters: PropTypes.bool,
  };

  static defaultProps = {
    t: (str) => str,
    showFilters: false,
  };

  render() {
    const {
      showFilters,
      t,
    } = this.props;

    return (
      <div className={`logs-filter-container ${showFilters ? 'unhide' : 'hide'}`}>
        {/* filters */}
        <div className="filters-section">
          <div className="left-pane">
            <div className="filter-container">
              <div className="filter-label">
                <label htmlFor="eventTime">{t('EVENT_TIME')}</label>
              </div>
              <div className="filter-dropdown-left">
                <DropDown
                  items={LOGS_EVENT_TIME}
                  setValue={noop}
                  defaultValue={{
                    value: '0',
                    label: t('SELECT_EVENT_TIME'),
                  }} />
              </div>
            </div>
            <div className="filter-container">
              <div className="filter-label">
                <label htmlFor="ecLocation">{t('CC_LOCATION')}</label>
              </div>
              <div className="filter-dropdown-left">
                <DropDown
                  items={LOGS_EC_LOCATION}
                  setValue={noop}
                  defaultValue={{
                    value: '0',
                    label: t('SELECT_CC_LOCATION'),
                  }} />
              </div>
            </div>
            <div className="filter-container">
              <div className="filter-label">
                <label htmlFor="ecVersion">{t('CC_VERSION')}</label>
              </div>
              <div className="filter-dropdown-left">
                <DropDown
                  items={LOGS_EC_VERSION}
                  setValue={noop}
                  defaultValue={{
                    value: '0',
                    label: t('SELECT_CC_VERSION'),
                  }} />
              </div>
            </div>
          </div>
          <div className="right-pane">
            <div className="filter-container-right">
              <div className="filter-label-right">
                <label htmlFor="hypervisorVersion">{t('HYPERVISOR_VERSION')}</label>
              </div>
              <div className="filter-dropdown-left">
                <DropDown
                  items={LOGS_HYPERVISOR_VERSION}
                  setValue={noop}
                  defaultValue={{
                    value: '0',
                    label: t('SELECT_HYPERVISOR_VERSION'),
                  }} />
              </div>
            </div>
          </div>
        </div>
        {/* buttons */}
        <div className="buttons">
          <button
            type="button"
            className="activate-button blue-button"
            // disabled={!status || status !== 'pending'}
            onClick={noop}>
            {t('APPLY_FILTER')}
          </button>
          <button
            type="button"
            className="clear-button"
            // disabled={!status || status !== 'pending'}
            onClick={noop}>
            {t('CLEAR_FILTERS')}
          </button>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({ ...LogsSelector.baseSelector(state) });

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleFiltersButton: toggleFilters,
  }, dispatch);
  return actions;
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(Filters));

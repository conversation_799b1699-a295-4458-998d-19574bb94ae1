/* eslint-disable jsx-a11y/label-has-for */
// @flow
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import NavTabs from 'components/navTabs';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import RadioTab from 'components/radioTab';
import FlipButton from 'components/flipButton';
import { BASE_LAYOUT } from 'config';

import * as LogsSelector from 'ducks/logs/selectors';
import {
  getLogsData,
  toggleTab,
  toggleFilters,
} from 'ducks/logs';
import {
  DeviceLogs,
  TrafficLogs,
} from './components';

export class Logs extends Component {
  static propTypes = {
    t: PropTypes.func,
    devicelogstabledata: PropTypes.arrayOf(PropTypes.shape()),
    trafficlogstabledata: PropTypes.arrayOf(PropTypes.shape()),
    toggleFiltersButton: PropTypes.func,
    toggleLogsTab: PropTypes.func,
    showDefault: PropTypes.bool,
    showFilters: PropTypes.bool,
    load: PropTypes.func,
    loadIPList: PropTypes.func,
    logsTab: PropTypes.arrayOf(PropTypes.shape()),
  };

  static defaultProps = {
    t: (str) => str,
    devicelogstabledata: null,
    trafficlogstabledata: null,
    toggleFiltersButton: noop,
    toggleLogsTab: noop,
    showDefault: false,
    showFilters: false,
    load: noop,
    loadIPList: noop,
    logsTab: [],
  };

  componentDidMount() {
    const { load } = this.props;
    load();
  }
  
  render() {
    const {
      devicelogstabledata,
      trafficlogstabledata,
      toggleLogsTab,
      logsTab,
      showDefault,
      showFilters,
      toggleFiltersButton,
      t,
    } = this.props;

    return (
      <Loading {...this.props}>
        <ServerError {...this.props}>
          <div className="main-container">
            <div className="configuration-nav-tab">
              <NavTabs
                tabConfiguration={[{
                  id: 'dashboard',
                  title: 'Dashboard',
                  to: `${BASE_LAYOUT}/dashboard/dashboard`,
                },
                {
                  id: 'logs',
                  title: 'Logs',
                  to: `${BASE_LAYOUT}/dashboard/logs`,
                },
                {
                  id: 'health',
                  title: 'Health',
                  to: `${BASE_LAYOUT}/dashboard/health`,
                }]} />
            </div>
            <div className="logs-header">
              <div className="logs-tab">
                <RadioTab
                  optionsList={logsTab}
                  toggler={toggleLogsTab} />
              </div>
              <div className="logs-filters">
                <FlipButton label={t('ADD_FILTERS')} clickCallback={toggleFiltersButton} show={showFilters} />
              </div>
            </div>
            <div className="container-row">
              <DeviceLogs
                showTable={showDefault}
                tableData={devicelogstabledata}
                handleEditAction={(str) => str} />
              <TrafficLogs
                showTable={!showDefault}
                tableData={trafficlogstabledata}
                handleEditAction={(str) => str} />
            </div>
          </div>
        </ServerError>
      </Loading>
    );
  }
}

const mapStateToProps = (state) => ({ ...LogsSelector.baseSelector(state) });

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: getLogsData,
    toggleLogsTab: toggleTab,
    toggleFiltersButton: toggleFilters,
  }, dispatch);
  return actions;
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(Logs));

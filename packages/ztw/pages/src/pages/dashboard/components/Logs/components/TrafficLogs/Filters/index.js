/* eslint-disable jsx-a11y/label-has-for */
// @flow
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import DropDown from 'components/dropDown';
import {
  LOGS_FWD_METHOD,
  LOGS_ZIP,
  LOGS_DEST_IP,
  LOGS_EC_GROUP,
} from 'config';

import * as LogsSelector from 'ducks/logs/selectors';
import {
  getLogsData,
  toggleTab,
  toggleFilters,
} from 'ducks/logs';

export class Filters extends Component {
  static propTypes = {
    t: PropTypes.func,
    showFilters: PropTypes.bool,
    load: PropTypes.func,
  };

  static defaultProps = {
    t: (str) => str,
    showFilters: false,
    load: noop,
  };

  componentDidMount() {
    const { load } = this.props;
    load();
  }
  
  render() {
    const {
      showFilters,
      t,
    } = this.props;

    return (
      <div className={`logs-filter-container ${showFilters ? 'unhide' : 'hide'}`}>
        {/* filters */}
        <div className="filters-section">
          <div className="left-pane">
            <div className="filter-container">
              <div className="filter-label">
                <label htmlFor="fwdMethod">{t('FWD_TYPE')}</label>
              </div>
              <div className="filter-dropdown-left">
                <DropDown
                  items={LOGS_FWD_METHOD}
                  setValue={noop}
                  defaultValue={{
                    value: 'ZIA',
                    label: t('ZIA'),
                  }} />
              </div>
            </div>
            <div className="filter-container">
              <div className="filter-label">
                <label htmlFor="zIP">{t('ZSCALER_IP')}</label>
              </div>
              <div className="filter-dropdown-left">
                <DropDown
                  items={LOGS_ZIP}
                  setValue={noop}
                  defaultValue={{
                    value: '0',
                    label: t('SELECT_ZSCALER_IP'),
                  }} />
              </div>
            </div>
            <div className="filter-container">
              <div className="filter-label">
                <label htmlFor="destinationIP">{t('DESTINATION_IP')}</label>
              </div>
              <div className="filter-dropdown-left">
                <DropDown
                  items={LOGS_DEST_IP}
                  setValue={noop}
                  defaultValue={{
                    value: '0',
                    label: t('SELECT_DESTINATION_IP'),
                  }} />
              </div>
            </div>
          </div>
          <div className="right-pane">
            <div className="filter-container-right">
              <div className="filter-label-right">
                <label htmlFor="ecGroup">{t('CLOUD_CONNECTOR_GROUP')}</label>
              </div>
              <div className="filter-dropdown-left" id="eventTime">
                <DropDown
                  items={LOGS_EC_GROUP}
                  setValue={noop}
                  defaultValue={{
                    value: '0',
                    label: t('SELECT_CC_GROUP'),
                  }} />
              </div>
            </div>
          </div>
        </div>
        {/* buttons */}
        <div className="buttons">
          <button
            type="button"
            className="activate-button blue-button"
            // disabled={!status || status !== 'pending'}
            onClick={noop}>
            {t('APPLY_FILTER')}
          </button>
          <button
            type="button"
            className="clear-button"
            // disabled={!status || status !== 'pending'}
            onClick={noop}>
            {t('CLEAR_FILTERS')}
          </button>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({ ...LogsSelector.baseSelector(state) });

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: getLogsData,
    toggleLogsTab: toggleTab,
    toggleFiltersButton: toggleFilters,
  }, dispatch);
  return actions;
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(Filters));

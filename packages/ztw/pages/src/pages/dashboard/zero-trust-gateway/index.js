// @flow
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import { useTranslation } from 'react-i18next';
import { SubHeader } from 'components/label';
import NavTabs from 'components/navTabs';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  faSyncAlt,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  load, toggleConnectorMointoring, refreshCloudConnectorDetails,
  viewCloudConnectorDetails, applyFilters,
  handlePopupOpen, handleCloseApplianceNotification,
  handleResetDeviceId, handleResetShowTrafficFlow,
  handleSetLastUrl, handleCurrentPageChange,
} from 'ducks/ztGatewayDashboard';
import { loader } from 'ducks/cloudConfigurationAdvancedSettings';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import * as dashboardSelector from 'ducks/ztGatewayDashboard/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import Donut from 'components/pieChart/Donut';
import { BASE_LAYOUT, HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import {
  hasBsku, hasCsku, hasBCsku, isBranchType,
} from 'utils/helpers';
import { CONNECTOR_MONITORING_TABLE_CONFIGS } from 'ducks/ztGatewayDashboard/constants';
import ConfigTable from 'components/configTable/ConfigTableWithPagination';
import GeoView from '../components/GeoView';
import Filters from './Filters';

function ztGatewayDashboard(props) {
  const {
    accessPermissions,
    accessSubscriptions,
    actions,
    activeHealthData,
    connectortabledata,
    lastUrl,
    showCloudConnector,
    totalEcData,
    weblogTime,
  } = props;
  const { t } = useTranslation();

  useEffect(() => {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.ANALYZING_ZERO_TRUST_GATEWAY });
    
    if (lastUrl === 'cleanData') {
      actions.toggleConnectorMointoring();
    }
    actions.load(lastUrl === 'cleanData');
    return () => {
      actions.handleSetLastUrl('cleanData');
    };
  }, []);

  const getLoc = (row) => {
    let city = '-';
    let country = '-';
    if (row) {
      const { cityName, countryName } = row;
      city = cityName;
      country = countryName;
    }
    
    return city + ' , ' + country;
  };

  const getTableData = () => {
    const tableData = connectortabledata?.map((row) => {
      let haStatus = isBranchType(row?.deploymentType) || row?.deviceType === 'PHYSICAL' ? row.haStatus : '';
      const vmSize = (row?.deviceType === 'PHYSICAL' || row?.deployAsGateway) ? t('N/A') : row.vmSize;
      const autoScale = isBranchType(row?.deploymentType) || row?.deviceType === 'PHYSICAL' ? t('N/A') : String(row?.autoScale || 'FALSE').toUpperCase();
      
      if (row?.deployAsGateway) {
        haStatus = t('N/A');
      }
      // Region
      // Location Name
      // number of endpoint

      return {
        ...row,
        isZeroTrustGateway: true,
        ecId: row.ecId,
        ecName: row.name,
        location: row?.provData?.locationName,
        group: row.group,
        deploymentType: row.deploymentType || 'AWS',
        geo: getLoc(row.geoLocation),
        vmSize,
        status: t(row.healthStatus),
        autoScale,
        haStatus,
        errorCodes: row.errorCodes,
        isReadOnly: true,
        isDeletable: false,
        isEditable: false,
      };
    });
    return tableData;
  };
 
  const hasBCSubscription = hasBCsku(accessSubscriptions);
  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);

  if (!hasBSubscription && !hasCSubscription) {
    return <SubscriptionRequired />;
  }

  if (accessPermissions.EDGE_CONNECTOR_DASHBOARD === 'NONE') {
    return (
      <PermissionRequired />
    );
  }
  const bcTitle = () => {
    if (hasBCSubscription) return t('BRANCH_AND_CLOUD_CONNECTOR_MONITORING');
    if (hasBSubscription) return t('BRANCH_CONNECTOR_MONITORING');
    if (hasCSubscription) return t('CLOUD_CONNECTOR_MONITORING');
    return '';
  };

  return (
    <>
      <Loading {...props} />
      <HelpArticle article={HELP_ARTICLES.ANALYZING_ZERO_TRUST_GATEWAY} />
      <div className={`dashboard ${showCloudConnector ? 'hide' : 'unhide'}`}>
        <div className="dual-container">
          <h1 className="page-title">
            {t('DASHBOARD')}
          </h1>
          <div className="left-section">
            <span>
              {t('LAST_UPDATE')}
              {' '}
              {weblogTime}
            </span>
          </div>
          <div
            className="right-section"
            onClick={() => actions.load()}
            onKeyPress={() => actions.load()}
            type="button"
            role="presentation">
            <span>
              <FontAwesomeIcon
                className="far refresh"
                icon={faSyncAlt}
                size="lg" />
              {' '}
              {t('REFRESH')}
            </span>
          </div>
        </div>
        <div className="main-container provisioning-template-header">
          <div className="configuration-nav-tab">
            <NavTabs
              tabConfiguration={[{
                id: 'connector-monitoring',
                title: bcTitle(),
                to: `${BASE_LAYOUT}/dashboard/connector-monitoring`,
              },
              {
                id: 'traffic-monitoring',
                title: t('TRAFFIC_MONITORING'),
                to: `${BASE_LAYOUT}/dashboard/traffic-monitoring`,
              },
              {
                id: 'zero-trust-gateway',
                title: t('ZERO_TRUST_GATEWAY'),
                to: `${BASE_LAYOUT}/dashboard/zero-trust-gateway`,
              }]} />
          </div>
        </div>
        <Filters {...props} handleSubmit={actions.applyFilters} />
        <ServerError {...props}>
          <div className="pie-container">
            <Donut
              data={totalEcData}
              displayName={t('ENTITLEMENT_STATUS')}
              tooltip={t('ENTITLEMENT_STATUS_TOOLTIP')} />
            <Donut
              data={activeHealthData}
              displayName={t('HEALTH_STATUS')}
              tooltip={t('HEALTH_STATUS_TOOLTIP')} />
          </div>
          {!showCloudConnector
              && (
                <div className="dashboard-row map-content">
                  <SubHeader title={t('GEO_VIEW')} />
                  <GeoView
                    data={getTableData()}
                    actions={actions}
                    showConnectorToolTip
                    isZeroTrustGateway />
                </div>
              )}
          <div className="dashboard-row table-content cloud-monitoring-table">
            <ConfigTable
              {...CONNECTOR_MONITORING_TABLE_CONFIGS}
              // eslint-disable-next-line react/jsx-handler-names
              onHandleRowView={actions.viewCloudConnectorDetails}
              onHandleCurrentPageChange={actions.handleCurrentPageChange}
              pagination
              sizesPerPage={[50, 100, 150, 200, 250]}
              data={getTableData()} />
          </div>
        </ServerError>
      </div>
    </>
  );
}

const mapStateToProps = (state) => ({
  ...dashboardSelector.baseSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  accessDetailSubscriptions: loginSelectors.accessDetailSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    applyFilters,
    handleCloseApplianceNotification,
    handleCurrentPageChange,
    handlePopupOpen,
    handleResetDeviceId,
    handleResetShowTrafficFlow,
    handleSetLastUrl,
    load,
    loadAdvancedSettings: loader,
    refreshCloudConnectorDetails,
    toggleConnectorMointoring,
    viewCloudConnectorDetails,
  }, dispatch);
  return { actions };
};

ztGatewayDashboard.propTypes = {
  actions: PropTypes.shape({
    applyFilters: PropTypes.func,
    handleCurrentPageChange: PropTypes.func,
    handleSetLastUrl: PropTypes.func,
    load: PropTypes.func,
    refreshCloudConnectorDetails: PropTypes.func,
    toggleConnectorMointoring: PropTypes.func,
    viewCloudConnectorDetails: PropTypes.func,
  }),
  accessPermissions: PropTypes.shape({
    EDGE_CONNECTOR_DASHBOARD: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  activeHealthData: PropTypes.shape({}),
  cloudConnectorData: PropTypes.shape({
    deployAsGateway: PropTypes.bool,
  }),
  connectortabledata: PropTypes.arrayOf(PropTypes.shape()),
  currentPage: PropTypes.arrayOf(PropTypes.shape()),
  deviceStatuslogTime: PropTypes.string,
  geoData: PropTypes.arrayOf(PropTypes.shape({})),
  lastUrl: PropTypes.string,
  perm: PropTypes.string,
  showCloudConnector: PropTypes.bool,
  t: PropTypes.func,
  totalEcData: PropTypes.shape({}),
  weblogTime: PropTypes.string,
};

ztGatewayDashboard.defaultProps = {
  actions: {
    load: noop,
  },
  accessPermissions: {},
  accessSubscriptions: [],
  activeHealthData: {},
  cloudConnectorData: {},
  connectortabledata: null,
  currentPage: [],
  deviceStatuslogTime: '',
  geoData: [],
  lastUrl: '',
  perm: '',
  showCloudConnector: false,
  t: (str) => str,
  totalEcData: {},
  weblogTime: '',
};

export default connect(mapStateToProps, mapDispatchToProps)(ztGatewayDashboard);

import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { HELP_ARTICLES, BASE_LAYOUT } from 'config';
import HelpArticle from 'components/HelpArticle';
import LineItem from 'components/LineItem';
import {
  faArrowLeft,
  faSyncAlt,
} from '@fortawesome/pro-regular-svg-icons';
import {
  faExclamationCircle,
} from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import NavTabs from 'components/navTabs';
import {
  convertZeros, getIcon, getStatusIcon, fromMaskToCIDR, verifyConfigData,
} from 'utils/helpers';
import { isEmpty, orderBy } from 'utils/lodash';
import {
  BuildLan, BuildWan, BuildZiaOrZPAWanTraffic, BuildDirectTraffic,
} from './Components/index.js';

const getGeoInfo = (geoLocation) => {
  if (!geoLocation) return '';

  if (typeof geoLocation === 'string') {
    return geoLocation;
  } if (typeof geoLocation === 'object') {
    return `${geoLocation.cityName ? geoLocation.cityName : '-'}, ${geoLocation.countryCode ? geoLocation.countryCode : '-'}`;
  }
  return '';
};

const printIpList = (array) => {
  if (isEmpty(array)) return '---';
  if (typeof array === 'string') return convertZeros(array);
  if ((typeof array === 'object') && (array.length > 0)) {
    return array.map((ip) => (
      <p key={ip}>
        {convertZeros(ip)}
      </p>
    ));
  }

  return '';
};

export function CloudConnector(props) {
  const {
    showCloudConnector,
    cloudConnectorData,
    backToParent,
    refresh,
    deviceStatuslogTime,
    t,
  } = props;

  const {
    ecName,
    location,
    geoLocation,
    status,
    deploymentType,
    group,
    version,

    ziaGw = [],
    zpaBroker = [],
    internalGwIpAddr,
    managementNw,
    deviceDescription = '',
    configurationTemplate = '',
    lastTemplateConfigPushFailed = false,
    intfs = [],
    routes = [],
    tunnel = [],
    deviceModelNo = '',
    deviceName = '',
    deviceSerialNo = '',
    deviceType = '',
  } = cloudConnectorData;

  if (!showCloudConnector) return null;
  const plainRoutes = routes.map((x) => x.meta).flat();

  const wanConfig = orderBy(intfs.filter((x) => x.interfaceType === 'INTF_TYPE_WAN'), ['portId', 'vlanId']);
  const lanConfig = orderBy(intfs.filter((x) => x.interfaceType === 'INTF_TYPE_LAN'), ['portId', 'vlanId']);

  const local = localStorage.getItem('configData');
  const configData = (local && local.length) ? JSON.parse(local) : {};
  const enableTunnelMonitoring = verifyConfigData({ configData, key: 'enableTunnelMonitoring' });

  return (
    <>
      <HelpArticle article={HELP_ARTICLES.CLOUD_CONNECTOR_DETAILS} />
      <div className="dashboard deploy-as-gateway-info ">
        {/* navigation header */}
        <div className="title-container">
          <FontAwesomeIcon
            className="far title-back"
            icon={faArrowLeft}
            size="lg"
            onClick={() => backToParent()}
            role="button"
            aria-label="Back to Parent"
            tabIndex="0"
            onKeyPress={() => backToParent()} />
          <div className="title-nav">
            <span>
              {t('BRANCH_AND_CLOUD_MONITORING')}
            </span>
          </div>
          <div className="title-nav-right">
            <span style={{ paddingRight: '1em' }}>
              {t('>')}
            </span>
            <span>
              {getIcon(deploymentType, status, deviceType)}
              {' '}
              {ecName}
            </span>
            <span
              className="refresh space-between"
              onClick={() => refresh()}
              role="button"
              aria-label="Refresh"
              tabIndex="0"
              onKeyPress={() => refresh()}>
              <FontAwesomeIcon
                className="far refresh"
                icon={faSyncAlt}
                size="lg" />
              {' '}
              {t('REFRESH')}
            </span>
          </div>
        </div>
        {/* Tabs Section */}
        <div className="config-navagation-tab">
          {/* Tabs */}
          <div>
            <div className="nav-tabs space-between">
              <NavTabs
                tabConfiguration={[{
                  id: 'connector-monitoring',
                  title: t(['CENTOS', 'REDHAT_LINUX', 'MICROSOFT_HYPER_V', 'VMWARE_ESXI'].includes(deploymentType) || deviceType === 'PHYSICAL' ? 'BC_DETAILS' : 'CC_DETAILS'),
                  to: `${BASE_LAYOUT}/dashboard/connector-monitoring`,
                },
                {
                  id: 'traffic-monitoring',
                  title: t('TRAFFIC_FLOW'),
                  to: `${BASE_LAYOUT}/dashboard/traffic-monitoring`,
                }]} />
              {/* Status & Action */}
              <div className="status">
                {lastTemplateConfigPushFailed && (
                  <span className="last-config-template-failed">
                    <FontAwesomeIcon
                      className="last-config-template-failed-icon"
                      icon={faExclamationCircle}
                      size="lg" />
                    {t('LAST_CONFIG_TEMPLATE_PUSH_FAILED')}
                  </span>
                )}
                <span>
                  {t('LAST_UPDATE')}
                  {' '}
                  {deviceStatuslogTime}
                </span>
              </div>
            </div>

          </div>
        </div>

        {/* Top Row */}
        <div className="dashboard-detail-panel">
          <div className="side-header">
            {t('')}
          </div>
          <div className="content connector-monitoring">
            <div className="content-box-flex">
              {/* Left Segment */}
              <div className="right-border">
                <div className="content-header">
                  <div>
                    {t('GENERAL')}
                  </div>
                </div>
                <div className="container">
                  <LineItem label={t('OPERATIONAL_STATUS')} value={getStatusIcon(status)} styleValue="margin-left-0px" />
                  <LineItem label={t('DEVICE_MODEL')} value={deviceModelNo} />
                  <LineItem label={t('CONFIGURATION_TEMPLATE_NAME')} value={configurationTemplate} />
                  <LineItem label={t('LOCATION')} value={location} />
                  <LineItem label={t('GEO_LOCATION')} value={getGeoInfo(geoLocation)} />
                  <LineItem label={t('BC_GROUP')} value={group} />
                  <LineItem label={t('VERSION')} value={version} />
                  <LineItem label={t('DEPLOY_AS_GATEWAY')} value={getStatusIcon('Enabled')} />
                </div>
              </div>
              {/* Middle Segment */}
              <div className="right-border">
                <div className="content-header">
     
                </div>
                <div className="container">
                  <div className="key-bold">
                    <LineItem label={t('ZSCALER_GATEWAY_DETAILS')} value={internalGwIpAddr ? convertZeros(internalGwIpAddr) : ''} />
                  </div>
                  <LineItem label={t('ZIA_GATEWAY')} value={printIpList(ziaGw)} />
                  <LineItem label={t('ZPA_BROKER')} value={printIpList(zpaBroker)} />
                </div>
              </div>
              {/* Right Segment */}
              <div className="content-header" />
            </div>
          </div>
          { enableTunnelMonitoring && (
            <>
              {!isEmpty(tunnel) && !isEmpty(tunnel?.filter((x) => x?.fwdType === 'ZIA'))
              && (
                <div className="content connector-monitoring">
                  <div className="content-box-flex min-width-1280px">
                    <div className="right-border">
                      <div className="content-header">
                        {t('ZIA_TUNNEL')}
                      </div>
                      <div className="content connector-monitoring interfaces">
                        <BuildZiaOrZPAWanTraffic data={tunnel.filter((x) => x?.fwdType === 'ZIA')} t={t} />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {!isEmpty(tunnel) && !isEmpty(tunnel?.filter((x) => x?.fwdType === 'ZPA'))
              && (
                <div className="content connector-monitoring">
                  <div className="content-box-flex min-width-1280px">
                    <div className="right-border">
                      <div className="content-header">
                        {t('ZPA_TUNNEL')}
                      </div>
                      <div className="content connector-monitoring interfaces">
                        <BuildZiaOrZPAWanTraffic data={tunnel?.filter((x) => x?.fwdType === 'ZPA')} t={t} isZPA />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {!isEmpty(tunnel) && !isEmpty(tunnel?.filter((x) => x?.fwdType === 'DIRECT'))
              && (
                <div className="content connector-monitoring">
                  <div className="content-box-flex min-width-1280px">
                    <div className="right-border">
                      <div className="content-header">
                        {t('DIRECT')}
                      </div>
                      <div className="content connector-monitoring interfaces">
                        <BuildDirectTraffic data={tunnel?.filter((x) => x?.fwdType === 'DIRECT')} t={t} />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
          
          <div className="content connector-monitoring">
            <div className="content-box-flex">
              {/* Left Segment */}
              <div className="right-border">
                <div className="content-header">
                  <div>
                    {t('SYSTEM')}
                  </div>
                </div>
                <div className="container">
                  <div>
                    {t('DEVICE_INFO')}
                  </div>
                  <LineItem label={t('DEVICE_SERIAL_NUMBER')} value={deviceSerialNo} />
                  <LineItem label={t('DEVICE_NAME')} value={deviceName} />
                  
                  <div className="description">
                    <LineItem label={t('DESCRIPTION')} value={deviceDescription || '---'} />
                  </div>
                  {/* <LineItem label={t('DEVICE_TYPE')} value={location} />
                  <LineItem label={t('MODEL_NUMBER')} value={getGeoInfo(geoLocation)} /> */}
                </div>
              </div>
              {/* Middle Segment */}
              <div className="right-border">
                <div className="content-header">

                </div>
                <div className="container">
                  <div>
                    {t('MANAGEMENT_INTERFACE')}
                  </div>
                  <LineItem label={t('PORT')} value={(deviceModelNo === 'ZT800') ? 'GE3' : 'GE1'} />
                  <LineItem label={t('SHUTDOWN')} value={managementNw?.shutdownOn ? 'Yes' : 'No'} />
                  <LineItem label={t('DHCP')} value={managementNw?.nwType === 'MANUAL' ? getStatusIcon('Disabled') : getStatusIcon('Enabled')} />
                  <LineItem label={t('IP_ADDRESS')} value={(managementNw?.managementIp?.ipStart) ? `${convertZeros(managementNw?.managementIp?.ipStart)}/${fromMaskToCIDR(managementNw?.managementIp?.netmask)}` : '-'} />
                  <LineItem label={t('DEFAULT_GATEWAY')} value={(managementNw?.defaultGw) ? convertZeros(managementNw?.defaultGw) : '-'} />
                  <LineItem label={t('PRIMARY_DNS_SERVER')} value={managementNw?.dnsIp[0]} />
                  <LineItem label={t('SECONDARY_DNS_SERVER')} value={managementNw?.dnsIp[1]} />
                  <LineItem label={t('NAT_IP_ADDRESS')} value={(managementNw?.natIp) ? convertZeros(managementNw?.natIp) : '-'} />
                  <LineItem label={t('PORT_STATUS')} value={getStatusIcon(managementNw?.portStatusUp ? 'Up' : 'Down')} />
                </div>
              </div>
              {/* Right Segment */}
              <div className="content-header" />
            </div>
          </div>

          <div className="content connector-monitoring">
            <div className="content-box-flex min-width-1280px">
              {/* Left Segment */}
              <div className="right-border">
                <div className="content-header">
                  {t('WAN')}
                </div>
                <div className="content connector-monitoring interfaces">
                  {BuildWan(wanConfig, cloudConnectorData, t)}
                </div>
              </div>
            </div>
          </div>

          <div className="content connector-monitoring">
            <div className="content-box-flex min-width-1120px">
              {/* Left Segment */}
              <div className="right-border">
                <div className="content-header">
                  {t('LAN')}
                </div>
                <div className="content connector-monitoring interfaces">
                  {BuildLan(lanConfig, cloudConnectorData, t)}
                </div>
              </div>
            </div>
          </div>

          <div className="content connector-monitoring margin-bottom-50px">
            <div className="content-box-flex">
              {/* Left Segment */}
              <div className="right-border">
                <div className="content-header">
                  {t('ROUTING')}
                </div>
                <div className="content connector-monitoring interfaces">
                  
                  {plainRoutes.map((x, idx) => (
                    // eslint-disable-next-line react/no-array-index-key
                    <div key={idx} className="last-row-interface min-height-32px no-backgrond-color full-width position-relative">
                      <span className="row-item-1">
                        <span className="light-color">
                          {idx === 0 && t('STATIC_ROUTE')}
                        </span>
                      </span>
                    
                      <span className="row-item-2">
                        <span className="light-color">{`${t('ROUTE')}   `}</span>
                        {x?.route}
                      </span>
                      <span className="row-item-3">
                        <span className="light-color">{`${t('GATEWAY')}   `}</span>
                        {x?.gateway}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </>
  );
}

CloudConnector.propTypes = {
  cloudConnectorData: PropTypes.shape({
    ecName: PropTypes.string,
    location: PropTypes.string,
    geoLocation: PropTypes.string,
    status: PropTypes.string,
    deploymentType: PropTypes.string,
    group: PropTypes.string,
    version: PropTypes.string,

    ziaGw: PropTypes.arrayOf(),
    zpaBroker: PropTypes.arrayOf(),
    internalGwIpAddr: PropTypes.string,
    managementNw: PropTypes.shape({
      managementIp: PropTypes.shape({
        ipStart: PropTypes.string,
        netmask: PropTypes.string,
      }),
      defaultGw: PropTypes.string,
      dnsIp: PropTypes.arrayOf(PropTypes.string),
      natIp: PropTypes.string,
      nwType: PropTypes.string,
      portStatusUp: PropTypes.bool,
      shutdownOn: PropTypes.bool,
    }),
    deviceDescription: PropTypes.string,
    configurationTemplate: PropTypes.string,
    lastTemplateConfigPushFailed: PropTypes.bool,
    intfs: PropTypes.arrayOf(),
    routes: PropTypes.arrayOf(),
    tunnel: PropTypes.arrayOf(),
    deviceModelNo: PropTypes.string,
    deviceName: PropTypes.string,
    deviceSerialNo: PropTypes.string,
    deviceType: PropTypes.string,
  }),
  showCloudConnector: PropTypes.bool,
  backToParent: PropTypes.func,
  deviceStatuslogTime: PropTypes.string,
  t: PropTypes.func,
  refresh: PropTypes.func,
};

CloudConnector.defaultProps = {
  cloudConnectorData: {
    lastModifiedTime: '',
    managementNw: {
      managementIp: {
        ipEnd: '',
        ipStart: '',
      },
      defaultGw: '',
    },
    tunnel: [],
  },
  showCloudConnector: false,
  backToParent: null,
  deviceStatuslogTime: '',
  t: (str) => str,
  refresh: (str) => str,
};

export default withTranslation()(CloudConnector);

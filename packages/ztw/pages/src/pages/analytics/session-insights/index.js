import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
// import html2canvas from 'html2canvas';
// import JSpdf from 'jspdf';
import { faAngleLeft, faAngleRight } from '@fortawesome/pro-regular-svg-icons';
import { Field, reduxForm } from 'redux-form';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Print from 'components/button/Print';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import PlainModal from 'components/modal/PlainModal';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasBsku, hasCsku } from 'utils/helpers';
import * as loginSelectors from 'ducks/login/selectors';
import * as SessionInsightsSelectors from 'ducks/sessionInsights/selectors';
import {
  loader,
  toggleAccordion,
  togglePrintPreview,
  getPreselectedFilters,
  applyFilters,
  updateMenu,
} from 'ducks/sessionInsights';
import { GenericFilters, InsightsWidget } from './components';

import { DataFilter } from './components/GenericFilters/Dropdown';
import PrintPreview from './components/PrintPreview';

export class SessionInsightsForm extends Component {
  static propTypes = {
    t: PropTypes.func,
    showAccordion: PropTypes.bool,
    showPreview: PropTypes.bool,
    handleAccordion: PropTypes.func,
    togglePreview: PropTypes.func,
    download: PropTypes.func,
    load: PropTypes.func,
    loading: PropTypes.bool,
    actions: PropTypes.shape(),
    downloadCsvData: PropTypes.arrayOf(PropTypes.string),
    accessPermissions: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  };

  static defaultProps = {
    t: (str) => str,
    showAccordion: false,
    showPreview: false,
    handleAccordion: noop,
    togglePreview: noop,
    download: noop,
    load: noop,
    loading: false,
    actions: {},
    downloadCsvData: [],
    accessPermissions: {},
    accessSubscriptions: [],
  };

  componentDidMount() {
    PersistentStorage.setItem({
      [LS_HELP_ARTICLE]: HELP_ARTICLES.ANALYTICS_INSIGHTS,
    });
    const { load } = this.props;
    load();
  }

  handleExportPDF = () => {
    const { togglePreview } = this.props;
    togglePreview(true);
  };

  render() {
    const {
      t,
      showAccordion,
      handleAccordion,
      showPreview,
      history,
      loading,
      accessPermissions,
      accessSubscriptions,
    } = this.props;

    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPermissions.EDGE_CONNECTOR_DASHBOARD === 'NONE') {
      return <PermissionRequired accessPermissions={accessPermissions} />;
    }

    if (!history.length && loading) return <Loading loading={loading} />;

    return (
      <>
        <HelpArticle article={HELP_ARTICLES.ANALYTICS_INSIGHTS} />
        <div
          className="accordion-container"
          id="session-details"
          style={showPreview ? { display: 'none' } : { display: 'inline' }}>
          <div className="outer-layer">
            <div
              className={`slide ${showAccordion ? 'filter-expand' : 'filter'}`}>
              <div className="filter-expand-width">
                <ServerError {...this.props}>
                  <GenericFilters {...this.props} />
                </ServerError>
              </div>
            </div>
            <div className={`${showAccordion ? 'filter-expand-space ' : 'hidden'}`} />
            <ServerError {...this.props}>
              <div
                className="knob-holder"
                onClick={() => handleAccordion()}
                onKeyPress={() => handleAccordion()}
                type="button"
                role="presentation">
                <FontAwesomeIcon
                  className={`fad fa-stack-1x knob ${
                    showAccordion ? 'hide' : ''
                  }`}
                  // className="fad fa-stack-1x knob"
                  icon={faAngleRight}
                  size="lg" />
                <FontAwesomeIcon
                  className={`fad fa-stack-1x knob ${
                    showAccordion ? '' : 'hide'
                  }`}
                  // className="fad fa-stack-1x knob"
                  icon={faAngleLeft}
                  size="lg" />
              </div>
              <div
                className={`slide ${
                  showAccordion ? 'tablesec-shrink' : 'tablesec'
                }`}>
                <div className="main-insights-container">
                  <div className="header">
                    <span className="component-header">
                      {t('SESSION_INSIGHTS')}
                    </span>
                  </div>
                  <div className="printView">
                    <Print label="" onActionCb={this.handleExportPDF} />
                  </div>
                  <div className="data-filter-container">
                    <Field
                      id="addFilter"
                      name="addFilter"
                      component={DataFilter}
                      show />
                  </div>
                  <ServerError {...this.props}>
                    <InsightsWidget {...this.props} />
                  </ServerError>
                </div>
              </div>
            </ServerError>
          </div>
        </div>
        <ServerError {...this.props}>
          <PlainModal isOpen={showPreview} contentClass="no-max-height">
            <PrintPreview showPreview={showPreview} {...this.props} />
          </PlainModal>
        </ServerError>
      </>
    );
  }
}

SessionInsightsForm.propTypes = {
  t: PropTypes.func,
  sessionlogstabledata: PropTypes.arrayOf(PropTypes.shape()),
  showAccordion: PropTypes.bool,
  handleAccordion: PropTypes.func,
  download: PropTypes.func,
  load: PropTypes.func,
  actions: PropTypes.shape(),
  downloadCsvData: PropTypes.arrayOf(PropTypes.string),
  history: PropTypes.arrayOf(PropTypes.shape({})),
};

SessionInsightsForm.defaultProps = {
  t: (str) => str,
  sessionlogstabledata: null,
  showAccordion: false,
  handleAccordion: noop,
  download: noop,
  load: noop,
  actions: {},
  downloadCsvData: [],
  history: [],
};

const mapStateToProps = (state) => ({
  ...SessionInsightsSelectors.default(state),
  ...SessionInsightsSelectors.formValuesSelector(state),
  filters: SessionInsightsSelectors.addFilterDropdownSelector(state),
  initialValues: {
    timeFrame: SessionInsightsSelectors.timeFrameDefaultSelector(state),
    chartType: 'line',
    units: 'SESSIONS',
    ecInstanceType: 'INCLUDE',
    ecVmType: 'INCLUDE',
    ecGroupType: 'INCLUDE',
    platformType: 'INCLUDE',
    awsRegionType: 'INCLUDE',
    availabilityZoneType: 'INCLUDE',
    azureRegionType: 'INCLUDE',
    azureAvailabilityZoneType: 'INCLUDE',
    locationType: 'INCLUDE',
  },
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators(
    {
      load: loader,
      // load: getLogsData,
      handleAccordion: toggleAccordion,
      togglePreview: togglePrintPreview,
      togglePrintPreview,
      getPreselectedFilters,
      applyFilters,
      updateMenu,
    },
    dispatch,
  );
  return actions;
};

const SessionInsights = connect(
  mapStateToProps,
  mapDispatchToProps,
)(
  reduxForm({
    form: 'sessionInsightsFiltersForm',
    destroyOnUnmount: false,
    // forceUnregisterOnUnmount: true,
    initialValues: {
      timeFrame: { id: 'current_day', value: 'current_day', label: 'CURRENT_DAY' },
      chartType: 'line',
      units: 'SESSIONS',
      ecInstanceType: 'INCLUDE',
      ecVmType: 'INCLUDE',
      ecGroupType: 'INCLUDE',
      platformType: 'INCLUDE',
      awsRegionType: 'INCLUDE',
      availabilityZoneType: 'INCLUDE',
      azureRegionType: 'INCLUDE',
      azureAvailabilityZoneType: 'INCLUDE',
      locationType: 'INCLUDE',
    },
    asyncBlurFields: [],
  })(withTranslation()(SessionInsightsForm)),
);

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(SessionInsights));

/* eslint-disable react/jsx-handler-names */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Loading from 'components/spinner/Loading';
import * as DNSLogsSelectors from 'ducks/dnsLogs/selectors';
import * as CCAdvancedSettings from 'ducks/cloudConfigurationAdvancedSettings/selectors';
import TableWrapper from 'components/tablePro/TableWrapper';
import CustomizeColumns from 'components/tablePro/CustomizeColumns';
import CustomizeColsHeader from 'components/tablePro/CustomizeColsHeader';
import HeaderRowSearchTool from 'components/tablePro/HeaderRowSearchTool';
import ReactTooltip from 'react-tooltip';
import {
  updateMenu,
  handleOnSearchFilter,
  handleOnClearFilter,
  handleOnSortClick,
} from 'ducks/dnsLogs';

class DNSLogsTable extends Component {
  static propTypes = {
    tableData: PropTypes.arrayOf(PropTypes.shape()),
    actions: PropTypes.shape({
      load: PropTypes.func,
    }),
    showForm: PropTypes.bool,
    dnsColumns: PropTypes.arrayOf(PropTypes.shape()),
    t: PropTypes.func,
    searchData: PropTypes.string,
    showSearch: PropTypes.bool,
    sortable: PropTypes.number,
    accountIdEnabled: PropTypes.bool,
    subIdEnabled: PropTypes.bool,
    projectIdEnabled: PropTypes.bool,
  };

  static defaultProps = {
    tableData: [],
    actions: {
      load: null,
    },
    showForm: null,
    dnsColumns: [],
    t: (str) => str,
    searchData: '',
    showSearch: false,
  };

  state = {
    showComponent: false,
    searchField: '',
  };

  componentDidUpdate() {
    ReactTooltip.rebuild();
  }

  handleOnMouseOverCb = () => this.setState(() => ({ showComponent: true }));
  
  onLeave = () => {
    this.setState({ showComponent: false });
  };

  handleSearchField = (value) => {
    this.setState({ searchField: value });
  };
  
  getColumns = () => {
    const {
      dnsColumns,
      actions,
      t,
      accountIdEnabled,
      subIdEnabled,
      projectIdEnabled,
      searchData,
      showSearch,
    } = this.props;
    const {
      editRowHandler,
    } = actions;
    const {
      searchField,
    } = this.state;

    dnsColumns.map((i) => {
      // eslint-disable-next-line no-param-reassign
      i.headerRenderer = i.searchable ? (
        <span className="display-flex-space-between" data-tip={t(i.name)} data-for="header-tooltip">
          <span className="no-overflow-text">
            {t(i.name)}
          </span>
          <HeaderRowSearchTool
            fieldName={i.key}
            showSearchBox={showSearch}
            searchField={searchField}
            searchData={searchData}
            onStartSearch={this.handleSearchField}
            onClearFilter={actions.handleOnClearFilter}
            onSearchFilter={actions.handleOnSearchFilter} />
        </span>
      ) : (
        <span className="display-flex-space-between" data-tip={t(i.name)} data-for="header-tooltip">
          {t(i.name)}
        </span>
      );
      return i;
    });
    const lastColumn = dnsColumns[dnsColumns.length - 1];
    const { showComponent } = this.state;
    const lastColWithElipsis = {
      ...lastColumn,
      name: '',
      visible: true,
      draggable: false,
      resizable: false,
      checkbox: false,
      sortable: false,
      frozen: false,
      width: 40,
      headerRenderer: (
        <CustomizeColsHeader
          props={this}
          handleLeave={this.onLeave}
          showComponent={showComponent}
          onMouseOverCb={this.handleOnMouseOverCb} />
      ),
      // formatter: <RowActions showSubEdit />,
      events: {
        onClick(ev, row) {
          editRowHandler(row);
        },
      },
    };
    dnsColumns[dnsColumns.length - 1] = lastColWithElipsis;
    return [...dnsColumns.filter((x) => !(x.key === 'accountId' && !accountIdEnabled)
        && !(x.key === 'subscriptionId' && !subIdEnabled)
        && !(x.key === 'projectId' && !projectIdEnabled)),
    ];
  };

  render() {
    const {
      tableData,
      actions,
      sortable,
    } = this.props;

    const { showComponent } = this.state;
    const columns = this.getColumns();

    return (
      <Loading {...this.props}>
        <div className="table-layout-header">
          <ReactTooltip place="top" type="light" effect="solid" data-multiline="true" id="header-tooltip" className="react-tooltip" />
          <TableWrapper
            key={sortable}
            initialRows={tableData}
            initialColumns={columns}
            formatTimeStamp={['eventTimestamp', 'logTimestamp']}
            updateMenu={actions.updateMenu}
            handleOnSortClick={actions.handleOnSortClick}
            showNoDataValue />
          <CustomizeColumns
            // hasSelectAll
            initialItems={columns}
            handleLeave={this.onLeave}
            showComponent={showComponent}
            dragnDrop={actions.updateMenu} />
        </div>
      </Loading>
    );
  }
}

const mapStateToProps = (state) => ({
  ...DNSLogsSelectors.baseSelector(state),
  accountIdEnabled: CCAdvancedSettings.accountId(state),
  subIdEnabled: CCAdvancedSettings.subscriptionId(state),
  projectIdEnabled: CCAdvancedSettings.projectId(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateMenu,
    handleOnSearchFilter,
    handleOnClearFilter,
    handleOnSortClick,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(DNSLogsTable));

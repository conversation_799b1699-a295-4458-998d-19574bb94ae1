/* eslint-disable react/jsx-handler-names */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { NavLink } from 'react-router-dom';
import { isEmpty } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRedo } from '@fortawesome/pro-solid-svg-icons';
import { faFilter } from '@fortawesome/pro-light-svg-icons';
import { BASE_LAYOUT } from 'config';
import {
  Field,
  change,
  reduxForm,
} from 'redux-form';
import NavTabs from 'components/navTabs';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import ECRadioGroup from 'components/ecRadioGroup';
import * as DnsLogsSelectors from 'ducks/dnsLogs/selectors';
import {
  handleStartOver, getPreselectedFilters, handleNoOfRecords,
  handleClearFilters, handleRemoveFilter, handleSelectedAddFilter, applyFilters,
  validatePortFrom, validatePortTo, validateInbytesFrom, validateInbytesTo,
  validateOutbytesFrom, validateOutbytesTo, validateRequestDurationFrom, validateRequestDurationTo,
  handleToggleDownloadCSV,
} from 'ducks/dnsLogs';
import ClearFilters from './ClearFilters';

import {
  AddFilter,
  LogsTimeFrame,
  Location,
  RequestedDomain,
  RequestAction,
  DnsRequestType, // .... Mulitselect Enum → DnsRequestType.js

  ProtocolType,
  RequestDuration,
  EcName,
  DnsGwFlag,
  DnsGwName,
  DnsRuleName,
  Inbytes,
  Outbytes,
  EcInstance,
  EcVM,
  EcGroup,
  ForwardingMethod,
  Platform,
  AwsAccountId,
  AwsRegion,
  AwsZone,
  AzureSubscriptionId,
  AzureRegion,
  AzureZone,
  GcpProjectId,
  GcpRegion,
  GcpZone,
} from './Dropdown';

class DnsFiltersForm extends PureComponent {
  componentDidMount() {
    const { actions, initialValues } = this.props;
    actions.getPreselectedFilters(initialValues);
  }

  render() {
    const {
      showAccordion,
      actions,
      noOfRecords,
      downloadCSV,
      t,
      formSyncErrors,
    } = this.props;
    const disableApply = !isEmpty(formSyncErrors);

    if (!showAccordion) return null;

    return (
      <div className="expander">
        <div className="filters-container">
          <form onSubmit={actions.applyFilters}>
            {/* Header */}
            <div className="header">
              {/* <div className="title">
                <span>
                  {t('LOGS')}
                </span>
              </div> */}
              <div className="title">
                <span>
                  <NavTabs
                    tabConfiguration={[
                      {
                        id: 'dns-insights',
                        title: t('INSIGHTS'),
                        to: `${BASE_LAYOUT}/analytics/dnsinsights`,
                      },
                      {
                        id: 'dns-logs',
                        title: t('LOGS'),
                        to: `${BASE_LAYOUT}/analytics/dnslogs`,
                      }]} />
                </span>
              </div>
            </div>
            <div className="separator-line-header" />
            {/* TimeFrame */}
            <div className="filter-box">
              <div className="filter-sideheader">
                <span>{t('TIME_FRAME')}</span>
              </div>
              <div className="filter-card-small">
                <Field
                  id="timeFrame"
                  name="timeFrame"
                  component={LogsTimeFrame}
                  parse={(value) => value.id} />
              </div>
            </div>
            <div className="separator-line" />
            {/* # of Download CSV Toggle Displayed */}
            <div className="filter-box">
              <div className="filter-card-small download-display">
                <ECRadioGroup
                  id="downloadCSV"
                  name="downloadCSV"
                  styleClass="full-width"
                  onChange={actions.handleToggleDownloadCSV}
                  options={[{
                    name: 'downloadCSV', value: 'DOWNLOAD', checked: downloadCSV === 'DOWNLOAD', label: t('DOWNLOAD_CSV'),
                  },
                  {
                    name: 'downloadCSV', value: 'DISPLAY', checked: downloadCSV === 'DISPLAY', label: t('DISPLAY'),
                  }]} />
              </div>
            </div>
            <div className="separator-line" />
            {/* # of Records Displayed */}
            <div className={`filter-box ${downloadCSV === 'DOWNLOAD' ? 'hidden' : ''}`}>
              <div className="filter-sideheader">
                <span>{t('NUMBER_OF_RECORDS_DISPLAYED')}</span>
              </div>
              <div className="filter-card-small-log">
                <ECRadioGroup
                  id="noOfRecords"
                  name="noOfRecords"
                  styleClass="full-width"
                  onChange={actions.handleNoOfRecords}
                  options={[{
                    name: 'noOfRecords', value: '1000', checked: noOfRecords === '1000', label: t('1k'),
                  },
                  {
                    name: 'noOfRecords', value: '5000', checked: noOfRecords === '5000', label: t('5k'),
                  },
                  {
                    name: 'noOfRecords', value: '10000', checked: noOfRecords === '10000', label: t('10k'),
                  },
                  {
                    name: 'noOfRecords', value: '25000', checked: noOfRecords === '25000', label: t('25k'),
                  }]} />
              </div>
            </div>
            <div className="separator-line" />
            {/* Select Filters */}
            <div className="filter-box">
              <div className="side-header">
                <div className="filter-sideheader">
                  <FontAwesomeIcon icon={faFilter} />
                  {' '}
                  <span>{t('FILTERS')}</span>
                </div>
                <div className="filter-card-large">
                  <div className="filter-container">
                    <Field
                      id="addFilter"
                      name="addFilter"
                      component={AddFilter}
                      show />
                  </div>
                </div>
              </div>
              <div className="filter-card-large">
                <AwsAccountId {...this.props} />
                <AwsZone {...this.props} />
                <AwsRegion {...this.props} />
                <AzureZone {...this.props} />
                <AzureRegion {...this.props} />
                <AzureSubscriptionId {...this.props} />
                <EcGroup {...this.props} />
                <EcInstance {...this.props} />
                <EcName {...this.props} />
                <EcVM {...this.props} />
                <ForwardingMethod {...this.props} />
                <GcpZone {...this.props} />
                <GcpProjectId {...this.props} />
                <GcpRegion {...this.props} />
                <DnsRequestType {...this.props} />
                <DnsRuleName {...this.props} />
                <DnsGwName {...this.props} />
                <DnsGwFlag {...this.props} />
                <Inbytes {...this.props} />
                <Location {...this.props} />
                <Outbytes {...this.props} />
                <Platform {...this.props} />
                <ProtocolType {...this.props} />
                <RequestAction {...this.props} />
                <RequestDuration {...this.props} />
                <RequestedDomain {...this.props} />
              </div>
            </div>
            {/* Apply */}
            <div className="external-id-buttons apply-filters">
              <button
                type="button"
                disabled={disableApply}
                className="primary-button"
                onClick={actions.applyFilters}>
                {t('APPLY_FILTERS')}
              </button>
              <button
                type="button"
                onClick={actions.handleStartOver}
                className="secondary-button">
                <FontAwesomeIcon icon={faRedo} />
                <span className="refresh-text">{t('RESET')}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }
}

DnsFiltersForm.propTypes = {
  showAccordion: PropTypes.bool,
  actions: PropTypes.shape({}),
  noOfRecords: PropTypes.string,
  initialValues: PropTypes.shape({}),
  showFilters: PropTypes.shape({}),
  filterFormValues: PropTypes.shape(),
  downloadCSV: PropTypes.string,
  t: PropTypes.func,
  formSyncErrors: PropTypes.shape({}),
};

DnsFiltersForm.defaultProps = {
  showAccordion: false,
  actions: {},
  noOfRecords: '',
  initialValues: {},
  showFilters: {},
  filterFormValues: {},
  downloadCSV: 'DOWNLOAD',
  t: (str) => str,
  formSyncErrors: {},
};

const mapStateToProps = (state) => ({
  ...DnsLogsSelectors.default(state),
  filterFormValues: DnsLogsSelectors.formValuesSelector(state),
  formMeta: DnsLogsSelectors.formMetaSelector(state),
  formSyncErrors: DnsLogsSelectors.formSyncErrorsSelector(state),
  filters: DnsLogsSelectors.addFilterDropdownSelector(state),
  initialValues: {
    timeFrame: { id: 'last_24_hrs', value: 'last_24_hrs', label: 'LAST_24_HOURS' },
    noOfRecords: '1000',
    ecNameEmpty: 'yes',
    requestedDomainEmpty: 'yes',
    ecNameMatchType: 'EXACT_MATCH',
    requestedDomainMatchType: 'EXACT_MATCH',
    deviceAppVersionMatchType: 'EXACT_MATCH',
    deviceHostnameMatchType: 'EXACT_MATCH',
    deviceModelMatchType: 'EXACT_MATCH',
    deviceNameMatchType: 'EXACT_MATCH',
    deviceOsTypeMatchType: 'EXACT_MATCH',
    deviceOsVersionMatchType: 'EXACT_MATCH',
    deviceOwnerMatchType: 'EXACT_MATCH',
    ecGroupNameMatchType: 'EXACT_MATCH',
    ecVmNameMatchType: 'EXACT_MATCH',
    resolvedIpOrNameMatchType: 'EXACT_MATCH',
    resolverIpOrNameMatchType: 'EXACT_MATCH',
    vpcNameMatchType: 'EXACT_MATCH',
    ecInstanceType: 'INCLUDE',
    ecVmType: 'INCLUDE',
    ecGroupType: 'INCLUDE',
    platformType: 'INCLUDE',
    awsRegionType: 'INCLUDE',
    zoneType: 'INCLUDE',
    azureRegionType: 'INCLUDE',
    azureAvailabilityZoneType: 'INCLUDE',
    locationType: 'INCLUDE',
  },
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateFormState: change,
    handleStartOver,
    getPreselectedFilters,
    handleNoOfRecords,
    handleClearFilters,
    handleRemoveFilter,
    handleSelectedAddFilter,
    applyFilters,
    validatePortFrom,
    validatePortTo,
    validateInbytesFrom,
    validateInbytesTo,
    validateOutbytesFrom,
    validateOutbytesTo,
    validateRequestDurationFrom,
    validateRequestDurationTo,
    handleToggleDownloadCSV,
  }, dispatch);

  return {
    actions,
  };
};

const GenericFilters = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'dnsFilersForm',
  // destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  initialValues: {
    timeFrame: { id: 'last_24_hrs', value: 'last_24_hrs', label: 'LAST_24_HOURS' },
    noOfRecords: '1000',
    ecNameMatchType: 'EXACT_MATCH',
    deviceAppVersionMatchType: 'EXACT_MATCH',
    deviceHostnameMatchType: 'EXACT_MATCH',
    deviceModelMatchType: 'EXACT_MATCH',
    deviceNameMatchType: 'EXACT_MATCH',
    deviceOsTypeMatchType: 'EXACT_MATCH',
    deviceOsVersionMatchType: 'EXACT_MATCH',
    deviceOwnerMatchType: 'EXACT_MATCH',
    ecGroupNameMatchType: 'EXACT_MATCH',
    ecVmNameMatchType: 'EXACT_MATCH',
    requestedDomainMatchType: 'EXACT_MATCH',
    resolvedIpOrNameMatchType: 'EXACT_MATCH',
    resolverIpOrNameMatchType: 'EXACT_MATCH',
    vpcNameMatchType: 'EXACT_MATCH',
    ecInstanceType: 'INCLUDE',
    ecVmType: 'INCLUDE',
    ecGroupType: 'INCLUDE',
    platformType: 'INCLUDE',
    awsRegionType: 'INCLUDE',
    zoneType: 'INCLUDE',
    azureRegionType: 'INCLUDE',
    azureAvailabilityZoneType: 'INCLUDE',
    locationType: 'INCLUDE',
  },
})(withTranslation()(DnsFiltersForm)));

export default GenericFilters;

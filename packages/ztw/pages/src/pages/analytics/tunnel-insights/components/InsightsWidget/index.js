// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import GenericErrorMessage from 'components/errors/ServerError/GenericErrorMessage';
import HorizontalBar from 'components/barChart/HorizontalBar';
import InsightsPieWidget from 'components/pieChart/InsightsPieWidget';
import LineChartInsights from 'components/lineChartInsights';

import * as TunnelInsightsSelectors from 'ducks/tunnelInsights/selectors';
import {
  updateMenu, drilldownSessionLogs, setDataTypeSearchString, handleSelectedDataTypeFilter,
} from 'ducks/tunnelInsights';
import { DataFilterDrillDown } from '../GenericFilters/Dropdown';
import History from '../History';
import InsightsTable from '../InsightsTable';

const getWidget = (label, props) => {
  const {
    actions, reportData, trendData,
    totalTrendDataKeys,
  } = props;
  const htData = reportData;
  let ht;
  if (htData.length === 0) {
    ht = '40em';
  } else if (htData.length > 5) {
    ht = 10 + (htData.length * 2) + 'em';
  } else {
    ht = 10 + (htData.length) + 'em';
  }

  switch (true) {
  case label === 'bar':
    return (
      <HorizontalBar
        {...props}
        height={ht}
        handleAssetDetails={actions.updateMenu}
        dataSequence={reportData}
        barKey={['total']}
        clickMoreInfo={actions.drilldownSessionLogs}
        DrillDownAction={DataFilterDrillDown} />
    );
  case label === 'pie':
    return (
      <div style={{ height: '525px' }}>
        <InsightsPieWidget
          data={reportData}
          colors={['#90D9F4', '#9FD67F', '#FFD155', '#D16464', '#C1BD82', '#DA80BC']}
          innerText="KB"
          legendsPos="bottom-left"
          onSearchString={actions.handleSearchString}
          onClickCb={actions.handleSelectedDataTypeFilter}
          clickMoreInfo={actions.drilldownSessionLogs}
          DrillDownAction={DataFilterDrillDown} />
      </div>
    );
  case label === 'line':
    return (
      <LineChartInsights
        {...props}
        data={trendData}
        dataKeys={totalTrendDataKeys}
        height="525"
        clickMoreInfo={actions.drilldownSessionLogs}
        DrillDownAction={DataFilterDrillDown} />
    );
  case label === 'table':
    return (
      <InsightsTable
        {...props}
        clickMoreInfo={actions.drilldownSessionLogs}
        DrillDownAction={DataFilterDrillDown} />
    );
  default:
    return <GenericErrorMessage {...props} />;
  }
};

export function InsightsWidget(props) {
  const { history, cardId } = props;

  // if (!history.length) return null;

  if (!history.length) {
    return (
      <div className="insights-container">
        <div style={{ height: '41em' }}>
          <GenericErrorMessage {...props} />
        </div>
      </div>
    );
  }
  // eslint-disable-next-line prefer-destructuring
  const chart = history[cardId].chart;

  return (
    <ServerError {...props}>
      <div className="insights-container">
        <div style={{ height: '41em' }}>
          <Loading {...props}>
            {getWidget(chart, props)}
          </Loading>
        </div>
      </div>
      <History {...props} />
    </ServerError>
  );
}

InsightsWidget.propTypes = {
  t: PropTypes.func,
  showFilters: PropTypes.shape({}),
  actions: PropTypes.shape({}),
  history: PropTypes.arrayOf(PropTypes.shape({})),
  cardId: PropTypes.number,
};
  
InsightsWidget.defaultProps = {
  t: (str) => str,
  showFilters: {},
  actions: {},
  history: [],
  cardId: 0,
};

const mapStateToProps = (state) => ({ ...TunnelInsightsSelectors.baseSelector(state) });

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateMenu,
    drilldownSessionLogs,
    handleSearchString: setDataTypeSearchString,
    handleSelectedDataTypeFilter,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(InsightsWidget));

/* eslint-disable react/jsx-handler-names */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { NavLink } from 'react-router-dom';
import { isEmpty } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRedo } from '@fortawesome/pro-solid-svg-icons';
import { faFilter } from '@fortawesome/pro-light-svg-icons';
import {
  Field,
  reduxForm,
} from 'redux-form';
import NavTabs from 'components/navTabs';
import { BASE_LAYOUT } from 'config';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import ECRadioGroup from 'components/ecRadioGroup';
import * as SessionLogsSelectors from 'ducks/sessionLogs/selectors';
import {
  handleStartOver, getPreselectedFilters, handleNoOfRecords,
  handleClearFilters, handleRemoveFilter, handleSelectedAddFilter, applyFilters,
  validatePort<PERSON>rom, validatePortTo, validateEcSourcePortFrom, validateEcSourcePortTo,
  validateServerDestinationPortFrom, validateServerDestinationPortTo,
  validateServerSourcePortFrom, validateServerSourcePortTo,
  validateClientDestinationPortFrom, validateClientDestinationPortTo,
  validateClientSourcePortFrom, validateClientSourcePortTo,
  handleToggleDownloadCSV,
} from 'ducks/sessionLogs';

import {
  // BranchCloudConnectorGroupName,
  // CltRxBytes,
  // CltTxBytes,
  // CltTxDrops,
  // DataCenter,
  // ServerIpCategory,
  // SrvRxBytes,
  // SrvTxBytes,
  // SrvTxDrops,
  // VpcVnetName, // Yet to get API support
  // ZpaPolicyViolationIndicator,
  AddFilter,
  ApplicationSegment, // ZpaAppSegment
  AwsAccountId,
  AwsRegion,
  AwsZone,
  AzureRegion,
  AzureSubscriptionId,
  AzureZone,
  ClientDestinationIP,
  ClientDestinationName,
  ClientDestinationPort,
  ClientNetworkProtocol,
  ClientSourceIP,
  ClientSourcePort,
  Country, // serverCountryCode
  EcGroup,
  EcInstance,
  EcName,
  DeviceID,
  DeviceHostname,
  DeviceAppVersion,
  DeviceType,
  DeviceOsType,
  EcSourceIp,
  EcSourcePort,
  EcVM,
  ForwardingMethod,
  ForwardingRule,
  GatewayDestinationIp,
  GatewayDestinationPort,
  GatewayName,
  GcpProjectId,
  GcpRegion,
  GcpZone,
  Location,
  NetworkService,
  Platform,
  ServerDestinationIP,
  ServerDestinationPort,
  ServerNetworkProtocol,
  ServerSourceIP,
  ServerSourcePort,
  LogsTimeFrame,
  TrafficType,
} from './Dropdown';
import ClearFilters from './ClearFilters';

// const showOrHide = (show) => {
//   if (!show) return 'hide';
//   return 'filter-container';
// };

class GenericFiltersForm extends PureComponent {
  componentDidMount() {
    const { actions, initialValues } = this.props;
    actions.getPreselectedFilters(initialValues);
  }

  render() {
    const {
      showAccordion,
      actions,
      noOfRecords,
      downloadCSV,
      t,
      formSyncErrors,
    } = this.props;
    const disableApply = !isEmpty(formSyncErrors);

    if (!showAccordion) return null;

    return (
      <div className="expander">
        <div className="filters-container">
          <form onSubmit={actions.applyFilters}>
            {/* Header */}
            <div className="header">
              <div className="title">
                <span>
                  <NavTabs
                    tabConfiguration={[
                      {
                        id: 'session-insights',
                        title: t('INSIGHTS'),
                        to: `${BASE_LAYOUT}/analytics/sessioninsights`,
                      },
                      {
                        id: 'session-logs',
                        title: t('LOGS'),
                        to: `${BASE_LAYOUT}/analytics/sessionlogs`,
                      }]} />
                </span>
              </div>
            </div>
            <div className="separator-line-header" />
            {/* TimeFrame */}
            <div className="filter-box">
              <div className="filter-sideheader">
                <span>{t('TIME_FRAME')}</span>
              </div>
              <div className="filter-card-small">
                <Field
                  id="timeFrame"
                  name="timeFrame"
                  component={LogsTimeFrame}
                  parse={(value) => value.id} />
              </div>
            </div>
            <div className="separator-line" />
            {/* # of Download CSV Toggle Displayed */}
            <div className="filter-box">
              <div className="filter-card-small download-display">
                <ECRadioGroup
                  id="downloadCSV"
                  name="downloadCSV"
                  styleClass="full-width"
                  onChange={actions.handleToggleDownloadCSV}
                  options={[{
                    name: 'downloadCSV', value: 'DOWNLOAD', checked: downloadCSV === 'DOWNLOAD', label: t('DOWNLOAD_CSV'),
                  },
                  {
                    name: 'downloadCSV', value: 'DISPLAY', checked: downloadCSV === 'DISPLAY', label: t('DISPLAY'),
                  }]} />
              </div>
            </div>
            <div className="separator-line" />
            {/* # of Records Displayed */}
            <div className={`filter-box ${downloadCSV === 'DOWNLOAD' ? 'hidden' : ''}`}>
              <div className="filter-sideheader">
                <span>{t('NUMBER_OF_RECORDS_DISPLAYED')}</span>
              </div>
              <div className="filter-card-small-log">
                <ECRadioGroup
                  id="noOfRecords"
                  name="noOfRecords"
                  styleClass="full-width"
                  onChange={actions.handleNoOfRecords}
                  options={[{
                    name: 'noOfRecords', value: '1000', checked: noOfRecords === '1000', label: t('1k'),
                  },
                  {
                    name: 'noOfRecords', value: '5000', checked: noOfRecords === '5000', label: t('5k'),
                  },
                  {
                    name: 'noOfRecords', value: '10000', checked: noOfRecords === '10000', label: t('10k'),
                  },
                  {
                    name: 'noOfRecords', value: '25000', checked: noOfRecords === '25000', label: t('25k'),
                  }]} />
              </div>
            </div>
            <div className="separator-line" />
            {/* Select Filters */}
            <div className="filter-box">
              <div className="side-header">
                <div className="filter-sideheader">
                  <FontAwesomeIcon icon={faFilter} />
                  {' '}
                  <span>{t('FILTERS')}</span>
                </div>
                <div className="filter-card-large">
                  <div className="filter-container">
                    <Field
                      id="addFilter"
                      name="addFilter"
                      component={AddFilter}
                      show />
                  </div>
                </div>
              </div>
              <div className="filter-card-large">
                <AwsAccountId {...this.props} />
                <AwsZone {...this.props} />
                <AwsRegion {...this.props} />
                <ApplicationSegment {...this.props} />
                <AzureZone {...this.props} />
                <AzureRegion {...this.props} />
                <AzureSubscriptionId {...this.props} />
                <ClientDestinationIP {...this.props} />
                <ClientDestinationName {...this.props} />
                <ClientDestinationPort {...this.props} />
                <ClientNetworkProtocol {...this.props} />
                <ClientSourceIP {...this.props} />
                <ClientSourcePort {...this.props} />
                <EcGroup {...this.props} />
                <EcInstance {...this.props} />
                <EcName {...this.props} />
                <DeviceID {...this.props} />
                <DeviceHostname {...this.props} />
                <DeviceAppVersion {...this.props} />
                <EcSourceIp {...this.props} />
                <EcSourcePort {...this.props} />
                <EcVM {...this.props} />
                <Country {...this.props} />
                <ForwardingRule {...this.props} />
                <ForwardingMethod {...this.props} />
                <GatewayDestinationIp {...this.props} />
                <GatewayDestinationPort {...this.props} />
                <GatewayName {...this.props} />
                <GcpZone {...this.props} />
                <GcpProjectId {...this.props} />
                <GcpRegion {...this.props} />
                <Location {...this.props} />
                <NetworkService {...this.props} />
                <Platform {...this.props} />
                <ServerDestinationIP {...this.props} />
                <ServerDestinationPort {...this.props} />
                <ServerNetworkProtocol {...this.props} />
                <ServerSourceIP {...this.props} />
                <ServerSourcePort {...this.props} />
                <TrafficType {...this.props} />
                <DeviceType {...this.props} />
                <DeviceOsType {...this.props} />
              </div>
            </div>
            {/* Apply */}
            <div className="external-id-buttons apply-filters">
              <button
                type="button"
                disabled={disableApply}
                className="primary-button"
                onClick={actions.applyFilters}>
                {t('APPLY_FILTERS')}
              </button>
              <button
                type="button"
                onClick={actions.handleStartOver}
                className="secondary-button">
                <FontAwesomeIcon icon={faRedo} />
                <span className="refresh-text">{t('RESET')}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }
}

GenericFiltersForm.propTypes = {
  showAccordion: PropTypes.bool,
  actions: PropTypes.shape({}),
  noOfRecords: PropTypes.string,
  initialValues: PropTypes.shape({}),
  showFilters: PropTypes.shape({}),
  filterFormValues: PropTypes.shape(),
  downloadCSV: PropTypes.string,
  t: PropTypes.func,
  formSyncErrors: PropTypes.shape({}),
};

GenericFiltersForm.defaultProps = {
  showAccordion: false,
  actions: {},
  noOfRecords: '',
  initialValues: {},
  showFilters: {},
  filterFormValues: {},
  downloadCSV: 'DOWNLOAD',
  t: (str) => str,
  formSyncErrors: {},
};

const mapStateToProps = (state) => ({
  ...SessionLogsSelectors.default(state),
  filterFormValues: SessionLogsSelectors.formValuesSelector(state),
  formMeta: SessionLogsSelectors.formMetaSelector(state),
  formSyncErrors: SessionLogsSelectors.formSyncErrorsSelector(state),
  filters: SessionLogsSelectors.addFilterDropdownSelector(state),
  initialValues: {
    timeFrame: SessionLogsSelectors.timeFrameDefaultSelector(state),
    noOfRecords: '1000',
    locationType: 'INCLUDE',
    ecInstanceType: 'INCLUDE',
    ecVmType: 'INCLUDE',
    ecGroupType: 'INCLUDE',
    platformType: 'INCLUDE',
    awsRegionType: 'INCLUDE',
    availabilityZoneType: 'INCLUDE',
    azureRegionType: 'INCLUDE',
    azureAvailabilityZoneType: 'INCLUDE',
    ecNameMatchType: 'EXACT_MATCH',
    deviceNameMatchType: 'EXACT_MATCH',
    deviceHostnameMatchType: 'EXACT_MATCH',
    deviceAppVersionMatchType: 'EXACT_MATCH',
    vpcVnetNameMatchType: 'EXACT_MATCH',
    clientDestinationNameMatchType: 'EXACT_MATCH',
    ecNameEmpty: 'yes',
    deviceNameEmpty: 'yes',
    deviceHostnameEmpty: 'yes',
    deviceAppVersionEmpty: 'yes',
  },
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    // updateFormState: change,
    handleStartOver,
    getPreselectedFilters,
    handleNoOfRecords,
    handleClearFilters,
    handleRemoveFilter,
    handleSelectedAddFilter,
    applyFilters,
    validatePortFrom,
    validatePortTo,
    validateEcSourcePortFrom,
    validateEcSourcePortTo,
    validateServerDestinationPortFrom,
    validateServerDestinationPortTo,
    validateServerSourcePortFrom,
    validateServerSourcePortTo,
    validateClientDestinationPortFrom,
    validateClientDestinationPortTo,
    validateClientSourcePortFrom,
    validateClientSourcePortTo,
    handleToggleDownloadCSV,
  }, dispatch);

  return {
    actions,
  };
};

const GenericFilters = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'sessionFiltersForm',
  // destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  initialValues: {
    timeFrame: { id: 'last_24_hrs', value: 'last_24_hrs', label: 'LAST_24_HOURS' },
    noOfRecords: '1000',
    ecNameMatchType: 'EXACT_MATCH',
    deviceNameMatchType: 'EXACT_MATCH',
    deviceHostnameMatchType: 'EXACT_MATCH',
    deviceAppVersionMatchType: 'EXACT_MATCH',
    vpcVnetNameMatchType: 'EXACT_MATCH',
    locationType: 'INCLUDE',
    ecInstanceType: 'INCLUDE',
    ecVmType: 'INCLUDE',
    ecGroupType: 'INCLUDE',
    platformType: 'INCLUDE',
    awsRegionType: 'INCLUDE',
    availabilityZoneType: 'INCLUDE',
    azureRegionType: 'INCLUDE',
    azureAvailabilityZoneType: 'INCLUDE',
  },
})(withTranslation()(GenericFiltersForm)));

export default GenericFilters;

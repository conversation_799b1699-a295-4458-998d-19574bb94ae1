import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import zscaler<PERSON>ogo from 'images/zscaler-logo-full.png';

export function PageHeader(props) {
  const { t } = props;
  return (
    <div className="print-view-header">
      <div className="print-view-logo">
        <img src={zscalerLogo} alt="Zscaler" />
      </div>
      <div className="print-view-header-text">
        <div className="print-view-header-title">{t('DNS_INSIGHTS')}</div>
      </div>
    </div>
  );
}

PageHeader.propTypes = {
  t: PropTypes.func,
};

PageHeader.defaultProps = {
  t: (str) => str,
};

export default withTranslation()(PageHeader);

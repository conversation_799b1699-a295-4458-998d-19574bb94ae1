/* eslint-disable react/jsx-handler-names */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { NavLink } from 'react-router-dom';
import { isEmpty } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { BASE_LAYOUT } from 'config';
import { faRedo } from '@fortawesome/pro-solid-svg-icons';
import { faFilter } from '@fortawesome/pro-light-svg-icons';
import {
  Field,
  getFormValues,
  change,
  reduxForm,
} from 'redux-form';
import NavTabs from 'components/navTabs';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import ECRadioGroup from 'components/ecRadioGroup';
import * as TunnelLogsSelectors from 'ducks/tunnelLogs/selectors';
import {
  handleStartOver, getPreselectedFilters, handleNoOfRecords,
  handleClearFilters, handleRemoveFilter, handleSelectedAddFilter, applyFilters,
  validatePortFrom, validatePortTo, validateInbytesFrom, validateInbytesTo,
  validateOutbytesFrom, validateOutbytesTo, validateRequestDurationFrom, validateRequestDurationTo,
  handleToggleDownloadCSV,
} from 'ducks/tunnelLogs';
import ClearFilters from './ClearFilters';

import {
  AddFilter,
  AwsAccountId,
  AzureSubscriptionId,
  DataCenter,
  LogsTimeFrame,
  Location,
  TunnelDestinationIP,
  TunnelSourceIP,
} from './Dropdown';

class TunnelFiltersForm extends PureComponent {
  componentDidMount() {
    const { actions, initialValues } = this.props;
    actions.getPreselectedFilters(initialValues);
  }

  render() {
    const {
      showAccordion,
      actions,
      noOfRecords,
      downloadCSV,
      t,
      formSyncErrors,
    } = this.props;
    const disableApply = !isEmpty(formSyncErrors);

    if (!showAccordion) return null;

    return (
      <div className="expander">
        <div className="filters-container">
          <form onSubmit={actions.applyFilters}>
            {/* Header */}
            <div className="header">
              {/* <div className="title">
                <span>
                  {t('LOGS')}
                </span>
              </div> */}
              <div className="title">
                <span>
                  <NavTabs
                    tabConfiguration={[
                      {
                        id: 'tunnel-insights',
                        title: t('INSIGHTS'),
                        to: `${BASE_LAYOUT}/analytics/tunnelinsights`,
                      },
                      {
                        id: 'tunnel-logs',
                        title: t('LOGS'),
                        to: `${BASE_LAYOUT}/analytics/tunnellogs`,
                      }]} />
                </span>
              </div>
            </div>
            <div className="separator-line-header" />
            {/* TimeFrame */}
            <div className="filter-box">
              <div className="filter-sideheader">
                <span>{t('TIME_FRAME')}</span>
              </div>
              <div className="filter-card-small">
                <Field
                  id="timeFrame"
                  name="timeFrame"
                  component={LogsTimeFrame}
                  parse={(value) => value.id} />
              </div>
            </div>
            <div className="separator-line" />
            {/* # of Download CSV Toggle Displayed */}
            <div className="filter-box">
              <div className="filter-card-small download-display">
                <ECRadioGroup
                  id="downloadCSV"
                  name="downloadCSV"
                  styleClass="full-width"
                  onChange={actions.handleToggleDownloadCSV}
                  options={[{
                    name: 'downloadCSV', value: 'DOWNLOAD', checked: downloadCSV === 'DOWNLOAD', label: t('DOWNLOAD_CSV'),
                  },
                  {
                    name: 'downloadCSV', value: 'DISPLAY', checked: downloadCSV === 'DISPLAY', label: t('DISPLAY'),
                  }]} />
              </div>
            </div>
            <div className="separator-line" />
            {/* # of Records Displayed */}
            <div className={`filter-box ${downloadCSV === 'DOWNLOAD' ? 'hidden' : ''}`}>
              <div className="filter-sideheader">
                <span>{t('NUMBER_OF_RECORDS_DISPLAYED')}</span>
              </div>
              <div className="filter-card-small-log">
                <ECRadioGroup
                  id="noOfRecords"
                  name="noOfRecords"
                  styleClass="full-width"
                  onChange={actions.handleNoOfRecords}
                  options={[{
                    name: 'noOfRecords', value: '1000', checked: noOfRecords === '1000', label: t('1k'),
                  },
                  {
                    name: 'noOfRecords', value: '5000', checked: noOfRecords === '5000', label: t('5k'),
                  },
                  {
                    name: 'noOfRecords', value: '10000', checked: noOfRecords === '10000', label: t('10k'),
                  },
                  {
                    name: 'noOfRecords', value: '25000', checked: noOfRecords === '25000', label: t('25k'),
                  }]} />
              </div>
            </div>
            <div className="separator-line" />
            {/* Select Filters */}
            <div className="filter-box">
              <div className="side-header">
                <div className="filter-sideheader">
                  <FontAwesomeIcon icon={faFilter} />
                  {' '}
                  <span>{t('FILTERS')}</span>
                </div>
                <div className="filter-card-large">
                  <div className="filter-container">
                    <Field
                      id="addFilter"
                      name="addFilter"
                      component={AddFilter}
                      show />
                  </div>
                </div>
              </div>
              <div className="filter-card-large">
                <AwsAccountId {...this.props} />
                <AzureSubscriptionId {...this.props} />
                <DataCenter {...this.props} />
                <Location {...this.props} />
                <TunnelDestinationIP {...this.props} />
                <TunnelSourceIP {...this.props} />
              </div>
            </div>
            {/* Apply */}
            <div className="external-id-buttons apply-filters">
              <button
                type="button"
                disabled={disableApply}
                className="primary-button"
                onClick={actions.applyFilters}>
                {t('APPLY_FILTERS')}
              </button>
              <button
                type="button"
                onClick={actions.handleStartOver}
                className="secondary-button">
                <FontAwesomeIcon icon={faRedo} />
                <span className="refresh-text">{t('RESET')}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }
}

TunnelFiltersForm.propTypes = {
  showAccordion: PropTypes.bool,
  actions: PropTypes.shape({}),
  noOfRecords: PropTypes.string,
  initialValues: PropTypes.shape({}),
  showFilters: PropTypes.shape({}),
  filterFormValues: PropTypes.shape(),
  downloadCSV: PropTypes.string,
  t: PropTypes.func,
  formSyncErrors: PropTypes.shape({}),
};

TunnelFiltersForm.defaultProps = {
  showAccordion: false,
  actions: {},
  noOfRecords: '',
  initialValues: {},
  showFilters: {},
  filterFormValues: {},
  downloadCSV: 'DOWNLOAD',
  t: (str) => str,
  formSyncErrors: {},
};

const mapStateToProps = (state) => ({
  ...TunnelLogsSelectors.default(state),
  filterFormValues: getFormValues('tunnelFilersForm')(state),
  filters: TunnelLogsSelectors.addFilterDropdownSelector(state),
  initialValues: {
    timeFrame: { id: 'last_24_hrs', value: 'last_24_hrs', label: 'LAST_24_HOURS' },
    noOfRecords: '1000',
    dataCenterEmpty: 'yes',
    dataCenterMatchType: 'EXACT_MATCH',
    locationType: 'INCLUDE',
  },
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateFormState: change,
    handleStartOver,
    getPreselectedFilters,
    handleNoOfRecords,
    handleClearFilters,
    handleRemoveFilter,
    handleSelectedAddFilter,
    applyFilters,
    validatePortFrom,
    validatePortTo,
    validateInbytesFrom,
    validateInbytesTo,
    validateOutbytesFrom,
    validateOutbytesTo,
    validateRequestDurationFrom,
    validateRequestDurationTo,
    handleToggleDownloadCSV,
  }, dispatch);

  return {
    actions,
  };
};

const GenericFilters = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'tunnelFilersForm',
  // destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  initialValues: {
    timeFrame: { id: 'last_24_hrs', value: 'last_24_hrs', label: 'LAST_24_HOURS' },
    noOfRecords: '1000',
    dataCenterEmpty: 'yes',
    dataCenterMatchType: 'EXACT_MATCH',
    locationType: 'INCLUDE',
  },
})(withTranslation()(TunnelFiltersForm)));

export default GenericFilters;

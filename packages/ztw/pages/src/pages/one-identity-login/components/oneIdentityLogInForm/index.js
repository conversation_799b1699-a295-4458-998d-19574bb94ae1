import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { noop } from 'utils/lodash';
import { BASE_LAYOUT } from 'config';
import FormErrorSummary from 'components/formErrorSummary';
import { ErrorLabel } from 'components/label';
import DropDown from 'components/dropDown';
import Spinner from 'components/spinner';
import withRouter from 'layout/withRouter';
import { getDefaultLandingPages } from 'utils/helpers';
import * as oneIdentityLoginSelectors from 'ducks/oneIdentityLogin/selectors';

import * as constants from 'ducks/oneIdentityLogin/constants';
import { faGlobe } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  checkSession,
  login,
  setLocale,
  setRememberUser,
  adminSSO,
  backToUserID,
} from 'ducks/oneIdentityLogin';
import { togglePasswordExpiryModal } from 'ducks/passwordExpiry';

import { withTranslation } from 'react-i18next';

import InputContainer from './InputContainer';

class LoginForm extends React.Component {
  static propTypes = {
    actions: PropTypes.shape({
      checkSession: PropTypes.func,
      login: PropTypes.func,
      setPageLocale: PropTypes.func,
      setRememberUser: PropTypes.func,
      adminSSOLogin: PropTypes.func,
      backToUserIDForm: PropTypes.func,
    }),
    rememberUser: PropTypes.bool,
    data: PropTypes.shape(),
    error: PropTypes.string,
    accessPrivileges: PropTypes.shape({}),
    t: PropTypes.func,
    locale: PropTypes.shape(),
    router: PropTypes.shape(),
    // redirect: PropTypes.func,
    // redirectExpiredPassword: PropTypes.func,
    showPasswordForm: PropTypes.bool,
    redirectLocation: PropTypes.string,
    loading: PropTypes.bool,
  };

  static defaultProps = {
    actions: {
      checkSession: noop,
      login: noop,
      setPageLocale: noop,
      setRememberUser: noop,
      adminSSOLogin: noop,
      backToUserIDForm: noop,
    },
    rememberUser: null,
    data: {},
    error: '',
    t: (str) => str,
    locale: null,
    // redirect: noop,
    showPasswordForm: false,
    redirectLocation: '',
    loading: false,
  };

  state = {
    username: '',
    password: '',
    formErrorSummaryText: null,
    usernameErrorText: null,
    passwordErrorText: null,
    // loading: false,
  };

  componentDidMount() {
    const {
      actions: {
        checkSession, // eslint-disable-line no-shadow
        login, // eslint-disable-line
      },
      // redirectLocation,
      isLoggedIn,
    } = this.props;

    console.log('oui login props', this.props);
    console.log('oui login state', this.state);

    // if (redirectLocation) {
    //   window.location.replace(redirectLocation);
    // }
    
    const usernameFS = localStorage.getItem('username');

    if (usernameFS !== '' && typeof usernameFS !== 'undefined' && usernameFS !== null) {
      this.setState({ username: usernameFS });
    }

    checkSession();
  }

  updateTextbox = (key) => (event) => {
    const { value } = event.target;
    this.setState({ [key]: value });
  };

  handleRememberUsername = (isRemember) => {
    const { username } = this.state;
    if (isRemember) {
      localStorage.setItem('username', username);
    } else {
      localStorage.removeItem('username');
    }
  };

  handleSSOLogin = (e) => {
    e.preventDefault();
    const {
      actions,
      t,
      locale,
    } = this.props;

    const {
      username,
    } = this.state;

    let hasError = false;
    const localizationText = locale;
    
    if (!username) {
      this.setState({ formErrorSummaryText: t(localizationText.INVALID_CREDENTIALS_MESSAGE) });
      hasError = true;
    } else {
      this.setState({ username: username.trim() });
      hasError = false;
    }
    if (!hasError) {
      actions.adminSSOLogin(username);
    }
  };

  handleBackClick = (e) => {
    e.preventDefault();

    const {
      actions,
    } = this.props;

    actions.backToUserIDForm();
  };

  redirectExpiredPassword = () => {
    const { router: { navigate }, accessPrivileges } = this.props;
    navigate(`${BASE_LAYOUT}/administration/loading`, {
      state: {
        path: getDefaultLandingPages(accessPrivileges),
        accessPrivileges,
      },
    });
  };

  redirect = () => {
    const { router: { navigate }, accessPrivileges } = this.props;
    navigate(`${BASE_LAYOUT}/dashboard/connector-monitoring`, {
      state: {
        path: getDefaultLandingPages(accessPrivileges),
        accessPrivileges,
      },
    });
  };

  handleLogin = (event) => {
    event.preventDefault();

    const { username, password } = this.state;
    const {
      actions,
      locale,
      t,
      rememberUser,
    } = this.props;
    let hasError = false;
    const localizationText = locale;
    const rememberUserID = JSON.parse(localStorage.getItem('rememberUser')) || rememberUser;
    
    if (!username || !password) {
      this.setState({ formErrorSummaryText: t(localizationText.INVALID_CREDENTIALS_MESSAGE) });
      hasError = true;
    } else {
      hasError = false;
    }

    if (!hasError) {
      this.setState({ formErrorSummaryText: null });
      this.handleRememberUsername(rememberUserID);

      actions.login(username, password)
        .then(() => {
          const { data: { isAuthenticated, isPasswordExpired } } = this.props;
          // this.setState({ loading: false });
          if (isAuthenticated) {
            const remainingDays = constants.START_ALERT_IN_DAYS;
            if (isPasswordExpired || remainingDays < constants.START_ALERT_IN_DAYS) {
              actions.showPasswordExpiryModal(true);
              this.redirectExpiredPassword();
            } else {
              this.redirect();
            }
          }
        });
    }
  };

  render() {
    const {
      error: loginError,
      t,
      actions: { setPageLocale, setUserId },
      locale,
      showPasswordForm,
      loading: loadingFromProps,
      redirectLocation,
      isLoggedIn,
    } = this.props;
    const {
      username,
      usernameErrorText,
      password,
      passwordErrorText,
      formErrorSummaryText,
    } = this.state;

    // this.setState({ loading: loadingFromProps });

    if (redirectLocation) {
      window.location.replace(redirectLocation);
    }

    const localizationText = locale;
    const rememberUser = JSON.parse(localStorage.getItem('rememberUser'));

    if (isLoggedIn) {
      this.redirect();
      return null;
    }

    return (
      <div id="login-content" className="login-content ec-login">
        <div className={loadingFromProps ? '' : 'hide'}>
          <Spinner />
        </div>
        <div className="login-input-container">
          <form onSubmit={!showPasswordForm ? this.handleSSOLogin : this.handleLogin}>
            <div className="login-button-container large-width">
              
              {!showPasswordForm
                ? (
                  <>
                    <InputContainer
                      id="login-text"
                      inputLabel={t(localizationText.LOGIN_ID_LABEL)}
                      placeholder={t(localizationText.LOGIN_ID_PLACEHOLDER)}
                      inputType="text"
                      updateTextbox={this.updateTextbox}
                      inputVal={username}
                      name="username" />
                    <ErrorLabel text={usernameErrorText} />
                  </>
                )
                : ''}
              {showPasswordForm
                ? (
                  <>
                    <InputContainer
                      id="login-pwd"
                      inputLabel={`${t(localizationText.PASSWORD_LABEL)}`}
                      placeholder={t(localizationText.LOGIN_PASSWORD_PLACEHOLDER)}
                      username={username}
                      inputType="password"
                      updateTextbox={this.updateTextbox}
                      inputVal={password}
                      name="password" />
                    <ErrorLabel text={passwordErrorText} />
                  </>
                )
                : ''}
              {!showPasswordForm
                ? (
                  <div className="input-container login-signin-button">
                    <button
                      type="button"
                      onClick={this.handleSSOLogin}
                      id="login-panel-signin-button"
                      className="login-btn submit">
                      {t(localizationText.NEXT_LABEL)}
                    </button>
                  </div>
                )
                : ''}
              {showPasswordForm
                ? (
                  <div className="input-container login-signin-button">
                    <button
                      type="submit"
                      id="login-panel-signin-button"
                      className="login-btn submit">
                      {t(localizationText.SIGN_IN_LABEL)}
                    </button>
                  </div>
                )
                : ''}
            </div>
            {
              // handleBackClick
              showPasswordForm
                ? (
                  <a className="back-btn" role="button" tabIndex={0} onClick={this.handleBackClick} onKeyPress={this.handleBackClick}>Back</a>
                )
                : ''
            }
            {!showPasswordForm
              ? (
                <>
                  <div className="login-remember-me">
                    <label htmlFor="input-remember-username" className="rember-my-login">
                      <input
                        onClick={setUserId}
                        type="checkbox"
                        id="input-remember-username"
                        defaultChecked={rememberUser}
                        className="login-remember-username" />
                      <span>{t(localizationText.REMEMBER_ME)}</span>
                    </label>
                  </div>
                  <div className="language-selector">
                    <div className="login-language-container">
                      <div className="icon-container">
                        <FontAwesomeIcon icon={faGlobe} />
                      </div>
                      <div className="login-dropdown">
                        <DropDown
                          items={constants.DROPDOWN_LANG_DATA}
                          setValue={setPageLocale}
                          defaultValue={{
                            value: localizationText.LOCALE,
                            label: localizationText.language,
                          }} />
                      </div>
                    </div>
                  </div>
                </>
              )
              : ''}
            
          </form>
          {formErrorSummaryText || loginError ? (
            <FormErrorSummary summaryText={t(formErrorSummaryText || loginError)} />
          ) : null}
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...state.oneIdentityLogin,
  accessPrivileges: oneIdentityLoginSelectors.accessPermissionsSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    checkSession,
    login,
    setPageLocale: setLocale,
    setUserId: setRememberUser,
    showPasswordExpiryModal: togglePasswordExpiryModal,
    adminSSOLogin: adminSSO,
    backToUserIDForm: backToUserID,
  }, dispatch);
  return {
    actions,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(withRouter(LoginForm)));

// @flow
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';

import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import { Field, reduxForm } from 'redux-form';
import Loading from 'components/spinner/Loading';
import Modal from 'components/modal';
import PageTabs from 'components/navTabs/PageTabs';
import PermissionRequired from 'components/PermissionRequired';
import RBAC from 'components/rbac';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasBsku, hasCsku, getReadOnly } from 'utils/helpers';
import ServerError from 'components/errors/ServerError';

import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import * as constants from 'ducks/login/constants';
import * as NetworkServicesSelectors from 'ducks/networkServices/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import {
  changePageTab,
  editNetworkServicesRow,
  editNetworkServiceGroupsRow,
  handleDeleteNetworkServices,
  handleDeleteNetworkServiceGroups,
  loadData,
  tabConfiguration,
  toggleClose,
  toggleDeleteForm,
} from 'ducks/networkServices';
import {
  NetworkServiceForm,
  NetworkServiceGroupForm,
  NetworkServicesTable,
  NetworkServiceGroupsTable,
  ActionBar,
} from './components';

export class NetworkServices extends Component {
  static propTypes = {
    actions: PropTypes.shape(),
    accessPermissions: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    addEditFormToggle: PropTypes.bool,
    data: PropTypes.shape({
      networkServices: PropTypes.arrayOf(PropTypes.shape({})),
    }),
    modalLoading: PropTypes.bool,
    modalTitle: PropTypes.string,
    pageTabs: PropTypes.string,
    selectedRowId: PropTypes.string,
    showDeleteForm: PropTypes.bool,
    showForm: PropTypes.bool,
    t: PropTypes.func,
    authType: PropTypes.string,
  };

  static defaultProps = {
    actions: {
      loadNetworkServices: noop,
    },
    accessPermissions: {},
    accessSubscriptions: [],
    addEditFormToggle: false,
    data: {
      networkServices: [],
    },
    modalLoading: false,
    modalTitle: '',
    selectedRowId: null,
    showDeleteForm: false,
    showForm: false,
    t: (str) => str,
    authType: '',
  };

  componentDidMount() {
    const { actions } = this.props;
    actions.load();
  }

  componentDidUpdate(prevProps) {
    const { actions, pageTabs } = this.props;

    if (pageTabs !== prevProps.pageTabs) {
      actions.changePageTab(pageTabs);
    }
  }

  render() {
    const {
      actions,
      t,
      showForm,
      accessPermissions,
      accessSubscriptions,
      modalTitle,
      pageTabs,
      showDeleteForm,
      modalLoading,
      selectedRowId,
      authType,
    } = this.props;
    /* eslint-disable no-shadow */
    const {
      handleClose,
      handleEditAction,
      handleEditGroupAction,
      toggleDeleteConfirmationForm,
      handleDeleteNetworkServices,
      handleDeleteNetworkServiceGroups,
    } = actions;

    const isNetworkServiceTab = pageTabs === 'NETWORK_SERVICES';
    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);
    const permission = accessPermissions.EDGE_CONNECTOR_FORWARDING;
    const readOnly = getReadOnly(permission, authType);
    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (permission === 'NONE') {
      return <PermissionRequired accessPermissions={accessPermissions} />;
    }

    return (
      <div className="networkService-main-container">
        <div className="page-title ">
          <span className="source-ip-groups">
            {t('NETWORK_SERVICES')}
          </span>
        </div>
        <Field
          id="pageTabs"
          name="pageTabs"
          component={PageTabs} />
        {
          !readOnly && <ActionBar />
        }
        <div className="source-ip-wrapper">
          <RBAC privilege={constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_FORWARDING}>
            <Loading {...this.props}>
              {isNetworkServiceTab && (
                <HelpArticle
                  article={HELP_ARTICLES.NETWORK_SERVICE} />
              )}
              {!isNetworkServiceTab && (
                <HelpArticle
                  article={HELP_ARTICLES.NETWORK_SERVICE_GROUPS} />
              )}
              <ServerError {...this.props}>
                {isNetworkServiceTab
                  ? (
                    <NetworkServicesTable
                      permission={permission}
                      handleEditAction={handleEditAction}
                      handleViewAction={handleEditAction}
                      onMouseOverCb={() => this.handleOnMouseOverCb} />
                  )
                  : (
                    <NetworkServiceGroupsTable
                      permission={permission}
                      handleEditAction={handleEditGroupAction}
                      handleViewAction={handleEditGroupAction}
                      onMouseOverCb={() => this.handleOnMouseOverCb} />
                  )}

                <Modal
                  title={t(modalTitle)}
                  isOpen={showForm}
                  styleClass="network-service-modal"
                  closeModal={handleClose}>
                  {isNetworkServiceTab ? <NetworkServiceForm /> : <NetworkServiceGroupForm />}
                </Modal>

                <Modal
                  title={t('DELETE_CONFIRMATION')}
                  isOpen={showDeleteForm}
                  closeModal={() => toggleDeleteConfirmationForm(false)}>
                  <DeleteConfirmationForm
                    modalLoading={modalLoading}
                    selectedRowID={selectedRowId}
                    handleCancel={toggleDeleteConfirmationForm}
                    handleDelete={isNetworkServiceTab
                      ? handleDeleteNetworkServices
                      : handleDeleteNetworkServiceGroups} />
                </Modal>

              </ServerError>
            </Loading>
          </RBAC>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state, ownProps) => ({
  ...ownProps,
  ...state.networkServices,
  ...NetworkServicesSelectors.formValuesSelector(state),
  data: NetworkServicesSelectors.dataSelector(state),
  modalLoading: NetworkServicesSelectors.modalLoadingSelector(state),
  selectedRowID: NetworkServicesSelectors.selectedRowIdSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators(
    {
      changePageTab,
      handleClose: toggleClose,
      handleDeleteNetworkServices,
      handleDeleteNetworkServiceGroups,
      handleEditAction: editNetworkServicesRow,
      handleEditGroupAction: editNetworkServiceGroupsRow,
      load: loadData,
      toggleDeleteConfirmationForm: toggleDeleteForm,
    },
    dispatch,
  );

  return {
    actions,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'networkServicePage',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  initialValues: {
    pageTabs: 'NETWORK_SERVICES',
    tabConfiguration,
  },
})(withTranslation()(NetworkServices)));

import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm } from 'redux-form';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import networkServiceValidation from 'utils/helpers/networkServiceValidation';
import Loading from 'components/spinner/Loading';
import {
  handleAddorEditNetworkServices,
  toggleClose,
  toggleDeleteForm,
} from 'ducks/networkServices';
import * as NetworkServicesSelectors from 'ducks/networkServices/selectors';

import NetworkServiceFragment from './components/NetworkServiceFragment';

export function BasicCustomAppForm(props) {
  const {
    actions,
    t,
    formMode,
    appData,
    handleSubmit,
    modalLoading,
    submitting,
  } = props;
  const { cancelHandle, saveFormData, handleDeleteAction } = actions;

  const onSubmit = () => {
    saveFormData(formMode);
  };

  return (
    <Loading loading={modalLoading}>
      <form onSubmit={handleSubmit(onSubmit)} className="networkService-form">
        <div className="form-sections-container">
          <NetworkServiceFragment {...props} />
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            {formMode !== 'VIEW' && <button type="submit" disabled={submitting} className="submit">{t('SAVE')}</button>}
            <button type="button" className="cancel" onClick={() => cancelHandle(false)}>{t('CANCEL')}</button>
          </div>
          <div className="dialog-footer-right">
            {formMode === 'EDIT' && <button type="button" className="button big delete" onClick={() => handleDeleteAction(true, appData)}>{t('DELETE')}</button>}
          </div>
        </div>
      </form>
    </Loading>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
  }),
  t: PropTypes.func,
  handleSubmit: PropTypes.func,
  submitting: PropTypes.bool,
  formMode: PropTypes.string,
  appData: PropTypes.shape({}),
  modalLoading: PropTypes.bool,
  serviceFragmentSection: PropTypes.shape({}),
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
  },
  t: (str) => str,
  handleSubmit: (str) => str,
  submitting: false,
  formMode: 'NEW',
  appData: {},
  modalLoading: false,
  serviceFragmentSection: {},
};

const NetworkServiceForm = reduxForm({
  form: 'networkService',
  validate: networkServiceValidation,
})(BasicCustomAppForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleClose,
    saveFormData: handleAddorEditNetworkServices,
    handleDeleteAction: toggleDeleteForm,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => {
  const {
    name,
    description,
    srcTcpPorts,
    destTcpPorts,
    srcUdpPorts,
    destUdpPorts,
    type,
  } = NetworkServicesSelectors.appDataSelector(state);
  const formMode = NetworkServicesSelectors.formModeSelector(state);

  return {
    initialValues: {
      formMode,
      name,
      definition: type,
      description,
      srcTcpPorts: formMode === 'EDIT' ? srcTcpPorts : NetworkServicesSelectors.srcTcpPortSelector(state),
      destTcpPorts: formMode === 'EDIT' ? destTcpPorts : NetworkServicesSelectors.destTcpPortSelector(state),
      srcUdpPorts: formMode === 'EDIT' ? srcUdpPorts : NetworkServicesSelectors.srcUdpPortSelector(state),
      destUdpPorts: formMode === 'EDIT' ? destUdpPorts : NetworkServicesSelectors.destUdpPortSelector(state),
    
    },
    ...NetworkServicesSelectors.baseSelector(state),
    ...NetworkServicesSelectors.formValuesSelector(state),
    formMeta: NetworkServicesSelectors.formNetworkServiceMetaSelector(state),
    formSyncErrors: NetworkServicesSelectors.formNetworkServiceSyncErrorsSelector(state),
    modalLoading: NetworkServicesSelectors.modalLoadingSelector(state),
  };
};

/* eslint-disable max-len */
export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(NetworkServiceForm));

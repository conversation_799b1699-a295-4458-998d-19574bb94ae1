import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm } from 'redux-form';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import networkServiceGroupValidation from 'utils/helpers/validatePassword';
import Loading from 'components/spinner/Loading';
import {
  handleAddorEditNetworkServiceGroups,
  toggleClose,
  toggleDeleteForm,
} from 'ducks/networkServices';
import * as NetworkServicesSelectors from 'ducks/networkServices/selectors';
import NetworkServiceGroupFragment from './components/NetworkServiceGroupFragment';

export function BasicCustomAppForm(props) {
  const {
    actions,
    t,
    formMode,
    appData,
    modalLoading,
    handleSubmit,
    submitting,
  } = props;
  const { cancelHandle, saveFormData, handleDeleteAction } = actions;

  const onSubmit = () => {
    saveFormData(formMode);
  };

  return (
    <Loading loading={modalLoading}>
      <form onSubmit={handleSubmit(onSubmit)} className="networkService-form">
        <div className="form-sections-container">
          <NetworkServiceGroupFragment {...props} />
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            {formMode !== 'VIEW' && <button type="submit" disabled={submitting} className="submit">{t('SAVE')}</button>}
            <button type="button" className="cancel" onClick={() => cancelHandle(false)}>{t('CANCEL')}</button>
          </div>
          <div className="dialog-footer-right">
            {formMode === 'EDIT' && <button type="button" className="button big delete" onClick={() => handleDeleteAction(true, appData)}>{t('DELETE')}</button>}
          </div>
        </div>
      </form>
    </Loading>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
  }),
  t: PropTypes.func,
  handleSubmit: PropTypes.func,
  submitting: PropTypes.bool,
  formMode: PropTypes.string,
  appData: PropTypes.shape({}),
  modalLoading: PropTypes.bool,
  serviceFragmentSection: PropTypes.shape({}),
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
  },
  t: (str) => str,
  handleSubmit: (str) => str,
  submitting: false,
  formMode: 'NEW',
  appData: {},
  modalLoading: false,
  serviceFragmentSection: {},
};

const NetworkServiceGroupForm = reduxForm({
  form: 'networkServiceGroup',
  validate: networkServiceGroupValidation,
})(BasicCustomAppForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleClose,
    saveFormData: handleAddorEditNetworkServiceGroups,
    handleDeleteAction: toggleDeleteForm,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => {
  const {
    name,
    description,
    services,
  } = NetworkServicesSelectors.appDataSelector(state);
  const formMode = NetworkServicesSelectors.formModeSelector(state);

  return {
    initialValues: {
      formMode,
      name,
      services: services || [],
      description,
    },
    ...NetworkServicesSelectors.baseSelector(state),
    ...NetworkServicesSelectors.formValuesSelector(state),
    formMetaGroup: NetworkServicesSelectors.formNetworkServiceGroupMetaSelector(state),
    formSyncErrorsGroup: NetworkServicesSelectors.formNetworkServiceGroupSyncErrorsSelector(state),
    modalLoading: NetworkServicesSelectors.modalLoadingSelector(state),
  };
};

/* eslint-disable max-len */
export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(NetworkServiceGroupForm));

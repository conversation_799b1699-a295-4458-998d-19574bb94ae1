import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { noop } from 'utils/lodash';
import { withTranslation } from 'react-i18next';

import SimpleSearchInput from 'components/Input/SimpleSearchInput';
import Dropdown from 'components/dropDown';

import {
  handleOnProtocolChange,
  handleOnSearchFilter,
  toggleAddForm,
} from 'ducks/networkServices';
import * as NetworkServicesSelectors from 'ducks/networkServices/selectors';
import ActionButtons from '../ActionButtons';

export function UtilBar({
  actions, pageTab, protocols, showCancel, t,
}) {
  const isNetworkServicePage = pageTab === 'NETWORK_SERVICES';

  return (
    <div className="table-layout-header">
      <div className="controls-container">
        <div className="grid-toolbar-left networkService-toolbar-left">
          <ActionButtons
            toggleAddForm={actions.toggleAddForm}
            pageTab={pageTab} />
        </div>
        <div className="grid-toolbar-right networkService-toolbar-right">
          {isNetworkServicePage
            && (
              <>
                <span className="dropdown-label">{t('PROTOCOL')}</span>
                <Dropdown
                  items={protocols}
                  name="protocol"
                  setValue={(str) => str}
                  onCallBack={actions.handleOnProtocolChange}
                  defaultValue={{
                    value: 'all',
                    label: t('ALL'),
                  }} />
              </>
            )}
          <SimpleSearchInput
            withButton
            showCancel={showCancel}
            onKeyPressCb={actions.handleOnSearchFilter(isNetworkServicePage ? 'networkServices' : 'networkServiceGroups')} />
        </div>
      </div>
    </div>
  );
}

UtilBar.propTypes = {
  actions: PropTypes.shape(),
  showCancel: PropTypes.bool,
  pageTab: PropTypes.string,
  protocols: PropTypes.arrayOf(PropTypes.shape({})),
  t: PropTypes.func,
};

UtilBar.defaultProps = {
  actions: {
    handleOnSearchFilter: noop,
    handleOnProtocolChange: noop,
    toggleAddForm: noop,
  },
  showCancel: false,
  pageTab: 'NETWORK_SERVICES',
  protocols: [],
  t: (str) => str,
};

const mapStateToProps = (state, ownProps) => {
  return {
    ...ownProps,
    ...NetworkServicesSelectors.baseSelector(state),
    showCancel: NetworkServicesSelectors.cancelButton(state),
    pageTab: NetworkServicesSelectors.getPageTab(state),
  };
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    handleOnProtocolChange,
    handleOnSearchFilter,
    toggleAddForm,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(
  withTranslation()(UtilBar),
);

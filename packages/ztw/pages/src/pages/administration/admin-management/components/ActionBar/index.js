// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  handleOnSearchFilter,
  toggleAddForm,
} from 'ducks/adminManagement';
import { withTranslation } from 'react-i18next';
import SimpleSearchInput from 'components/Input/SimpleSearchInput';

import ActionButtons from '../ActionButtons';

export function UtilBar({ actions, isReadOnly, isOneIdentityEnabled }) {
  return (
    <div className="table-layout-header">
      <div className="controls-container">
        <div className="grid-toolbar-left adminManagement-toolbar-left">
          <ActionButtons isReadOnly={isReadOnly} isOneIdentityEnabled={isOneIdentityEnabled} />
        </div>
        <div className="grid-toolbar-right adminManagement-toolbar-right">
          <SimpleSearchInput
            withButton
            showCancel={actions.showCancel}
            onKeyPressCb={actions.handleOnSearchFilter} />
        </div>
      </div>
    </div>
  );
}

UtilBar.propTypes = {
  actions: PropTypes.shape(),
  isReadOnly: PropTypes.bool,
  isOneIdentityEnabled: PropTypes.bool,
};

UtilBar.defaultProps = {
  actions: {},
  isReadOnly: true,
  isOneIdentityEnabled: false,
};

const mapStateToProps = (state, ownProps) => {
  return {
    ...ownProps,
    ...state.locations,
  };
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleAddForm,
    handleOnSearchFilter,
  }, dispatch);
  
  return {
    actions,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(
  withTranslation()(UtilBar),
);

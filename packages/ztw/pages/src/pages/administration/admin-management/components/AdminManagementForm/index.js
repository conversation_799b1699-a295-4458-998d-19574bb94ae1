import React, { useEffect, useRef } from 'react';
import { connect, useSelector, useDispatch } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm } from 'redux-form';
import { filter } from 'utils/lodash';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { notifyError } from 'ducks/notification';
import * as AdminManagementSelectors from 'ducks/adminManagement/selectors';
import validatePassword from 'utils/helpers/validatePassword';

import {
  saveForm,
  deleteForm,
  toggleClose,
  toggleDeleteForm,
} from 'ducks/adminManagement';
import AdminManagementFragment from '../AdminManagementFragment';

export function BasicCustomAppForm(props) {
  const {
    actions, t, viewOnly, addAdmin, appData, submitFailed, isOneIdentityEnabled,
  } = props;
  const { isDefaultAdmin } = appData || {};
  const { cancelHandle, saveFormData, handleDeleteAction } = actions;

  const {
    handleSubmit, submitting,
  } = props;
  
  const onSubmit = () => {
    saveFormData(addAdmin);
  };

  const { isOpen } = useSelector((state) => state.locationsDropdown);
  const dispatch = useDispatch();
  const elRef = useRef(null);

  useEffect(() => {
    if (elRef.current && isOpen) {
      elRef.current.scrollTop = elRef.current.scrollHeight;
    }
  }, [isOpen]);

  useEffect(() => {
    if (submitFailed) dispatch(notifyError('SAVE_ERROR_MESSAGE'));
  }, [submitFailed]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="adminManagement-form">
      <div className="form-sections-container" ref={elRef}>
        <AdminManagementFragment {...props} />
      </div>
      <div className="dialog-footer">
        <div className="dialog-footer-left">
          {!viewOnly && <button type="submit" disabled={submitting} className="submit">{t('SAVE')}</button>}
          <button type="button" className="cancel" onClick={() => cancelHandle(false)}>{t('CANCEL')}</button>
        </div>
        <div className="dialog-footer-right">
          {!isOneIdentityEnabled && !viewOnly && !addAdmin && !isDefaultAdmin && <button type="button" className="button big delete" onClick={() => handleDeleteAction(true, appData)}>{t('DELETE')}</button>}
        </div>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
  }),
  t: PropTypes.func,
  handleSubmit: PropTypes.func,
  submitting: PropTypes.bool,
  submitFailed: PropTypes.bool,
  addAdmin: PropTypes.bool,
  appData: PropTypes.shape({}),
  adminFragmentSection: PropTypes.shape({}),
  viewOnly: PropTypes.bool,
  isOneIdentityEnabled: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: null,
  },
  t: (str) => str,
  handleSubmit: (str) => str,
  submitting: false,
  addAdmin: false,
  appData: {},
  adminFragmentSection: {},
  viewOnly: true,
  isOneIdentityEnabled: false,
};

const BasicCustomForm = reduxForm({
  form: 'adminManagement',
  validate: validatePassword,
  destroyOnUnmount: false,
})(BasicCustomAppForm);

const AdminManagementForm = connect(
  (state) => {
    const {
      loginId,
      domain,
      email,
      name,
      role,
      adminScopeType,
      disabled,
      newLocationCreateAllowed,
      adminScopeScopeEntities,
      comments,
      isPasswordLoginAllowed,
    } = AdminManagementSelectors.appDataSelector(state);
    const addAdmin = AdminManagementSelectors.addAdminSelector(state);

    return ({
      initialValues: {
        addAdmin,
        loginId,
        domain,
        email,
        name,
        role,
        scope: { name: adminScopeType, id: adminScopeType },
        status: disabled ? 'disabled' : 'enabled',
        newLocationCreateAllowed,
        locationName: filter(adminScopeScopeEntities, (el) => el.name !== 'None'),
        comments,
        isPasswordLoginAllowed,
      },
    });
  },
)(BasicCustomForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleClose,
    saveFormData: saveForm,
    deleteData: deleteForm,
    handleDeleteAction: toggleDeleteForm,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  ...AdminManagementSelectors.baseSelector(state),
  ...AdminManagementSelectors.formValuesSelector(state),
});

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(AdminManagementForm));

import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm } from 'redux-form';
import { withTranslation } from 'react-i18next';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import {
  hasBsku, hasCsku, getReadOnly, verifyConfigData,
} from 'utils/helpers';
import * as CCAdvancedSettings from 'ducks/cloudConfigurationAdvancedSettings/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import {
  loader,
  handleCancel,
  saveCCAdvancedSettings,
} from 'ducks/cloudConfigurationAdvancedSettings';
import {
  AwsImportAccountId,
  AzureImportSubscriptionId,
  GCPImportProjectId,
} from './components';

export function BasicCustomAppForm(props) {
  useEffect(() => {
    const { load } = props;
    load();
  }, []);

  const {
    t,
    handleSubmit,
    submitting,
    formValues: { accountIdEnabled, subIdEnabled, projectIdEnabled } = {},
    initialValues,
    cancel,
    accessPermissions,
    authType,
    accessSubscriptions,
    configData,
  } = props;

  const isReadOnly = getReadOnly(accessPermissions.EDGE_CONNECTOR_ADMIN_MANAGEMENT, authType);
  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);

  if (!hasBSubscription && !hasCSubscription) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }
  if (accessPermissions.EDGE_CONNECTOR_ADMIN_MANAGEMENT === 'NONE') {
    return <PermissionRequired accessPermissions={accessPermissions} />;
  }

  const onSubmit = () => {
    const { save } = props;
    save();
  };

  const untouched = accountIdEnabled === initialValues.accountIdEnabled
  && subIdEnabled === initialValues.subIdEnabled
  && projectIdEnabled === initialValues.projectIdEnabled;

  const enableGcp = verifyConfigData({ configData, key: 'enableGcp' });

  return (
    <div className="main-container-aws-azure-accounts">
      <HelpArticle article={HELP_ARTICLES.ADVANCED_SETTINGS} />
      <div className="page-title header-3">
        <span className="source-ip-groups">
          {t('ADVANCED_SETTINGS')}
        </span>
      </div>
      <Loading {...props}>
        <form onSubmit={handleSubmit(onSubmit)} className="add-custom-app-form cloud-configuration-advanced-settings">
          <div className="source-ip-wrapper">
            <ServerError {...props}>
              <AwsImportAccountId {...props} viewOnly={isReadOnly} />
              <AzureImportSubscriptionId {...props} viewOnly={isReadOnly} />
              {enableGcp && <GCPImportProjectId {...props} viewOnly={isReadOnly} />}
            </ServerError>
            <div className="dialog-footer">
              <button type="submit" className="submit" disabled={untouched || submitting}>{t('SAVE')}</button>
              <button type="button" className="cancel" disabled={untouched || submitting} onClick={cancel}>{t('CANCEL')}</button>
            </div>
          </div>
        </form>
      </Loading>
    </div>
  );
}

BasicCustomAppForm.propTypes = {
  handleCancel: PropTypes.func,
  handleRegenerate: PropTypes.func,
  t: PropTypes.func,
  modalLoading: PropTypes.bool,
  selectedRow: PropTypes.shape({}),
  valid: PropTypes.bool,
  handleSubmit: PropTypes.func,
  submitting: PropTypes.bool,
  initialValues: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  cancel: PropTypes.func,
  accessPermissions: PropTypes.shape({}),
  authType: PropTypes.string,
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  save: PropTypes.func,
  configData: PropTypes.shape(),
};

BasicCustomAppForm.defaultProps = {
  handleCancel: null,
  handleRegenerate: null,
  t: (str) => str,
  modalLoading: false,
  selectedRow: null,
  valid: true,
  handleSubmit: (str) => str,
  submitting: false,
  initialValues: {},
  formValues: {},
  cancel: null,
  accessPermissions: {},
  authType: '',
  accessSubscriptions: [],
  save: null,
  configData: {},
};

const BasicCustomForm = reduxForm({
  form: 'cloudConfigurationAdvancedSettingsForm',
  enableReinitialize: true,
})(BasicCustomAppForm);

const AdvancedSettings = connect((state) => {
  const {
    accountIdEnabled,
    subIdEnabled,
    projectIdEnabled,
  } = CCAdvancedSettings.baseSelector(state) || {};

  return ({
    initialValues: {
      accountIdEnabled,
      subIdEnabled,
      projectIdEnabled,
    },
  });
})(BasicCustomForm);

const mapStateToProps = (state) => ({
  ...CCAdvancedSettings.baseSelector(state),
  formValues: CCAdvancedSettings.formValuesSelector(state),
  formMeta: CCAdvancedSettings.formMetaSelector(state),
  formSyncErrors: CCAdvancedSettings.formSyncErrorsSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  configData: loginSelectors.configDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loader,
    save: saveCCAdvancedSettings,
    cancel: handleCancel,
  }, dispatch);
  return actions;
};

AdvancedSettings.propTypes = {
  t: PropTypes.func,
  handleSubmit: PropTypes.func,
  submitting: PropTypes.bool,
};

AdvancedSettings.defaultProps = {
  t: (str) => str,
  handleSubmit: (str) => str,
  submitting: false,
};
export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(AdvancedSettings));

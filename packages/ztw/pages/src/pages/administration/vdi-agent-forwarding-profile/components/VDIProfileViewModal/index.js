// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import withRouter from 'layout/withRouter';
import { reduxForm } from 'redux-form';
import { noop } from 'utils/lodash';
import { FormFieldLabel } from 'components/label';

import { toggleViewModal } from 'ducks/vdiProfile';

import * as VDIProfileSelectors from 'ducks/vdiProfile/selectors';

export function BasicCustomAppForm({
  actions,
  t,
  appData,
}) {
  const {
    name,
    desc,
    include,
    exclude,
  } = appData;
  const handleCancelClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    const { cancelHandle } = actions;
    
    cancelHandle(appData, false);
  };

  return (
    <form className="wizard-form configure-provisioning-template">
      <div className="form-sections-container cc-provisoning">
        {' '}
        <p className="review-section-title">{t('GENERAL_INFORMATION')}</p>
        <div className="form-section provisioning-url">
          <div className="input-container review full-width">
            <FormFieldLabel text={t('NAME')} styleClass="no-margin-top" />
            <p className="disabled-input">{name}</p>
          </div>

          <div className="input-container review full-width">
            <FormFieldLabel text={t('DESCRIPTION')} styleClass="no-margin-top" />
            <p className="disabled-input">{desc || t('--')}</p>
          </div>
        </div>

        <div className="form-section provisioning-url">
          <div className="input-container review full-width">
            <FormFieldLabel text={t('INCLUDE_IP_ADDRESSES')} styleClass="no-margin-top" />
            <p className="disabled-input">{(include && include.join(', ')) || t('--')}</p>
          </div>
          <div className="input-container review full-width">
            <FormFieldLabel text={t('EXCLUDE_IP_ADDRESSES')} styleClass="no-margin-top" />
            <p className="disabled-input">{(exclude && exclude.join(', ')) || t('--')}</p>
          </div>
        </div>
 
      </div>
      <div className="modal-footer">
        <button type="submit" onClick={handleCancelClick} className="next primary-button">{t('CANCEL')}</button>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
  }),
  t: PropTypes.func,
  appData: PropTypes.shape(),
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
  },
  t: (str) => str,
  appData: {},
};

const VDIProfileViewModal = reduxForm({
  form: 'ProvisioningTemplateViewForm',
})(BasicCustomAppForm);

const mapStateToProps = (state) => ({
  ...VDIProfileSelectors.default(state),
  appData: VDIProfileSelectors.appDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleViewModal,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(VDIProfileViewModal)));

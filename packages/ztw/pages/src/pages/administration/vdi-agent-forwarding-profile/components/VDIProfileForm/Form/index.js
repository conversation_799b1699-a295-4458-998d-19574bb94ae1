import React, { useEffect } from 'react';
import { Field, reduxForm } from 'redux-form';
import { get, noop, isEmpty } from 'utils/lodash';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import {
  required,
  noWhiteSpacesAllowed,
  maxLength,
  ipAddressesOrRangesListWithPortAndProtocol,
  ipAddressesOrRangesWithPortAndProtocol,
} from 'utils/validations';
import { FormFieldLabel } from 'components/label';
import Input from 'components/Input';
// import ECRadioGroup from 'components/ecRadioGroup';
import ListBuilder from 'components/listBuilder';

const maxLength128 = maxLength(128);
const maxLength10240 = maxLength(10240);

const hasError = false;
const toolTipClientSourceIPAdresses = i18n.t('TOOLTIP_VDI_FORWRDING_PROFILE_IPS').split(/{[0-9]}/g);
const toolTipJSX = (
  <>
    {hasError && (
      <b className="alert-red">
        {i18n.t('VALIDATION_ERROR_INVALID_IP_ADDRESS')}
        <br />
        <br />
      </b>
    )}
    {toolTipClientSourceIPAdresses[0]}
    <ul className="tooltip-text-list">
      <li>{toolTipClientSourceIPAdresses[1]}</li>
      <li>{toolTipClientSourceIPAdresses[2]}</li>
      <li>{toolTipClientSourceIPAdresses[3]}</li>
      <li>{toolTipClientSourceIPAdresses[4]}</li>
      <li>{toolTipClientSourceIPAdresses[5]}</li>
    </ul>
    {toolTipClientSourceIPAdresses[6]}
  </>
);

function ConfigureEdgeconnectorForm(props) {
  useEffect(() => {
    const {
      actions,
      initialValues,
      // include,
      includeIps,
      excludeIps,
    } = props;
    const {
      autoFillFormValues,
    } = actions;

    let { include, exclude } = initialValues;

    if (includeIps && includeIps.length > 0) {
      include = includeIps;
    }

    if (excludeIps && excludeIps.length > 0) {
      exclude = excludeIps;
    }

    autoFillFormValues('vdiAgentProfile', 'include', initialValues.include || include);
    autoFillFormValues('vdiAgentProfile', 'exclude', initialValues.exclude || exclude);
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    const {
      saveVDIForwardingProfile,
    } = props;
  
    saveVDIForwardingProfile();
  };

  const {
    closeModal,
    t,
    formMeta,
    formSyncErrors,
    initialValues,
    actions,
    includeIps,
    excludeIps,
    valid,
  } = props;

  const {
    onIncludeIpAddress,
    onExcludeIpAddress,
  } = actions;

  let { include, exclude } = initialValues;

  if (includeIps) {
    include = includeIps;
  }

  if (excludeIps) {
    exclude = excludeIps;
  }

  const fieldName = 'name';
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);
  const hasError1 = meta && !!error;

  const {
    autoFillFormValues,
  } = actions;

  autoFillFormValues('vdiAgentProfile', 'include', include);
  autoFillFormValues('vdiAgentProfile', 'exclude', exclude);

  const validateInclude = (exclude && !isEmpty(exclude)) ? [
    ipAddressesOrRangesListWithPortAndProtocol,
  ] : [
    required,
    ipAddressesOrRangesListWithPortAndProtocol,
  ];

  const validateExclude = (include && !isEmpty(include)) ? [
    ipAddressesOrRangesListWithPortAndProtocol,
  ] : [
    required,
    ipAddressesOrRangesListWithPortAndProtocol,
  ];

  return (
    <div className="edgeconnector-modal">
      <div className="modal-content modal-body cc-provisioning-modal-content vdi-profile">
        <div className="vdi-form-wrapper">
          <form onSubmit={handleSubmit} className="vdi-form configure-vdi-template">
            <div className="form-sections-container">
              <div className="form-section">
                <div className="form-width">
                  <FormFieldLabel
                    styleClass={`${hasError1 ? 'invalid' : ''}`}
                    error={hasError1 ? t(error) : null}
                    text={t('NAME')}
                    tooltip={t('TOOLTIP_VDI_AGENT_PROFILE_NAME')} />
                  <Field
                    id="name"
                    name="name"
                    component={Input}
                    placeholder={t('ENTER_TEXT')}
                    validate={[
                      required,
                      noWhiteSpacesAllowed,
                      maxLength128,
                    ]}
                    styleClass="max-width" />

                  <FormFieldLabel
                    tooltip={t('TOOLTIP_VDI_AGENT_DESCRIPTION')}
                    text={t('DESCRIPTION')} />

                  <Field
                    id="desc"
                    name="desc"
                    component="textarea"
                    placeholder={t('VDI_AGENT_DESCRIPTION_PLACEHOLDER_TEXT')}
                    validate={[maxLength10240]}
                    className="cc-provisioning-description" />

                  <div className="section-divider"></div>

                  <div className="form-input-row">
                    <FormFieldLabel styleClass="criteria-title-text" text={t('CRITERIA_TEXT')} />
                    <div className="disabled-input criteria-content">{t('VDI_AGENT_FORWARDING_PROFILE_CRITERIA_TEXT')}</div>
                  </div>

                  <div className="checkbox-container full-width">
                    <FormFieldLabel
                      tooltip={toolTipJSX}
                      text={t('INCLUDE_IP_ADDRESSES')} />
                    <Field
                      id="include"
                      className="dns-client-source-ips"
                      name="include"
                      autoFillReduxForm={autoFillFormValues}
                      validate={validateInclude}
                      props={{
                        t,
                        hasSearch: true,
                        value: include?.map((item) => item?.toUpperCase()),
                        action: onIncludeIpAddress,
                        disabled: false,
                        removeItemsText: t('REMOVE_ALL'),
                        addButtonText: t('ADD_ITEMS'),
                        placeholder: t('VDI_AGENT_FORWARDING_PROFILE_IP_ADDRESS_PLACEHOLDER_TEXT'),
                        styleConfig: { inputMarginRight: 10 },
                        validation: ipAddressesOrRangesWithPortAndProtocol,
                      }}
                      component={ListBuilder}
                      onChange={noop} />
                  </div>

                  <div className="checkbox-container marginTop13 full-width">
                    <FormFieldLabel
                      tooltip={toolTipJSX}
                      text={t('EXCLUDE_IP_ADDRESSES')} />
                    <Field
                      id="exclude"
                      className="dns-client-source-ips"
                      name="exclude"
                      autoFillReduxForm={autoFillFormValues}
                      validate={validateExclude}
                      // value={exclude}
                      props={{
                        t,
                        hasSearch: true,
                        value: exclude?.map((item) => item?.toUpperCase()),
                        action: onExcludeIpAddress,
                        disabled: false,
                        removeItemsText: t('REMOVE_ALL'),
                        addButtonText: t('ADD_ITEMS'),
                        placeholder: t('VDI_AGENT_FORWARDING_PROFILE_IP_ADDRESS_PLACEHOLDER_TEXT'),
                        styleConfig: { inputMarginRight: 10 },
                        validation: ipAddressesOrRangesWithPortAndProtocol,
                      }}
                      component={ListBuilder}
                      onChange={noop} />
                  </div>

                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
      <div className="modal-footer">
        <div>
          <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
          <button type="submit" disabled={!valid} onClick={handleSubmit} className="next primary-button">{t('SUBMIT')}</button>
        </div>
      </div>
    </div>
  );
}

ConfigureEdgeconnectorForm.propTypes = {
  closeModal: PropTypes.func,
  t: PropTypes.func,
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  actions: PropTypes.shape({
    autoFillFormValues: PropTypes.func,
    onExcludeIpAddress: PropTypes.func,
    onIncludeIpAddress: PropTypes.func,
  }),
  includeIps: PropTypes.arrayOf(PropTypes.string),
  excludeIps: PropTypes.arrayOf(PropTypes.string),
  initialValues: PropTypes.shape(),
  valid: PropTypes.bool,
  saveVDIForwardingProfile: PropTypes.func,
};

ConfigureEdgeconnectorForm.defaultProps = {
  closeModal: (str) => str,
  t: (str) => str,
  formMeta: {},
  formSyncErrors: {},
  includeIps: null,
  excludeIps: null,
  initialValues: {},
  actions: {
    autoFillFormValues: noop,
    onExcludeIpAddress: noop,
    onIncludeIpAddress: noop,
  },
  valid: true,
  saveVDIForwardingProfile: noop,
};

const VDIAgentProfileForm = (reduxForm({
  form: 'vdiAgentProfile',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
  enableReinitialize: true,
})(withTranslation()(ConfigureEdgeconnectorForm)));

export default VDIAgentProfileForm;

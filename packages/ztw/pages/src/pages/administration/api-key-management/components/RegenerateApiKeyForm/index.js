// @flow
import React from 'react';
import { reduxForm } from 'redux-form';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
 
export function BasicCustomAppForm({
  valid,
  t,
  modalLoading,
  selectedRow,
  handleCancel,
  handleRegenerate,
}) {
  const onSubmitHandler = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    handleRegenerate(selectedRow);
  };

  return (
    <form onSubmit={onSubmitHandler} className="add-custom-app-form delete-confirm-form">
      <Loading loading={modalLoading} />
      <div className="form-sections-container">
        <div className="form-section">
          <p className="modal-text">
            {t('REGENERATE_API_KEY_CONFIRMATION_MESSAGE')}
          </p>
        </div>
      </div>
      <div className="dialog-footer">
        <button type="submit" className={`submit ${!valid ? 'disabled' : ''}`}>{t('OK')}</button>
        <button type="button" className="cancel" onClick={() => handleCancel(false)}>{t('CANCEL')}</button>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  handleCancel: PropTypes.func,
  handleRegenerate: PropTypes.func,
  t: PropTypes.func,
  modalLoading: PropTypes.bool,
  selectedRow: PropTypes.shape({}),
  valid: PropTypes.bool,
  
};

BasicCustomAppForm.defaultProps = {
  handleCancel: null,
  handleRegenerate: null,
  t: (str) => str,
  modalLoading: false,
  selectedRow: null,
  valid: true,
};

const RegenerateApiKeyForm = reduxForm({
  form: 'regenerateApiKeyForm',
})(BasicCustomAppForm);

export default (withTranslation()(RegenerateApiKeyForm));

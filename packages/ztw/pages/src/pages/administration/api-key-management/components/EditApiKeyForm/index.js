// @flow
import React from 'react';
import { reduxForm } from 'redux-form';
import { connect } from 'react-redux';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import ReactTooltip from 'react-tooltip';
import { validateApiKey } from 'utils/helpers';
import * as apiKeyManagementSelectors from 'ducks/apiKeyManagement/selectors';
import NewApiKey from '../NewApiKey';

export function BasicCustomAppForm(props) {
  const {
    valid,
    t,
    modalLoading,
    selectedRow,
    handleCancel,
    handleEdit,
    handleSubmit,
    formMeta,
    formSyncErrors,
  } = props;

  const onSubmitHandler = () => handleEdit(selectedRow);
  
  const { keyValue } = selectedRow || {};
  const meta = formMeta.newApiKey;

  return (
    <form onSubmit={handleSubmit(onSubmitHandler)} autoComplete="off" className="add-custom-app-form edit-api-confirm-form">
      <Loading loading={modalLoading} />
      <div className="form-sections-container">
        <div className="form-section">
          <p className="modal-text">
            {t('EDIT_ORGANIZATION_API_KEY_CONFIRMATION_MESSAGE')}
          </p>
        </div>
        <div className="form-section">

          <div className="form-input-rows">
            <div className="form-input-row">
              <div className="form-input-label">Current API Key</div>
              <div className="form-input">
                <span className="form-input-text-disabled">
                  {keyValue}
                </span>
              </div>
            </div>
            <div className="form-input-row">
              <NewApiKey
                t={t}
                formMeta={formMeta}
                formSyncErrors={formSyncErrors}
                selectedRow={selectedRow} />
            </div>
          </div>

          <ReactTooltip
            place="top"
            type="light"
            effect="solid"
            border
            className="form-field-tooltip-container"
            borderColor="#939393" />
        </div>
      </div>
      <div className="dialog-footer">
        <button type="submit" disabled={!valid || !(meta && meta.touched)} className={`submit ${!valid ? 'disabled' : ''}`}>{t('CONFIRM')}</button>
        <button type="button" className="cancel" onClick={() => handleCancel(false)}>{t('CANCEL')}</button>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  handleCancel: PropTypes.func,
  handleEdit: PropTypes.func,
  handleSubmit: PropTypes.func,
  t: PropTypes.func,
  modalLoading: PropTypes.bool,
  selectedRow: PropTypes.shape({}),
  valid: PropTypes.bool,
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
};

BasicCustomAppForm.defaultProps = {
  handleCancel: null,
  handleEdit: null,
  handleSubmit: null,
  t: (str) => str,
  modalLoading: false,
  selectedRow: null,
  valid: true,
  formMeta: {},
  formSyncErrors: {},
};

const mapStateToProps = (state) => ({
  formMeta: apiKeyManagementSelectors.formMetaSelector(state),
  formSyncErrors: apiKeyManagementSelectors.formSyncErrorsSelector(state),
});

const EditApiKeyForm = connect(mapStateToProps, null)(reduxForm({
  form: 'editApiKeyForm',
  validate: validateApiKey,
})(BasicCustomAppForm));

export default (withTranslation()(EditApiKeyForm));

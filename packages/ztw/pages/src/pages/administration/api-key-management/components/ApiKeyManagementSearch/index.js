// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { handleOnSearchFilter, toggleAddForm } from 'ducks/roleManagement';
import { withTranslation } from 'react-i18next';

export function ApiKeyManagementSearch({ t, cloudName }) {
  return (
    <>
      <ul className="content-tabs">
        <li className="content-tab active" data-id="view621_0" data-index="0" data-label="API_KEY">
          <span className="content-tab-title">
            {t('API_KEY')}
          </span>
        </li>
      </ul>
      <div className="table-layout-header-api-key">
        <div className="tips-text-container">
          <div className="tips-text">
            {t('THE_BASE_URL_FOR_YOUR_API')}
            {' '}
            <b>{`${cloudName}/api/v1`}</b>
          </div>
        </div>
      </div>
    </>
  );
}

ApiKeyManagementSearch.propTypes = {
  t: PropTypes.func,
  cloudName: PropTypes.string,
};

ApiKeyManagementSearch.defaultProps = {
  t: null,
  cloudName: '',
};

const mapStateToProps = (state, ownProps) => {
  return {
    ...ownProps,
    ...state.apiKeyManagement,
  };
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleAddForm,
    handleOnSearchFilter,
  }, dispatch);
  
  return {
    actions,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(
  withTranslation()(ApiKeyManagementSearch),
);

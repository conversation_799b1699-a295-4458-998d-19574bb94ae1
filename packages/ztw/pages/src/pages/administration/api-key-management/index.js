import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import { hasBsku, hasCsku, getReadOnly } from 'utils/helpers';
import * as apiKeyManagementSelectors from 'ducks/apiKeyManagement/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import {
  loader,
  toggleDeleteConfirmationForm,
  toggleRegenerateForm,
  toggleEditForm,
  delete<PERSON><PERSON><PERSON><PERSON>,
  edit<PERSON><PERSON><PERSON><PERSON>,
  regenerate<PERSON><PERSON><PERSON><PERSON>,
  create<PERSON><PERSON><PERSON><PERSON>,
} from 'ducks/apiKeyManagement';
import {
  ApiKeyManagementTable,
  ApiKeyManagementSearch,
  RegenerateApiKeyForm,
  EditApiKeyForm,
  GenerateApiKey,
} from './components';

export class ApiKeyManagement extends Component {
  static propTypes = {
    t: PropTypes.func,
    rbactabledata: PropTypes.arrayOf(PropTypes.shape()),
    handleClose: PropTypes.func,
    handleEditAction: PropTypes.func,
    handleViewAction: PropTypes.func,
    showForm: PropTypes.bool,
    showDeleteForm: PropTypes.bool,
    toggleDeleteConfirmationForm: PropTypes.func,
    selectedRow: PropTypes.string,
    showRegenerateForm: PropTypes.func,
    showEditForm: PropTypes.func,
    handleDelete: PropTypes.func,
    handleRegenerate: PropTypes.func,
    handleEdit: PropTypes.func,
    handleDeleteConfirmationForm: PropTypes.func,
    handleRegenerateForm: PropTypes.func,
    handleEditForm: PropTypes.func,
    handleCreateApiKey: PropTypes.func,
    load: PropTypes.func,
    modalTitle: PropTypes.string,
    modalLoading: PropTypes.bool,
    accessPermissions: PropTypes.shape({}),
    authType: PropTypes.string,
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  };

  static defaultProps = {
    t: (str) => str,
    rbactabledata: [],
    handleClose: null,
    handleEditAction: null,
    handleViewAction: null,
    showForm: false,
    showDeleteForm: false,
    toggleDeleteConfirmationForm: null,
    selectedRow: null,
    showRegenerateForm: null,
    showEditForm: null,
    handleDelete: null,
    handleRegenerate: null,
    handleEdit: null,
    handleDeleteConfirmationForm: null,
    handleRegenerateForm: null,
    handleEditForm: null,
    handleCreateApiKey: null,
    load: null,
    modalTitle: '',
    modalLoading: false,
    accessPermissions: {},
    authType: '',
    accessSubscriptions: [],
  };

  componentDidMount() {
    const { load } = this.props;
    load();
  }

  render() {
    const {
      t,
      rbactabledata,
      handleEditAction,
      handleViewAction,
      accessPermissions,
      authType,
      accessSubscriptions,

      modalLoading,
      showDeleteForm,
      showRegenerateForm,
      showEditForm,
      handleDelete,
      handleRegenerate,
      handleEdit,
      selectedRow,
      handleDeleteConfirmationForm,
      handleRegenerateForm,
      handleEditForm,
      handleCreateApiKey,
    } = this.props;
    const filteredData = rbactabledata;

    const isReadOnly = getReadOnly(accessPermissions.APIKEY_MANAGEMENT, authType);
    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }
    if (accessPermissions.APIKEY_MANAGEMENT === 'NONE') {
      return <PermissionRequired accessPermissions={accessPermissions} />;
    }

    return (
      <div className="main-container">
        <HelpArticle article={HELP_ARTICLES.ABOUT_API_KEY_MANAGEMENT} />
        <div className="page-title  header-3">
          {t('API_KEY_MANAGEMENT')}
        </div>
        <Loading {...this.props}>
          <div className="controls-container flex-direction-column">
            <ApiKeyManagementSearch isReadOnly={isReadOnly} />
            {!isReadOnly && (
              <GenerateApiKey
                handleCreateApiKey={handleCreateApiKey}
                isEmpty={filteredData.length === 0} />
            )}
          </div>
          <div className="source-ip-wrapper">
            <ServerError {...this.props}>
              <ApiKeyManagementTable
                tableData={filteredData}
                handleEditAction={handleEditAction}
                handleViewAction={handleViewAction}
                isReadOnly={isReadOnly}
                onMouseOverCb={() => this.handleOnMouseOverCb} />
              <Modal
                title={t('DELETE_CONFIRMATION')}
                isOpen={showDeleteForm}
                styleClass="password-expire-confirm-form"
                closeModal={() => handleDeleteConfirmationForm(false)}>
                <DeleteConfirmationForm
                  modalLoading={modalLoading}
                  selectedRow={selectedRow}
                  handleCancel={handleDeleteConfirmationForm}
                  handleDelete={handleDelete} />
              </Modal>
              <Modal
                title={t('REGENERATE_API_KEY')}
                isOpen={showRegenerateForm}
                styleClass="password-expire-confirm-form"
                closeModal={() => handleRegenerateForm(false)}>
                <RegenerateApiKeyForm
                  modalLoading={modalLoading}
                  selectedRow={selectedRow}
                  handleCancel={() => handleRegenerateForm(false)}
                  handleRegenerate={handleRegenerate} />
              </Modal>
              <Modal
                title={t('EDIT_CLOUD_SERVICE_API_KEY')}
                isOpen={showEditForm}
                // styleClass="confirm-existing-admin-form"
                closeModal={() => handleEditForm(false)}>
                <EditApiKeyForm
                  modalLoading={modalLoading}
                  selectedRow={selectedRow}
                  handleCancel={() => handleEditForm(false)}
                  handleEdit={handleEdit} />
              </Modal>
            </ServerError>
          </div>
        </Loading>
      </div>
    );
  }
}

ApiKeyManagement.propTypes = {
  searchData: PropTypes.string,
};

ApiKeyManagement.defaultProps = {
  searchData: '',
};

const mapStateToProps = (state) => ({
  ...apiKeyManagementSelectors.baseSelector(state),
  modalLoading: apiKeyManagementSelectors.modalLoadingSelector(state),
  selectedRow: apiKeyManagementSelectors.selectedRowSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loader,
    handleDeleteConfirmationForm: toggleDeleteConfirmationForm,
    handleRegenerateForm: toggleRegenerateForm,
    handleEditForm: toggleEditForm,
    handleDelete: deleteApiKey,
    handleRegenerate: regenerateApiKey,
    handleEdit: editApiKey,
    handleCreateApiKey: createApiKey,
  }, dispatch);
  return actions;
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(ApiKeyManagement));

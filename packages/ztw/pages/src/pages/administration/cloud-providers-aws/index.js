import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import NavTabs from 'components/navTabs';
import AddNewButton from 'components/addNewButton';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import RBAC from 'components/rbac';
import { BASE_LAYOUT } from 'config';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasBsku, hasCsku } from 'utils/helpers';
import ServerError from 'components/errors/ServerError';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import * as constants from 'ducks/login/constants';
import * as CloudProvidersSelectors from 'ducks/cloudProviders/selectors';
import * as loginSelectors from 'ducks/login/selectors';

import {
  toggleForm,
  loadAWSData,
  deleteAWSData,
  toggleDeleteForm,
  toggleViewModal,
} from 'ducks/cloudProviders';
import {
  CloudProvidersTable,
  CloudProvidersForm,
  AWSViewModel,
} from './components';

export class CloudProvidersAWS extends Component {
  static propTypes = {
    t: PropTypes.func,
    cloudProvidersAWSData: PropTypes.arrayOf(PropTypes.shape()),
    toggleCloudProvidersForm: PropTypes.func,
    showForm: PropTypes.bool,
    load: PropTypes.func,
    deleteCpAwsRowData: PropTypes.func,
    formTitle: PropTypes.string,
    showDeleteForm: PropTypes.bool,
    modalLoading: PropTypes.bool,
    selectedRowID: PropTypes.string,
    toggleDeleteConfirmationForm: PropTypes.func,
    accessPrivileges: PropTypes.shape({}),
    handleViewAwsForm: PropTypes.func,
    showViewForm: PropTypes.bool,
    accessPermissions: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  };

  static defaultProps = {
    t: (str) => str,
    cloudProvidersAWSData: null,
    toggleCloudProvidersForm: noop,
    showForm: false,
    load: noop,
    deleteCpAwsRowData: noop,
    formTitle: '',
    showDeleteForm: false,
    modalLoading: false,
    selectedRowID: null,
    toggleDeleteConfirmationForm: noop,
    accessPrivileges: {},
    accessSubscriptions: [],
  };

  componentDidMount() {
    const { load } = this.props;
    load();
  }

  render() {
    const {
      t,
      cloudProvidersAWSData,
      toggleCloudProvidersForm,
      showForm,
      deleteCpAwsRowData,
      formTitle,
      showDeleteForm,
      modalLoading,
      selectedRowID,
      toggleDeleteConfirmationForm,
      accessPrivileges,
      handleViewAwsForm,
      showViewForm,
      accessSubscriptions,
    } = this.props;

    const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_CLOUD_PROVISIONING;
    
    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
      return <PermissionRequired accessPermissions={accessPrivileges} />;
    }

    return (
      <div className="main-container">
        <div className="configuration-nav-tab">
          <NavTabs
            tabConfiguration={[{
              id: 'cloudproviders-aws',
              title: 'AWS',
              to: `${BASE_LAYOUT}/administration/cloudproviders-aws`,
            },
            {
              id: 'cloudproviders-azure',
              title: 'Azure',
              to: `${BASE_LAYOUT}/administration/cloudproviders-azure`,
            }]} />
        </div>
        <div className={`sipg-fragment ${accessPrivileges[permKey] === 'READ_ONLY' ? 'hide' : ''}`}>
          <AddNewButton label={t('ADD_AWS_CLOUD_ACCOUNT')} clickCallback={() => toggleCloudProvidersForm(true, null, 'ADD_AWS_CLOUD_ACCOUNT')} />
        </div>
        <div className="cloud-provider-wrapper">
          <RBAC privilege={permKey}>
            <Loading {...this.props}>
              <ServerError {...this.props}>
                <CloudProvidersTable
                  permission={accessPrivileges[permKey]}
                  tableData={cloudProvidersAWSData}
                  handleEditAction={toggleCloudProvidersForm}
                  handleViewAction={handleViewAwsForm}
                  handleDeleteAction={toggleDeleteConfirmationForm} />
                
                <Modal
                  title={t(formTitle)}
                  isOpen={showForm}
                  closeModal={() => toggleCloudProvidersForm(false)}>
                  <CloudProvidersForm />
                </Modal>
                <Modal
                  title={t('VIEW_CLOUD_PROVIDER_AWS')}
                  isOpen={showViewForm}
                  closeModal={() => handleViewAwsForm(false)}>
                  <AWSViewModel />
                </Modal>
                <Modal
                  title={t('DELETE_CONFIRMATION')}
                  isOpen={showDeleteForm}
                  closeModal={() => toggleDeleteConfirmationForm(false)}>
                  <DeleteConfirmationForm
                    modalLoading={modalLoading}
                    selectedRowID={selectedRowID}
                    handleCancel={toggleDeleteConfirmationForm}
                    handleDelete={deleteCpAwsRowData} />
                </Modal>
              </ServerError>
            </Loading>
          </RBAC>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...CloudProvidersSelectors.baseSelector(state),
  modalLoading: CloudProvidersSelectors.modalLoadingSelector(state),
  selectedRowID: CloudProvidersSelectors.selectedRowIDSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loadAWSData,
    toggleCloudProvidersForm: toggleForm,
    deleteCpAwsRowData: deleteAWSData,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    handleViewAwsForm: toggleViewModal,
  }, dispatch);
  return actions;
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(CloudProvidersAWS));

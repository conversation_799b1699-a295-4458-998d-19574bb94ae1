// @flow
import React from 'react';
import { connect } from 'react-redux';
import { isEmpty, noop } from 'utils/lodash';
import { bindActionCreators } from 'redux';
import { reduxForm, autofill } from 'redux-form';
import Loading from 'components/spinner/Loading';
import { FormSectionLabel } from 'components/label';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import * as loginSelectors from 'ducks/login/selectors';

import {
  saveForm,
  toggleForm,
  toggleViewModal,
} from 'ducks/dnsGateways';
import * as GatewaySelectors from 'ducks/dnsGateways/selectors';

import {
  FailureBehavior,
  Name,
  PrimaryDns,
  SecondaryDns,
} from './components';

export function BasicCustomAppForm(props) {
  const {
    actions,
    formSyncErrors,
    formTitle,
    formValues,
    handleSubmit,
    initialValues,
    modalLoading,
    showViewForm,
    t,
    valid,
  } = props;
  const {
    cancelHandle,
    cancelViewHandle,
    addGateway,
  } = actions;

  const hasError = formSyncErrors;

  const noChange = formTitle === 'EDIT_DNS_GATEWAY'
  && initialValues.name === formValues.name
  && initialValues.primaryIp === formValues.primaryIp
  && initialValues.secondaryIp === formValues.secondaryIp
  && initialValues?.ecDnsGatewayOptionsPrimary?.id === formValues?.ecDnsGatewayOptionsPrimary?.id
  // eslint-disable-next-line max-len
  && initialValues?.ecDnsGatewayOptionsSecondary?.id === formValues?.ecDnsGatewayOptionsSecondary?.id
  && initialValues.failureBehavior === (
    (formValues.failureBehavior && formValues.failureBehavior.id)
      || formValues.failureBehavior);

  return (
    <form onSubmit={handleSubmit(addGateway)} className="add-custom-app-form add-edit-dns-gateways">
      <Loading loading={modalLoading} />
      <div className="form-sections-container">
        <FormSectionLabel text={t('GATEWAY')} />
        <div className="form-section">
          <div className="g-row full-width">
            <Name
              {...props}
              isReadOnly={showViewForm} />
          </div>

          <div className="separator-line full-width" />

          <PrimaryDns
            {...props}
            isWanCTR={formValues?.name === 'WAN CTR'}
            isReadOnly={showViewForm} />
          <SecondaryDns
            {...props}
            isWanCTR={formValues?.name === 'WAN CTR'}
            isReadOnly={showViewForm} />

          <div className="separator-line full-width" />

          <div className="g-row extra-space">
            <FailureBehavior
              {...props}
              isReadOnly={showViewForm} />
          </div>
        </div>
      </div>
      <div className="dialog-footer">
        <div className="dialog-footer-left">
          {!showViewForm
          && <button type="submit" disabled={!valid || !hasError || noChange} className={`submit ${!valid || !hasError || noChange ? 'disabled' : ''}`}>{t('SAVE')}</button>}
          <button
            type="button"
            className="cancel"
            onClick={() => (showViewForm
              ? cancelViewHandle(null, false)
              : cancelHandle(null, false))}>
            {t('CANCEL')}
          </button>
        </div>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
    addGateway: PropTypes.func,
  }),
  dcApiCalled: PropTypes.bool,
  dcData: PropTypes.shape(),
  formSyncErrors: PropTypes.shape(),
  formTitle: PropTypes.string,
  formValues: PropTypes.shape(),
  handleSubmit: PropTypes.func,
  initialValues: PropTypes.shape(),
  modalLoading: PropTypes.bool,
  showViewForm: PropTypes.bool,
  t: PropTypes.func,
  valid: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
    saveLocationTemplate: noop,
  },
  dcApiCalled: false,
  dcData: [],
  formSyncErrors: {},
  formTitle: '',
  formValues: {},
  handleSubmit: noop,
  initialValues: {},
  modalLoading: false,
  showViewForm: false,
  t: (str) => str,
  valid: true,
};

const DnsGatewayForm = reduxForm({
  form: 'addEditDnsGatewayForm',
})(BasicCustomAppForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleForm,
    cancelViewHandle: toggleViewModal,
    addGateway: saveForm,
    autoFillReduxForm: autofill,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => {
  // ecDnsGatewayOptionsPrimary
  // ecDnsGatewayOptionsSecondary
  let initialValues = GatewaySelectors.appDataSelector(state);
  
  const isCustomPrimary = !isEmpty(initialValues?.primaryIp);
  const isCustomSecondary = !isEmpty(initialValues?.secondaryIp);
  const { ecDnsGatewayOptionsPrimary, ecDnsGatewayOptionsSecondary } = initialValues;
  if (isEmpty(initialValues)) {
    initialValues = {
      ...initialValues,
      ecDnsGatewayOptionsPrimary: { id: 'CUSTOM_DNS_SERVER', label: 'CUSTOM_DNS_SERVER' },
      ecDnsGatewayOptionsSecondary: { id: 'CUSTOM_DNS_SERVER', label: 'CUSTOM_DNS_SERVER' },
    };
  } else {
    if (isCustomPrimary) {
      initialValues = ({
        ...initialValues,
        ecDnsGatewayOptionsPrimary: {
          id: 'CUSTOM_DNS_SERVER',
          label: 'CUSTOM_DNS_SERVER',
        },
      });
    } else {
      initialValues = ({
        ...initialValues,
        ecDnsGatewayOptionsPrimary: {
          id: ecDnsGatewayOptionsPrimary,
          label: ecDnsGatewayOptionsPrimary,
        },
      });
    }
    if (isCustomSecondary || !ecDnsGatewayOptionsSecondary) {
      initialValues = ({
        ...initialValues,
        ecDnsGatewayOptionsSecondary: {
          id: 'CUSTOM_DNS_SERVER',
          label: 'CUSTOM_DNS_SERVER',
        },
      });
    } else {
      initialValues = ({
        ...initialValues,
        ecDnsGatewayOptionsSecondary: {
          id: ecDnsGatewayOptionsSecondary,
          label: ecDnsGatewayOptionsSecondary,
        },
      });
    }
  }
  
  return ({
  // dcApiCalled: GatewaySelectors.dcApiCalledSelector(state),
  // dcData: GatewaySelectors.dcDataSelector(state),
    formTitle: GatewaySelectors.formTitleSelector(state),
    formMeta: GatewaySelectors.formMetaSelector(state),
    formSyncErrors: GatewaySelectors.formSyncErrorsSelector(state),
    formValues: GatewaySelectors.formValuesSelector(state),
    initialValues,
    modalLoading: GatewaySelectors.modalLoadingSelector(state),
    configData: loginSelectors.configDataSelector(state),
  });
};

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(DnsGatewayForm));

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import AddNewButton from 'components/addNewButton';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import SimpleSearchInput from 'components/Input/SimpleSearchInput';
import NavTabs from 'components/navTabs';
import { DNS_GATEWAY_TABLE_CONFIGS } from 'ducks/dnsGateways/constants';
import ConfigTable from 'components/configTable';
import RBAC from 'components/rbac';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import {
  hasBsku, hasCsku, getReadOnly, verifyConfigData,
} from 'utils/helpers';
import ServerError from 'components/errors/ServerError';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import * as constants from 'ducks/login/constants';
import * as dnsGatewaySelectors from 'ducks/dnsGateways/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import {
  loadGatewaysData,
  toggleDeleteForm,
  deleteGateways,
  toggleForm,
  toggleViewModal,
  handleOnSearchFilter,
} from 'ducks/dnsGateways';
import {
  GatewayForm,
} from './components';

export class DNSGateways extends Component {
  static propTypes = {
    accessPermissions: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    authType: PropTypes.string,
    configData: PropTypes.shape(),
    formTitle: PropTypes.string,
    gatewaystabledata: PropTypes.arrayOf(PropTypes.shape()),
    handleDelete: PropTypes.func,
    handleSearch: PropTypes.func,
    handleViewGatewaysForm: PropTypes.func,
    load: PropTypes.func,
    modalLoading: PropTypes.bool,
    searchData: PropTypes.string,
    selectedRowID: PropTypes.string,
    showDeleteForm: PropTypes.bool,
    showForm: PropTypes.bool,
    showViewForm: PropTypes.bool,
    t: PropTypes.func,
    toggleDeleteConfirmationForm: PropTypes.func,
    toggleGatewayForm: PropTypes.func,
  };

  static defaultProps = {
    accessPermissions: {},
    accessSubscriptions: [],
    authType: '',
    configData: {},
    formTitle: '',
    gatewaystabledata: null,
    handleDelete: noop,
    handleViewGatewaysForm: noop,
    load: noop,
    modalLoading: false,
    searchData: '',
    selectedRowID: null,
    showDeleteForm: false,
    showForm: false,
    showViewForm: false,
    t: (str) => str,
    toggleDeleteConfirmationForm: noop,
    toggleGatewayForm: noop,
  };

  componentDidMount() {
    const { load } = this.props;
    load();
  }

  fromAPItoUI = (value) => {
    if (value && value.includes('LAN_PRI_DNS')) return 'LAN_PRI_DNS';
    if (value && value.includes('LAN_SEC_DNS')) return 'LAN_SEC_DNS';
    if (value && value.includes('WAN_PRI_DNS')) return 'WAN_PRI_DNS';
    if (value && value.includes('WAN_SEC_DNS')) return 'WAN_SEC_DNS';
  
    return '';
  };
  
  getTableData = () => {
    const {
      gatewaystabledata, accessPermissions, authType, searchData,
    } = this.props;
    const isReadOnly = getReadOnly(
      accessPermissions.EDGE_CONNECTOR_FORWARDING,
      authType,
    );
    const tableData = gatewaystabledata.filter(
      (row) => searchData === ''
      || row.name.toLowerCase().includes(searchData.toLowerCase()),
    ).map((row) => {
      return {
        ...row,
        id: row.id,
        name: row.name,
        ecDnsGatewayOptionsPrimary: this.fromAPItoUI(row.ecDnsGatewayOptionsPrimary),
        ecDnsGatewayOptionsSecondary: this.fromAPItoUI(row.ecDnsGatewayOptionsSecondary),
        isReadOnly: ['Default Connector Self', 'Default EC Self', 'LAN CTR', 'WAN CTR'].includes(row.name) || isReadOnly,
        isDeletable: !['Default Connector Self', 'Default EC Self', 'LAN CTR', 'WAN CTR'].includes(row.name) && !isReadOnly,
        isEditable: !['Default Connector Self', 'Default EC Self', 'LAN CTR', 'WAN CTR'].includes(row.name) && !isReadOnly,
      };
    });

    return tableData;
  };

  render() {
    const {
      accessPermissions,
      accessSubscriptions,
      authType,
      configData,
      formTitle,
      handleDelete,
      handleSearch,
      handleViewGatewaysForm,
      modalLoading,
      selectedRowID,
      showDeleteForm,
      showForm,
      showViewForm,
      t,
      toggleDeleteConfirmationForm,
      toggleGatewayForm,
    } = this.props;
    const isReadOnly = getReadOnly(
      accessPermissions.EDGE_CONNECTOR_FORWARDING,
      authType,
    );
    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    const disableDnsGateway = verifyConfigData({ configData, key: 'disableDnsGateway' });

    if (accessPermissions.EDGE_CONNECTOR_FORWARDING === 'NONE' || disableDnsGateway) {
      return <PermissionRequired accessPermissions={accessPermissions} />;
    }

    return (
      <div className="main-container main-container-gateways">
        <div className="page-title header-3">
          {t('DNS_GATEWAY')}
        </div>
        <div className="gateways-fragment">
          <div className="grid-toolbar-left">
            {!isReadOnly && (
              <AddNewButton
                label={t('ADD_DNS_GATEWAY')}
                clickCallback={() => toggleGatewayForm({}, true, 'ADD_DNS_GATEWAY')} />
            )}
          </div>
          <div className="grid-toolbar-right">
            <SimpleSearchInput
              withButton
              onKeyPressCb={handleSearch} />
          </div>
        </div>
        <div className="gateways-wrapper zia-gateways">
          <RBAC privilege={constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_FORWARDING}>
            <Loading {...this.props}>
              <HelpArticle article={HELP_ARTICLES.LOG_AND_CONTROL_GATEWAY} />
              <ServerError {...this.props}>
                <ConfigTable
                  {...DNS_GATEWAY_TABLE_CONFIGS}
                  onHandleRowEdit={(e) => toggleGatewayForm(e, true, 'EDIT_DNS_GATEWAY')}
                  onHandleRowDelete={toggleDeleteConfirmationForm}
                  onHandleRowView={(e) => handleViewGatewaysForm(e, true, 'VIEW_DNS_GATEWAYS')}
                  data={this.getTableData()} />

                <Modal
                  title={formTitle}
                  isOpen={showForm || showViewForm}
                  styleClass="gateway-modal dns-gateway"
                  closeModal={() => (showViewForm
                    ? handleViewGatewaysForm(null, false)
                    : toggleGatewayForm(null, false))}>
                  <GatewayForm showViewForm={showViewForm} />
                </Modal>
                <Modal
                  title={t('DELETE_CONFIRMATION')}
                  isOpen={showDeleteForm}
                  closeModal={() => toggleDeleteConfirmationForm(null, false)}>
                  <DeleteConfirmationForm
                    modalLoading={modalLoading}
                    selectedRowID={selectedRowID}
                    handleCancel={toggleDeleteConfirmationForm}
                    handleDelete={handleDelete} />
                </Modal>
              </ServerError>
            </Loading>
          </RBAC>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...dnsGatewaySelectors.baseSelector(state),
  modalLoading: dnsGatewaySelectors.modalLoadingSelector(state),
  selectedRowID: dnsGatewaySelectors.selectedRowIDSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  configData: loginSelectors.configDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loadGatewaysData,
    toggleGatewayForm: toggleForm,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deleteGateways,
    handleViewGatewaysForm: toggleViewModal,
    handleSearch: handleOnSearchFilter,
  }, dispatch);
  return actions;
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(DNSGateways));

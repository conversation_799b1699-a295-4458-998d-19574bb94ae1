/* eslint-disable max-len */
/* eslint-disable eqeqeq */
// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import withRouter from 'layout/withRouter';
import { reduxForm } from 'redux-form';
import { noop, isEmpty } from 'utils/lodash';
import { convertFromMinutesToTimeUnit, getSubscriptionLicenses } from 'utils/helpers';
import { FormSectionLabel, FormFieldLabel } from 'components/label';
import Loading from 'components/spinner/Loading';
import { toggleViewModal, saveForm } from 'ducks/locations';

import * as LocationsSelectors from 'ducks/locations/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import {
  AUP,
  AupTimeoutInDays,
  // AupBlockInternet,
  // AupForceSSL,
  BandwithControl,
  BandwithControlDownload,
  BandwithControlUpload,
  Caution,
  EnforceAuth,
  EnforceFirewall,
  // GreTunnelInfo,
  IpsControl,
  XffForwarding,

  SurrogateIp,
  SurrogateIdleTimeInMinutes,
  EnforceSurrogateIp,
  SurrogateRefreshRate,
} from './components';

export function BasicCustomAppForm({
  actions,
  t,
  appData,
  formValues,
  handleSubmit,
  submitting,
  formMeta,
  formSyncErrors,
  editForm,
  modalLoading,
  accessDetailSubscriptions,
}) {
  const {
    name,
    country,
    state,
    tz,
    // ecLocation,
    staticLocationGroups = {},
    dynamiclocationGroups = [],
    excludeFromManualGroups,
    excludeFromDynamicGroups,
    profile,
    description,
    // ipAddresses = [],
    // ports,
    // vpnCredentials,
    // virtualZens,
    // virtualZenClusters,
  } = appData;

  const {
    xffForwardEnabled,
    authRequired,
    cautionEnabled,
    aupEnabled,
    aupTimeoutInDays,
    // aupBlockInternetUntilAccepted,
    // aupForceSslInspection,
    ofwEnabled,
    ipsControl,

    surrogateIP,
    idleTimeInMinutes,
    displayTimeUnit,
    surrogateIPEnforcedForKnownBrowsers,
    surrogateRefreshTimeInMinutes,
    surrogateRefreshTimeUnit,
    
    bandwidthEnabled,
    dnBandwidth,
    upBandwidth,
  } = formValues;

  // const greTunnelInfo = gre.filter(x => ipAddresses.includes(x.ipAddresses));

  const handleCancelClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    const { cancelHandle } = actions;
    
    cancelHandle(null, false);
  };
  const disableSave = !isEmpty(formSyncErrors);
  // need to be == to validate BOOLEAN is equal
  const noChange = appData.aupBlockInternetUntilAccepted == formValues.aupBlockInternetUntilAccepted
  && appData.aupEnabled == formValues.aupEnabled
  && appData.aupForceSslInspection == formValues.aupForceSslInspection
  && (!appData.aupEnabled || appData.aupTimeoutInDays.toString() === formValues.aupTimeoutInDays)
  && appData.authRequired == formValues.authRequired
  && (appData.bandwidthEnabled
      || (appData.dnBandwidth && appData.dnBandwidth !== 0)
      || (appData.upBandwidth && appData.upBandwidth !== 0) ? 'true' : 'false') === formValues.bandwidthEnabled
  && appData.cautionEnabled == formValues.cautionEnabled
  && (!appData.surrogateIP || appData.displayTimeUnit === formValues.displayTimeUnit)
  && (!appData.surrogateIP || String(convertFromMinutesToTimeUnit(appData.displayTimeUnit, appData.idleTimeInMinutes)) === formValues.idleTimeInMinutes)
  && (!appData.ofwEnabled || appData.ipsControl == formValues.ipsControl)
  && appData.ofwEnabled == formValues.ofwEnabled
  && appData.surrogateIP == formValues.surrogateIP
  && appData.surrogateIPEnforcedForKnownBrowsers == formValues.surrogateIPEnforcedForKnownBrowsers
  && (!appData.surrogateIPEnforcedForKnownBrowsers || String(convertFromMinutesToTimeUnit(appData.surrogateRefreshTimeUnit, appData.surrogateRefreshTimeInMinutes)) === formValues.surrogateRefreshTimeInMinutes)
  && (!appData.surrogateIPEnforcedForKnownBrowsers || appData.surrogateRefreshTimeUnit === formValues.surrogateRefreshTimeUnit)
  && (Number.parseInt(appData.dnBandwidth / 1000, 10) == (Number.parseInt(formValues.dnBandwidth, 10) || 0))
  && (Number.parseInt(appData.upBandwidth / 1000, 10) == (Number.parseInt(formValues.upBandwidth, 10) || 0))
  && appData.xffForwardEnabled == formValues.xffForwardEnabled;

  const onSubmit = () => {
    actions.handleSaveForm();
  };

  const zfwNgWithLog = getSubscriptionLicenses(accessDetailSubscriptions, 'ZFW_NG_WITH_LOG') > 0;
  const zFirewalIps = getSubscriptionLicenses(accessDetailSubscriptions, 'Z_FIREWALL_IPS') > 0;
  
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="wizard-form wizard-form-location">
      <Loading loading={modalLoading} />
      <div>
        <div className="form-sections-container">
          <FormSectionLabel text={t('LOCATION')} />
          <div className="form-section">
            <div className="input-container half-width">
              <FormFieldLabel text={t('NAME')} styleClass="no-margin-top" />
              <p className="disabled-input">{name}</p>
            </div>
            <div className="input-container half-width">
              <FormFieldLabel text={t('COUNTRY')} styleClass="no-margin-top" />
              <p className="disabled-input">{t(country || '---')}</p>
            </div>
            <div className="input-container half-width">
              <FormFieldLabel text={t('STATE_PROVINCE')} styleClass="no-margin-top" />
              <p className="disabled-input">{t(state || '---')}</p>
            </div>
            <div className="input-container half-width">
              <FormFieldLabel text={t('TIME_ZONE')} styleClass="no-margin-top" />
              <p className="disabled-input">{t(tz || '---')}</p>
            </div>
            <div className="input-container half-width">
              <FormFieldLabel text={t('STATIC_LOCATION_GROUPS')} styleClass="no-margin-top" />
              <p className="disabled-input">{t(!isEmpty(staticLocationGroups) ? staticLocationGroups.map((x) => x.name).join(',') : '---')}</p>
            </div>
            <div className="input-container half-width">
              <FormFieldLabel text={t('DYNAMIC_LOCATION_GROUPS')} styleClass="no-margin-top" />
              <p className="disabled-input">
                {t(dynamiclocationGroups && !isEmpty(dynamiclocationGroups.filter((x) => x.name !== 'Unassigned Locations'))
                  ? dynamiclocationGroups.map((x) => x.name).join(',')
                  : '---')}
              </p>
            </div>
            <div className="input-container half-width">
              <FormFieldLabel text={t('EXCLUDE_FROM_STATIC_LOCATION_GROUPS')} styleClass="no-margin-top" />
              <p className="disabled-input">{t(excludeFromManualGroups ? 'ENABLED' : 'DISABLED')}</p>
            </div>
            <div className="input-container half-width">
              <FormFieldLabel text={t('EXCLUDE_FROM_DYNAMIC_LOCATION_GROUPS')} styleClass="no-margin-top" />
              <p className="disabled-input">{t(excludeFromDynamicGroups ? 'ENABLED' : 'DISABLED')}</p>
            </div>
            <div className="input-container half-width">
              <FormFieldLabel text={t('LOCATION_GROUP_TYPE')} styleClass="no-margin-top" />
              <p className="disabled-input">{t(profile || '---')}</p>
            </div>
            <div className="input-container full-width">
              <FormFieldLabel text={t('DESCRIPTION')} styleClass="no-margin-top" />
              <p className="disabled-input">{t(description || '---')}</p>
            </div>
          </div>

          {/* <FormSectionLabel text={t('ADDRESSING')} />
          <div className="form-section">
            <div className="input-container review full-width">
              <FormFieldLabel text={t('STATIC_IP_ADDRESSES')} styleClass="no-margin-top" />
              <p className="disabled-input">{!isEmpty(ipAddresses) ?
                t(ipAddresses.join(',')) :
                '---'}</p>
            </div>
            <div className="input-container review half-width">
              <FormFieldLabel text={t('PORTS')} styleClass="no-margin-top" />
              <p className="disabled-input">{t(ports || '---')}</p>
            </div>
            <div className="input-container review half-width">
              <FormFieldLabel text={t('VPN_CREDENTIALS')} styleClass="no-margin-top" />
              <p className="disabled-input">{t(vpnCredentials || '---')}</p>
            </div>
            {!isEmpty(greTunnelInfo) && (
              <div className="input-container review full-width">
                <FormFieldLabel text={t('GRE_TUNNEL_INFO')} styleClass="no-margin-top" />
                <GreTunnelInfo gre={greTunnelInfo} />
              </div>
            )}
            <div className="input-container review half-width">
              <FormFieldLabel text={t('VSE_NODES')} styleClass="no-margin-top" />
              <p className="disabled-input">{t(virtualZens || '---')}</p>
            </div>
            <div className="input-container review half-width">
              <FormFieldLabel text={t('VSE_CLUSTERS')} styleClass="no-margin-top" />
              <p className="disabled-input">{t(virtualZenClusters || '---')}</p>
            </div>
          </div> */}

          <FormSectionLabel text={t('GATEWAY_OPTIONS')} />
          <div className="form-section">
            <XffForwarding
              value={xffForwardEnabled}
              viewOnly={!editForm} />
            <EnforceAuth
              value={authRequired}
              viewOnly={!editForm} />
            {authRequired && !editForm && (
              <>
                <SurrogateIp
                  value={surrogateIP}
                  viewOnly={!editForm} />
                {surrogateIP && (
                  <SurrogateIdleTimeInMinutes
                    value={idleTimeInMinutes}
                    displayTimeUnit={displayTimeUnit}
                    meta={formMeta.idleTimeInMinutes}
                    error={formSyncErrors.idleTimeInMinutes}
                    viewOnly={!editForm} />
                )}
                {surrogateIP && (
                  <EnforceSurrogateIp
                    value={surrogateIPEnforcedForKnownBrowsers}
                    viewOnly={!editForm} />
                )}
                {surrogateIP && surrogateIPEnforcedForKnownBrowsers && (
                  <SurrogateRefreshRate
                    value={surrogateRefreshTimeInMinutes}
                    surrogateRefreshTimeUnit={surrogateRefreshTimeUnit}
                    meta={formMeta.surrogateRefreshTimeInMinutes}
                    error={formSyncErrors.surrogateRefreshTimeInMinutes}
                    viewOnly={!editForm} />
                )}
              </>
            )}
            {(!authRequired || editForm) && (
              <>
                <Caution
                  value={cautionEnabled}
                  viewOnly={!editForm} />
                <AUP
                  value={aupEnabled}
                  viewOnly={!editForm} />
                {aupEnabled && (
                  <AupTimeoutInDays
                    value={aupTimeoutInDays}
                    meta={formMeta.aupTimeoutInDays}
                    error={formSyncErrors.aupTimeoutInDays}
                    viewOnly={!editForm} />
                )}
              </>
            )}
            <EnforceFirewall
              value={ofwEnabled}
              viewOnly={!editForm} />
            {ofwEnabled
            && (zfwNgWithLog || zFirewalIps)
            && (
              <IpsControl
                value={ipsControl}
                viewOnly={!editForm} />
            )}
          </div>
          
          {/* {aupEnabled && (
            <>
              <FormSectionLabel text={t('BANDWIDTH_CONTROL')} />
              <div className="form-section">
                <AupBlockInternet
                  value={aupBlockInternetUntilAccepted}
                  viewOnly={!editForm} />
                <AupForceSSL
                  value={aupForceSslInspection}
                  viewOnly={!editForm} />
              </div>
            </>
          )} */}

          <FormSectionLabel text={t('BANDWIDTH_CONTROL')} />
          <div className="form-section">
            <BandwithControl
              value={bandwidthEnabled === 'true' ? 'true' : 'false'}
              viewOnly={!editForm} />
            {bandwidthEnabled === 'true' && (
              <div className="g-row input-container">
                <BandwithControlDownload
                  value={dnBandwidth}
                  meta={formMeta.dnBandwidth}
                  error={formSyncErrors.dnBandwidth}
                  viewOnly={!editForm} />
                <BandwithControlUpload
                  value={upBandwidth}
                  meta={formMeta.upBandwidth}
                  error={formSyncErrors.upBandwidth}
                  viewOnly={!editForm} />
                  
              </div>
            )}
          </div>
          
        </div>
        
      </div>
      <div className="modal-footer">
        {editForm && <button type="submit" disabled={disableSave || noChange || submitting} className="submit primary-button">{t('SAVE')}</button>}
        <button type="submit" onClick={handleCancelClick} className="cancel">{t('CANCEL')}</button>
      </div>

    </form>
  );
}

BasicCustomAppForm.propTypes = {
  editForm: PropTypes.bool,
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
  }),
  t: PropTypes.func,
  submitting: PropTypes.bool,
  modalLoading: PropTypes.bool,
  handleSubmit: PropTypes.func,
  appData: PropTypes.shape(),
  formValues: PropTypes.shape(),
  formMeta: PropTypes.shape(),
  formSyncErrors: PropTypes.shape(),
  accessDetailSubscriptions: PropTypes.arrayOf(PropTypes.shape()),
};

BasicCustomAppForm.defaultProps = {
  editForm: false,
  actions: {
    cancelHandle: noop,
  },
  t: (str) => str,
  appData: {},
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  submitting: false,
  modalLoading: false,
  handleSubmit: (str) => str,
  accessDetailSubscriptions: [],
};

const BasicCustomForm = reduxForm({
  form: 'LocationsForm',
  // destroyOnUnmount: false,
})(BasicCustomAppForm);

const LocationViewModal = connect(
  (state) => {
    const {
      authRequired,
      bandwidthEnabled,
      cautionEnabled,
      xffForwardEnabled,
      aupEnabled,
      aupTimeoutInDays,
      aupBlockInternetUntilAccepted,
      aupForceSslInspection,
      ofwEnabled,
      ipsControl,
      dnBandwidth,
      upBandwidth,
      surrogateIP,
      surrogateIPEnforcedForKnownBrowsers,
      surrogateRefreshTimeInMinutes,
      surrogateRefreshTimeUnit,
      idleTimeInMinutes,
      displayTimeUnit,
    } = LocationsSelectors.appDataSelector(state);
  
    return ({
      initialValues: {
        authRequired,
        bandwidthEnabled: bandwidthEnabled || (dnBandwidth && dnBandwidth !== 0) || (upBandwidth && upBandwidth !== 0) ? 'true' : 'false',
        cautionEnabled,
        xffForwardEnabled,
        aupEnabled,
        aupTimeoutInDays: (aupTimeoutInDays && aupTimeoutInDays !== 0) ? aupTimeoutInDays.toString() : '1',
        aupBlockInternetUntilAccepted,
        aupForceSslInspection,
        ofwEnabled,
        ipsControl,
        dnBandwidth: (dnBandwidth && dnBandwidth !== 0) ? (dnBandwidth / 1000).toString() : '',
        upBandwidth: (upBandwidth && upBandwidth !== 0) ? (upBandwidth / 1000).toString() : '',
        surrogateIP,
        surrogateIPEnforcedForKnownBrowsers,
        surrogateRefreshTimeInMinutes: surrogateRefreshTimeInMinutes
          ? String(convertFromMinutesToTimeUnit(surrogateRefreshTimeUnit, surrogateRefreshTimeInMinutes)) : '1',
        surrogateRefreshTimeUnit: surrogateRefreshTimeUnit || 'HOUR',
        idleTimeInMinutes: idleTimeInMinutes ? String(convertFromMinutesToTimeUnit(displayTimeUnit, idleTimeInMinutes)) : '2',
        displayTimeUnit: displayTimeUnit || 'HOUR',
      },
    });
  },
)(BasicCustomForm);

const mapStateToProps = (state) => ({
  ...LocationsSelectors.default(state),
  appData: LocationsSelectors.appDataSelector(state),
  formValues: LocationsSelectors.formValuesSelector(state),
  formMeta: LocationsSelectors.formMetaSelector(state),
  formSyncErrors: LocationsSelectors.formSyncErrorsSelector(state),
  accessDetailSubscriptions: loginSelectors.accessDetailSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleViewModal,
    handleSaveForm: saveForm,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(LocationViewModal)));

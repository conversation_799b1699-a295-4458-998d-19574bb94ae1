// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import NavTabs from 'components/navTabs';
import SearchBox from 'components/searchBox';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import { HELP_ARTICLES, BASE_LAYOUT } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import { LOCATIONS_TABLE_CONFIGS } from 'ducks/locations/constants';
import ConfigTable from 'components/configTable';
import * as constants from 'ducks/login/constants';
import RBAC from 'components/rbac';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasBsku, hasCsku, getReadOnly } from 'utils/helpers';
import * as locationSelector from 'ducks/locations/selectors';
import * as sublocationSelector from 'ducks/sublocations/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import {
  deleteLocationData,
  hanldeExpandClick,
  loadLocationData,
  toggleDeleteForm,
  toggleEditModal,
  toggleViewModal,
} from 'ducks/locations';

import {
  handlePageNumber,
  handlePageSize,
  handleCloseSubLocation,
  loadSublocationData,
  handleOpenSubLocation,
  toggleViewSublocation,
} from 'ducks/sublocations';

import {
  LocationViewModal,
  SubLocationViewModal,
} from './components';

export class Locations extends React.Component {
  static propTypes = {
    accessPrivileges: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    authType: PropTypes.string,
    deleteLocationRowItem: PropTypes.func,
    editForm: PropTypes.bool,
    expandAll: PropTypes.bool,
    hasNextPage: PropTypes.bool,
    load: PropTypes.func,
    locationsdata: PropTypes.arrayOf(PropTypes.shape()),
    modalLoading: PropTypes.bool,
    moreItemsLoading: PropTypes.bool,
    onLoadSubLocation: PropTypes.func,
    sublocations: PropTypes.shape({}),
    selectedRowID: PropTypes.string,
    showDeleteForm: PropTypes.bool,
    showForm: PropTypes.bool,
    showViewSublocation: PropTypes.bool,
    t: PropTypes.func,
    tableColumns: PropTypes.arrayOf(PropTypes.shape()),
    toggleCloseSubLocation: PropTypes.func,
    toggleDeleteConfirmationForm: PropTypes.func,
    toggleLocationEditForm: PropTypes.func,
    toggleLocationViewForm: PropTypes.func,
  };

  static defaultProps = {
    accessPrivileges: {},
    accessSubscriptions: [],
    authType: '',
    deleteLocationRowItem: noop,
    editForm: false,
    expandAll: null,
    hasNextPage: false,
    load: noop,
    locationsdata: [],
    modalLoading: false,
    moreItemsLoading: false,
    onLoadSubLocation: noop,
    sublocations: {},
    selectedRowID: null,
    showDeleteForm: false,
    showForm: false,
    showViewSublocation: false,
    t: (str) => str,
    tableColumns: [],
    toggleCloseSubLocation: null,
    toggleDeleteConfirmationForm: noop,
    toggleLocationEditForm: noop,
    toggleLocationViewForm: noop,
  };

  componentDidMount() {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.LOCATIONS });
    const { load } = this.props;
    load(null, null, null, true); // passing fourth arg true when page loads for the first time
  }

  getTableData = () => {
    const { locationsdata, accessPrivileges, authType } = this.props;
    const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_LOCATION_MANAGEMENT, authType);

    const tableData = locationsdata.map((row) => {
      return {
        ...row,
        id: row.id,
        name: row.name,
        dynamiclocationGroups: row.dynamiclocationGroups || [],
        sslScanEnabled: row.sslScanEnabled,
        vpcInfo: row.vpcInfo || {},
        isReadOnly: isReadOnly || !row.ecLocation,
        isDeletable: false && !isReadOnly && row.ecLocation,
        isEditable: !isReadOnly && row.ecLocation,
      };
    });
    return tableData;
  };

  render() {
    const {
      accessPrivileges,
      accessSubscriptions,
      deleteLocationRowItem,
      editForm,
      hasNextPage,
      load,
      modalLoading,
      moreItemsLoading,
      onLoadSubLocation,
      sublocations,
      selectedRowID,
      showDeleteForm,
      showForm,
      t,
      toggleCloseSubLocation,
      toggleDeleteConfirmationForm,
      toggleLocationEditForm,
      toggleLocationViewForm,
    } = this.props;

    const permKey = [constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_LOCATION_MANAGEMENT,
      constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_TEMPLATE];
    const hasTemplate = accessPrivileges.EDGE_CONNECTOR_TEMPLATE !== 'NONE';
    
    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPrivileges.EDGE_CONNECTOR_LOCATION_MANAGEMENT === 'NONE') {
      return <PermissionRequired accessPermissions={accessPrivileges} />;
    }

    return (
      <div className="main-container locations-main-container">
        <div className="location-header">
          <div className="configuration-nav-tab">
            <div className="page-title  header-3">
              {t('LOCATIONS')}
      
            </div>
          </div>
          <div className="search-container align-self-end ">
            <SearchBox placeholder={t('SEARCH')} clickCallback={() => load(null, null, null, true)} />
          </div>
        </div>
        <div className="container-row locations">
          <RBAC privilege={permKey}>
            <Loading {...this.props}>
              <HelpArticle article={HELP_ARTICLES.LOCATIONS} />
              <ServerError {...this.props}>
                <ConfigTable
                  {...LOCATIONS_TABLE_CONFIGS}
                  // eslint-disable-next-line max-len
                  permission={accessPrivileges[constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_LOCATION_MANAGEMENT]}
                  onHandleLink={(appData) => handleOpenSubLocation(appData)}
                  linkNameParameter="childCount"
                  onHandleRowDelete={toggleDeleteConfirmationForm}
                  onHandleRowView={toggleLocationViewForm}
                  onHandleRowEdit={toggleLocationEditForm}
                  moreItemsLoading={moreItemsLoading}
                  hasNextPage={hasNextPage}
                  loadMore={load}
                  pagination
                  data={this.getTableData()} />
                <Modal
                  title={t(`${editForm ? 'EDIT_LOCATIONS' : 'VIEW_LOCATIONS'}`)}
                  isOpen={showForm}
                  styleClass="location-view-modal"
                  closeModal={() => toggleLocationViewForm(null, false)}>
                  <LocationViewModal />
                </Modal>
                <Modal
                  title={t('VIEW_SUB_LOCATIONS')}
                  isOpen={sublocations && sublocations.openSublocations}
                  styleClass="location-view-modal sublocation"
                  closeModal={() => toggleCloseSubLocation()}>
                  <SubLocationViewModal
                    {...this.props}
                    loadMore={onLoadSubLocation}
                    handleCancelClick={() => toggleCloseSubLocation()} />
                  <Modal
                    title={t('VIEW_SUBLOCATIONS')}
                    isOpen={sublocations?.showViewSublocation}
                    styleClass="location-view-modal"
                    closeModal={() => toggleLocationViewForm(null, false)}>
                    <LocationViewModal />
                  </Modal>
                </Modal>
                <Modal
                  title={t('DELETE_CONFIRMATION')}
                  isOpen={showDeleteForm}
                  closeModal={() => toggleDeleteConfirmationForm(null, false)}>
                  <DeleteConfirmationForm
                    modalLoading={modalLoading}
                    selectedRowID={selectedRowID}
                    handleCancel={toggleDeleteConfirmationForm}
                    handleDelete={deleteLocationRowItem} />
                </Modal>
              </ServerError>
            </Loading>
          </RBAC>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...locationSelector.baseSelector(state),
  sublocations: { ...sublocationSelector.baseSelector(state) },
  modalLoading: locationSelector.modalLoadingSelector(state),
  selectedRowID: locationSelector.selectedRowIDSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loadLocationData,
    hanldeExpandClick,
    onHandlePageNumber: handlePageNumber,
    onHandlePageSize: handlePageSize,
    toggleCloseSubLocation: handleCloseSubLocation,
    toggleLocationViewForm: toggleViewModal,
    toggleLocationEditForm: toggleEditModal,
    deleteLocationRowItem: deleteLocationData,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    handleViewSublocation: toggleViewSublocation,
    onLoadSubLocation: loadSublocationData,
  }, dispatch);
  return actions;
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(Locations));

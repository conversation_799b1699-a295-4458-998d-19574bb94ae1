// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Trans, withTranslation } from 'react-i18next';
import { formValueSelector, isValid } from 'redux-form';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDotCircle, faCircle, faCircleCheck } from '@fortawesome/pro-solid-svg-icons';

const formValidity = {};
const selector = formValueSelector('provisioningTemplateWizard');
let type;

function WizardNav(props) {
  const {
    activePage,
    typeFromState,
    valid,
    wizardNavConfig,
  } = props;

  // track form validity for each sub-form
  formValidity[activePage] = valid;

  // if a sub-form is invalid, mark all next sub-forms as invalid
  if (!valid) {
    wizardNavConfig.forEach((item, index) => {
      if (index > activePage) {
        formValidity[index] = false;
      }
    });
  }

  // if the entire wizard form is filled out and user comes back to 1st screen and
  // changes the type, unset validity for all other pages to reflect change
  // in Additional params
  if (activePage === 0 && type !== typeFromState) {
    for (let i = 1, l = wizardNavConfig.length; i < l; i += 1) {
      formValidity[i] = false;
    }

    type = typeFromState;
  }
  
  return (
    <div className="wizard-nav">
      <ul>
        {wizardNavConfig.map((item, index) => {
          const widgetTitle = <span className="name"><Trans>{item}</Trans></span>;

          return (
            <li
              className={`${index === activePage ? 'active' : ''} ${index < activePage ? 'valid' : ''}`}
              key={item}>
              <button
                type="button"
                disabled={index !== 0}>
                <span className="fa-layers fa-fw">
                  {index === activePage && <FontAwesomeIcon icon={faDotCircle} size="lg" />}
                  {index > activePage && <FontAwesomeIcon icon={faCircle} size="lg" />}
                  {index < activePage && <FontAwesomeIcon icon={faCircleCheck} className="fa-check-circle-normal" size="lg" />}
                </span>
                {widgetTitle}
              </button>
            </li>
          );
        })}
      </ul>
    </div>
  );
}

WizardNav.propTypes = {
  activePage: PropTypes.number,
  typeFromState: PropTypes.string,
  valid: PropTypes.bool,
  wizardNavConfig: PropTypes.arrayOf(PropTypes.string),
};

WizardNav.defaultProps = {
  activePage: 0,
  typeFromState: '',
  valid: false,
  wizardNavConfig: {},
};

export default connect((state) => ({
  typeFromState: selector(state, 'type'),
  valid: isValid('provisioningTemplateWizard')(state),
}))(withTranslation()(WizardNav));

export { WizardNav };

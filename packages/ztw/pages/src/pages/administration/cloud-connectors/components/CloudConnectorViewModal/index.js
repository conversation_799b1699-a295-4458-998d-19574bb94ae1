/* eslint-disable max-len */
// @flow

import React from 'react';
import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';
import { bindActionCreators } from 'redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle } from '@fortawesome/pro-light-svg-icons';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import withRouter from 'layout/withRouter';
import { reduxForm } from 'redux-form';
import Loading from 'components/spinner/Loading';
import moment from 'moment-timezone';
import {
  noop, isEmpty, uniqBy, isString,
} from 'utils/lodash';
import { FormFieldLabel, FormSectionLabel } from 'components/label';
import {
  toggleViewModal, loadBCData, selectBCData, keepMinimumTimeInterval, saveSchedule,
  handleStatusChange, toggleDisableForm, toggleEnableForm,
} from 'ducks/cloudConnectors';
import * as cloudConnectorSelector from 'ducks/cloudConnectors/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import ECRadioGroup from 'components/ecRadioGroup';
import {
  hasBEncryptSku, hasCEncryptSku, calculateNextUpdate, isNullOrUndefined, timeZoneConverter,
  getWeekDayByVal, trinary, verifyConfigData,
} from 'utils/helpers';
import IPList from './components/IPList';

import {
  TimePickerWeekdayFrom,
  TimePickerHHFrom,
  TimePickerMMFrom,
  TimePickerPeriodFrom,
  ZiaTunnelMode,
} from '../GenericFilter';

const ipFilter = (ipAddress) => {
  if (ipAddress === '0.0.0.0' || typeof ipAddress === 'undefined' || ipAddress === '') {
    return '---';
  }
  return ipAddress;
};

export class BasicCustomAppForm extends React.Component {
  static propTypes = {
    actions: PropTypes.shape({
      cancelHandle: PropTypes.func,
      handleCCStatuschange: PropTypes.func,
      handleDisableConfirmationForm: PropTypes.func,
      handleEnableConfirmationForm: PropTypes.func,
      handleSaveSchedule: PropTypes.func,
      updateSchedule: PropTypes.func,
    }),
    t: PropTypes.func,
    configData: PropTypes.shape({}),
    handleSubmit: PropTypes.func,
    value: PropTypes.shape({}),
    viewOnly: PropTypes.bool,
    submitting: PropTypes.bool,
    loadedBCData: PropTypes.oneOfType([
      PropTypes.shape(),
      PropTypes.arrayOf(PropTypes.shape({})),
    ]),
    selectedBCData: PropTypes.shape(),
    formType: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({}),
    ]),
    formData: PropTypes.shape({
      autoPopulateDnsCache: PropTypes.string,
      zpaUserTunnel: PropTypes.bool,
      hhFrom: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
      }),
      hhTo: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
      }),
      mmFrom: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
      }),
      mmTo: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
      }),
      nextUpdate: PropTypes.string,
      operationalStatus: PropTypes.string,
      periodFrom: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
      }),
      periodTo: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
      }),
      weekday: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
        val: PropTypes.number,
      }),
      ziaTunnelMode: PropTypes.string,
      fallBackToTls: PropTypes.string,
    }),
    initialValues: PropTypes.shape({
      autoPopulateDnsCache: PropTypes.string,
      zpaUserTunnel: PropTypes.bool,
      maxUserTunnelsPerConnector: PropTypes.string,
      hhFrom: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
      }),
      hhTo: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
      }),
      mmFrom: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
      }),
      mmTo: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
      }),
      operationalStatus: PropTypes.string,
      periodFrom: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
      }),
      periodTo: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
      }),
      weekday: PropTypes.shape({
        id: PropTypes.string,
        name: PropTypes.string,
        val: PropTypes.number,
      }),
      ziaTunnelMode: PropTypes.string,
      fallBackToTls: PropTypes.string,
    }),
    cloudConnectorsData: PropTypes.arrayOf(PropTypes.shape({})),
    scheduledVersion: PropTypes.string,
    hasBCEncryptSubscription: PropTypes.bool,
    modalLoading: PropTypes.bool,
    vmHealth: PropTypes.shape(),
  };

  static defaultProps = {
    actions: {
      cancelHandle: noop,
      handleCCStatuschange: noop,
    },
    t: (str) => str,
    handleSubmit: (str) => str,
    value: {},
    formData: {},
    initialValues: {},
    viewOnly: true,
    submitting: false,
    loadedBCData: [],
    cloudConnectorsData: [],
    hasBCEncryptSubscription: false,
    modalLoading: false,
    vmHealth: {},
  };
    
  handleCancelClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    const {
      actions,
    } = this.props;
    
    const { cancelHandle } = actions;
    
    cancelHandle(null, false);
  };
  
  render() {
    const {
      viewOnly,
      actions,
      formType,
      formData,
      initialValues,
      selectedBCData,
      loadedBCData,
      cloudConnectorsData,
      t,
      handleSubmit,
      submitting,
      hasBCEncryptSubscription,
      modalLoading,
      vmHealth,
      scheduledVersion,
      configData,
    } = this.props;
    const { id, parentId } = formType || {};

    const {
      name,
      location,
      ecVMs,
      tunnelMode,
      zeroTrustGroup: isZTG,
    } = selectedBCData;
    const enableFallBackToTls = verifyConfigData({ configData, key: 'enableFallBackToTls' });

    const {
      ecVMs: groupVMs,
      // maxEcCount,
    } = loadedBCData;
    
    const { lastUpgradeTime, buildVersion } = (ecVMs && ecVMs[0]) || {};

    const invalidInput = ((
      !formData
      || isEmpty(formData.hhFrom) || isEmpty(formData.hhTo)
      || isEmpty(formData.mmFrom) || isEmpty(formData.mmTo)
    )
      && !(isEmpty(formData.hhFrom) && isEmpty(formData.hhTo) && isEmpty(formData.mmFrom) && isEmpty(formData.mmTo)));

    const initialValuesStringify = JSON.stringify(initialValues);
    const formDataStringify = JSON.stringify(formData);
    const hasChange = formDataStringify !== initialValuesStringify || (formType === 'BULK_CHANGE_FAIL_OPEN');

    const disableSave = invalidInput || !hasChange;

    const hasOperationalStatusChange = formData.operationalStatus !== initialValues.operationalStatus;

    const isMultipleLocation = formType === 'BULK_CHANGE' && cloudConnectorsData.length > 1
    && (uniqBy(cloudConnectorsData.filter((x) => x.scheduled), 'location.id')).length > 1;
    const bulkLocation = (cloudConnectorsData.find((x) => x.scheduled));
    const timeZone = formType === 'BULK_CHANGE' ? (bulkLocation && bulkLocation.location && bulkLocation.location.tz) || ''
      : location && location.tz;

    const onSubmit = () => {
      const origin = {
        id: parentId ? 'ONE_CC' : 'ONE_CC_GROUP',
        rows: parentId ? groupVMs.filter((x) => x.id === id) : groupVMs,
      };
      if (hasOperationalStatusChange && formData.operationalStatus === 'ENABLE') {
        actions.handleEnableConfirmationForm(true, origin);
        return;
      }
      if (hasOperationalStatusChange && formData.operationalStatus === 'DISABLE') {
        actions.handleDisableConfirmationForm(true, origin);
        return;
      }
      actions.handleSaveSchedule({ id, parentId });
    };
    
    if (modalLoading) return <Loading loading />;
    
    return (
      <form onSubmit={handleSubmit(onSubmit)} className="cloud-connector-group-form configure-provisioning-template">
        { !isZTG && id && parentId && formType !== 'BULK_CHANGE' && (
          <div className="form-sections-container">
            <FormSectionLabel text={t('OPERATIONAL_STATUS')} />
            <div className="form-section">
              <div className="input-container review half-width">
                <FormFieldLabel text={t('STATUS')} styleClass="no-margin-top" />
                <ECRadioGroup
                  id="operationalStatus"
                  name="operationalStatus"
                  styleClass="cc-status"
                  disabled={viewOnly}
                  options={[{
                    name: 'operationalStatus', value: 'ENABLE', checked: formData.operationalStatus === 'ENABLE', label: t('ENABLE'),
                  },
                  {
                    name: 'operationalStatus', value: 'DISABLE', checked: formData.operationalStatus === 'DISABLE', label: t('DISABLE'),
                  }]} />
              </div>
              <div className={`input-container review half-width ${parentId ? '' : 'hidden'}`}>
                <FormFieldLabel text={t('LAST_HEARTBEAT_RECEIVED_ON')} styleClass="no-margin-top" />
                <p className="disabled-input">
                  {vmHealth.lastModifiedTime ? moment(vmHealth.lastModifiedTime * 1000).format('MMM, DD YYYY hh:mm A') : t('NA')}
                </p>
              </div>
            </div>
          </div>
        )}

        { (!isZTG || !id) && (!isString(formType) || formType === 'BULK_CHANGE') && (
          <div className="form-sections-container">
            <FormSectionLabel text={t('UPGRADE_SCHEDULE')} />
            <div className="form-section">
              <div className="cc-schedule-time-container cc-schedule-time-container-weekday full-width">
                {/* <div className="cc-title-select-upgrade-window full-width">{t('SOFTWARE_UPGRADE_SCHEDULE')}</div> */}
                <FormFieldLabel text={t('SOFTWARE_UPGRADE_SCHEDULE')} styleClass="no-margin-top" tooltip={t('SOFTWARE_UPGRADE_SCHEDULE_TOOLTIP')} />
                <div className="cc-schedule-time-container-from half-width">
                  <TimePickerWeekdayFrom
                    t={t}
                    viewOnly={viewOnly}
                    updateSchedule={actions.updateSchedule} />
                </div>
                <span className="cc-schedule-time-separator">{t('AT')}</span>
                <div className="cc-schedule-time-container-to">
                  <TimePickerHHFrom
                    value={formData.hhFrom}
                    viewOnly={viewOnly}
                    updateSchedule={actions.updateSchedule} />
                  <TimePickerMMFrom
                    value={formData.mmFrom}
                    viewOnly={viewOnly}
                    updateSchedule={actions.updateSchedule} />
                  <TimePickerPeriodFrom
                    value={formData.periodFrom}
                    viewOnly={viewOnly}
                    updateSchedule={actions.updateSchedule} />
                </div>
              </div>
              <div className="cc-title-select-upgrade-window">
                {formData.nextUpdate ? ` ${t('NEXT_UPDATE')} ${formData.nextUpdate}` : t('NEXT_PERIODIC_UPDATE')}
                {!isMultipleLocation && (
                  <span className="cc-upgrades-will-be-scheduled-location">
                    {' '}
                    {t(timeZone)}
                    {' '}
                    <FontAwesomeIcon
                      className="information-icon"
                      icon={faInfoCircle}
                      data-for="connectorGroup"
                      data-tip={t('LOCAL_TIME_ZONE_CC_GROUP')} />
                  </span>
                )}
              </div>
              {isMultipleLocation && (
                <div className="cc-upgrades-will-be-scheduled">
                  <FontAwesomeIcon icon={faInfoCircle} />
                  {' '}
                  {t('UPGRADE_WILL_BE_SCHEDULED')}
                </div>
              )}

              {id && parentId && formType !== 'BULK_CHANGE'
                  && (
                    <div className="form-section-version">
                      <div className="input-container review half-width">
                        <FormFieldLabel text={t('LAST_UPGRADE_ON')} styleClass="no-margin-top" />
                        <p className="disabled-input">{lastUpgradeTime ? moment(lastUpgradeTime * 1000).format('MMM, DD YYYY hh:mm A') : t('NA') }</p>
                      </div>
                      <div className="input-container review  half-width">
                        <FormFieldLabel text={t('CURRENT_VERSION')} styleClass="no-margin-top" />
                        <p className="disabled-input">{(buildVersion) || t('NA')}</p>
                      </div>
                      <div className="input-container review  half-width">
                        <FormFieldLabel text={t('SCHEDULED_VERSION')} styleClass="no-margin-top" />
                        <p className="disabled-input">{(scheduledVersion) || t('NA')}</p>
                      </div>
                    </div>
                  )}
            </div>
          </div>
        )}
      
        {id && !parentId && formType !== 'BULK_CHANGE' && (
          <>
            {hasBCEncryptSubscription && (
              <div className="form-sections-container">
                <FormSectionLabel text={t('TUNNEL_INFORMATION')} />
                <div className="form-section">
                  <div className="input-container review">
                    <FormFieldLabel text={t('ZIA_TUNNEL_MODEL')} styleClass="no-margin-top" tooltip={t('TOOLTIP_ZIA_TUNNEL_MODEL')} />
                    <ZiaTunnelMode
                      value={t(tunnelMode)}
                      enableFallBackToTls={enableFallBackToTls}
                      ziaTunnelMode={formData?.ziaTunnelMode}
                      fallBackToTls={formData?.fallBackToTls}
                      viewOnly={viewOnly} />
                  </div>
                </div>
              </div>
            )}
            {!hasBCEncryptSubscription && (
              <div className="form-sections-container">
                <FormSectionLabel text={t('TUNNEL_INFORMATION')} />
                <div className="form-section">
                  <div className="input-container review">
                    <FormFieldLabel text={t('ZIA_TUNNEL_MODEL')} styleClass="no-margin-top" tooltip={t('TOOLTIP_ZIA_TUNNEL_MODEL')} />
                    <p className="disabled-input margin-botton-12px">{t('SELECT')}</p>
                    <p className="disabled-input">
                      <FontAwesomeIcon icon={faInfoCircle} className="fa-info-circle-icon" />
                      {' '}
                      {t('PLEASE_REACH_OUT_TO_YOUR_ACCOUNT_TEAM')}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="form-sections-container">
              <FormSectionLabel text={t('GROUP_INFORMATION')} />
              <div className="form-section">
                <div className="input-container review">
                  <FormFieldLabel text={t('NAME')} styleClass="no-margin-top" />
                  <p className="disabled-input">{(name) || t('NONE')}</p>
                </div>
                <div className="input-container review">
                  <FormFieldLabel text={t('LOCATION_NAME')} styleClass="no-margin-top" />
                  <p className="disabled-input">{(location && location.name) || t('NONE')}</p>
                </div>
                        
                {groupVMs && (
                  <>
                    <div className="input-container review">
                      <FormFieldLabel text={t('NUMBER_OF_CONNECTORS')} styleClass="no-margin-top" />
                      <p className="disabled-input">{groupVMs.length}</p>
                    </div>
           
                    <div className="input-container review">
                      <FormFieldLabel text={t('CONNECTOR_NAMES')} styleClass="no-margin-top" />
                      {groupVMs.map((vm) => (
                        <p key={vm.id} className="disabled-input no-marging-bottom">{vm.name}</p>
                      ))}
                    </div>
                  </>
                )}
              </div>
            </div>
          </>
        )}
  
        {!isEmpty(ecVMs) && formType !== 'BULK_CHANGE' && (
          <>
            {ecVMs.map((ecVM) => {
              const {
                formFactor,
                provTemplate = {},
                name: nameVM = '',
                managementNw: {
                  ipStart: managementIPAddress1 = '0.0.0.0',
                  dns: {
                    ips: managementDNSServer = ['0.0.0.0'],
                  } = {},
                  defaultGateway: managementOutgoingGatewayIP = '0.0.0.0',
                } = {},
                ecInstances: [
                  {
                    serviceNw: {
                      ipStart: serviceIPAddress1 = '0.0.0.0',
                      defaultGateway: internalGatewayIPAddress = '0.0.0.0',
                      dns: {
                        ips: DNSServer = ['0.0.0.0'],
                      } = {},
                    } = {},
                  // virtualNw: {
                  //   ipStart: loadBalancerIPAddress1 = '0.0.0.0',
                  // } = {},
                  } = { serviceNw: {}, virtualNw: {} },
                ] = [{ serviceNw: {}, virtualNw: {} }],
              } = ecVM;
              const { ecInstances } = ecVM;

              return (
                <div key={nameVM}>
                  {hasBCEncryptSubscription && (
                    <div className="form-sections-container">
                      <FormSectionLabel text={t('TUNNEL_INFORMATION')} />
                      <div className="form-section">
                        <div className="input-container review">
                          <FormFieldLabel text={t('ZIA_TUNNEL_MODEL')} styleClass="no-margin-top" tooltip={t('TOOLTIP_ZIA_TUNNEL_MODEL')} />
                          <p className="disabled-input">{t(tunnelMode) || t('NONE')}</p>
                        </div>
                        {tunnelMode !== 'TLS' && enableFallBackToTls && (
                          <div className="input-container review">
                            <FormFieldLabel text={t('FALLBACK_TO_TLS')} styleClass="no-margin-top" tooltip={t('')} />
                            <p className="disabled-input">{t(formData?.fallBackToTls) || t('---')}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  {!hasBCEncryptSubscription && (
                    <div className="form-sections-container">
                      <FormSectionLabel text={t('TUNNEL_INFORMATION')} />
                      <div className="form-section">
                        <div className="input-container review">
                          <FormFieldLabel text={t('ZIA_TUNNEL_MODEL')} styleClass="no-margin-top" tooltip={t('TOOLTIP_ZIA_TUNNEL_MODEL')} />
                          <p className="disabled-input margin-botton-12px">{t('SELECT')}</p>
                          <p className="disabled-input">
                            <FontAwesomeIcon icon={faInfoCircle} className="fa-info-circle-icon" />
                            {' '}
                            {t('PLEASE_REACH_OUT_TO_YOUR_ACCOUNT_TEAM')}
                          </p>
                        </div>
                      </div>
                      {/* <div className="input-container review half-width">
                          <FormFieldLabel text={t('MANAGEMENT_IP_ADDRESS')} styleClass="no-margin-top" />
                          <p className="disabled-input">{`${ipFilter(managementIPAddress1)}`}</p>
                        </div>
                        <IPList label={t('MANAGEMENT_DNS_SERVER')} ipList={managementDNSServer} /> */}
                    </div>
                  )}

                  {/* {has5G && (
                      <div className="form-sections-container">
                        <FormSectionLabel text={t('CELLULAR_CONFIGURATION')} />
                        <div className="form-section">
                          <div className="input-container review">
                            <FormFieldLabel text={t('CELLULAR_CONFIGURATION')} styleClass="no-margin-top" />
                            <p className="disabled-input">{(name) || t('NONE')}</p>
                          </div>
                          <div className="input-container review">
                            <FormFieldLabel text={t('CONFIGURATION_MODE')} styleClass="no-margin-top" />
                            <p className="disabled-input">{(location && location.name) || t('NONE')}</p>
                          </div>
                          <div className="input-container review">
                            <FormFieldLabel text={t('CELLULAR_CONFIGURATION_SELECTION')} styleClass="no-margin-top" />
                            <p className="disabled-input">{(location && location.name) || t('NONE')}</p>
                          </div>
                        </div>
                      </div>
                    )} */}

                  <div className="form-sections-container">
                    <FormSectionLabel text={t('LOCATION_INFORMATION')} />
                    <div className="form-section">
                      <div className="input-container review half-width">
                        <FormFieldLabel text={t('NAME')} styleClass="no-margin-top" />
                        <p className="disabled-input">{(nameVM) || t('NONE')}</p>
                      </div>
                      <div className="input-container review half-width">
                        <FormFieldLabel text={t('LOCATION_NAME')} styleClass="no-margin-top" />
                        <p className="disabled-input">{(location && location.name) || t('NONE')}</p>
                      </div>
                      <div className="input-container review half-width">
                        <FormFieldLabel text={t('CONNECTOR_VM_SIZE')} styleClass="no-margin-top" />
                        <p className="disabled-input">{t(formFactor) || t('NONE')}</p>
                      </div>
                      <div className="input-container review half-width">
                        <FormFieldLabel text={t('CONFIGURATION_TEMPLATE_NAME')} styleClass="no-margin-top" />
                        <p className="disabled-input">{t(provTemplate?.name) || t('NONE')}</p>
                      </div>
                    </div>
                  </div>

                  {!isZTG && (
                    <>
                      <div className="form-sections-container">
                        <FormSectionLabel text={t('CLOUD_CONNECTOR_INFORMATION')} />
                        <div className="form-section">
                          <div className="input-container review half-width">
                            <FormFieldLabel text={t('MANAGEMENT_OUTGOING_GATEWAY_IP_ADDRESS')} styleClass="no-margin-top" />
                            <p className="disabled-input">{ipFilter(managementOutgoingGatewayIP)}</p>
                          </div>
                          <div className="input-container review half-width">
                            <FormFieldLabel text={t('MANAGEMENT_IP_ADDRESS')} styleClass="no-margin-top" />
                            <p className="disabled-input">{`${ipFilter(managementIPAddress1)}`}</p>
                          </div>
                          <IPList label={t('MANAGEMENT_DNS_SERVER')} ipList={managementDNSServer} />
                        </div>
                      </div>

                      <div className="form-sections-container">
                        <FormSectionLabel text={t('FORWARDING_INFORMATION')} />
                        <div className="form-section">

                          <div className="input-container review half-width">
                            <FormFieldLabel text={t('INTERNAL_GATEWAY_IP_ADDRESS')} styleClass="no-margin-top" />
                            <p className="disabled-input">{ipFilter(internalGatewayIPAddress)}</p>
                          </div>
                          <IPList label={t('DNS_SERVER')} ipList={DNSServer} />
                        </div>
                      </div>

                      <div className="form-sections-container">
                        <FormSectionLabel text={t('SERVICE_INFORMATION')} />
                        <div className="form-section">
                          <div className="input-container review half-width">
                            {ecInstances.length === 1
                          && (
                            <>
                              <FormFieldLabel text={t('SERVICE_IP')} styleClass="no-margin-top" />
                              <p className="disabled-input">{ipFilter(serviceIPAddress1)}</p>
                            </>
                          )}
                            {ecInstances.length > 1
                          && (ecInstances.filter((x) => x.ecInstanceType === 'SME').map((instance, idx) => (
                            <div key={instance.name}>
                              <FormFieldLabel text={`${t('SERVICE_IP')} ${idx + 1}`} styleClass="no-margin-top" />
                              <p className="disabled-input">{instance.serviceNw ? ipFilter(instance.serviceNw.ipStart) : ipFilter('')}</p>
                            </div>
                          ))
                          )}
                          </div>
                          <div className="input-container review half-width">
                            {ecInstances.filter((x) => x.ecInstanceType !== 'SME').length > 0
                              ? ecInstances.filter((x) => x.ecInstanceType !== 'SME').map((instance) => (
                                <div key={instance.name}>
                                  <FormFieldLabel text={t('VIRTUAL_IP_ADDRESS')} styleClass="no-margin-top" />
                                  <p className="disabled-input">{instance?.virtualNw ? ipFilter(instance?.virtualNw?.ipStart) : ipFilter('')}</p>
                                </div>
                              ))
                              : (
                                <>
                                  <FormFieldLabel text={t('VIRTUAL_IP_ADDRESS')} styleClass="no-margin-top" />
                                  <p className="disabled-input">{ecInstances.length > 0 && ecInstances[0]?.virtualNw ? ipFilter(ecInstances[0]?.virtualNw.ipStart) : ipFilter('')}</p>
                                </>
                              )}
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              );
            })}

          </>
        )}
        <div className="dialog-footer">
          {!viewOnly && <button type="submit" disabled={disableSave || submitting} className="submit">{t('SAVE')}</button>}
          <button type="submit" onClick={this.handleCancelClick} className="cancel">{t('CANCEL')}</button>
        </div>
        <ReactTooltip
          id="connectorGroup"
          place="top"
          type="light"
          offset={{ top: -10 }}
          effect="solid"
          border
          className="cc-group-tooltip-container"
          borderColor="#939393" />
      </form>
    );
  }
}

const BasicCustomForm = reduxForm({
  form: 'CloudConnectorViewForm',
})(BasicCustomAppForm);

const CloudConnectorViewModal = connect(
  (state, ownProps) => {
    const {
      t, formType, selectedBCData: selectedBC, cloudConnectorsData, configData,
    } = ownProps;

    let clouConnectorData = [];
    let operationalStatus;
    let hhFrom;
    let mmFrom;
    let periodFrom;
    let nextUpdate;
    let weekday;
    let statusArray = [];
    const isModalLoading = cloudConnectorSelector.modalLoadingSelector(state);
    if (isModalLoading) return {};
    const selectedBCData = cloudConnectorSelector.selectedBCDataSelector(state);
    const {
      tunnelMode, fallBackToTls, ecVMs, zpaUserTunnel = false,
    } = selectedBCData;
    const { status = [] } = (ecVMs && ecVMs[0]) || {};
    const { id, parentId } = cloudConnectorSelector.formTypeSelector(state);
    if (!parentId) clouConnectorData = cloudConnectorSelector.dataTableSelector(state).filter((x) => x.parentId === id);
  
    const { location } = selectedBC;
    const bulkLocation = (cloudConnectorsData.find((x) => x.scheduled));
    const timeZone = formType === 'BULK_CHANGE' ? (bulkLocation && bulkLocation.location && bulkLocation.location.tz) || ''
      : location && location.tz;

    const localDate = new Date();
    const serverDate = timeZone && timeZoneConverter(timeZone);
    const offset = serverDate && (localDate.getTimezoneOffset() - serverDate.offset);

    const dataTable = cloudConnectorSelector.dataTableSelector(state);
    const accessSubscriptions = loginSelectors.accessSubscriptionSelector(state);

    const isBCPage = false;
    const isCCPage = true;

    const enableFallBackToTls = verifyConfigData({ configData, key: 'enableFallBackToTls' });

    const hasBCEncryptSubscription = ((isBCPage && hasBEncryptSku(accessSubscriptions))
      || (isCCPage && hasCEncryptSku(accessSubscriptions))
    );
    // eslint-disable-next-line prefer-const
    let { upgradeWindow, upgradeDayOfWeek } = dataTable.find((x) => x.id === id) || {};
    
    let initialUpgradeWindow = {};
    if (!isEmpty(upgradeWindow) && upgradeWindow !== 'Multiple') {
      hhFrom = Number(upgradeWindow.slice(0, upgradeWindow.indexOf(':')));
      upgradeWindow = upgradeWindow.substring(upgradeWindow.indexOf(':') + 1);
      mmFrom = Number(upgradeWindow.slice(0, upgradeWindow.indexOf(' ')));
      upgradeWindow = upgradeWindow.substring(upgradeWindow.indexOf(' ') + 1);
      periodFrom = upgradeWindow.slice(0, upgradeWindow.indexOf('-')).trim();
      upgradeWindow = upgradeWindow.substring(upgradeWindow.indexOf('-') + 1);
      const hhTo = Number(upgradeWindow.slice(0, upgradeWindow.indexOf(':')));
      upgradeWindow = upgradeWindow.substring(upgradeWindow.indexOf(':') + 1);
      const mmTo = Number(upgradeWindow.slice(0, upgradeWindow.indexOf(' ')));
      upgradeWindow = upgradeWindow.substring(upgradeWindow.indexOf(' ') + 1);
      const periodTo = upgradeWindow.trim();
      initialUpgradeWindow = {
        hhFrom: { id: hhFrom, name: hhFrom },
        mmFrom: { id: mmFrom, name: mmFrom },
        periodFrom: { id: periodFrom, name: periodFrom },
        hhTo: { id: hhTo, name: hhTo },
        mmTo: { id: mmTo, name: mmTo },
        periodTo: { id: periodTo, name: periodTo },
      };
      weekday = getWeekDayByVal(upgradeDayOfWeek, t);
      nextUpdate = weekday && hhFrom && !isNullOrUndefined(mmFrom) && periodFrom ? calculateNextUpdate(weekday, hhFrom, mmFrom, periodFrom, offset) : '';
    } else {
      initialUpgradeWindow = {
        periodFrom: { id: 'AM', name: 'AM' },
        periodTo: { id: 'AM', name: 'AM' },
      };
    }
 
    if (!parentId) {
      // If it is a parent check all the childs and return the childs value if they are all the same, otherwise blank.
      statusArray = clouConnectorData && clouConnectorData.map((x) => {
        return x.status.includes('DISABLING') || x.status.includes('DISABLED') || x.status.includes('DISABLE') ? 'DISABLE' : 'ENABLE';
      });
      operationalStatus = statusArray.length > 0 && statusArray.every((item) => item === statusArray[0]) ? statusArray[0] : '';
    } else {
      operationalStatus = status.includes('DISABLING') || status.includes('DISABLED') || status.includes('DISABLE') ? 'DISABLE' : 'ENABLE';
    }
    const zpaUserTunnelValue = trinary(zpaUserTunnel, 'ENABLE', 'DISABLE');

    return ({
      initialValues: {
        ...initialUpgradeWindow,
        ...(hasBCEncryptSubscription && { ziaTunnelMode: tunnelMode || 'PLAIN_UDP' }),
        ...((hasBCEncryptSubscription && enableFallBackToTls) && { fallBackToTls: trinary(fallBackToTls, 'ENABLE', 'DISABLE') }),
        operationalStatus,
        zpaUserTunnel: zpaUserTunnelValue,
        nextUpdate,
        weekday,
        offset,
      },
    });
  },
)(BasicCustomForm);

const mapStateToProps = (state) => ({
  ...cloudConnectorSelector.default(state),
  nextBuild: cloudConnectorSelector.nextBuildSelector(state),
  appData: cloudConnectorSelector.appDataSelector(state),
  formData: cloudConnectorSelector.formDataValuesSelector(state),
  formMeta: cloudConnectorSelector.formMetaSelector(state),
  formSyncErrors: cloudConnectorSelector.formSyncErrorsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  configData: loginSelectors.configDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleViewModal,
    load: loadBCData,
    selectData: selectBCData,
    updateSchedule: keepMinimumTimeInterval,
    handleSaveSchedule: saveSchedule,
    handleCCStatuschange: handleStatusChange,
    handleDisableConfirmationForm: toggleDisableForm,
    handleEnableConfirmationForm: toggleEnableForm,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(CloudConnectorViewModal)));

/* eslint-disable react/jsx-handler-names */
// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import {
  hasBsku, hasCsku, hasBEncryptSku, hasCEncryptSku, getReadOnly,
} from 'utils/helpers';
import DeleteConfirmationGroupForm from 'components/DeleteConfirmationForm/connectorGroupForm';
import DeleteDoubleConfirmationGroupForm from 'components/DeleteConfirmationForm/connectorGroupConfirmForm';
import DisableConfirmationCCGroupForm from 'components/DeleteConfirmationForm/ccDisableGroupForm';
import EnableConfirmationCCGroupForm from 'components/DeleteConfirmationForm/ccEnableGroupForm';
import * as cloudConnectorSelector from 'ducks/cloudConnectors/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import { HELP_ARTICLES } from 'config';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';

import {
  hanldeExpandClick,
  toggleViewModal,
  deleteBCGroupData,
  toggleDeleteForm,
  toggleDeleteConfirmForm,
  toggleDisableForm,
  toggleEnableForm,
  handleDisableEnable,
  toggleSortBy,
  toggleShowHideChilds,
  toggleCheckConnectorGroup,
} from 'ducks/cloudConnectors';

import CloudConnectorViewModal from '../CloudConnectorViewModal';

export class CloudConnectors extends React.Component {
  static propTypes = {
    actions: PropTypes.shape({
      deleteBCGroupRowData: PropTypes.func,
      handleDeleteAdditionalForm: PropTypes.func,
      handleDeleteConfirmationForm: PropTypes.func,
      handleDisable: PropTypes.func,
      handleDisableConfirmationForm: PropTypes.func,
      handleDisableEnable: PropTypes.func,
      handleEnable: PropTypes.func,
      handleEnableConfirmationForm: PropTypes.func,
      handleToggleForm: PropTypes.func,
      load: PropTypes.func,
    }),
    cloudConnectorsData: PropTypes.arrayOf(PropTypes.shape()),
    showForm: PropTypes.bool,
    t: PropTypes.func,
    showDeleteForm: PropTypes.bool,
    showGroupDeleteForm: PropTypes.bool,
    modalLoading: PropTypes.bool,
    selectedRowID: PropTypes.string,
    authType: PropTypes.string,
    accessPrivileges: PropTypes.shape({
      EDGE_CONNECTOR_CLOUD_PROVISIONING: PropTypes.string,
    }),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    formType: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({}),
    ]),
    showDisableForm: PropTypes.bool,
    showEnableForm: PropTypes.bool,
    disableType: PropTypes.shape({
      id: PropTypes.string,
    }),
    enableType: PropTypes.shape({
      id: PropTypes.string,
    }),
    pathname: PropTypes.string,
  };

  static defaultProps = {
    actions: {
      load: null,
      handleToggleForm: null,
      handleDeleteConfirmationForm: null,
      handleDeleteAdditionalForm: null,
      handleDisableConfirmationForm: null,
      handleEnableConfirmationForm: null,
      deleteBCGroupRowData: null,
      handleDisable: null,
      handleEnable: null,
    },
    cloudConnectorsData: [],
    showForm: false,
    t: (str) => str,
    showDeleteForm: false,
    showGroupDeleteForm: false,
    modalLoading: false,
    selectedRowID: null,
    accessPrivileges: {},
    authType: '',
    accessSubscriptions: [],
    showDisableForm: false,
    showEnableForm: false,
    disableType: {},
    enableType: {},
    pathname: '',
  };

  componentDidMount() {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.BRANCH_AND_CLOUD_GROUP });
  }

  render() {
    const {
      cloudConnectorsData,
      actions,
      showForm,
      t,
      showDeleteForm,
      showGroupDeleteForm,
      showDisableForm,
      showEnableForm,
      disableType,
      enableType,
      selectedRowID,
      accessPrivileges,
      accessSubscriptions,
      formType,
      authType,
      modalLoading,
      pathname,
    } = this.props;
    const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING, authType);
    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);
    const isBCPage = pathname.includes('branch-devices');
    const isBCPhysical = pathname.includes('branch-devices/physical');
    const isCCPage = pathname.includes('cloud-connector-groups');
    const hasBCEncryptSubscription = ((isBCPage && hasBEncryptSku(accessSubscriptions))
    || (isCCPage && hasCEncryptSku(accessSubscriptions))
    );

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
      return <PermissionRequired accessPermissions={accessPrivileges} />;
    }

    const disableTitle = (id) => {
      if (id === 'ONE_CC') return isCCPage ? t('DISABLE_CLOUD_CONNECTOR') : t('DISABLE_BRANCH_CONNECTOR');
      if (id === 'ONE_CC_GROUP') return isCCPage ? t('DISABLE_CLOUD_CONNECTOR_GROUP') : t('DISABLE_BRANCH_CONNECTOR_GROUP');
      if (id === 'SELECTED_CC') return isCCPage ? t('DISABLE_CLOUD_CONNECTOR_SELECTED') : t('DISABLE_BRANCH_CONNECTOR_SELECTED');
      return '';
    };

    const enableTitle = (id) => {
      if (id === 'ONE_CC') return isCCPage ? t('ENABLE_CLOUD_CONNECTOR') : t('ENABLE_BRANCH_CONNECTOR');
      if (id === 'ONE_CC_GROUP') return isCCPage ? t('ENABLE_CLOUD_CONNECTOR_GROUP') : t('ENABLE_BRANCH_CONNECTOR_GROUP');
      if (id === 'SELECTED_CC') return isCCPage ? t('ENABLE_CLOUD_CONNECTOR_SELECTED') : t('ENABLE_BRANCH_CONNECTOR_SELECTED');
      return '';
    };

    const pageTitle = () => {
      if (formType === 'BULK_CHANGE') return t('SCHEDULE_UPGRADE');
      if (isReadOnly && isCCPage) return t('VIEW_CONNECTORS');
      if (!isReadOnly && isCCPage) return t('EDIT_CONNECTORS');
      return '';
    };

    return (
      <>
        <Modal
        // eslint-disable-next-line no-nested-ternary
          title={pageTitle()}
          isOpen={showForm}
          styleClass="schdule-upgrade"
          closeModal={() => actions.handleToggleForm(null, false)}>
          <Loading loading={modalLoading} />
          <CloudConnectorViewModal
            viewOnly={isReadOnly}
            // setModalStateLoading={this.setModalStateLoading}
            hasBCEncryptSubscription={hasBCEncryptSubscription} />
        </Modal>

        <Modal
          title={disableTitle(disableType && disableType.id)}
          isOpen={showDisableForm}
          styleClass="disable-cc-group"
          closeModal={() => actions.handleDisableConfirmationForm(false)}>
          <Loading loading={modalLoading} />
          <DisableConfirmationCCGroupForm
            isCCPage={isCCPage}
            isBCPhysical={isBCPhysical}
            disableType={disableType}
            handleCancel={() => actions.handleDisableConfirmationForm(false)}
            handleDisable={() => actions.handleDisableEnable('DISABLE', disableType && disableType.id)} />
        </Modal>

        <Modal
          title={enableTitle(enableType && enableType.id)}
          isOpen={showEnableForm}
          styleClass="enable-cc-group"
          closeModal={() => actions.handleEnableConfirmationForm(false)}>
          <Loading loading={modalLoading} />
          <EnableConfirmationCCGroupForm
            isCCPage={isCCPage}
            isBCPhysical={isBCPhysical}
            enableType={enableType}
            handleCancel={() => actions.handleEnableConfirmationForm(null, false)}
            handleEnable={() => actions.handleDisableEnable('ENABLE', enableType && enableType.id)} />
        </Modal>

        <Modal
          title={t('DELETE_CONFIRMATION')}
          isOpen={showDeleteForm}
          styleClass="delete-cc-group"
          closeModal={() => actions.handleDeleteConfirmationForm(null, false)}>
          <Loading loading={modalLoading} />
          <DeleteConfirmationGroupForm
            isCCPage={isCCPage}
            isBCPhysical={isBCPhysical}
            selectedRowID={selectedRowID}
            subItems={cloudConnectorsData.filter(
              (x) => (x.parentId === selectedRowID || x.id === selectedRowID),
            )}
            handleCancel={() => actions.handleDeleteConfirmationForm(null, false)}
            handleDelete={actions.deleteBCGroupRowData} />
        </Modal>

        <Modal
          title={t('DELETE_GROUP_CONFIRMATION')}
          isOpen={showGroupDeleteForm}
          styleClass="delete-cc-group"
          closeModal={() => actions.handleDeleteAdditionalForm(null, false)}>
          <Loading loading={modalLoading} />
          <DeleteDoubleConfirmationGroupForm
            isCCPage={isCCPage}
            selectedRowID={selectedRowID}
            parentItem={cloudConnectorsData.filter(
              (x) => x.id === selectedRowID,
            )}
            handleCancel={() => actions.handleDeleteAdditionalForm(null, false)}
            handleDelete={actions.handleDeleteConfirmationForm} />
        </Modal>
      </>
    );
  }
}

const mapStateToProps = (state) => ({
  showForm: cloudConnectorSelector.showFormSelector(state),
  showDeleteForm: cloudConnectorSelector.showDeleteFormSelector(state),
  showGroupDeleteForm: cloudConnectorSelector.showGroupDeleteFormSelector(state),
  showDisableForm: cloudConnectorSelector.showDisableFormSelector(state),
  showEnableForm: cloudConnectorSelector.showEnableFormFormSelector(state),
  disableType: cloudConnectorSelector.disableTypeSelector(state),
  enableType: cloudConnectorSelector.enableTypeSelector(state),
  modalLoading: cloudConnectorSelector.modalLoadingSelector(state),
  selectedRowID: cloudConnectorSelector.selectedRowIDSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  cloudConnectorsData: cloudConnectorSelector.dataTableSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    hanldeExpandClick,
    handleToggleForm: toggleViewModal,
    handleSortBy: toggleSortBy,
    handleShowHideChilds: toggleShowHideChilds,
    handleCheckConnectorGroup: toggleCheckConnectorGroup,
    deleteBCGroupRowData: deleteBCGroupData,
    handleDeleteConfirmationForm: toggleDeleteForm,
    handleDeleteAdditionalForm: toggleDeleteConfirmForm,
    handleDisableConfirmationForm: toggleDisableForm,
    handleEnableConfirmationForm: toggleEnableForm,
    handleDisableEnable,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(CloudConnectors));

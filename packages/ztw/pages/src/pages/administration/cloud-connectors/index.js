/* eslint-disable react/jsx-handler-names */
// @flow
import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import { Field, reduxForm } from 'redux-form';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PageTabs from 'components/navTabs/PageTabs';
import SearchBox from 'components/searchBox';
import ServerError from 'components/errors/ServerError';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import {
  hasBsku, hasCsku, getReadOnly,
} from 'utils/helpers';
import * as loginSelectors from 'ducks/login/selectors';
import * as cloudConnectorSelector from 'ducks/cloudConnectors/selectors';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';

import {
  deleteBCGroupData,
  hanldeExpandClick,
  handlePagetype,
  toggleCheckConnectorGroup,
  toggleDeleteForm,
  toggleDisableForm,
  toggleEnableForm,

  toggleShowHideChilds,
  toggleSortBy,
  toggleViewModal,
  resetConnectorGroups,
  handleSearchText,
  tabConfiguration,
} from 'ducks/cloudConnectors';
import { toggleAdminPanel } from 'ducks/adminNav';
import CloudConnectorsTable from './components/CloudConnectorsTable';

import { ActionBar, Modals } from './components';

let currentLocation;

function BasicCustomAppForm(props) {
  const {
    actions,
    authType,
    t,
    accessPrivileges,
    accessSubscriptions,
    bulkUpdate,
    bulkUpdateHasZTG,
  } = props;
  const location = useLocation();
  const { pathname } = location;
  currentLocation = pathname;

  useEffect(() => {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.BRANCH_AND_CLOUD_GROUP });
    actions.toggleAdminPanel(false);
    actions.handlePagetype(pathname);
    return () => actions.resetConnectorGroups();
  }, [pathname]);

  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);

  if (!hasBSubscription && !hasCSubscription) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }

  if (accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
    return <PermissionRequired accessPermissions={accessPrivileges} />;
  }
  const isBCPage = pathname.includes('branch-connector-groups');
  const isCCPage = pathname.includes('cloud-connector-groups');
  const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING, authType);
    
  return (
    <ServerError {...props}>
      <div className="main-container cc-group">
        <HelpArticle article={HELP_ARTICLES.CLOUD_CONNECTOR_GROUP} />
        <div className="page-title  header-3">
          {t('CONNECTOR_GROUPS')}
        </div>
        <Field
          id="pageTabs"
          name="pageTabs"
          component={PageTabs} />
        <div className="actions-row">
          {bulkUpdate && !isReadOnly && (
            <ActionBar
              t={t}
              disable={!!bulkUpdateHasZTG}
              disabledReason={t('ACTIONS_NOT_ALLOWED_ON_ZTG')}
              handleDnsCache={() => actions.handleToggleForm('BULK_CHANGE_DNS_CACHE', true)}
              handleDisable={() => actions.handleToggleDisableForm(true)}
              handleEnable={() => actions.handleToggleEnableForm(true)}
              handleScheduleUpgrade={() => actions.handleToggleForm('BULK_CHANGE', true)} />
          )}
          <div className="search-container-cc-group">
            <SearchBox
              placeholder={t('SEARCH')}
              clickCallback={(value) => actions.handleSearchText(value)} />
          </div>
        </div>
        {((isBCPage && !hasBSubscription) || (isCCPage && !hasCSubscription))
          ? <SubscriptionRequired accessSubscriptions={accessSubscriptions} />
          : (
            <div className="container-row-cc-group bc-connectors">
              <CloudConnectorsTable pathname={pathname} />
              <Modals pathname={pathname} {...props} />
            </div>
          )}
      </div>
    </ServerError>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    deleteBCGroupRowData: PropTypes.func,
    handleDeleteConfirmationForm: PropTypes.func,
    handlePagetype: PropTypes.func,
    handleSearchText: PropTypes.func,
    handleToggleDisableForm: PropTypes.func,
    handleToggleEnableForm: PropTypes.func,
    handleToggleForm: PropTypes.func,
    load: PropTypes.func,
    resetConnectorGroups: PropTypes.func,
    toggleAdminPanel: PropTypes.func,
  }),
  t: PropTypes.func,
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_CLOUD_PROVISIONING: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  bulkUpdate: PropTypes.bool,
  bulkUpdateHasZTG: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  actions: {
    load: null,
    handleToggleForm: null,
    handleDeleteConfirmationForm: null,
    deleteBCGroupRowData: null,
  },
  t: (str) => str,
  authType: '',
  accessPrivileges: {},
  accessSubscriptions: [],
};

const BasicCustomForm = reduxForm({
  form: 'cloudConnectorsPage',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(BasicCustomAppForm);

const CloudConnectors = connect((state) => {
  const accessSubscriptions = loginSelectors.accessSubscriptionSelector(state);
  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);

  return ({
    initialValues: {
      pageTabs: currentLocation,
      tabConfiguration: tabConfiguration(hasBSubscription, hasCSubscription),
    },
  });
})(BasicCustomForm);

const mapStateToProps = (state) => ({
  bulkUpdate: cloudConnectorSelector.bulkUpdateSelector(state),
  bulkUpdateHasZTG: cloudConnectorSelector.bulkUpdateHasZTGSelector(state),
  formType: cloudConnectorSelector.formTypeSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    hanldeExpandClick,
    handlePagetype,
    handleToggleForm: toggleViewModal,
    handleToggleDisableForm: toggleDisableForm,
    handleToggleEnableForm: toggleEnableForm,
    handleSortBy: toggleSortBy,
    handleShowHideChilds: toggleShowHideChilds,
    handleCheckConnectorGroup: toggleCheckConnectorGroup,
    deleteBCGroupRowData: deleteBCGroupData,
    handleDeleteConfirmationForm: toggleDeleteForm,
    handleSearchText,
    resetConnectorGroups,
    toggleAdminPanel,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(CloudConnectors));

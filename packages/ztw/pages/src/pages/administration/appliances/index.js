import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import SearchBox from 'components/searchBox';
import Modal from 'components/modal';
import ServerError from 'components/errors/ServerError';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';

import * as constants from 'ducks/login/constants';
import RBAC from 'components/rbac';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasBsku, getSubscriptionLicenses } from 'utils/helpers';
import * as appliancesSelector from 'ducks/appliances/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import {
  loader,
  toggleViewForm,
  toggleEditForm,
  deleteAppliance,
  toggleDeleteConfirmationForm,
} from 'ducks/appliances';

import {
  ApplianceViewModal,
  AppliancesTable,
} from './components';

export function Appliances(props) {
  const {
    accessPrivileges,
    accessSubscriptions,
    accessDetailSubscriptions,
    modalLoading,
    selectedRowID,
    showDeleteForm,
    showEditForm,
    showViewForm,
    t,
    actions,
  } = props;

  useEffect(() => {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.LOCATIONS });
    // passing fourth arg true when page loads for the first time
    actions.load(true);
  }, []);

  const permKey = [constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_CLOUD_PROVISIONING,
    constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_TEMPLATE];
   
  const hasBSubscription = hasBsku(accessSubscriptions);

  if (!hasBSubscription) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }

  const bcDevice400Licenses = getSubscriptionLicenses(accessDetailSubscriptions, 'BC_DEVICE_CONNECTOR_400');
  const bcDevice600Licenses = getSubscriptionLicenses(accessDetailSubscriptions, 'BC_DEVICE_CONNECTOR_600');
  const bcDevice800Licenses = getSubscriptionLicenses(accessDetailSubscriptions, 'BC_DEVICE_CONNECTOR_800');

  if ((bcDevice400Licenses === 0) && (bcDevice600Licenses === 0) && (bcDevice800Licenses === 0)) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }

  if (accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE' && accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
    return <PermissionRequired accessPermissions={accessPrivileges} />;
  }

  return (
    <div className="main-container appliance-container">
      <div className="page-title">
        {t('ZT_DEVICES')}
      </div>
      <div className="appliance-content">
        <div className="search-container">
          <SearchBox placeholder={t('SEARCH')} clickCallback={() => actions.load(true)} />
        </div>
        <div className="appliance-table">
          <div className="container-row">
            <RBAC privilege={permKey}>
              <HelpArticle article={HELP_ARTICLES.ABOUT_ZT_DEVICES} />
              <ServerError {...props}>
                <AppliancesTable {...props} />
                <Modal
                  title={t(`${showEditForm ? 'EDIT_ZT_DEVICE' : 'VIEW_ZT_DEVICE'}`)}
                  isOpen={showEditForm || showViewForm}
                  styleClass="appliance-view-modal"
                  closeModal={() => (showEditForm
                    ? actions.toggleEditForm(null, false)
                    : actions.toggleViewForm(null, false))}>
                  <ApplianceViewModal
                    handleSubmit={actions.updateApplianceData}
                    showViewForm={showViewForm}
                    closeModal={() => (showEditForm
                      ? actions.toggleEditForm(null, false)
                      : actions.toggleViewForm(null, false))} />
                </Modal>
                <Modal
                  title={t('DELETE_CONFIRMATION')}
                  isOpen={showDeleteForm}
                  closeModal={() => actions.toggleDeleteConfirmationForm(null, false)}>
                  <DeleteConfirmationForm
                    modalLoading={modalLoading}
                    selectedRowID={selectedRowID}
                    handleCancel={actions.toggleDeleteConfirmationForm}
                    handleDelete={actions.deleteApplianceRowItem} />
                </Modal>
              </ServerError>
            </RBAC>
          </div>
        </div>
      </div>
    </div>
  );
}

const mapStateToProps = (state) => ({
  ...appliancesSelector.baseSelector(state),
  modalLoading: appliancesSelector.modalLoadingSelector(state),
  selectedRowID: appliancesSelector.selectedRowIDSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  accessDetailSubscriptions: loginSelectors.accessDetailSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loader,
    toggleViewForm,
    toggleEditForm,
    deleteApplianceRowItem: deleteAppliance,
    toggleDeleteConfirmationForm,
  }, dispatch);
  return { actions };
};

Appliances.propTypes = {
  accessPrivileges: PropTypes.shape({}),
  accessDetailSubscriptions: PropTypes.arrayOf(PropTypes.shape({})),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  editForm: PropTypes.bool,
  expandAll: PropTypes.bool,
  hasNextPage: PropTypes.bool,
  load: PropTypes.func,
  modalLoading: PropTypes.bool,
  moreItemsLoading: PropTypes.bool,
  selectedRowID: PropTypes.string,
  showDeleteForm: PropTypes.bool,
  showEditForm: PropTypes.bool,
  showViewForm: PropTypes.bool,
  t: PropTypes.func,
  tableColumns: PropTypes.arrayOf(PropTypes.shape()),
  actions: PropTypes.shape({}),
};

Appliances.defaultProps = {
  accessPrivileges: {},
  accessDetailSubscriptions: [],
  accessSubscriptions: [],
  authType: '',
  editForm: false,
  expandAll: null,
  hasNextPage: false,
  load: noop,
  modalLoading: false,
  moreItemsLoading: false,
  selectedRowID: null,
  showDeleteForm: false,
  showEditForm: false,
  showViewForm: false,
  t: (str) => str,
  tableColumns: [],
  actions: {},
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(Appliances));

/* eslint-disable react/jsx-handler-names */
// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import {
  hasBsku, hasCsku, hasBEncryptSku, getReadOnly,
} from 'utils/helpers';
import DeleteConfirmationGroupForm from 'components/DeleteConfirmationForm/connectorGroupForm';
import DisableConfirmationCCGroupForm from 'components/DeleteConfirmationForm/ccDisableGroupForm';
import EnableConfirmationCCGroupForm from 'components/DeleteConfirmationForm/ccEnableGroupForm';
import * as branchConnectorSelector from 'ducks/branchConnectors/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import { HELP_ARTICLES } from 'config';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';

import {
  hanldeExpandClick,
  toggleViewModal,
  deleteBCGroupData,
  toggleDeleteForm,
  toggleDisableForm,
  toggleEnableForm,
  handleDisableEnable,
  toggleSortBy,
  toggleShowHideChilds,
  toggleCheckConnectorGroup,
} from 'ducks/branchConnectors';

// eslint-disable-next-line import/no-named-as-default-member
import BranchConnectorViewModal from '../BranchConnectorViewModal';

export class BranchConnectors extends React.Component {
  static propTypes = {
    actions: PropTypes.shape({
      load: PropTypes.func,
      handleToggleForm: PropTypes.func,
      handleDeleteConfirmationForm: PropTypes.func,
      handleDisableConfirmationForm: PropTypes.func,
      handleEnableConfirmationForm: PropTypes.func,
      deleteBCGroupRowData: PropTypes.func,
      handleDisable: PropTypes.func,
      handleDisableEnable: PropTypes.func,
      handleEnable: PropTypes.func,
    }),
    branchConnectorsData: PropTypes.arrayOf(PropTypes.shape()),
    showForm: PropTypes.bool,
    t: PropTypes.func,
    showDeleteForm: PropTypes.bool,
    modalLoading: PropTypes.bool,
    selectedRowID: PropTypes.string,
    authType: PropTypes.string,
    accessPrivileges: PropTypes.shape({
      EDGE_CONNECTOR_CLOUD_PROVISIONING: PropTypes.string,
    }),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    formType: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({}),
    ]),
    showDisableForm: PropTypes.bool,
    showEnableForm: PropTypes.bool,
    disableType: PropTypes.shape({
      id: PropTypes.string,
    }),
    enableType: PropTypes.shape({
      id: PropTypes.string,
    }),
    pathname: PropTypes.string,
  };

  static defaultProps = {
    actions: {
      load: null,
      handleToggleForm: null,
      handleDeleteConfirmationForm: null,
      handleDisableConfirmationForm: null,
      handleEnableConfirmationForm: null,
      deleteBCGroupRowData: null,
      handleDisable: null,
      handleEnable: null,
    },
    branchConnectorsData: [],
    showForm: false,
    t: (str) => str,
    showDeleteForm: false,
    modalLoading: false,
    selectedRowID: null,
    accessPrivileges: {},
    authType: '',
    accessSubscriptions: [],
    showDisableForm: false,
    showEnableForm: false,
    disableType: {},
    enableType: {},
    pathname: '',
  };

  componentDidMount() {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.BRANCH_AND_BRANCH_GROUP });
  }

  render() {
    const {
      branchConnectorsData,
      actions,
      showForm,
      t,
      showDeleteForm,
      showDisableForm,
      showEnableForm,
      disableType,
      enableType,
      selectedRowID,
      accessPrivileges,
      accessSubscriptions,
      formType,
      authType,
      modalLoading,
      pathname,
    } = this.props;
    const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING, authType);
    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);
    const isBCPhysical = pathname.includes('branch-devices/physical');
    const isBCVirtual = pathname.includes('branch-devices/virtual');
    const hasBCEncryptSubscription = hasBEncryptSku(accessSubscriptions);

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
      return <PermissionRequired accessPermissions={accessPrivileges} />;
    }

    const disableTitle = (id) => {
      if (id === 'ONE_CC') return t('DISABLE_BRANCH_CONNECTOR');
      if (id === 'ONE_CC_GROUP') return t('DISABLE_BRANCH_CONNECTOR_GROUP');
      if (id === 'SELECTED_CC') return t('DISABLE_BRANCH_CONNECTOR_SELECTED');
      return '';
    };

    const enableTitle = (id) => {
      if (id === 'ONE_CC') return t('ENABLE_BRANCH_CONNECTOR');
      if (id === 'ONE_CC_GROUP') return t('ENABLE_BRANCH_CONNECTOR_GROUP');
      if (id === 'SELECTED_CC') return t('ENABLE_BRANCH_CONNECTOR_SELECTED');
      return '';
    };

    const pageTitle = () => {
      if (formType === 'BULK_CHANGE') return t('SCHEDULE_UPGRADE');
      if (formType === 'BULK_CHANGE_DNS_CACHE') return t('DNS_CACHE');
      if (formType === 'BULK_CHANGE_FAIL_OPEN') return t('FAIL_OPEN');
      if (isReadOnly && isBCPhysical) return t('VIEW_PHYSICAL_BRANCH_DEVICE');
      if (!isReadOnly && isBCPhysical) return t('EDIT_PHYSICAL_BRANCH_DEVICE');
      if (isReadOnly && isBCVirtual) return t('VIEW_VIRTUAL_BRANCH_DEVICE');
      if (!isReadOnly && isBCVirtual) return t('EDIT_VIRTUAL_BRANCH_DEVICE');
      return '';
    };

    return (
      <>
        <Modal
        // eslint-disable-next-line no-nested-ternary
          title={pageTitle()}
          isOpen={showForm}
          styleClass="schdule-upgrade"
          closeModal={() => actions.handleToggleForm(null, false)}>
          <Loading loading={modalLoading} />
          <BranchConnectorViewModal
            viewOnly={isReadOnly}
            isBCPhysical={isBCPhysical}
            hasBCEncryptSubscription={hasBCEncryptSubscription} />
        </Modal>

        <Modal
          title={disableTitle(disableType && disableType.id)}
          isOpen={showDisableForm}
          styleClass="disable-cc-group"
          closeModal={() => actions.handleDisableConfirmationForm(false)}>
          <Loading loading={modalLoading} />
          <DisableConfirmationCCGroupForm
            isCCPage={false}
            disableType={disableType}
            handleCancel={() => actions.handleDisableConfirmationForm(false)}
            handleDisable={() => actions.handleDisableEnable('DISABLE')} />
        </Modal>

        <Modal
          title={enableTitle(enableType && enableType.id)}
          isOpen={showEnableForm}
          styleClass="enable-cc-group"
          closeModal={() => actions.handleEnableConfirmationForm(false)}>
          <Loading loading={modalLoading} />
          <EnableConfirmationCCGroupForm
            isCCPage={false}
            enableType={enableType}
            handleCancel={() => actions.handleEnableConfirmationForm(null, false)}
            handleEnable={() => actions.handleDisableEnable('ENABLE')} />
        </Modal>

        <Modal
          title={t('DELETE_CONFIRMATION')}
          isOpen={showDeleteForm}
          styleClass="delete-cc-group"
          closeModal={() => actions.handleDeleteConfirmationForm(null, false)}>
          <Loading loading={modalLoading} />
          <DeleteConfirmationGroupForm
            isCCPage={false}
            isBCPhysical={isBCPhysical}
            selectedRowID={selectedRowID}
            subItems={branchConnectorsData.filter(
              (x) => (x.parentId === selectedRowID || x.id === selectedRowID),
            )}
            handleCancel={() => actions.handleDeleteConfirmationForm(null, false)}
            handleDelete={actions.deleteBCGroupRowData} />
        </Modal>
      </>
    );
  }
}

const mapStateToProps = (state) => ({
  showForm: branchConnectorSelector.showFormSelector(state),
  showDeleteForm: branchConnectorSelector.showDeleteFormSelector(state),
  showDisableForm: branchConnectorSelector.showDisableFormSelector(state),
  showEnableForm: branchConnectorSelector.showEnableFormFormSelector(state),
  disableType: branchConnectorSelector.disableTypeSelector(state),
  enableType: branchConnectorSelector.enableTypeSelector(state),
  modalLoading: branchConnectorSelector.modalLoadingSelector(state),
  selectedRowID: branchConnectorSelector.selectedRowIDSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  branchConnectorsData: branchConnectorSelector.dataTableSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    hanldeExpandClick,
    handleToggleForm: toggleViewModal,
    handleSortBy: toggleSortBy,
    handleShowHideChilds: toggleShowHideChilds,
    handleCheckConnectorGroup: toggleCheckConnectorGroup,
    deleteBCGroupRowData: deleteBCGroupData,
    handleDeleteConfirmationForm: toggleDeleteForm,
    handleDisableConfirmationForm: toggleDisableForm,
    handleEnableConfirmationForm: toggleEnableForm,
    handleDisableEnable,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(BranchConnectors));

/* eslint-disable react/jsx-handler-names */
// @flow
import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import { Field, reduxForm } from 'redux-form';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PageTabs from 'components/navTabs/PageTabs';
import SearchBox from 'components/searchBox';
import ServerError from 'components/errors/ServerError';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import {
  hasBsku, getReadOnly, verifyConfigData,
} from 'utils/helpers';
import * as loginSelectors from 'ducks/login/selectors';
import * as branchConnectorSelector from 'ducks/branchConnectors/selectors';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';

import {
  deleteBCGroupData,
  handlePagetype,
  handleSearchText,
  hanldeExpandClick,
  loadBranchConnectorData,
  resetConnectorGroups,
  tabConfiguration,
  toggleCheckConnectorGroup,
  toggleDeleteForm,
  toggleDisableForm,
  toggleEnableForm,
  toggleShowHideChilds,
  toggleSortBy,
  toggleViewModal,
} from 'ducks/branchConnectors';
import { toggleAdminPanel } from 'ducks/adminNav';
import BranchConnectorsTable from './components/BranchConnectorsTable';

import { ActionBar, Modals } from './components';

let currentLocation;

function BasicCustomAppForm(props) {
  const {
    accessPrivileges,
    accessSubscriptions,
    actions,
    authType,
    bulkUpdate,
    configData,
    loading,
    t,
  } = props;
  const location = useLocation();
  const { pathname } = location;
  currentLocation = pathname;

  useEffect(() => {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.BRANCH_AND_BRANCH_GROUP });
    actions.toggleAdminPanel(false);
    actions.handlePagetype(pathname);
    actions.loadBranchConnectorData(true, 1);
    return () => actions.resetConnectorGroups();
  }, [pathname]);

  const hasBSubscription = hasBsku(accessSubscriptions);

  if (!hasBSubscription) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }

  if (accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
    return <PermissionRequired accessPermissions={accessPrivileges} />;
  }
  const isBCPhysical = pathname.includes('branch-devices/physical');
  const isBCVirtual = pathname.includes('branch-devices/virtual');
  const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING, authType);
  const enableFailOpen = verifyConfigData({ configData, key: 'enableFailOpen' });

  return (
    <ServerError {...props}>
      <Loading loading={loading} />
      <div className="main-container cc-group">
        <HelpArticle article={HELP_ARTICLES.BRANCH_CONNECTOR_GROUP} />
        {isBCPhysical && <HelpArticle article={HELP_ARTICLES.ABOUT_PHYSYCAL_BRANCH_DEVICES} />}
        {isBCVirtual && <HelpArticle article={HELP_ARTICLES.ABOUT_VIRTUAL_BRANCH_DEVICES} />}
        <div className="page-title  header-3">
          {t('BRANCH_DEVICES')}
        </div>
        <Field
          id="pageTabs"
          name="pageTabs"
          disable={loading}
          component={PageTabs} />
        <div className="actions-row">
          {bulkUpdate && !isReadOnly && (
            <ActionBar
              t={t}
              isBCPhysical={isBCPhysical}
              enableFailOpen={enableFailOpen}
              handleScheduleUpgrade={() => actions.handleToggleForm('BULK_CHANGE', true)}
              handleDnsCache={() => actions.handleToggleForm('BULK_CHANGE_DNS_CACHE', true)}
              handleFailOpen={() => actions.handleToggleForm('BULK_CHANGE_FAIL_OPEN', true)}
              handleDisable={() => actions.handleToggleDisableForm(true)}
              handleEnable={() => actions.handleToggleEnableForm(true)} />
          )}
          <div className="search-container-cc-group">
            <SearchBox
              placeholder={t('SEARCH')}
              clickCallback={(value) => actions.handleSearchText(value)} />
          </div>
        </div>
        {(!hasBSubscription)
          ? <SubscriptionRequired accessSubscriptions={accessSubscriptions} />
          : (
            <div className="container-row-cc-group bc-connectors">
              <BranchConnectorsTable isBCVirtual={isBCVirtual} isBCPhysical={isBCPhysical} />
              <Modals pathname={pathname} {...props} />
            </div>
          )}
      </div>
    </ServerError>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    deleteBCGroupRowData: PropTypes.func,
    handleDeleteConfirmationForm: PropTypes.func,
    handlePagetype: PropTypes.func,
    handleSearchText: PropTypes.func,
    handleToggleDisableForm: PropTypes.func,
    handleToggleEnableForm: PropTypes.func,
    handleToggleForm: PropTypes.func,
    load: PropTypes.func,
    resetConnectorGroups: PropTypes.func,
    toggleAdminPanel: PropTypes.func,
  }),
  t: PropTypes.func,
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_CLOUD_PROVISIONING: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  configData: PropTypes.shape({}),
  bulkUpdate: PropTypes.bool,
  loading: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  actions: {
    load: null,
    handleToggleForm: null,
    handleDeleteConfirmationForm: null,
    deleteBCGroupRowData: null,
  },
  t: (str) => str,
  authType: '',
  accessPrivileges: {},
  accessSubscriptions: [],
  configData: {},
  loading: false,
};

const BasicCustomForm = reduxForm({
  form: 'branchConnectorsPage',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(BasicCustomAppForm);

const BranchConnectors = connect((state) => {
  const accessDetailSubscriptions = loginSelectors.accessDetailSubscriptionSelector(state);

  return ({
    initialValues: {
      pageTabs: currentLocation,
      tabConfiguration: tabConfiguration(accessDetailSubscriptions),
    },
  });
})(BasicCustomForm);

const mapStateToProps = (state) => ({
  bulkUpdate: branchConnectorSelector.bulkUpdateSelector(state),
  formType: branchConnectorSelector.formTypeSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  loading: branchConnectorSelector.loadingSelector(state),
  accessDetailSubscriptions: loginSelectors.accessDetailSubscriptionSelector(state),
  configData: loginSelectors.configDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    deleteBCGroupRowData: deleteBCGroupData,
    handleCheckConnectorGroup: toggleCheckConnectorGroup,
    handleDeleteConfirmationForm: toggleDeleteForm,
    handlePagetype,
    handleSearchText,
    handleShowHideChilds: toggleShowHideChilds,
    handleSortBy: toggleSortBy,
    handleToggleDisableForm: toggleDisableForm,
    handleToggleEnableForm: toggleEnableForm,
    handleToggleForm: toggleViewModal,
    hanldeExpandClick,
    loadBranchConnectorData,
    resetConnectorGroups,
    toggleAdminPanel,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(BranchConnectors));

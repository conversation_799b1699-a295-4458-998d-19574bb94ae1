import React from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';

import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasBsku, hasCsku } from 'utils/helpers';
import * as loginSelectors from 'ducks/login/selectors';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import awsLogo from 'images/aws-logo.png';
import awsLogoDark from 'images/aws-logo-dark-mode.png';
import azureLogo from 'images/azure.png';
import gcpLogo from 'images/gcp.png';

export function DeploymentTemplates() {
  const { t } = useTranslation();
  const accessPermissions = useSelector(
    (state) => loginSelectors.accessPermissionsSelector(state),
  );
  const accessSubscriptions = useSelector(
    (state) => loginSelectors.accessSubscriptionSelector(state),
  );

  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);

  if (!hasBSubscription && !hasCSubscription) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }
  if (accessPermissions.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
    return <PermissionRequired accessPermissions={accessPermissions} />;
  }
    
  const deprecatedText = t('DEPLOYMENT_TEMPLATES_DEPRECATED').split(/{[0-9]}/g);
  const deprecatedTextJSX = (
    <>
      {deprecatedText[0]}
      <a
        target="_blank"
        rel="noopener noreferrer"
        href="https://github.com/zscaler"
        className="tooltip-navlink">
        <b>{deprecatedText[1]}</b>
      </a>
      {deprecatedText[2]}
      <a
        target="_blank"
        rel="noopener noreferrer"
        href="https://help.zscaler.com/cloud-connector/deployment-templates-zscaler-cloud-connector"
        className="tooltip-navlink">
        <b>{deprecatedText[3]}</b>
      </a>
    </>
  );

  return (
    <div className="main-container ">
      <HelpArticle article={HELP_ARTICLES.CLOUD_AUTOMATION_SCRIPTS} />
      <div className="page-title  header-3">
        {t('DEPLOYMENT_TEMPLATES')}
      </div>

      <div className="add-custom-app-form cloud-configuration-advanced-settings deployment-template">
        <div className="deployment-template-depreciated">
          {deprecatedTextJSX}
        </div>
        <div className="source-ip-wrapper">

          <div className="form-sections-container">
            <div className="form-sections-container-title">
              <img src={awsLogo} className="cluster-child-icon as-icon aws-logo" alt="AWS" />
              <span className="header-5">AWS</span>
            </div>
            <div className="form-section">
              <div className="full-width as-link">
                <a
                  target="_blank"
                  rel="noopener noreferrer"
                  href="https://github.com/zscaler/terraform-aws-cloud-connector-modules/tree/main"
                  className="tooltip-navlink">
                  <b>{t('TERRA_FORMATION')}</b>
                </a>
              </div>
              <div className="full-width as-link">
                <a
                  target="_blank"
                  rel="noopener noreferrer"
                  href="https://github.com/zscaler/cloud-native-aws-cloud-connector-deploy"
                  className="tooltip-navlink">
                  <b>{t('CLOUD_FORMATION')}</b>
                </a>
              </div>
            </div>
          </div>

          <div className="form-sections-container">
            <div className="form-sections-container-title">
              <img src={azureLogo} className="cluster-child-icon as-icon" alt="Azure" />
              <span className="header-5">AZURE</span>
            </div>
            <div className="form-section">
              <a
                target="_blank"
                rel="noopener noreferrer"
                href="https://github.com/zscaler/terraform-azurerm-cloud-connector-modules"
                className="tooltip-navlink">
                <b>{t('TERRA_FORMATION')}</b>
              </a>
            </div>

          </div>

          <div className="form-sections-container">
            <div className="form-sections-container-title">
              <img src={gcpLogo} className="cluster-child-icon as-icon" alt="Azure" />
              <span className="header-5">GCP</span>
            </div>
            <div className="form-section">
              <a
                target="_blank"
                rel="noopener noreferrer"
                href="https://github.com/zscaler/terraform-gcp-cloud-connector-modules"
                className="tooltip-navlink">
                <b>{t('TERRA_FORMATION')}</b>
              </a>
            </div>
          </div>

          <div className="deployment-template-depreciated">
          </div>
        </div>
      </div>
    </div>

  );
}

export default (DeploymentTemplates);

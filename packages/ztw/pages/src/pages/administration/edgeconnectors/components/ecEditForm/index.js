// @flow
import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Field, reduxForm } from 'redux-form';
import PropTypes from 'prop-types';
import { FormFieldLabel } from 'components/label';
import Input from 'components/Input';
import RadioGroup from 'components/radioGroup';
import { withTranslation } from 'react-i18next';
import { required, maxLength } from 'utils/validations';
import { PROVISIONING_KEY } from 'ducks/edgeconnectors/constants';
import { noop } from 'utils/lodash';
import * as edgeconnectorSelectors from 'ducks/edgeconnectors/selectors';

import {
  toggleForm,
} from 'ducks/edgeconnectors';

import ECGroupsDropdown from '../ecgDropdown';
import ManagementIPDropdown from '../ManagementIPDropdown';

const maxLength50 = maxLength(50);

export class BasicCustomAppForm extends React.Component {
  static propTypes = {
    actions: PropTypes.shape({
      cancelHandle: PropTypes.func,
    }),
    valid: PropTypes.bool,
    initialValues: PropTypes.shape(),
    t: PropTypes.func,
  };

  static defaultProps = {
    actions: {
      cancelHandle: noop,
    },
    valid: true,
    initialValues: {},
    t: (str) => str,
  };

  state = {};

  render() {
    const {
      valid,
      actions,
      t,
      initialValues,
    } = this.props;

    const { cancelHandle } = actions;

    return (
      <form className="add-custom-app-form">
        <div className="container">
          <div className="container-fields">
            <Field
              name="name"
              id="fieldName"
              component={Input}
              type="text"
              label={t('NAME')}
              validate={[
                required,
                maxLength50,
              ]} />
            <Field
              name="desc"
              id="desc"
              component={Input}
              type="text"
              label={t('DESCRIPTION')}
              placeholder={t('TYPE_DESCRIPTION_HERE')} />
            <div className="input-container">
              <FormFieldLabel text="Provisioning Key" />
              <p className="disabled-input provisioning-key">{PROVISIONING_KEY}</p>
            </div>
            <RadioGroup
              name="trafficForward"
              label={t('TRAFFIC_FORWARDING')}
              options={[{ name: 'trafficForward', value: true, label: 'Enable' },
                { name: 'trafficForward', value: false, label: 'Disable' }]} />
            <div className="management-ip-section">
              <FormFieldLabel text="Management IP" />
              <Field
                id="mgmtDropdown"
                name="managementIPType"
                onChange={noop}
                parse={(value) => value.id}
                component={ManagementIPDropdown} />
            </div>
            <div className={`management-ip-static-fields ${initialValues.managementIPType === 'managementIPManual' ? 'unhide' : 'hide'}`}>
              <Field
                name="managementStaticIP"
                id="staticIP"
                component={Input}
                type="text"
                label={t('STATIC_IP_ADDRESS')} />
              <Field
                name="managementInterfaceName"
                id="ZSInterface"
                component={Input}
                type="text"
                label={t('ZSCALER_INTERFACE_NAME')} />
              <Field
                name="managementDefaultGateway"
                id="defaultGateway"
                component={Input}
                type="text"
                label={t('DEFAULT_GATEWAY')} />
              <Field
                name="managementDns1"
                id="dnsServer1"
                component={Input}
                type="text"
                label={t('DNS_SERVER_ONE')} />
              <Field
                name="managementDns2"
                id="dnsServer2"
                component={Input}
                type="text"
                label={t('DNS_SERVER_TWO_OPTIONAL')} />
            </div>
            <div className="ecg-location-wrapper">
              <div>
                <div className="input-container">
                  <FormFieldLabel text={t('CLOUD_CONNECTOR_GROUP')} />
                  {
                    initialValues.trafficForward
                      ? (
                        <div className="input-container">
                          <p className="disabled-input">EC Group 1</p>
                        </div>
                      ) : (
                        <Field
                          name="ecg-dropdown"
                          id="ecg"
                          component={ECGroupsDropdown}
                          placeholder={t('ALL')} />
                      )
                  }
                </div>
              </div>
              <div>
                <div className="input-container disabled">
                  <FormFieldLabel text={t('LOCATION')} />
                  <p className="disabled-input">San Jose, California, USA</p>
                </div>
              </div>
            </div>
            <div className="input-container">
              <FormFieldLabel text="URL" />
              <p className="disabled-input provisioning-key">https://msmca.zscaler.com/some</p>
            </div>
          </div>
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="submit" className={`submit ${!valid ? 'disabled' : ''}`}>{t('SAVE')}</button>
            <button type="button" className="cancel" onClick={() => cancelHandle(false)}>{t('CANCEL')}</button>
          </div>
        </div>
      </form>
    );
  }
}

const ECEditForm = reduxForm({
  form: 'ECEditForm',
})(BasicCustomAppForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleForm,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state, ownProps) => ({
  initialValues: edgeconnectorSelectors.appDataSelector(state),
  ...ownProps,
});

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(ECEditForm));

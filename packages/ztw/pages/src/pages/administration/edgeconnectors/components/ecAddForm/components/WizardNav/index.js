// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Trans, withTranslation } from 'react-i18next';
import { formValueSelector, isValid } from 'redux-form';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle, faCircle } from '@fortawesome/pro-solid-svg-icons';

const formValidity = {};
const selector = formValueSelector('edgeconnectorWizard');
let type;

function WizardNav(props) {
  const {
    activePage, goToPage, typeFromState, valid, wizardNavConfig,
  } = props;

  // track form validity for each sub-form
  formValidity[activePage] = valid;

  // if a sub-form is invalid, mark all next sub-forms as invalid
  if (!valid) {
    wizardNavConfig.forEach((item, index) => {
      if (index > activePage) {
        formValidity[index] = false;
      }
    });
  }

  // if the entire wizard form is filled out and user comes back to 1st screen and
  // changes the type, unset validity for all other pages to reflect change
  // in Additional params
  if (activePage === 0 && type !== typeFromState) {
    for (let i = 1, l = wizardNavConfig.length; i < l; i += 1) {
      formValidity[i] = false;
    }

    type = typeFromState;
  }

  return (
    <div className="wizard-nav">
      <ul>
        {wizardNavConfig.map((item, index) => (
          <li
            className={`${index === activePage ? 'active' : ''} ${formValidity[index] ? 'valid' : ''}`}
            key={item}>
            <button
              type="button"
              onClick={() => goToPage(index)}
              disabled={index !== 0 && !formValidity[index - 1]}>
              <span className="fa-layers fa-fw">
                <FontAwesomeIcon icon={faCircle} size="lg" />
                <span className="fa-stack-1x page-index">{index + 1}</span>
                <FontAwesomeIcon icon={faCheckCircle} size="lg" />
              </span>
              <span className="name"><Trans>{item}</Trans></span>
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
}

WizardNav.propTypes = {
  activePage: PropTypes.number,
  goToPage: PropTypes.func,
  typeFromState: PropTypes.string,
  valid: PropTypes.bool,
  wizardNavConfig: PropTypes.arrayOf(PropTypes.string),
};

WizardNav.defaultProps = {
  activePage: 0,
  goToPage: (str) => str,
  typeFromState: '',
  valid: false,
  wizardNavConfig: {},
};

export default connect((state) => ({
  typeFromState: selector(state, 'type'),
  valid: isValid('edgeconnectorWizard')(state),
}))(withTranslation()(WizardNav));

export { WizardNav };

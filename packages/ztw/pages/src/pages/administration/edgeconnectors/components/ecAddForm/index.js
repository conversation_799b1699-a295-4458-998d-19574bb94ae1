/* eslint-disable react/jsx-handler-names */
// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import Modal from 'react-modal';
import { withTranslation } from 'react-i18next';
import { getFormValues, change } from 'redux-form';
import withRouter from 'layout/withRouter';
import { get } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/pro-solid-svg-icons';
import { BASE_LAYOUT } from 'config';

import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import {
  toggleWizard,
  saveEdgeconnector,
  setWizardActiveNavPage,
} from 'ducks/edgeconnectorWizard';

import { toggleAddEcForm } from 'ducks/edgeconnectors';
import * as edgeconnectorWizardSelectors from 'ducks/edgeconnectorWizard/selectors';

import {
  WizardNav,
  CreateProvisioningKey,
  ManagementIp,
  EdgeConnectorGroups,
  StaticServiceIp,
  Review,
  ProvisioningUrl,
} from './components';

class ECAddForm extends React.Component {
  state = {
    wizardNavConfig: [
      'Create Provisioning Key',
      'Management IP',
      'Edgeconnector Groups',
      'Static Service IP',
      'Review',
      'Provisioning URL',
    ],
  };

  componentDidMount() {
    Modal.setAppElement('body');
  }

  componentWillUnmount() {
    const { actions: { toggleWizardModal }, isMonitorWizardOpen } = this.props;

    if (isMonitorWizardOpen) {
      toggleWizardModal(false, null, null);
    }
  }

  closeModal = () => {
    const { actions, history, match } = this.props;
    const { toggleWizardModal } = actions;
    const id = get(match, 'params.id', null);

    toggleWizardModal(false, null, null);

    if (id) {
      history.push(`${BASE_LAYOUT}/configuration/monitors`);
    }
  };

  handleNextPage = () => {
    // eslint-disable-next-line
    const { actions: { setWizardActiveNavPage }, activePage } = this.props;

    setWizardActiveNavPage(activePage + 1);
  };

  previousPage = () => {
    // eslint-disable-next-line
    const { actions: { setWizardActiveNavPage }, activePage } = this.props;

    setWizardActiveNavPage(activePage - 1);
  };

  goToPage = (pageNum) => {
    // eslint-disable-next-line
    const { actions: { setWizardActiveNavPage } } = this.props;

    setWizardActiveNavPage(pageNum);
  };

  onSubmit = (values) => {
    const { actions: { saveEdgeconnector }, history } = this.props; // eslint-disable-line no-shadow

    saveEdgeconnector(values)
      .then(() => {
        if (/\/new$/.test(window.location.pathname)) {
          history.push(`${BASE_LAYOUT}/configuration/monitors`);
        }
      });
  };

  renderForm = (activePage) => {
    const { type } = this.props;

    switch (activePage) {
    case 0:
      return (
        <CreateProvisioningKey
          onSubmit={this.handleNextPage}
          closeModal={this.closeModal}
          type={type} />
      );

    case 1:
      return (
        <ManagementIp
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal}
          type={type} />
      );

    case 2:
      return (
        <EdgeConnectorGroups
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal}
          type={type} />
      );

    case 3:
      return (
        <StaticServiceIp
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal}
          type={type} />
      );
    
    case 4:
      return (
        <Review
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal}
          type={type} />
      );
    
    case 5:
      return (
        <ProvisioningUrl
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal}
          type={type} />
      );

    default:
      return null;
    }
  };

  render() {
    const {
      t,
      activePage,
      isMonitorWizardOpen,
      mode,
    } = this.props;
    const { wizardNavConfig } = this.state;
    let label;

    switch (mode) {
    case 'NEW':
    case 'PRESET':
      label = t('ADD_CLOUD_CONNECTOR');
      break;

    case 'EDIT':
      label = t('EDIT_CLOUD_CONNECTOR');
      break;

    case 'COPY':
      label = t('COPY_CLOUD_CONNECTOR');
      break;

    default:
      label = '';
      break;
    }

    return (
      <div className="edgeconnector-modal">
        <Modal
          isOpen={isMonitorWizardOpen}
          contentLabel={label}
          className="ec-modal-body"
          closeTimeoutMS={400}
          overlayClassName="ec-modal-overlay">
          <div className="modal-header">
            {label}
            <button onClick={this.closeModal} type="button" className="close-button">
              <FontAwesomeIcon icon={faTimes} />
            </button>
          </div>
          <div className="modal-content">
            <Loading {...this.props}>
              <ServerError {...this.props}>
                <WizardNav
                  activePage={activePage}
                  goToPage={this.goToPage}
                  wizardNavConfig={wizardNavConfig} />
                {this.renderForm(activePage)}
              </ServerError>
            </Loading>
          </div>
        </Modal>
      </div>
      
    );
  }
}

ECAddForm.propTypes = {
  actions: PropTypes.shape(),
  activePage: PropTypes.number,
  history: PropTypes.shape(),
  match: PropTypes.shape(),
  name: PropTypes.string,
  type: PropTypes.string,
  mode: PropTypes.string,
  isMonitorWizardOpen: PropTypes.bool,
  t: PropTypes.func,
};

ECAddForm.defaultProps = {
  actions: null,
  activePage: 0,
  history: {},
  match: {},
  name: '',
  type: 'WEB',
  mode: 'NEW',
  isMonitorWizardOpen: false,
  t: (str) => str,
};

const mapStateToProps = (state) => ({
  ...edgeconnectorWizardSelectors.default(state),
  ...getFormValues('edgeconnectorWizard')(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleWizardModal: toggleWizard,
    updateFormState: change,
    saveEdgeconnector,
    setWizardActiveNavPage,
    cancelHandle: toggleAddEcForm,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(ECAddForm)));

// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import AddNewButton from 'components/addNewButton';
import { toggleWizard } from 'ducks/edgeconnectorWizard';
import { BASE_LAYOUT } from 'config';

import * as edgeconnectorSelector from 'ducks/edgeconnectors/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import Modal from 'components/modal';
import {
  getEdgeConnectorsData,
  toggleForm,
  toggleAddEcForm,
} from 'ducks/edgeconnectors';
import NavTabs from 'components/navTabs';
import {
  ECTable,
  ECEditForm,
  ECAddForm,
} from './components';

export class EdgeConnectors extends React.Component {
  static propTypes = {
    actions: PropTypes.shape({
      load: PropTypes.func,
      toggleECForm: PropTypes.func,
      toggleAddEcForm: PropTypes.func,
    }),
    ectabledata: PropTypes.arrayOf(PropTypes.shape()),
    showForm: PropTypes.bool,
  };

  static defaultProps = {
    actions: {
      load: noop,
      toggleECForm: noop,
      toggleAddEcForm: noop,
    },
    ectabledata: [],
    showForm: null,
  };

  componentDidMount() {
    const { actions } = this.props;
    const { load } = actions;
    load();
  }

  openModal = (event) => {
    event.preventDefault();
    // eslint-disable-next-line
    const { actions: { toggleWizard } } = this.props;
    toggleWizard(true, 'NEW', null);
  };

  render() {
    const {
      ectabledata,
      actions,
      showForm,
    } = this.props;

    return (
      <div className="main-container">
        <div className="configuration-nav-tab">
          <NavTabs
            tabConfiguration={[{
              id: 'edgeconnectors',
              title: 'CLOUD_CONNECTORS',
              to: `${BASE_LAYOUT}/administration/edgeconnectors`,
            },
            {
              id: 'edgeconnectorgroups',
              title: 'CLOUD_CONNECTOR_GROUP',
              to: `${BASE_LAYOUT}/administration/edgeconnectorgroups`,
            }]} />
        </div>
        <AddNewButton label="Add Edgeconnector" clickCallback={this.openModal} />
        <div className="container-row">
          <Loading {...this.props}>
            <ServerError {...this.props}>
              <ECTable
                ectabledata={ectabledata}
                handleEditAction={actions.toggleECForm} />
              <Modal
                title="Edit Edgeconnector"
                isOpen={showForm}
                closeModal={() => actions.toggleECForm(false)}>
                <ECEditForm />
              </Modal>
              <ECAddForm />
            </ServerError>
          </Loading>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...edgeconnectorSelector.default(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: getEdgeConnectorsData,
    toggleECForm: toggleForm,
    toggleAddEcForm,
    toggleWizard,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(EdgeConnectors));

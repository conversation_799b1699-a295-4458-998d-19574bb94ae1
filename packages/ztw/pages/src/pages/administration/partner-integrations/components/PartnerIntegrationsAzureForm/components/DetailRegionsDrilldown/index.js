/* eslint-disable react/jsx-handler-names */
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as constants from 'ducks/login/constants';
import ConfigTableWithPaginationAndSort from 'components/configTable/ConfigTableWithPaginationAndSort';
import { REGIONS_DRILLDOWN } from 'ducks/partnerIntegrationsAzure/constants';
import { getReadOnly } from 'utils/helpers';
import PropTypes from 'prop-types';
import * as partnerIntegrationsSelector from 'ducks/partnerIntegrationsAzure/selectors';

import {
  handlePageSize,
  handlePageNumber,
  refreshSunscriptionStatus,
  handleLinkDetailRegionSubscription,
} from 'ducks/partnerIntegrationsAzure';

import { isEmpty } from 'utils/lodash';

function DetailRegionsDrilldown(props) {
  const {
    accessPrivileges,
    authType,
    appData,
  } = props;
  const { subscriptionStatusList } = appData || {};
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => partnerIntegrationsSelector.baseSelector(state));
  const {
    pageNumber,
    pageSize,
    numberOfLines,
    searchText,
    sortField,
    sortDirection,
  } = baseSelector || {};

  const isReadOnly = getReadOnly(
    accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    authType,
  );
  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT;

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData
      .map((row) => {
        return {
          ...row,
          link: `./${row.subscriptionId}`,
          isReadOnly,
          // to remove Icon from the line
          parentId: null,
          hideIcon: true,
        };
      });
    return tableData;
  };
  
  const filteredData = subscriptionStatusList?.filter(
    (x) => t(x.name).toUpperCase().includes(searchText.toUpperCase())
    || t(x.subscriptionId).toUpperCase().includes(searchText.toUpperCase())
    ,
  );
  const data = getTableData(filteredData);

  return (
    <ConfigTableWithPaginationAndSort
      {...(REGIONS_DRILLDOWN(t))}
      permission={accessPrivileges[permKey]}
      onHandleLinkSelf={(value, name) => dispatch(
        handleLinkDetailRegionSubscription(value, { id: value.id, name }, 3),
      )}
      onHandleRowRefresh={async (e) => { dispatch(refreshSunscriptionStatus(e)); }}
      tableHeight={20000}
      maxTableHeight="20000px"
      sortField={sortField}
      sortDirection={sortDirection}
      numberOfLines={numberOfLines}
      pageChangeHandler={(e) => dispatch(handlePageNumber(e))}
      pageNumber={pageNumber}
      pageSize={pageSize}
      setPageSizeAPI={(e) => dispatch(handlePageSize(e))}
      loading={false}
      data={data} />
  );
}

DetailRegionsDrilldown.propTypes = {
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  appData: PropTypes.shape({}),
  authType: PropTypes.string,
  groupIndex: PropTypes.number,
  partnerIntegrationsAzureData: PropTypes.arrayOf(PropTypes.shape({})),

};

DetailRegionsDrilldown.defaultProps = {
  accessPrivileges: {},
  accessSubscriptions: [],
  appData: {},
  authType: '',
  groupIndex: 0,
  partnerIntegrationsAzureData: [],
};

export default DetailRegionsDrilldown;

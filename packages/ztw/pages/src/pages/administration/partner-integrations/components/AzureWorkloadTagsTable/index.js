/* eslint-disable react/jsx-handler-names */
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as constants from 'ducks/login/constants';
import ConfigTableWithPaginationAndSort from 'components/configTable/ConfigTableWithPagination';
import { AZURE_WORKLOAD_TAGS_TABLE_CONFIGS } from 'ducks/partnerIntegrationsAzure/constants';
import PropTypes from 'prop-types';
import * as partnerIntegrationsSelector from 'ducks/partnerIntegrationsAzure/selectors';

import {
  // handleOpenDrawer,
  handlePageNumber,
  handlePageSize,
} from 'ducks/partnerIntegrationsAzure';

import { isEmpty } from 'utils/lodash';

function AzureWorkloadTagsTable(props) {
  const {
    accessPrivileges,
    workloadsTagsData,
  } = props;
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => partnerIntegrationsSelector.baseSelector(state));
  const {
    pageNumber,
    pageSize,
    numberOfLines,
    sortField,
    sortDirection,
    loading,
  } = baseSelector || {};

  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_CLOUD_PROVISIONING;

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData
      .map((row) => {
        return {
          name: row.name,
          key: row.key,
          value: row.value,
        };
      });
    return tableData;
  };
  
  const data = getTableData(workloadsTagsData);

  return (
    <div className="azure-user-defined-tab-position">
      <ConfigTableWithPaginationAndSort
        {...(AZURE_WORKLOAD_TAGS_TABLE_CONFIGS(t))}
        permission={accessPrivileges[permKey]}
        // onHandleLink={appData => handleOpenDrawer(appData, drillData)}
        tableHeight={9999999999}
        maxTableHeight="9999999999px"
        sortField={sortField}
        sortDirection={sortDirection}
        // isApiPagination={false}
        numberOfLines={numberOfLines}
        pageChangeHandler={(e) => dispatch(handlePageNumber(e))}
        pageNumber={pageNumber}
        pageSize={pageSize}
        setPageSizeAPI={(e) => dispatch(handlePageSize(e))}
        loading={loading}
        data={data} />
    </div>
  );
}

AzureWorkloadTagsTable.propTypes = {
  accessPrivileges: PropTypes.shape({}),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  workloadsTagsData: PropTypes.arrayOf(PropTypes.shape({})),
  workloadsData: PropTypes.arrayOf(PropTypes.shape({})),
  pathname: PropTypes.string,
};

AzureWorkloadTagsTable.defaultProps = {
  accessPrivileges: {},
  accessSubscriptions: [],
  authType: '',
  workloadsTagsData: [],
  workloadsData: [],
  pathname: '',
};

export default AzureWorkloadTagsTable;

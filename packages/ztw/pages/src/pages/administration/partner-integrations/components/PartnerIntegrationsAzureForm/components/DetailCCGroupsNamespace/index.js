/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import { get, isEmpty } from 'utils/lodash';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as constants from 'ducks/login/constants';
import ConfigTableWithPaginationAndSort from 'components/configTable/ConfigTableWithPaginationAndSort';
import { REVIEW_CC_GROUP_AND_NAMESPACE } from 'ducks/partnerIntegrationsAzure/constants';
import { getReadOnly } from 'utils/helpers';
import PropTypes from 'prop-types';
import * as partnerIntegrationsSelector from 'ducks/partnerIntegrationsAzure/selectors';

import {
  handlePageSize,
  handlePageNumber,
  handleSearchText,
} from 'ducks/partnerIntegrationsAzure';

function DetailCCGroupsNamespace(props) {
  const {
    appData,
    accessPrivileges,
    authType,
    groupIndex = 0,
    isSearchActive,
  } = props;
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => partnerIntegrationsSelector.baseSelector(state));
  const cloudConnectorGroupList = get(appData, `subscriptionGroups[${groupIndex}].cloudConnectorGroupList`, null);
  const {
    pageNumber,
    pageSize,
    numberOfLines,
    searchText,
    sortField,
    sortDirection,
  } = baseSelector || {};

  const isReadOnly = getReadOnly(
    accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    authType,
  );
  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT;

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData
      .map((row) => {
        return {
          ...row,
          isRefreshable: !isReadOnly,
          isDeletable: false && !isReadOnly,
          isDisable: false && !isReadOnly,
          isReadOnly,
          // to remove Icon from the line
          parentId: null,
          hideIcon: true,
        };
      });
    return tableData;
  };
  
  const filteredData = cloudConnectorGroupList?.filter(
    (x) => t(x?.name).toUpperCase().includes(searchText.toUpperCase())
    || (x?.namespace).toUpperCase().includes(searchText.toUpperCase()),
  );
  const data = isSearchActive
    ? getTableData(filteredData)
    : getTableData(cloudConnectorGroupList);

  useEffect(() => {
    if (searchText !== '')dispatch(handleSearchText(''));
    return () => dispatch(handleSearchText(''));
  }, []);

  return (
    <ConfigTableWithPaginationAndSort
      {...(REVIEW_CC_GROUP_AND_NAMESPACE(t))}
      permission={accessPrivileges[permKey]}
      tableHeight={20000}
      maxTableHeight="20000px"
      sortField={sortField}
      sortDirection={sortDirection}
      numberOfLines={numberOfLines}
      pageChangeHandler={(e) => dispatch(handlePageNumber(e))}
      pageNumber={pageNumber}
      pageSize={pageSize}
      setPageSizeAPI={(e) => dispatch(handlePageSize(e))}
      loading={false}
      data={data} />
  );
}

DetailCCGroupsNamespace.propTypes = {
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: PropTypes.string,
  }),
  appData: PropTypes.shape({ }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  groupIndex: PropTypes.number,
  isSearchActive: PropTypes.bool,
  partnerIntegrationsAzureData: PropTypes.arrayOf(PropTypes.shape({})),

};

DetailCCGroupsNamespace.defaultProps = {
  accessPrivileges: {},
  appData: {},
  accessSubscriptions: [],
  authType: '',
  groupIndex: 0,
  isSearchActive: false,
  partnerIntegrationsAzureData: [],

};

export default DetailCCGroupsNamespace;

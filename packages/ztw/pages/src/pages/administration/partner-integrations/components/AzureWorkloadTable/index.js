/* eslint-disable react/jsx-handler-names */
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as constants from 'ducks/login/constants';
import ConfigTableWithPaginationAndSort from 'components/configTable/ConfigTableWithPagination';
import { REGIONS_SUBSCRIPTIONS_DRILLDOWN } from 'ducks/partnerIntegrationsAzure/constants';
import PropTypes from 'prop-types';
import * as partnerIntegrationsSelector from 'ducks/partnerIntegrationsAzure/selectors';

import {
  handleOpenDrawer,
  handlePageNumber,
  handlePageSize,
} from 'ducks/partnerIntegrationsAws';

import { isEmpty } from 'utils/lodash';

function AzureWorkloadTable(props) {
  const {
    accessPrivileges,
    pathname,
    workloadsData,
  } = props;
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => partnerIntegrationsSelector.baseSelector(state));
  const {
    drillData,
    pageNumber,
    pageSize,
    numberOfLines,
    sortField,
    sortDirection,
    loading,
  } = baseSelector || {};

  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_CLOUD_PROVISIONING;

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData
      .map((row) => {
        return {
          ...row,
          name: row.networkId.ip,
          link: pathname,
        };
      });
    return tableData;
  };
  
  const data = getTableData(workloadsData);

  return (
    <>
      <ConfigTableWithPaginationAndSort
        {...(REGIONS_SUBSCRIPTIONS_DRILLDOWN(t))}
        permission={accessPrivileges[permKey]}
        onHandleLink={(appData) => handleOpenDrawer(appData, drillData)}
        tableHeight={9999999999}
        maxTableHeight="9999999999px"
        sortField={sortField}
        sortDirection={sortDirection}
        // isApiPagination={false}
        numberOfLines={numberOfLines}
        pageChangeHandler={(e) => dispatch(handlePageNumber(e))}
        pageNumber={pageNumber}
        pageSize={pageSize}
        setPageSizeAPI={(e) => dispatch(handlePageSize(e))}
        loading={loading}
        data={data} />

    </>
  );
}

AzureWorkloadTable.propTypes = {
  accessPrivileges: PropTypes.shape({}),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  partnerIntegrationsAwsData: PropTypes.arrayOf(PropTypes.shape({})),
  workloadsData: PropTypes.arrayOf(PropTypes.shape({})),
  pathname: PropTypes.string,
};

AzureWorkloadTable.defaultProps = {
  accessPrivileges: {},
  accessSubscriptions: [],
  authType: '',
  partnerIntegrationsAwsData: [],
  workloadsData: [],
  pathname: '',
};

export default AzureWorkloadTable;

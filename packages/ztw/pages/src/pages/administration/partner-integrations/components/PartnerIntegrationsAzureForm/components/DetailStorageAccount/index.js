/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as constants from 'ducks/login/constants';
import ConfigTableWithPaginationAndSort from 'components/configTable/ConfigTableWithPaginationAndSort';
import { DETAIL_STORAGE_ACCOUNT } from 'ducks/partnerIntegrationsAzure/constants';
import { getReadOnly } from 'utils/helpers';
import PropTypes from 'prop-types';
import * as partnerIntegrationsSelector from 'ducks/partnerIntegrationsAzure/selectors';

import {
  handlePageSize,
  handlePageNumber,
  handleSearchText,
  refreshEventGridStatus,
} from 'ducks/partnerIntegrationsAzure';

import { isEmpty } from 'utils/lodash';

function DetailStorageAccount(props) {
  const {
    accessPrivileges,
    authType,
    appData,
  } = props;
  const { publicCloudStorageAccounts } = appData || {};

  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => partnerIntegrationsSelector.baseSelector(state));
  const {
    pageNumber,
    pageSize,
    numberOfLines,
    searchText,
    sortField,
    sortDirection,
  } = baseSelector || {};

  const isReadOnly = getReadOnly(
    accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    authType,
  );
  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT;
  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData.map((row) => {
      return {
        ...row,
        isRefreshable: !isReadOnly,
        isReadOnly,
        // to remove Icon from the line
        parentId: null,
        hideIcon: true,
      };
    });
    return tableData;
  };
  
  const filteredData = publicCloudStorageAccounts?.filter(
    (x) => t(x?.region?.name)?.toUpperCase().includes(searchText.toUpperCase())
    || (x?.subscription)?.toUpperCase().includes(searchText.toUpperCase())
    || (x?.resourceGroup)?.toUpperCase().includes(searchText.toUpperCase()),
  );
  const data = getTableData(filteredData);

  useEffect(() => {
    if (searchText !== '')dispatch(handleSearchText(''));
    return () => dispatch(handleSearchText(''));
  }, []);

  return (
    <ConfigTableWithPaginationAndSort
      {...(DETAIL_STORAGE_ACCOUNT(t))}
      // onHandleRowRefresh={async (e) => { dispatch(handleRefresEventGrid(e)); }}
      onHandleRowRefresh={async (e) => { dispatch(refreshEventGridStatus(e)); }}
      permission={accessPrivileges[permKey]}
      tableHeight={20000}
      maxTableHeight="20000px"
      sortField={sortField}
      sortDirection={sortDirection}
      numberOfLines={numberOfLines}
      pageChangeHandler={(e) => dispatch(handlePageNumber(e))}
      pageNumber={pageNumber}
      pageSize={pageSize}
      setPageSizeAPI={(e) => dispatch(handlePageSize(e))}
      loading={false}
      data={data} />
  );
}

DetailStorageAccount.propTypes = {
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  appData: PropTypes.shape({
    publicCloudTopics: PropTypes.arrayOf(),
  }),
  authType: PropTypes.string,
  groupIndex: PropTypes.number,
  partnerIntegrationsAzureData: PropTypes.arrayOf(PropTypes.shape({})),

};

DetailStorageAccount.defaultProps = {
  accessPrivileges: {},
  accessSubscriptions: [],
  authType: '',
  groupIndex: 0,
  partnerIntegrationsAzureData: [],

};

export default DetailStorageAccount;

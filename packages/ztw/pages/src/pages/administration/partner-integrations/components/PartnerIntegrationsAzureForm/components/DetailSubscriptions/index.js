/* eslint-disable react/jsx-handler-names */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as constants from 'ducks/login/constants';
import ConfigTableWithPaginationAndSort from 'components/configTable/ConfigTableWithPaginationAndSort';
import { DETAIL_SUBSCRIPTION } from 'ducks/partnerIntegrationsAzure/constants';
import { getReadOnly } from 'utils/helpers';
import PropTypes from 'prop-types';
import * as partnerIntegrationsSelector from 'ducks/partnerIntegrationsAzure/selectors';

import {
  handlePageSize,
  handlePageNumber,
  handleSearchText,
  refreshSunscriptionStatus,
} from 'ducks/partnerIntegrationsAzure';

import { isEmpty } from 'utils/lodash';

function DetailSubscriptions(props) {
  const {
    accessPrivileges,
    authType,
    appData,
    isSearchActive,
  } = props;
  const { subscriptionGroups = [] } = appData || {};
  const { subscriptionList } = subscriptionGroups[0] || {};
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => partnerIntegrationsSelector.baseSelector(state));
  const {
    pageNumber,
    pageSize,
    numberOfLines,
    searchText,
    sortField,
    sortDirection,
  } = baseSelector || {};

  const isReadOnly = getReadOnly(
    accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    authType,
  );
  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT;

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData
      .map((row) => {
        return {
          ...row,
          link: `./azure/details/${row.id}`,
          isRefreshable: !isReadOnly,
          isReadOnly,
          // to remove Icon from the line
          parentId: null,
          hideIcon: true,
        };
      });
    return tableData;
  };
  
  const filteredData = subscriptionList?.filter(
    (x) => t(x.name).toUpperCase().includes(searchText.toUpperCase()),
  );
  const data = isSearchActive
    ? getTableData(filteredData)
    : getTableData(subscriptionList);

  useEffect(() => {
    if (searchText !== '')dispatch(handleSearchText(''));
    return () => dispatch(handleSearchText(''));
  }, []);

  return (
    <ConfigTableWithPaginationAndSort
      {...(DETAIL_SUBSCRIPTION(t))}
      permission={accessPrivileges[permKey]}
      // onHandleLink={(value, name) => dispatch(
      //   handleLinkDetailRegion(appData, { id: value.id, name }, 1),
      // )}
      onHandleRowRefresh={async (e) => { dispatch(refreshSunscriptionStatus(e)); }}
      tableHeight={20000}
      maxTableHeight="20000px"
      sortField={sortField}
      sortDirection={sortDirection}
      numberOfLines={numberOfLines}
      pageChangeHandler={(e) => dispatch(handlePageNumber(e))}
      pageNumber={pageNumber}
      pageSize={pageSize}
      setPageSizeAPI={(e) => dispatch(handlePageSize(e))}
      loading={false}
      data={data} />
  );
}

DetailSubscriptions.propTypes = {
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  appData: PropTypes.shape({}),
  authType: PropTypes.string,
  groupIndex: PropTypes.number,
  isSearchActive: PropTypes.bool,
  partnerIntegrationsAzureData: PropTypes.arrayOf(PropTypes.shape({})),

};

DetailSubscriptions.defaultProps = {
  accessPrivileges: {},
  accessSubscriptions: [],
  appData: {},
  authType: '',
  groupIndex: 0,
  isSearchActive: false,
  partnerIntegrationsAzureData: [],
};

export default DetailSubscriptions;

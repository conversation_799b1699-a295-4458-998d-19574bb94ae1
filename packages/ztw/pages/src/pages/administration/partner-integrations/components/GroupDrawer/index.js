import React from 'react';
import PropTypes from 'prop-types';
import LineItem from 'components/LineItem';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import SimpleSearchInput from 'components/Input/SimpleSearchInput';
import {
  handleDrawerSearchText,
} from 'ducks/partnerIntegrationsAwsGroups';
import TableFiltersGroups from './component/TableFiltersGroups';
import {
  CloudConnectorGroupsAWS,
} from '../PartnerIntegrationsAwsGroupsForm/components/genericFilters';
import PartnerIntegrationsAwsSelectionReviewTable from '../PartnerIntegrationsAwsSelectionReviewTable';

function TagsFragment(props) {
  const { data } = props;
  const { t } = useTranslation();
  const dispatch = useDispatch();

  return (
    <div className="content account-groups-details-container">
      <LineItem label={t('DESCRIPTION')} value={data.description || '---'} />
      <CloudConnectorGroupsAWS
        {...props}
        props={{ isViewOnly: true }}
        label="NO_VALUE_SELECTED"
        isViewOnly />
      <div className="separator" />
      <div className="header">{`${t('ACCOUNT')}  (${data?.publicCloudAccounts?.length})`}</div>
      <div className="table-actions-container group-filter">
        <TableFiltersGroups />
        <div className="search-container">
          <SimpleSearchInput
            id="drawer-search"
            placeholder={t('SEARCH')}
            onKeyPressCb={(e, searchText) => dispatch(handleDrawerSearchText(e, searchText))} />
        </div>
      </div>
      <PartnerIntegrationsAwsSelectionReviewTable {...props} />
    </div>
  );
}
  
TagsFragment.propTypes = {
  data: PropTypes.shape({
    description: PropTypes.string,
    publicCloudAccounts: PropTypes.arrayOf(),
  }),
};
  
TagsFragment.defaultProps = {
  data: {},
};

export default TagsFragment;

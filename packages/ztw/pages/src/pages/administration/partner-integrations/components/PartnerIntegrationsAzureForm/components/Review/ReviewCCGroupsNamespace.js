/* eslint-disable react/jsx-handler-names */
import React from 'react';
import { get, isEmpty } from 'utils/lodash';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import * as constants from 'ducks/login/constants';
import ConfigTableWithPaginationAndSort from 'components/configTable/ConfigTableWithPaginationAndSort';
import { REVIEW_CC_GROUP_AND_NAMESPACE } from 'ducks/partnerIntegrationsAzure/constants';
import { getReadOnly } from 'utils/helpers';
import PropTypes from 'prop-types';
import * as partnerIntegrationsSelector from 'ducks/partnerIntegrationsAzure/selectors';

import {
  handlePageSize,
  handlePageNumber,
} from 'ducks/partnerIntegrationsAzure';

function ReviewCCGroupsNamespace(props) {
  const {
    accessPrivileges,
    authType,
    groupIndex = 0,
  } = props;
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const baseSelector = useSelector((state) => partnerIntegrationsSelector.baseSelector(state));
  const formValues = useSelector((state) => partnerIntegrationsSelector.formValuesSelector(state));
  const cloudConnectorGroupList = get(formValues, `subscriptionGroups[${groupIndex}].cloudConnectorGroupList`, null);
  const {
    pageNumber,
    pageSize,
    numberOfLines,
    searchText,
    sortField,
    sortDirection,
  } = baseSelector || {};

  const isReadOnly = true || getReadOnly(
    accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    authType,
  );
  const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT;

  const getTableData = (originalData) => {
    if (isEmpty(originalData)) return [];
    const tableData = originalData
      .filter((x) => x.checked)
      .map((row) => {
        return {
          ...row,
          link: `./aws/details/${row.id}`,
          isEditable: !isReadOnly,
          isEllipsisable: !isReadOnly,
          isRefreshable: !isReadOnly,
          isDeletable: false && !isReadOnly,
          isDisable: false && !isReadOnly,
          isReadOnly,
          // to remove Icon from the line
          parentId: null,
          hideIcon: true,
        };
      });
    return tableData;
  };
  
  const filteredData = cloudConnectorGroupList
    .filter((x) => t(x.name).toUpperCase().includes(searchText.toUpperCase())
    || (x.namespace).toUpperCase().includes(searchText.toUpperCase()));
  const data = getTableData(filteredData);

  return (
    <ConfigTableWithPaginationAndSort
      {...(REVIEW_CC_GROUP_AND_NAMESPACE(t))}
      permission={accessPrivileges[permKey]}
      tableHeight={20000}
      maxTableHeight="20000px"
      sortField={sortField}
      sortDirection={sortDirection}
      numberOfLines={numberOfLines}
      pageChangeHandler={(e) => dispatch(handlePageNumber(e))}
      pageNumber={pageNumber}
      pageSize={pageSize}
      setPageSizeAPI={(e) => dispatch(handlePageSize(e))}
      loading={false}
      data={data} />
  );
}

ReviewCCGroupsNamespace.propTypes = {
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  authType: PropTypes.string,
  groupIndex: PropTypes.number,
  partnerIntegrationsAzureData: PropTypes.arrayOf(PropTypes.shape({})),

};

ReviewCCGroupsNamespace.defaultProps = {
  accessPrivileges: {},
  accessSubscriptions: [],
  authType: '',
  groupIndex: 0,
  partnerIntegrationsAzureData: [],

};

export default ReviewCCGroupsNamespace;

/* eslint-disable react/jsx-handler-names */
// @flow
import React, { useEffect } from 'react';
import { bindActionCreators } from 'redux';
import { connect, useDispatch, useSelector } from 'react-redux';
import { faDownload } from '@fortawesome/pro-regular-svg-icons';
import { Field, reduxForm } from 'redux-form';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  hasCsku, getReadOnly, verifyConfigData,
} from 'utils/helpers';
import { HELP_ARTICLES } from 'config';
import { useLocation } from 'react-router-dom';
import { withTranslation } from 'react-i18next';
import * as loginSelectors from 'ducks/login/selectors';
import * as partnerIntegrationsAwsSelector from 'ducks/partnerIntegrationsAws/selectors';
import AddNewButton from 'components/addNewButton';
import HelpArticle from 'components/HelpArticle';
import FolderTabs from 'components/navTabs/FolderTabs';
import PageTabs from 'components/navTabs/PageTabs';
import PermissionRequired from 'components/PermissionRequired';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import PropTypes from 'prop-types';
import SearchBox from 'components/searchBox';
import ServerError from 'components/errors/ServerError';
import SubscriptionRequired from 'components/subscriptionRequired';
import WorkloadDiscoveryServiceRequired from 'components/workloadDiscoveryServiceRequired';
import { dropdownActions as awsSuportedRegions } from 'ducks/dropdowns/aws-supported-regions';

import {
  addNewPartner,
  handleDone,
  handlePagetype,
  handleToggleDisableForm,
  handleToggleEnableForm,
  handleToggleDeleteForm,
  handleRefreshForm,
  handleSearchText,
  handleOnSearchFilter,
  loader,
  tabConfiguration,
  folderConfiguration,
} from 'ducks/partnerIntegrationsAws';
import {
  ActionBar,
  EllipsisMenu,
  Modals,
  PartnerIntegrationsAwsTable,
  PartnerIntegrationsAwsForm,
  // TableFilters,
} from '../components';

let currentLocation;

function BasicCustomAppForm(props) {
  const {
    actions,
    authType,
    t,
    accessPrivileges,
    accessSubscriptions,
    showAddForm,
    showViewForm,
    showEditForm,
    bulkUpdate,
    configData,
  } = props;
  const location = useLocation();
  const dispatch = useDispatch();
  const formValues = useSelector((state) => partnerIntegrationsAwsSelector.formValuesSelector(state));
  const { folderTabs } = formValues || {};
  const { pathname } = location;
  currentLocation = pathname;
  useEffect(() => {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.ABOUT_PARTNER_INTEGRATIONS });
    actions.handleOnSearchFilter('');
    dispatch(handlePagetype(pathname));
    dispatch(awsSuportedRegions.load());
    return () => actions.handleDone();
  }, [pathname]);

  const hasCSubscription = hasCsku(accessSubscriptions);

  const enableWorkloadDiscoveryService = verifyConfigData({ configData, key: 'enableWorkloadDiscoveryService' });

  if (!hasCSubscription) {
    return (
      <>
        <HelpArticle article={HELP_ARTICLES.ABOUT_PARTNER_INTEGRATIONS} />
        <SubscriptionRequired accessSubscriptions={accessSubscriptions} />
      </>
    );
  }
 
  if (accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE'
    || accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT === 'NONE') {
    return (
      <>
        <HelpArticle article={HELP_ARTICLES.ABOUT_PARTNER_INTEGRATIONS} />
        <PermissionRequired accessPermissions={accessPrivileges} />
      </>
    );
  }
  const isReadOnly = getReadOnly(
    accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    authType,
  );

  return (
    <ServerError {...props}>
      {(!showAddForm && !showEditForm && !showViewForm) && (
        <div className="main-container partner-integrations partner-acc-accounts">
          <HelpArticle article={HELP_ARTICLES.ABOUT_PARTNER_INTEGRATIONS} />
          <div className="header">
            <span className="component-header-cc-group">
              {t('PARTNER_INTEGRATIONS')}
            </span>
          </div>
          <Field
            id="pageTabs"
            name="pageTabs"
            component={PageTabs} />
          <br />
          <br />
          {(!enableWorkloadDiscoveryService) ? (
            <>
              <HelpArticle article={HELP_ARTICLES.ABOUT_PARTNER_INTEGRATIONS} />
              <WorkloadDiscoveryServiceRequired />
            </>
          ) : (
            <>
              <Field
                id="folderTabs"
                name="folderTabs"
                folderTabs={folderTabs}
                component={FolderTabs} />
              <div className="table-actions-container">
                <div className="actions-row">
                  <div className="actions-items">
                    <div className={`sipg-fragment ${isReadOnly ? 'hide' : ''}`}>
                      {!isReadOnly && <AddNewButton label={t('ADD_AWS_ACCOUNT')} clickCallback={() => actions.addNewPartner()} />}
                    </div>
                    {bulkUpdate && (
                      <ActionBar
                        t={t}
                        handleRefreshForm={() => actions.handleRefreshForm('BULK_CHANGE', true)}
                        handleToggleDisableForm={() => actions.handleToggleDisableForm(true)}
                        handleToggleEnableForm={() => actions.handleToggleEnableForm(true)}
                        handleToggleDeleteForm={() => actions.handleToggleDeleteForm(true)} />
                    )}
                    <div className="cloud-formation-options-link">
                      <span className="cloud-formation-dot" />
                      <a href="https://zscaler-discovery-role.s3.amazonaws.com/zscaler_discovery_role.yaml" target="_blank" rel="noopener noreferrer" className="external-link-button">
                        <FontAwesomeIcon className="cloud-formation-options-link-icon fa-external-link fa-xs" icon={faDownload} size="xs" />
                        {t('DOWNLOAD_CLOUDFORMATION_TEMPLATE_FOR_LATER_EXECUTION')}
                      </a>
                    </div>
                  </div>

                  <div className="search-container">
                    <SearchBox
                      placeholder={t('SEARCH')}
                      clickCallback={(value) => {
                        actions.handleOnSearchFilter(value);
                        actions.loader(true, 1, value);
                      }}
                      onKeyPressCb={actions.handleOnSearchFilter} />
                  </div>
                </div>
            
                <div className="container-row-cc-group partner-integration-table-container">
                  <PartnerIntegrationsAwsTable {...props} />
                  <EllipsisMenu t={t} />
                  <Modals {...props} />
                </div>
              </div>
            </>
          )}
        </div>
      )}

      {(showAddForm || showEditForm || showViewForm) && <PartnerIntegrationsAwsForm {...props} />}
    </ServerError>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    addNewPartner: PropTypes.func,
    awsSuportedRegions: PropTypes.func,
    handleDone: PropTypes.func,
    handleOnSearchFilter: PropTypes.func,
    handlePagetype: PropTypes.func,
    handleRefreshForm: PropTypes.func,
    handleToggleDeleteForm: PropTypes.func,
    handleToggleDisableForm: PropTypes.func,
    handleToggleEnableForm: PropTypes.func,
    loader: PropTypes.func,
  }),
  authType: PropTypes.string,
  t: PropTypes.func,
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_CLOUD_PROVISIONING: PropTypes.string,
    EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  bulkUpdate: PropTypes.bool,
  showAddForm: PropTypes.bool,
  showViewForm: PropTypes.bool,
  showEditForm: PropTypes.bool,
  configData: PropTypes.shape(),
};

BasicCustomAppForm.defaultProps = {
  actions: {
    addNewPartner: null,
    awsSuportedRegions: null,
    handlePagetype: null,
    handleRefreshForm: null,
    handleOnSearchFilter: null,
    handleToggleDeleteForm: null,
    handleToggleDisableForm: null,
    handleToggleEnableForm: null,
    loader: null,
  },
  authType: '',
  t: (str) => str,
  accessPrivileges: {},
  accessSubscriptions: [],
  showAddForm: false,
  showViewForm: false,
  showEditForm: false,
  configData: {},
};

const BasicCustomForm = reduxForm({
  form: 'partnerIntegrationsAwsForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(BasicCustomAppForm);

const PartnerIntegrations = connect((state) => {
  const accessSubscriptions = loginSelectors.accessSubscriptionSelector(state);
  const hasCSubscription = hasCsku(accessSubscriptions);

  return ({
    initialValues: {
      pageTabs: currentLocation,
      folderTabs: 'ACCOUNTS',
      tabConfiguration: tabConfiguration(hasCSubscription),
      folderConfiguration: folderConfiguration(),
    },
  });
})(BasicCustomForm);

const mapStateToProps = (state) => ({
  ...partnerIntegrationsAwsSelector.baseSelector(state),
  bulkUpdate: partnerIntegrationsAwsSelector.bulkUpdateSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  configData: loginSelectors.configDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    addNewPartner,
    awsSuportedRegions,
    handleDone,
    handlePagetype,
    handleRefreshForm,
    handleSearchText,
    handleOnSearchFilter,
    handleToggleDeleteForm,
    handleToggleDisableForm,
    handleToggleEnableForm,
    loader,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(PartnerIntegrations));

/* eslint-disable react/jsx-handler-names */
// @flow
import React, { useEffect } from 'react';
import Drawer from 'components/Drawer';
import { bindActionCreators } from 'redux';
import { connect, useDispatch, useSelector } from 'react-redux';
import { Field, reduxForm } from 'redux-form';
import {
  hasCsku, getReadOnly, verifyConfigData,
} from 'utils/helpers';
import { BASE_LAYOUT, HELP_ARTICLES } from 'config';
import { useLocation } from 'react-router-dom';
import { withTranslation } from 'react-i18next';
import * as loginSelectors from 'ducks/login/selectors';
import * as partnerIntegrationsAwsSelector from 'ducks/partnerIntegrationsAws/selectors';
import * as partnerIntegrationsAwsGroupsSelector from 'ducks/partnerIntegrationsAwsGroups/selectors';
import AddNewButton from 'components/addNewButton';
import HelpArticle from 'components/HelpArticle';
import FolderTabs from 'components/navTabs/FolderTabs';
import PageTabs from 'components/navTabs/PageTabs';
import PermissionRequired from 'components/PermissionRequired';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import PropTypes from 'prop-types';
import SearchBox from 'components/searchBox';
import ServerError from 'components/errors/ServerError';
import SubscriptionRequired from 'components/subscriptionRequired';
import WorkloadDiscoveryServiceRequired from 'components/workloadDiscoveryServiceRequired';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCloud } from '@fortawesome/pro-regular-svg-icons';

import {
  loaderAllPages,
} from 'ducks/partnerIntegrationsAws';
import {
  folderConfiguration,
  handleCloseDrawer,
  handleOnSearchFilter,
  handleSearchText,
  handleToggleAddForm,
  handleToggleDeleteForm,
  handleToggleEditForm,
  loader,
  tabConfiguration,
} from 'ducks/partnerIntegrationsAwsGroups';
import {
  EllipsisMenu,
  GroupDrawer,
  GroupsModals,
  PartnerIntegrationsAwsGroupsTable,
  PartnerIntegrationsAwsGroupsForm,
  TableFiltersGroups,
} from '../components';

// eslint-disable-next-line no-unused-vars
let currentLocation;

function BasicCustomAppForm(props) {
  const {
    actions,
    authType,
    t,
    accessPrivileges,
    accessSubscriptions,
    showAddForm,
    showViewForm,
    showEditForm,
    configData,
  } = props;
  const location = useLocation();
  const dispatch = useDispatch();
  
  const partnerIntegrationsAwsSelectorFormValues = useSelector(
    (state) => partnerIntegrationsAwsSelector.formValuesSelector(state),
  );
  const { folderTabs } = partnerIntegrationsAwsSelectorFormValues || {};
  const { pathname } = location;
  useEffect(() => {
    dispatch(handleOnSearchFilter(''));
    dispatch(loaderAllPages(true, 1, ''));
    PersistentStorage.setItem({
      [LS_HELP_ARTICLE]: HELP_ARTICLES.ABOUT_AWS_GROUPS_PARTNER_INTEGRATIONS,
    });
  }, [pathname]);
  currentLocation = pathname;

  const openDrawer = useSelector(
    (state) => partnerIntegrationsAwsGroupsSelector.openDrawerSelector(state),
  );
  const drawerData = useSelector(
    (state) => partnerIntegrationsAwsGroupsSelector.drawerDataSelector(state),
  );
  const appData = useSelector(
    (state) => partnerIntegrationsAwsGroupsSelector.selectedRowDataSelector(state),
  );

  const hasCSubscription = hasCsku(accessSubscriptions);

  const enableWorkloadDiscoveryService = verifyConfigData({ configData, key: 'enableWorkloadDiscoveryService' });

  if (!hasCSubscription) {
    return (
      <>
        <HelpArticle article={HELP_ARTICLES.ABOUT_AWS_GROUPS_PARTNER_INTEGRATIONS} />
        <SubscriptionRequired />
      </>
    );
  }
 
  if (accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE'
  || accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT === 'NONE') {
    return (
      <>
        <HelpArticle article={HELP_ARTICLES.ABOUT_AWS_GROUPS_PARTNER_INTEGRATIONS} />
        <PermissionRequired />
      </>
    );
  }
  const isReadOnly = getReadOnly(
    accessPrivileges.EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT,
    authType,
  );

  useEffect(() => {
    dispatch(handleOnSearchFilter(''));
    dispatch(loaderAllPages(true, 1, ''));
    PersistentStorage.setItem({
      [LS_HELP_ARTICLE]: HELP_ARTICLES.ABOUT_AWS_GROUPS_PARTNER_INTEGRATIONS,
    });
  }, [pathname]);

  return (
    <ServerError {...props}>
      {(!showAddForm && !showEditForm && !showViewForm) && (
        <div className="main-container partner-integrations partner-acc-groups">
          <HelpArticle article={HELP_ARTICLES.ABOUT_AWS_GROUPS_PARTNER_INTEGRATIONS} />
          <div className="header">
            <span className="component-header-cc-group">
              {t('PARTNER_INTEGRATIONS')}
            </span>
          </div>
          <Field
            id="pageTabs"
            name="pageTabs"
            component={PageTabs} />
          <br />
          <br />
          {(!enableWorkloadDiscoveryService) ? (
            <>
              <HelpArticle article={HELP_ARTICLES.ABOUT_AWS_GROUPS_PARTNER_INTEGRATIONS} />
              <WorkloadDiscoveryServiceRequired />
            </>
          ) : (
            <>
              <Field
                id="folderTabs"
                name="folderTabs"
                folderTabs={folderTabs}
                component={FolderTabs} />

              <div className="table-actions-container group-filter">
                <TableFiltersGroups />
              </div>

              <div className="table-actions-container">
                <div className="actions-row">
                  <div className="actions-items">
                    <div className={`sipg-fragment ${isReadOnly ? 'hide' : ''}`}>
                      {!isReadOnly && <AddNewButton label={t('ADD_GROUP')} clickCallback={() => actions.handleToggleAddForm(null, true)} />}
                    </div>
                  </div>

                  <div className="search-container">
                    <SearchBox
                      placeholder={t('SEARCH')}
                      clickCallback={(value) => {
                        actions.handleOnSearchFilter(value);
                        actions.loader(true, 1, value);
                      }}
                      onKeyPressCb={actions.handleOnSearchFilter} />
                  </div>
                </div>
            
                <div className="container-row-cc-group partner-integration-table-container">
                  <PartnerIntegrationsAwsGroupsTable {...props} />
                  <EllipsisMenu t={t} />
                  <GroupsModals {...props} />
                </div>
              </div>
            </>
          )}
        </div>
      )}

      {(showAddForm || showEditForm || showViewForm)
      && <PartnerIntegrationsAwsGroupsForm {...props} />}

      <Drawer
        isOpen={openDrawer}
        isDeletable={drawerData?.isDeletable}
        isEditable={drawerData?.isEditable}
        handleEdit={() => { dispatch(handleToggleEditForm(appData, true)); }}
        handleDelete={() => dispatch(handleToggleDeleteForm(appData, true))}
        customHeader={(
          <div className="right-corner">
            <FontAwesomeIcon
              className="drawer-header-icon"
              role="presentation"
              icon={faCloud} />
            {drawerData?.name}
          </div>
        )}
        showBackdrop
        onClose={() => dispatch(handleCloseDrawer())}
        styleClass="acc-group-sliding-drawer">
        <div className="content-tabs-container">
          <GroupDrawer data={drawerData} />
        </div>
      </Drawer>
    </ServerError>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    awsSuportedRegions: PropTypes.func,
    handleOnSearchFilter: PropTypes.func,
    handleToggleDeleteForm: PropTypes.func,
    handleToggleDisableForm: PropTypes.func,
    handleToggleEnableForm: PropTypes.func,
    loader: PropTypes.func,
  }),
  authType: PropTypes.string,
  t: PropTypes.func,
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_CLOUD_PROVISIONING: PropTypes.string,
    EDGE_CONNECTOR_PUBLIC_CLOUD_CONFIG_MANAGEMENT: PropTypes.string,
  }),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  bulkUpdate: PropTypes.bool,
  showAddForm: PropTypes.bool,
  showViewForm: PropTypes.bool,
  showEditForm: PropTypes.bool,
  configData: PropTypes.shape(),
};

BasicCustomAppForm.defaultProps = {
  actions: {
    awsSuportedRegions: null,
    handleOnSearchFilter: null,
    handleToggleDeleteForm: null,
    handleToggleDisableForm: null,
    handleToggleEnableForm: null,
    loader: null,
  },
  authType: '',
  t: (str) => str,
  accessPrivileges: {},
  accessSubscriptions: [],
  showAddForm: false,
  showViewForm: false,
  showEditForm: false,
  configData: {},
};

const BasicCustomForm = reduxForm({
  form: 'partnerIntegrationsAwsGroupsForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(BasicCustomAppForm);

const PartnerIntegrations = connect((state) => {
  const accessSubscriptions = loginSelectors.accessSubscriptionSelector(state);
  const hasCSubscription = hasCsku(accessSubscriptions);

  return ({
    initialValues: {
      pageTabs: `${BASE_LAYOUT}/administration/partner-integrations/aws`,
      folderTabs: 'ACCOUNTS',
      tabConfiguration: tabConfiguration(hasCSubscription),
      folderConfiguration: folderConfiguration(),
    },
  });
})(BasicCustomForm);

const mapStateToProps = (state) => ({
  ...partnerIntegrationsAwsGroupsSelector.baseSelector(state),
  bulkUpdate: partnerIntegrationsAwsGroupsSelector.bulkUpdateSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  configData: loginSelectors.configDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    handleOnSearchFilter,
    handleSearchText,
    handleToggleAddForm,
    handleToggleDeleteForm,
    loader,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(PartnerIntegrations));

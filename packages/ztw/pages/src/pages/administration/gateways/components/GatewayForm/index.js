// @flow
import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm, Field, autofill } from 'redux-form';
import Input from 'components/Input';
import Loading from 'components/spinner/Loading';
import { ToggleCheckBox } from 'components/ecToggle';
import { FormSectionLabel, FormFieldLabel } from 'components/label';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import { required, maxLength } from 'utils/validations';

import {
  saveForm,
  toggleForm,
  handleFailCloseChange,
  handlePrimaryProxyChange,
  handleSecondaryProxyChange,
  loadDCData,
} from 'ducks/gateways';
import * as GatewaySelectors from 'ducks/gateways/selectors';
import { GatewayFragment } from './components/GatewayFragment';

const maxLength50 = maxLength(255);
const maxLength10240 = maxLength(10240);

const hanldeChangeEvent = (fn) => {
  return (e) => {
    fn(e.currentTarget.checked);
  };
};
 
export function BasicCustomAppForm({
  valid,
  actions,
  t,
  handleSubmit,
  initialValues,
  modalLoading,
  isFailCloseEnabled,
  selectedPrimaryType,
  selectedSecondaryType,
  dcData,
  dcApiCalled,
  formMeta,
  formSyncErrors,
}) {
  const {
    cancelHandle,
    addGateway,
    handlerForFailClose,
    primaryProxyChange,
    secondaryProxyChange,
    loadDropdownData,
    autoFillReduxForm,
  } = actions;

  let {
    failClosed,
  } = initialValues;
  
  if (isFailCloseEnabled !== null) {
    failClosed = isFailCloseEnabled;
  }

  const nameHasError = formSyncErrors.name && formMeta.name && formMeta.name.touched;
  const descriptionHasError = formSyncErrors.description
    && formMeta.description && formMeta.description.touched;

  return (
    <form onSubmit={handleSubmit(addGateway)} className="add-custom-app-form add-edit-gateways">
      <Loading loading={modalLoading} />
      <div className="form-sections-container">
        <FormSectionLabel text={t('GATEWAY')} />
        <div className="form-section">
          <div className="full-width">
            <div className="checkbox-container half-width">
              <FormFieldLabel
                tooltip={t('TOOLTIP_GATEWAY_NAME')}
                styleClass={`${nameHasError ? 'invalid' : ''}`}
                error={nameHasError ? t(formSyncErrors.name) : ''}
                text={t('NAME')} />
              <Field
                name="name"
                id="name"
                component={Input}
                type="text"
                customErrorText="NAME_MAX_LIMIT_ERROR"
                placeholder={t('TYPE_GATEWAY_NAME')}
                validate={[
                  required,
                  maxLength50,
                ]} />
            </div>
              
            <div className="checkbox-container half-width">
              <FormFieldLabel
                tooltip={t('TOOLTIP_GATEWAY_FAIL_CLOSE')}
                text={t('FAIL_CLOSE')} />
              <Field
                id="failClosed"
                name="failClosed"
                component={ToggleCheckBox}
                onChange={hanldeChangeEvent(handlerForFailClose)}
                checked={failClosed}
                styleClass="ec-toggle-checkbox" />
            </div>
          </div>
          
          <GatewayFragment
            t={t}
            autoFillReduxForm={autoFillReduxForm}
            isFailCloseEnabled={isFailCloseEnabled}
            selectedPrimaryType={selectedPrimaryType}
            selectedSecondaryType={selectedSecondaryType}
            initialValues={initialValues}
            handleSecondaryProxyChange={secondaryProxyChange}
            loadDCData={loadDropdownData}
            dcApiCalled={dcApiCalled}
            dropdownData={dcData}
            handlePrimaryProxyChange={primaryProxyChange} />
        </div>

        <FormSectionLabel
          tooltip={t('TOOLTIP_DESCRIPTION')}
          styleClass={`${descriptionHasError ? 'invalid' : ''}`}
          error={descriptionHasError ? t(formSyncErrors.description) : ''}
          text={t('DESCRIPTION')} />
        <div className="form-section textarea-wrapper">
          <Field
            name="description"
            id="description"
            customErrorText="DESCRIPTION_MAX_LIMIT_ERROR"
            component={Input}
            type="textarea"
            className="form-textarea"
            validate={[
              maxLength10240,
            ]} />
        </div>
      </div>
      <div className="dialog-footer">
        <div className="dialog-footer-left">
          <button type="submit" className={`submit ${!valid ? 'disabled' : ''}`}>{t('SAVE')}</button>
          <button type="button" className="cancel" onClick={() => cancelHandle(null, false)}>{t('CANCEL')}</button>
        </div>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
    addGateway: PropTypes.func,
  }),
  valid: PropTypes.bool,
  t: PropTypes.func,
  handleSubmit: PropTypes.func,
  selectedPrimaryType: PropTypes.string,
  selectedSecondaryType: PropTypes.string,
  initialValues: PropTypes.shape(),
  modalLoading: PropTypes.bool,
  dcData: PropTypes.shape(),
  dcApiCalled: PropTypes.bool,
  formMeta: PropTypes.shape(),
  formSyncErrors: PropTypes.shape(),
  isFailCloseEnabled: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
    saveLocationTemplate: noop,
  },
  valid: true,
  t: (str) => str,
  handleSubmit: noop,
  initialValues: {},
  modalLoading: false,
  dcData: [],
  dcApiCalled: false,
  formMeta: {},
  formSyncErrors: {},
};

const GatewayForm = reduxForm({
  form: 'addEditGatewayForm',
})(BasicCustomAppForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleForm,
    addGateway: saveForm,
    primaryProxyChange: handlePrimaryProxyChange,
    secondaryProxyChange: handleSecondaryProxyChange,
    handlerForFailClose: handleFailCloseChange,
    loadDropdownData: loadDCData,
    autoFillReduxForm: autofill,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  initialValues: GatewaySelectors.appDataSelector(state),
  formSyncErrors: GatewaySelectors.formSyncErrorsSelector(state),
  formMeta: GatewaySelectors.formMetaSelector(state),
  modalLoading: GatewaySelectors.modalLoadingSelector(state),
  selectedPrimaryType: GatewaySelectors.primaryTypeSelector(state),
  selectedSecondaryType: GatewaySelectors.secondaryTypeSelector(state),
  isFailCloseEnabled: GatewaySelectors.failCloseSelector(state),
  dcData: GatewaySelectors.dcDataSelector(state),
  dcApiCalled: GatewaySelectors.dcApiCalledSelector(state),
});

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(GatewayForm));

import React, { Component } from 'react';
// import PropTypes from 'prop-types';

export class Gateways extends Component {
  // static propTypes = {
  //   accessPermissions: PropTypes.shape({}),
  //   accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  //   authType: PropTypes.string,
  //   configData: PropTypes.shape(),
  //   formTitle: PropTypes.string,
  //   gatewaystabledata: PropTypes.arrayOf(PropTypes.shape()),
  //   handleDelete: PropTypes.func,
  //   handleSearch: PropTypes.func,
  //   handleViewGatewaysForm: PropTypes.func,
  //   load: PropTypes.func,
  //   modalLoading: PropTypes.bool,
  //   searchData: PropTypes.string,
  //   selectedRowID: PropTypes.string,
  //   showDeleteForm: PropTypes.bool,
  //   showForm: PropTypes.bool,
  //   showViewForm: PropTypes.bool,
  //   t: PropTypes.func,
  //   toggleDeleteConfirmationForm: PropTypes.func,
  //   toggleGatewayForm: PropTypes.func,
  // };

  // static defaultProps = {
  //   accessPermissions: {},
  //   accessSubscriptions: [],
  //   authType: '',
  //   configData: {},
  //   formTitle: '',
  //   gatewaystabledata: null,
  //   handleDelete: noop,
  //   handleViewGatewaysForm: noop,
  //   load: noop,
  //   modalLoading: false,
  //   searchData: '',
  //   selectedRowID: null,
  //   showDeleteForm: false,
  //   showForm: false,
  //   showViewForm: false,
  //   t: (str) => str,
  //   toggleDeleteConfirmationForm: noop,
  //   toggleGatewayForm: noop,
  // };

  // componentDidMount() {
  //   const { load } = this.props;
  //   load();
  // }


  render() {
    return (
      <div className="main-container main-container-gateways">
        <div className="page-title header-3">
          {('INTERNET_ACCESS_GATEWAY')}
          {/* <NavTabs
            isPageHeader
            tabConfiguration={[{
              id: 'zia-gateways',
              title: t('ZIA_GATEWAY'),
              to: `${BASE_LAYOUT}/administration/gateways`,
            },
            {
              id: 'log-and-control-gateways',
              title: t('LOG_AND_CONTROL_GATEWAY'),
              to: `${BASE_LAYOUT}/administration/log-and-control-gateways`,
            },
            ...(disableDnsGateway ? [] : [{
              id: 'log-and-control-gateways',
              title: t('DNS_GATEWAY'),
              to: `${BASE_LAYOUT}/administration/dns-gateways`,
            }])]} /> */}
        </div>
        
      </div>
    );
  }
}

// const mapStateToProps = (state) => ({
//   ...GatewaySelectors.baseSelector(state),
//   modalLoading: GatewaySelectors.modalLoadingSelector(state),
//   selectedRowID: GatewaySelectors.selectedRowIDSelector(state),
//   accessPermissions: loginSelectors.accessPermissionsSelector(state),
//   authType: loginSelectors.authTypeSelector(state),
//   accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
//   configData: loginSelectors.configDataSelector(state),
// });

// const mapDispatchToProps = (dispatch) => {
//   const actions = bindActionCreators({
//     load: loadGatewaysData,
//     toggleGatewayForm: toggleForm,
//     toggleDeleteConfirmationForm: toggleDeleteForm,
//     handleDelete: deleteGateways,
//     handleViewGatewaysForm: toggleViewModal,
//     handleSearch: handleOnSearchFilter,
//   }, dispatch);
//   return actions;
// };

export default (Gateways);
// export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(Gateways));

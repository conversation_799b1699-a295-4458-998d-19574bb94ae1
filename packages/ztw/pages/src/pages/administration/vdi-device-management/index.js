/* eslint-disable react/jsx-handler-names */
// @flow
import React, { useEffect } from 'react';
import { bindActionCreators } from 'redux';
import { connect, useDispatch } from 'react-redux';
import { Field, reduxForm } from 'redux-form';
import { hasBsku, hasCsku, getReadOnly } from 'utils/helpers';
import { HELP_ARTICLES, BASE_LAYOUT } from 'config';
import { useLocation } from 'react-router-dom';
import { withTranslation } from 'react-i18next';
import * as constants from 'ducks/login/constants';
import * as loginSelectors from 'ducks/login/selectors';
import * as vdiDeviceManagementSelector from 'ducks/vdiDeviceManagement/selectors';
import AddNewButton from 'components/addNewButton';
import HelpArticle from 'components/HelpArticle';
import PageTabs from 'components/navTabs/PageTabs';
import PermissionRequired from 'components/PermissionRequired';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import PropTypes from 'prop-types';
import SearchBox from 'components/searchBox';
import ServerError from 'components/errors/ServerError';
import SubscriptionRequired from 'components/subscriptionRequired';

import {
  handleLoadFilters,
  handleSearchText,
  handleToggleAddForm,
  handleToggleDeleteForm,
  tabConfiguration,
} from 'ducks/vdiDeviceManagement';
import {
  Modals,
  VdiDeviceManagementGroupTable,
  VdiDeviceManagementTable,
  VdiDeviceManagementForm,
} from './components';

function BasicCustomAppForm(props) {
  const {
    // actions,
    authType,
    t,
    accessPrivileges,
    accessSubscriptions,
    showAddForm,
    showViewForm,
    showEditForm,
  } = props;
  const location = useLocation();
  const dispatch = useDispatch();
  const { pathname } = location;
  let isGroupPage = false;

  useEffect(() => {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.BRANCH_AND_CLOUD_GROUP });
    dispatch(handleLoadFilters(t));
  }, [pathname]);

  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);

  if (!hasBSubscription && !hasCSubscription) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }

  if (accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
    return <PermissionRequired accessPermissions={accessPrivileges} />;
  }
  const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING, authType);
  const permKey = [constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_CLOUD_PROVISIONING];

  isGroupPage = (pathname === `${BASE_LAYOUT}/administration/vdi/device-management/groups`);
    
  return (
    <ServerError {...props}>
      {/* <Loading {...props} /> */}
      {(!showAddForm && !showEditForm && !showViewForm) && (
        <div className="main-container vdi-device-managenent">
          <HelpArticle article={HELP_ARTICLES.BRANCH_CONNECTOR_GROUP} />
          <div className="page-title header-3">
            {t('VDI_DEVICE_MANAGEMENT')}
          </div>
          <Field
            id="pageTabs"
            name="pageTabs"
            component={PageTabs} />
          <div className="table-actions-container">
            <div className="actions-row">
              <div className="actions-items">
                <div className={`${accessPrivileges[permKey] === 'READ_ONLY' ? 'hide' : ''}`}>
                  {isGroupPage && !isReadOnly
                    && (
                      <AddNewButton
                        label={t('ADD_DYNAMIC_VDI_GROUP')}
                        disabled={!isGroupPage}
                        clickCallback={() => dispatch(handleToggleAddForm(true))} />
                    )}
                </div>
              </div>

              <div className="search-container">
                <SearchBox
                  key={pathname}
                  placeholder={t('SEARCH')}
                  clickCallback={(value) => dispatch(handleSearchText(value))} />
              </div>
            </div>

            <div className="container-row-cc-group vdi-table-container">
              {isGroupPage
                ? <VdiDeviceManagementGroupTable {...props} />
                : <VdiDeviceManagementTable {...props} />}
              <Modals {...props} />
            </div>
          </div>
        </div>
      )}
      {(showAddForm || showEditForm || showViewForm) && <VdiDeviceManagementForm {...props} />}
    </ServerError>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    load: PropTypes.func,
    handleToggleForm: PropTypes.func,
    handleToggleDeleteForm: PropTypes.func,
    deleteBCGroupRowData: PropTypes.func,
  }),
  authType: PropTypes.string,
  t: PropTypes.func,
  accessPrivileges: PropTypes.shape({}),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  showAddForm: PropTypes.bool,
  showViewForm: PropTypes.bool,
  showEditForm: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  actions: {
    load: null,
    handleToggleForm: null,
    handleToggleDeleteForm: null,
    deleteBCGroupRowData: null,
  },
  authType: '',
  t: (str) => str,
  accessPrivileges: {},
  accessSubscriptions: [],
  showAddForm: false,
  showViewForm: false,
  showEditForm: false,
};

const BasicCustomForm = reduxForm({
  form: 'vdiDeviceManagementMainForm',
  destroyOnUnmount: true,
  forceUnregisterOnUnmount: true,
})(BasicCustomAppForm);

const PartnerIntegrations = connect((state) => {
  const accessSubscriptions = loginSelectors.accessSubscriptionSelector(state);
  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);
  const isDevices = window.location.pathname.includes('devices');

  return ({
    initialValues: {
      pageTabs: isDevices
        ? `${BASE_LAYOUT}/administration/vdi/device-management/devices`
        : `${BASE_LAYOUT}/administration/vdi/device-management/groups`,
      tabConfiguration: tabConfiguration(hasBSubscription, hasCSubscription),
    },
  });
})(BasicCustomForm);

const mapStateToProps = (state) => ({
  ...vdiDeviceManagementSelector.baseSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    handleToggleDeleteForm,
    handleSearchText,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(PartnerIntegrations));

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';

import { noop, isUndefined } from 'utils/lodash';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import RBAC from 'components/rbac';
import ServerError from 'components/errors/ServerError';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasBsku, hasCsku } from 'utils/helpers';
import * as constants from 'ducks/login/constants';
import * as AuditLogsSelectors from 'ducks/auditLogs/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';

import { AUDIT_LOGS_TABLE_CONFIGS } from 'ducks/auditLogs/constants';
import ConfigTable from 'components/configTable';

import {
  getAuditLogsData,
  loadAuditLogsTableData,
  toggleViewModal,
} from 'ducks/auditLogs';

import {
  AuditLogsForm,
  AuditLogsViewDiffModal,
} from './components';

export class AuditLogs extends Component {
  static propTypes = {
    t: PropTypes.func,
    actions: PropTypes.shape(),
    accessPrivileges: PropTypes.shape({}),
    auditLogsColumns: PropTypes.arrayOf(PropTypes.shape()),
    auditLogstabledata: PropTypes.arrayOf(PropTypes.shape()),
    showViewForm: PropTypes.bool,
    formTitle: PropTypes.string,
    accessPermissions: PropTypes.shape({}),
    authType: PropTypes.string,
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  };

  static defaultProps = {
    t: (str) => str,
    actions: noop,
    accessPrivileges: {},
    auditLogsColumns: [],
    showViewForm: false,
    formTitle: '',
    accessPermissions: {},
    authType: '',
    accessSubscriptions: [],
  };

  componentDidMount() {
    // PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.LOCATION_TEMPLATES });
    const { actions } = this.props;
    const { loadAuditLogsData, formData } = actions;
    loadAuditLogsData(formData);
  }

  getTableData = () => {
    const { auditLogstabledata } = this.props;

    const tableData = auditLogstabledata.map((row) => {
      return {
        id: row.index,
        timestamp: row.timestamp,
        actionType: row.actionType,
        category: row.category,
        subCategory: row.subCategory,
        objectName: row.objectName,
        userName: row.userName,
        clientIP: row.clientIP,
        resourceName: row.resourceName,
        actionInterface: row.actionInterface,
        actionResult: row.actionResult,
        dataAfter: row.dataAfter,
        dataBefore: row.dataBefore,
        isReadOnly: false,
        isDeletable: false,
        isEditable: false,
        isDownloadable: false,
        isViewDiff: (!isUndefined(row.dataAfter) || !isUndefined(row.dataBefore)),
      };
    });
    return tableData;
  };

  render() {
    const {
      t,
      actions,
      showViewForm,
      formTitle,
      accessPermissions,
      accessSubscriptions,
    } = this.props;

    const { handleAuditLogsViewModal } = actions;

    // update once we have cc sku for audit logs
    const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_ADMIN_MANAGEMENT;

    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPermissions.EDGE_CONNECTOR_ADMIN_MANAGEMENT === 'NONE') {
      return <PermissionRequired accessPermissions={accessPermissions} />;
    }

    return (
      <div className="main-container audit-log-container">
        <div className="audit-logs">
          <div className="page-title">
            {t('AUDIT_LOGS')}
          </div>
          <AuditLogsForm {...this.props} />
        </div>
        <div className="cloud-provider-wrapper audit-logs">
          <RBAC privilege={permKey}>
            <Loading {...this.props}>
              <HelpArticle article={HELP_ARTICLES.AUDIT_LOGS} />
              <ServerError {...this.props}>
                <ConfigTable
                  {...AUDIT_LOGS_TABLE_CONFIGS}
                  onHandleRowViewDiff={handleAuditLogsViewModal}
                  data={this.getTableData()} />
                <Modal
                  title={formTitle}
                  isOpen={showViewForm}
                  styleClass="audit-logs-modal"
                  closeModal={() => handleAuditLogsViewModal(null, false)}>
                  <AuditLogsViewDiffModal />
                </Modal>
              </ServerError>
            </Loading>
          </RBAC>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...AuditLogsSelectors.baseSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    loadTableData: getAuditLogsData,
    loadAuditLogsData: loadAuditLogsTableData,
    handleAuditLogsViewModal: toggleViewModal,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(AuditLogs));

// @flow
import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  reduxForm,
  autofill,
  getFormValues,
  Field,
} from 'redux-form';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop, isEqual } from 'utils/lodash';
import {
  loadAuditLogsTableData,
  downloadCSV,
} from 'ducks/auditLogs';
import SearchBox from 'components/searchBox';
import Download from 'components/button/Download';
import * as AuditLogsSelectors from 'ducks/auditLogs/selectors';
import { Filters } from './components';
import {
  SearchDropdown,
} from './components/Filters/Dropdown';

class BasicCustomAppForm extends PureComponent {
  static propTypes = {
    t: PropTypes.func,
    actions: PropTypes.shape({
      loadAuditLogsTableData: PropTypes.func,
    }),
    downloadCsvData: PropTypes.arrayOf(PropTypes.string),
    formValues: PropTypes.shape({ auditLogsSearch: '' }),
    auditLogsSearch: PropTypes.string,
  };

  static defaultProps = {
    t: (str) => str,
    actions: {
      loadAuditLogsTableData: noop,
    },
    downloadCsvData: [],
    formValues: {},
  };

  componentDidUpdate(prevProps) {
    const {
      actions,
      formValues,
    } = this.props;
    const {
      updateTableData,
    } = actions;

    if (!isEqual(prevProps.formValues, formValues) && prevProps.formValues && formValues) {
      if (isEqual(prevProps.formValues.auditLogsSearch, formValues.auditLogsSearch)) {
        updateTableData(formValues, prevProps.formValues);
      }
    }
  }

  handleSearchClick = (data) => {
    const {
      actions: { updateTableData },
      formValues,
    } = this.props;
    updateTableData(formValues, data);
  };

  render() {
    const {
      t,
      actions,
      formValues,
      downloadCsvData,
    } = this.props;
    const {
      updateTableData,
      autoFillReduxForm,
      downloadCSVDataFn,
    } = actions;

    return (
      <form className="add-edit-sip" onChange={this.handleOnChange}>
        <div className="ec-auditlogs-filters">
          <Filters
            autoFillReduxForm={autoFillReduxForm}
            formValues={formValues}
            downloadCsvData={downloadCsvData}
            downloadCSVDataFn={downloadCSVDataFn}
            updateTableData={updateTableData} />
        </div>
        <div className="ec-auditlogs-search">
          <div className="search-dropdown">
            <Field
              id="auditLogsSearch"
              name="auditLogsSearch"
              component={SearchDropdown}
              parse={(value) => value.id} />
            <div className="audit-log-search-container">
              <SearchBox placeholder={t('SEARCH')} clickCallback={this.handleSearchClick} />
            </div>
            <div className="download">
              <Download onActionCb={() => downloadCSVDataFn(formValues)} />
            </div>
          </div>
        </div>
      </form>
    );
  }
}

const AuditLogsForm = reduxForm({
  form: 'AuditLogsFiltersForm',
})(BasicCustomAppForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateTableData: loadAuditLogsTableData,
    autoFillReduxForm: autofill,
    downloadCSVDataFn: downloadCSV,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  ...AuditLogsSelectors.baseSelector(state),
  formValues: getFormValues('AuditLogsFiltersForm')(state),
});

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(AuditLogsForm));

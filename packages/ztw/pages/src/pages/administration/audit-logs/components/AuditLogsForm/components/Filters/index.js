/* eslint-disable react/jsx-handler-names */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import {
  Field,
} from 'redux-form';
import { noop } from 'utils/lodash';
import { withTranslation } from 'react-i18next';

import { FormFieldLabel } from 'components/label';

import {
  TimeFrame,
  ActionTypes,
  CategoriesDropdown,
  InterfaceDropdown,
  ResultDropdown,
  SubCategories,
  SearchDropdown,
} from './Dropdown';

class AuditLogsFilters extends PureComponent {
  static propTypes = {
    t: PropTypes.func,
    updateTableData: PropTypes.func,
    downloadCSVDataFn: PropTypes.func,
    autoFillReduxForm: PropTypes.func,
    formValues: PropTypes.shape(),
    downloadCsvData: PropTypes.arrayOf(PropTypes.string),
  };

  static defaultProps = {
    t: (str) => str,
    updateTableData: noop,
    downloadCSVDataFn: noop,
    autoFillReduxForm: noop,
    formValues: {},
    downloadCsvData: [],
  };

  componentDidMount() {
    const {
      autoFillReduxForm,
    } = this.props;

    autoFillReduxForm('AuditLogsFiltersForm', 'timeFrame', 'current_day');
    autoFillReduxForm('AuditLogsFiltersForm', 'auditLogsSearch', 'objectName');
  }

  // handleSearchClick = (data) => {
  //   const {
  //     updateTableData,
  //     formValues,
  //   } = this.props;
  //   updateTableData(formValues, data);
  // };

  render() {
    const {
      t,
      downloadCSVDataFn,
      formValues,
    } = this.props;

    return (
      <>
        <div className="audit-filters-container">
          <div>
            <span>{t('TIME_FRAME')}</span>
            <Field
              id="timeFrame"
              name="timeFrame"
              component={TimeFrame}
              parse={(value) => value.id} />
          </div>
          <div>
            <ActionTypes {...this.props} />
          </div>
          <div>
            <FormFieldLabel
              text={t('CATEGORY')} />
            <Field
              id="category"
              name="category"
              component={CategoriesDropdown}
              onChange={noop} />
          </div>
          <div>
            <SubCategories {...this.props} />
          </div>
          <div>
            <FormFieldLabel
              text={t('ACTION_INTERFACE')} />
            <Field
              id="actionInterface"
              name="actionInterface"
              component={InterfaceDropdown}
              onChange={noop} />
          </div>
          <div>
            <FormFieldLabel
              text={t('ACTION_RESULT')} />
            <Field
              id="actionResult"
              name="actionResult"
              component={ResultDropdown}
              onChange={noop} />
          </div>
            
        </div>
      </>
    );
  }
}

export default (withTranslation()(AuditLogsFilters));

// @flow

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import withRouter from 'layout/withRouter';
import { reduxForm } from 'redux-form';
import { noop } from 'utils/lodash';
import AceDiff from 'ace-diff';
import ace from 'brace';

import { toggleViewModal } from 'ducks/auditLogs';

import * as AuditLogsSelectors from 'ducks/auditLogs/selectors';

export class BasicCustomAppForm extends PureComponent {
  static propTypes = {
    actions: PropTypes.shape({
      cancelHandle: PropTypes.func,
    }),
    t: PropTypes.func,
    appData: PropTypes.shape(),
  };

  static defaultProps = {
    actions: {
      cancelHandle: noop,
    },
    t: (str) => str,
    appData: {},
  };

  componentDidMount() {
    const {
      appData,
    } = this.props;
    let {
      dataBefore = {},
      dataAfter = {},
    } = appData;
    dataBefore = JSON.stringify(dataBefore, null, '\t');
    dataAfter = JSON.stringify(dataAfter, null, '\t');
    // ace.Range = ace.acequire('ace/range').Range;
    // eslint-disable-next-line no-new
    new AceDiff({
      ace,
      element: '.acediff',
      showDiffs: true,
      showConnectors: true,
      left: {
        content: dataBefore,
        editable: false,
        copyLinkEnabled: false,
      },
      right: {
        content: dataAfter,
        editable: false,
        copyLinkEnabled: false,
      },
    });
  }

  handleCancelClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    const {
      actions,
    } = this.props;

    const { cancelHandle } = actions;
    
    cancelHandle(false);
  };

  render() {
    const {
      t,
    } = this.props;
    return (
      <form className="wizard-form configure-provisioning-template">
        <div className="acediff"></div>
        <div className="modal-footer acediff-modal-footer">
          <button type="submit" onClick={this.handleCancelClick} className="next primary-button">{t('CANCEL')}</button>
        </div>
      </form>
    );
  }
}

const AuditLogsViewDiffModal = reduxForm({
  form: 'AuditLogsViewDiffForm',
})(BasicCustomAppForm);

const mapStateToProps = (state) => ({
  ...AuditLogsSelectors.default(state),
  appData: AuditLogsSelectors.appDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleViewModal,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(AuditLogsViewDiffModal)));

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import NavTabs from 'components/navTabs';
import AddNewButton from 'components/addNewButton';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import RBAC from 'components/rbac';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasBsku, hasCsku, getReadOnly } from 'utils/helpers';
import ServerError from 'components/errors/ServerError';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import * as constants from 'ducks/login/constants';
import * as DestinationIPSelectors from 'ducks/destinationIpGroups/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import { HELP_ARTICLES, BASE_LAYOUT } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import { DESTINATION_TABLE_CONFIGS } from 'ducks/destinationIpGroups/constants';
import ConfigTable from 'components/configTable';

import {
  toggleForm,
  loadDestinationIPData,
  toggleDeleteForm,
  deleteDestinationIP,
  toggleViewModal,
} from 'ducks/destinationIpGroups';
import {
  DestinationIPForm,
  DestinationIPViewModal,
} from './components';

export class DestinationIPGroups extends Component {
  static propTypes = {
    t: PropTypes.func,
    destinationIPTableData: PropTypes.arrayOf(PropTypes.shape()),
    toggleDestinationIPForm: PropTypes.func,
    showForm: PropTypes.bool,
    load: PropTypes.func,
    formTitle: PropTypes.string,
    showDeleteForm: PropTypes.bool,
    modalLoading: PropTypes.bool,
    selectedRowID: PropTypes.string,
    authType: PropTypes.string,
    handleDelete: PropTypes.func,
    toggleDeleteConfirmationForm: PropTypes.func,
    handleDestinationIPView: PropTypes.func,
    accessPrivileges: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    ipAddressesList: PropTypes.arrayOf(PropTypes.string),
    formMode: PropTypes.string,
    showViewForm: PropTypes.bool,
  };

  static defaultProps = {
    t: (str) => str,
    destinationIPTableData: null,
    toggleDestinationIPForm: noop,
    showForm: false,
    load: noop,
    formTitle: '',
    showDeleteForm: false,
    modalLoading: false,
    selectedRowID: null,
    handleDelete: noop,
    toggleDeleteConfirmationForm: noop,
    handleDestinationIPView: noop,
    accessPrivileges: {},
    authType: '',
    accessSubscriptions: [],
    ipAddressesList: [],
    formMode: 'NEW',
    showViewForm: false,
  };

  componentDidMount() {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.DEST_GROUP });
    const { load } = this.props;
    load();
  }

  getTableData = () => {
    const { destinationIPTableData, accessPrivileges, authType } = this.props;
    const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_FORWARDING;
    const readOnly = getReadOnly(accessPrivileges[permKey], authType);
    const tableData = destinationIPTableData.map((row) => {
      return {
        id: row.id,
        description: row.description,
        addresses: row.addresses,
        name: row.name,
        type: row.type,
        countries: row.countries,
        // ipCategories: row.ipCategories,
        isDeletable: row.name !== 'ZPA Brokers' && !readOnly && row.creatorContext === 'EC',
        isReadOnly: row.name === 'ZPA Brokers' || readOnly || row.creatorContext !== 'EC',
        isEditable: !readOnly && row.creatorContext === 'EC',
      };
    });
    return tableData;
  };

  render() {
    const {
      t,
      toggleDestinationIPForm,
      showForm,
      formTitle,
      showDeleteForm,
      modalLoading,
      selectedRowID,
      handleDelete,
      toggleDeleteConfirmationForm,
      accessPrivileges,
      accessSubscriptions,
      ipAddressesList,
      formMode,
      showViewForm,
      handleDestinationIPView,
      authType,
    } = this.props;

    const permKey = constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_FORWARDING;
    const isReadOnly = getReadOnly(accessPrivileges[permKey], authType);
    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPrivileges.EDGE_CONNECTOR_FORWARDING === 'NONE') {
      return <PermissionRequired accessPermissions={accessPrivileges} />;
    }

    return (
      <div className="main-container ip-fqdn-container">
        <div className="page-title">
          {t('IP_FQDN_GROUPS')}
        </div>
        <div className="configuration-nav-tab">
          <NavTabs
            tabConfiguration={[
            // {
            //   id: 'source-ip-groups',
            //   title: t('SOURCE_IP_GROUPS'),
            //   to: `${BASE_LAYOUT}/administration/source-ip-groups`,
            // },
              {
                id: 'destination-ip-groups',
                title: t('DESTINATION_IP_GROUPS'),
                to: `${BASE_LAYOUT}/administration/destination-ip-groups`,
              },
            // {
            //   id: 'ip-pool',
            //   title: t('IP_POOL'),
            //   to: `${BASE_LAYOUT}/administration/ip-pool`,
            // },
            ]} />
        </div>
        <div className={`sip-add-new-btn ${accessPrivileges[permKey] === 'READ_ONLY' ? 'hide' : ''}`}>
          {!isReadOnly && <AddNewButton label={t('ADD_DESTINATION_IP_GROUP')} clickCallback={() => toggleDestinationIPForm(null, true, 'ADD_DESTINATION_IP')} />}
        </div>
        <div className="cloud-provider-wrapper location-templates ip-fqdn-dst-ip-groups">
          <RBAC privilege={permKey}>
            <Loading {...this.props}>
              <HelpArticle article={HELP_ARTICLES.DEST_GROUP} />
              <ServerError {...this.props}>
                <ConfigTable
                  {...DESTINATION_TABLE_CONFIGS}
                  permission={accessPrivileges[permKey]}
                  onHandleRowEdit={toggleDestinationIPForm}
                  onHandleRowDelete={toggleDeleteConfirmationForm}
                  onHandleRowView={handleDestinationIPView}
                  data={this.getTableData()} />
                
                <Modal
                  title={t(formTitle)}
                  isOpen={showForm}
                  styleClass="destination-ip-form"
                  closeModal={() => toggleDestinationIPForm(null, false)}>
                  <DestinationIPForm formMode={formMode} ipAddressesList={ipAddressesList} />
                </Modal>
                <Modal
                  title={t('VIEW_DESTINATION_IP_GROUP')}
                  isOpen={showViewForm}
                  closeModal={() => handleDestinationIPView(null, false)}>
                  <DestinationIPViewModal />
                </Modal>
                <Modal
                  title={t('DELETE_CONFIRMATION')}
                  isOpen={showDeleteForm}
                  closeModal={() => toggleDeleteConfirmationForm(null, false)}>
                  <DeleteConfirmationForm
                    modalLoading={modalLoading}
                    selectedRowID={selectedRowID}
                    handleCancel={toggleDeleteConfirmationForm}
                    handleDelete={handleDelete} />
                </Modal>
              </ServerError>
            </Loading>
          </RBAC>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...DestinationIPSelectors.baseSelector(state),
  modalLoading: DestinationIPSelectors.modalLoadingSelector(state),
  selectedRowID: DestinationIPSelectors.selectedRowIDSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  ipAddressesList: DestinationIPSelectors.ipAddressesSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loadDestinationIPData,
    toggleDestinationIPForm: toggleForm,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deleteDestinationIP,
    handleDestinationIPView: toggleViewModal,
  }, dispatch);
  return actions;
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(DestinationIPGroups));

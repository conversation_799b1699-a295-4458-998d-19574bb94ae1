// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import withRouter from 'layout/withRouter';
import { reduxForm } from 'redux-form';
import { noop } from 'utils/lodash';
import { FormFieldLabel } from 'components/label';

import { toggleViewModal } from 'ducks/destinationIpGroups';

import * as DestinationIPGroupsSelectors from 'ducks/destinationIpGroups/selectors';

const getCountries = (countries, t) => {
  const updatedList = countries.map((el) => t(el));
  return updatedList.join(', ');
};
export function BasicCustomAppForm({
  actions,
  t,
  appData,
}) {
  const {
    name,
    description,
    addresses,
    countries,
    type,
  } = appData;

  const handleCancelClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    const { cancelHandle } = actions;
    
    cancelHandle(null, false);
  };
  
  return (
    <form className="wizard-form configure-provisioning-template">
      <div className="form-sections-container">
        <div className="form-section provisioning-url">
          <div className="input-container review full-width">
            <FormFieldLabel text={t('NAME')} styleClass="no-margin-top" />
            <p className="disabled-input">{t(name)}</p>
          </div>
          <div className="input-container review full-width">
            <FormFieldLabel text={t('TYPE')} styleClass="no-margin-top" />
            <p className="disabled-input">{t(type)}</p>
          </div>
          <div className="input-container review full-width">
            <FormFieldLabel text={t('IP_ADDRESSES')} styleClass="no-margin-top" />
            <p className="disabled-input">{addresses && addresses.length > 0 ? addresses.join(', ') : '---'}</p>
          </div>
          <div className="input-container review full-width">
            <FormFieldLabel text={t('COUNTRIES')} styleClass="no-margin-top" />
            <p className="disabled-input">{(countries && getCountries(countries, t)) || '---'}</p>
          </div>
          <p className="review-section-title">{t('DESCRIPTION')}</p>
          <div className="input-container review desc full-width">
            <p className="disabled-input full-width">{description || '---'}</p>
          </div>
        </div>
      </div>
      <div className="modal-footer">
        <button type="submit" onClick={handleCancelClick} className="next primary-button">{t('CANCEL')}</button>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
  }),
  t: PropTypes.func,
  appData: PropTypes.shape(),
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
  },
  t: (str) => str,
  appData: {},
};

const SourceIPGroupsViewModal = reduxForm({
  form: 'SourceIpViewForm',
})(BasicCustomAppForm);

const mapStateToProps = (state) => ({
  ...DestinationIPGroupsSelectors.default(state),
  appData: DestinationIPGroupsSelectors.appDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleViewModal,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(SourceIPGroupsViewModal)));

// @flow
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  reduxForm, Field, autofill,
} from 'redux-form';
import Input from 'components/Input';
import Loading from 'components/spinner/Loading';
import { FormFieldLabel } from 'components/label';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop, map } from 'utils/lodash';
import {
  required, maxLength, ipAddressesOrRanges, pureFqdn, looseurlAddressSchemeless,
} from 'utils/validations';
import ListBuilder from 'components/listBuilder';
import ECRadioGroup from 'components/ecRadioGroup';

import {
  saveForm,
  toggleForm,
  handleIPAddresses,
  handleFQDNAddresses,
  handleWILDFQDNAddresses,
  handleTypeChange,
  updatedCountry,
} from 'ducks/destinationIpGroups';
import * as DestinationIPGroupsSelectors from 'ducks/destinationIpGroups/selectors';
import * as TrafficFwdPoliciesSelector from 'ducks/trafficFwdPolicies/selectors';
import Country from './Country';

const maxLength255 = maxLength(255);

const updateArrayValues = (array) => {
  const updatedValues = map(array, (el) => { return { name: el, id: el }; });
  return updatedValues;
};

export class BasicCustomAppForm extends Component {
  static propTypes = {
    actions: PropTypes.shape({
      cancelHandle: PropTypes.func,
      saveSourceIP: PropTypes.func,
      onipAddresses: PropTypes.func,
      onTypeChange: PropTypes.func,
      autoFillFormValues: PropTypes.func,
      onFQDNAddress: PropTypes.func,
      onWildFQDNAddress: PropTypes.func,
      updatedCountryValues: PropTypes.func,
    }),
    valid: PropTypes.bool,
    t: PropTypes.func,
    handleSubmit: PropTypes.func,
    initialValues: PropTypes.shape(),
    modalLoading: PropTypes.bool,
    formMode: PropTypes.string,
    ipAddressesList: PropTypes.arrayOf(PropTypes.string),
    fqdnList: PropTypes.arrayOf(PropTypes.string),
    wildFqdnList: PropTypes.arrayOf(PropTypes.string),
    type: PropTypes.string,
  };

  static defaultProps = {
    actions: {
      cancelHandle: noop,
      saveSourceIP: noop,
      onipAddresses: noop,
      onTypeChange: noop,
      autoFillFormValues: noop,
      onFQDNAddress: noop,
      onWildFQDNAddress: noop,
      updatedCountryValues: noop,
    },
    valid: true,
    t: (str) => str,
    handleSubmit: noop,
    initialValues: {},
    modalLoading: false,
    formMode: 'NEW',
    ipAddressesList: [],
    fqdnList: [],
    wildFqdnList: [],
    type: '',
  };

  componentDidMount() {
    const { actions, initialValues } = this.props;
    const {
      autoFillFormValues,
      onipAddresses,
      onFQDNAddress,
      onWildFQDNAddress,
    } = actions;

    const {
      type,
      addresses,
    } = initialValues;

    if (type === 'DSTN_IP' && addresses) {
      autoFillFormValues('destinationIPGroupForm', 'addresses', addresses);
      onipAddresses(initialValues.addresses);
    }

    if (type === 'DSTN_FQDN' && addresses) {
      autoFillFormValues('destinationIPGroupForm', 'fqdnAddresses', addresses);
      onFQDNAddress(addresses);
    }
    
    if (type === 'DSTN_DOMAIN' && addresses) {
      autoFillFormValues('destinationIPGroupForm', 'domainAddresses', addresses);
      onWildFQDNAddress(addresses);
    }
    
    if (initialValues.countries && initialValues.countries.length > 0) {
      autoFillFormValues('destinationIPGroupForm', 'countries', updateArrayValues(initialValues.countries));
    }
  }

  render() {
    const {
      valid,
      actions,
      t,
      handleSubmit,
      initialValues,
      formMode,
      modalLoading,
      ipAddressesList,
      fqdnList,
      wildFqdnList,
      type,
    } = this.props;

    const {
      cancelHandle,
      saveSourceIP,
      onipAddresses,
      onTypeChange,
      onFQDNAddress,
      onWildFQDNAddress,
      updatedCountryValues,
    } = actions;

    const ipAddressesData = ipAddressesList;
    const fqdnData = fqdnList;
    const wildFqdnData = wildFqdnList;
    let selectedType = type;

    if (initialValues.type) {
      selectedType = initialValues.type;
    }

    return (
      <form onSubmit={handleSubmit(saveSourceIP)} className="add-custom-app-form add-edit-dstnform">
        <Loading loading={modalLoading} />
        <div className="form-sections-container">
          <div className="form-section">
            <div className="full-width dest-ip-row">
              <Field
                name="name"
                id="name"
                component={Input}
                type="text"
                placeholder={t('TYPE_DSTN_IP_NAME')}
                styleClass="half-width"
                label={t('NAME')}
                tooltip={t('TOOLTIP_DESTINATION_IP_NAME')}
                validate={[
                  required,
                  maxLength255,
                ]} />
            </div>
            {
              formMode === 'NEW' && (
                <>
                  <div className="full-width form-field-wrapper dest-ip-row">
                    <FormFieldLabel text={t('TYPE')} tooltip={t('TOOLTIP_DESTINATION_TYPE')} />
                  </div>
                  <div className="full-width dest-ip-row">
                    <ECRadioGroup
                      id="type"
                      name="type"
                      onChange={onTypeChange}
                      options={[{
                        name: 'type',
                        value: 'DSTN_IP',
                        checked: selectedType === 'DSTN_IP',
                        label: t('IP_ADDRESS'),
                      },
                      {
                        name: 'type',
                        value: 'DSTN_FQDN',
                        checked: selectedType === 'DSTN_FQDN',
                        label: t('FQDN'),
                      },
                      {
                        name: 'type',
                        value: 'DSTN_DOMAIN',
                        checked: selectedType === 'DSTN_DOMAIN',
                        label: t('DSTN_WILDCARD_FQDN'),
                      },
                      {
                        name: 'type',
                        value: 'DSTN_OTHER',
                        checked: selectedType === 'DSTN_OTHER',
                        label: t('DSTN_OTHER'),
                      },
                      ]} />
                  </div>
                </>
              )
            }

            {
              formMode !== 'NEW' && (
                <div className="full-width dest-ip-row">
                  <FormFieldLabel text={t('TYPE')} tooltip={t('TOOLTIP_DESTINATION_TYPE')} />
                  <span className="disabled-input">{t(selectedType)}</span>
                </div>
              )
            }
            
            {
              selectedType === 'DSTN_IP'
              && (
                <div className="full-width dest-ip-row">
                  <FormFieldLabel text={t('IP_ADDRESSES')} tooltip={t('TOOLTIP_DESTINATION_IP_ADDRESS')} />
                  <Field
                    id="addresses"
                    name="addresses"
                    attribute="addresses"
                    props={{
                      t,
                      hasSearch: true,
                      value: ipAddressesData,
                      action: onipAddresses,
                      disabled: false,
                      removeItemsText: t('REMOVE_ALL'),
                      addButtonText: t('ADD_ITEMS'),
                      placeholder: t('ADD_ITEMS'),
                      styleConfig: { inputMarginRight: 10 },
                      validation: ipAddressesOrRanges,
                    }}
                    component={ListBuilder} />
                </div>
              )
            }
            {
              selectedType === 'DSTN_FQDN'
              && (
                <div className="full-width dest-ip-row">
                  <FormFieldLabel text={t('FQDN')} tooltip={t('TOOLTIP_DESTINATION_IP_FQDN')} />
                  <Field
                    id="fqdnAddresses"
                    name="fqdnAddresses"
                    attribute="fqdnAddresses"
                    props={{
                      t,
                      hasSearch: true,
                      value: fqdnData,
                      action: onFQDNAddress,
                      disabled: false,
                      removeItemsText: t('REMOVE_ALL'),
                      addButtonText: t('ADD_ITEMS'),
                      placeholder: t('ADD_ITEMS'),
                      styleConfig: { inputMarginRight: 10 },
                      validation: pureFqdn,
                    }}
                    component={ListBuilder} />
                </div>
              )
            }
            {
              selectedType === 'DSTN_DOMAIN'
              && (
                <div className="full-width dest-ip-row">
                  <FormFieldLabel text={t('DOMAINS')} tooltip={t('TOOLTIP_DESTINATION_IP_DOMAIN')} />
                  <Field
                    id="domainAddresses"
                    name="domainAddresses"
                    attribute="domainAddresses"
                    props={{
                      t,
                      hasSearch: true,
                      value: wildFqdnData,
                      action: onWildFQDNAddress,
                      disabled: false,
                      removeItemsText: t('REMOVE_ALL'),
                      addButtonText: t('ADD_ITEMS'),
                      placeholder: t('ADD_ITEMS'),
                      styleConfig: { inputMarginRight: 10 },
                      validation: looseurlAddressSchemeless,
                    }}
                    component={ListBuilder} />
                </div>
              )
            }
            {
              selectedType === 'DSTN_OTHER' && (
                <div className="full-width dest-ip-row">
                  <div className="full-width country-category">
                    <div className="half-width">
                      <FormFieldLabel text={t('COUNTRY')} tooltip={t('TOOLTIP_DESTINATION_IP_COUNTRIES')} />
                      <Country updatedCountryValues={updatedCountryValues} />
                    </div>
                  </div>
                  {/* <div className="full-width category-wrapper">
                    <div className="full-width">
                        <FormFieldLabel text={t('CATEGORY')} />
                        <Category />
                    </div>
                  </div> */}
                </div>
              )
            }
            <div className="desc-wrapper full-width">
              <FormFieldLabel
                text={t('DESCRIPTION')}
                tooltip={t('TOOLTIP_GENERAL_DESCRIPTION')} />
              <Field
                name="description"
                id="description"
                component="textarea"
                className="full-width form-textarea"
                label={t('DESCRIPTION')} />
            </div>
          </div>
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="submit" className={`submit ${!valid ? 'disabled' : ''}`}>{t('SAVE')}</button>
            <button type="button" className="cancel" onClick={() => cancelHandle(null, false)}>{t('CANCEL')}</button>
          </div>
        </div>
      </form>
    );
  }
}

const DestinationIPForm = reduxForm({
  form: 'destinationIPGroupForm',
})(BasicCustomAppForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleForm,
    saveSourceIP: saveForm,
    onipAddresses: handleIPAddresses,
    onFQDNAddress: handleFQDNAddresses,
    onWildFQDNAddress: handleWILDFQDNAddresses,
    onTypeChange: handleTypeChange,
    updatedCountryValues: updatedCountry,
    autoFillFormValues: autofill,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  ...DestinationIPGroupsSelectors.baseSelector(state),
  initialValues: DestinationIPGroupsSelectors.appDataSelector(state),
  modalLoading: DestinationIPGroupsSelectors.modalLoadingSelector(state),
  type: DestinationIPGroupsSelectors.typeSelector(state),
  country: DestinationIPGroupsSelectors.selectedCountrySelector(state),
  showTrafficPolicies: TrafficFwdPoliciesSelector.showFormSelector(state),
});

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(DestinationIPForm));

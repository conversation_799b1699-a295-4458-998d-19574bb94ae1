// @flow
import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm, Field, autofill } from 'redux-form';
import Input from 'components/Input';
import Loading from 'components/spinner/Loading';
import ECRadioGroup from 'components/ecRadioGroup';
import { FormSectionLabel, FormFieldLabel } from 'components/label';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import { required, maxLength } from 'utils/validations';

import {
  saveForm,
  toggleForm,
  downloadNssServer,
  generateSslCertificate,
  handleNssStatusChange,
} from 'ducks/nssSettingsServers';
import * as NssServersSelectors from 'ducks/nssSettingsServers/selectors';

const maxLength50 = maxLength(50);
 
export function BasicCustomAppForm({
  valid,
  actions,
  t,
  handleSubmit,
  initialValues,
  modalLoading,
  selectedStatus,
}) {
  const {
    cancelHandle,
    addNss,
    nssStatusChange,
    handleDownloadNssServer,
    handleGenerateSslCertificate,
  } = actions;

  const {
    id,
    name,
  } = initialValues;

  return (
    <form onSubmit={handleSubmit(addNss)} className="add-custom-app-form add-edit-gateways">
      <Loading loading={modalLoading} />
      <div className="form-sections-container">
        <FormSectionLabel text={t('NSS_SERVER')} />
        <div className="form-section">
          <div className="checkbox-container half-width">
            <FormFieldLabel
              tooltip={t('TOOLTIP_NSS_SERVER_NAME')}
              text={t('SERVER_NAME')} />
            {!id ? (
              <Field
                name="name"
                id="name"
                component={Input}
                type="text"
                placeholder={t('TYPE_NSS_SERVER_NAME')}
                validate={[
                  required,
                  maxLength50,
                ]} />
            )
              : <p className="disabled-input">{t(name)}</p>}
          </div>
          <div className="checkbox-container half-width">
            <FormFieldLabel
              tooltip={t('TOOLTIP_NSS_TYPE')}
              text={t('NSS_TYPE')} />
            <p className="disabled-input">{t(initialValues.type)}</p>
          </div>
          <div className="nss-server-status-container radio-button-container half-width">
            <FormFieldLabel
              tooltip={t('TOOLTIP_NSS_SERVER_STATUS')}
              text={t('STATUS')} />
            <ECRadioGroup
              id="nss-servers-state"
              name="status"
              styleClass="nss-server-status"
              onChange={nssStatusChange}
              options={[{
                name: 'status', value: 'ENABLED', checked: selectedStatus === 'ENABLED', label: t('ENABLED'),
              },
              {
                name: 'status', value: 'DISABLED', checked: selectedStatus === 'DISABLED', label: t('DISABLED'),
              }]} />
          </div>
          <div className="checkbox-container nss-server-state half-width">
            <FormFieldLabel
              tooltip={t('TOOLTIP_NSS_SERVER_STATE')}
              text={t('STATE')} />
            <p className="disabled-input">{t(initialValues.state)}</p>
          </div>
          {(initialValues && initialValues.name)
              && (
                <div className="checkbox-container nss-server-certificates full-width">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_SERVER_SSL_CERTIFICATE')}
                    text={t('SSL_CERTIFICATE')} />
                  <div className="nss-server-certificate-links">
                    <span role="none" className="download-nss-certificate" onClick={() => handleDownloadNssServer(initialValues.id)}>{t('DOWNLOAD_CERTIFICATE')}</span>
                    <span role="none" className="download-nss-certificate with-left-border" onClick={() => handleGenerateSslCertificate(initialValues.id)}>{t('GENERATE_NEW_CERTIFICATE')}</span>
                  </div>
                </div>
              )}
        </div>
      </div>
      <div className="dialog-footer">
        <div className="dialog-footer-left">
          <button type="submit" className={`submit ${!valid ? 'disabled' : ''}`}>{t('SAVE')}</button>
          <button type="button" className="cancel" onClick={() => cancelHandle(false)}>{t('CANCEL')}</button>
        </div>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    addNss: PropTypes.func,
    cancelHandle: PropTypes.func,
    handleDownloadNssServer: PropTypes.func,
    handleGenerateSslCertificate: PropTypes.func,
    handleNssServersStatusChange: PropTypes.func,
    nssStatusChange: PropTypes.func,
  }),
  valid: PropTypes.bool,
  t: PropTypes.func,
  handleSubmit: PropTypes.func,
  selectedStatus: PropTypes.string,
  initialValues: PropTypes.shape(),
  modalLoading: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
    saveLocationTemplate: noop,
  },
  valid: true,
  t: (str) => str,
  handleSubmit: noop,
  initialValues: {},
  modalLoading: false,
};

const NssServersForm = reduxForm({
  form: 'addEditNssServersForm',
})(BasicCustomAppForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleForm,
    addNss: saveForm,
    nssStatusChange: handleNssStatusChange,
    handleDownloadNssServer: downloadNssServer,
    handleGenerateSslCertificate: generateSslCertificate,
    autoFillReduxForm: autofill,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  initialValues: NssServersSelectors.appDataSelector(state),
  modalLoading: NssServersSelectors.modalLoadingSelector(state),
  selectedStatus: NssServersSelectors.statusSelector(state),
});

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(NssServersForm));

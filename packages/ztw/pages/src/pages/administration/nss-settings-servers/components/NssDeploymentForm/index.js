// @flow
import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm, Field, autofill } from 'redux-form';
import { get } from 'utils/lodash';
import Input from 'components/Input';
import ExternalLinkButton from 'components/externalLinkButton';
import ECRadioGroup from 'components/ecRadioGroup';
import { FormSectionLabel, FormFieldLabel } from 'components/label';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import i18n from 'utils/i18n';
import {
  required, isInteger, range,
} from 'utils/validations';

import {
  toggleDeploymentForm,
  computeVirtualAppliance,
  handleNssDeploymentPlatformChange,
  downloadNssVirtualApplianceVm,
} from 'ducks/nssSettingsServers';
import * as NssServersSelectors from 'ducks/nssSettingsServers/selectors';

const noOfUserRange = (value) => {
  return range(value, 1, 99999);
};

const peakSessionsRange = (value) => {
  if (value) {
    return range(value, 1, 100000000);
  }
  return null;
};

const peakDnsReqsRange = (value) => {
  if (value) {
    return range(value, 1, 10000000);
  }
  return null;
};

export function BasicCustomAppForm({
  actions,
  t,
  handleSubmit,
  selectedPlatform,
  vmSpecsResponse,
  amiSpecsResponse,
  computed,
  noOfUsers,
  peakSessions,
  peakDnsReqs,
  formMeta,
  formSyncErrors,
}) {
  const {
    cancelHandle,
    compute,
    nssDeploymentPlatformChange,
    handleDownloadNssVirtualApplianceVm,
    autoFillReduxForm,
  } = actions;

  const metaUsers = get(formMeta, 'users', null);
  const metaSessions = get(formMeta, 'sessions', null);
  const metaDns = get(formMeta, 'dns', null);

  const usersError = get(formSyncErrors, 'users', null);
  const sessionsError = get(formSyncErrors, 'sessions', null);
  const dnsError = get(formSyncErrors, 'dns', null);

  const hasErrorUsers = metaUsers && metaUsers.touched && !!usersError;
  const hasErrorSessions = metaSessions && metaSessions.touched && !!sessionsError;
  const hasErrorDns = metaDns && metaDns.touched && !!dnsError;

  if (computed) {
    if (!metaUsers) {
      autoFillReduxForm('addEditNssDeploymentForm', 'users', noOfUsers);
    }
    if (!metaSessions) {
      autoFillReduxForm('addEditNssDeploymentForm', 'sessions', peakSessions);
    }
    if (!metaDns) {
      autoFillReduxForm('addEditNssDeploymentForm', 'dns', peakDnsReqs);
    }
  }

  let computeButtonActive = false;
  if (selectedPlatform !== 'msAzure' && selectedPlatform !== 'gcpstorage') {
    computeButtonActive = true;
  }

  return (
    <form onSubmit={handleSubmit(compute)} className="add-custom-app-form add-edit-gateways ec-root">
      <div className="form-sections-container">
        <FormSectionLabel text={t('NSS_VIRTUAL_APPLIANCE_DEPLOYMENT')} />
        <div className="form-section">
          <div className="checkbox-container full-width nss-servers-deployment-type">
            <FormFieldLabel
              tooltip={t('TOOLTIP_NSS_TYPE')}
              text={t('NSS_TYPE')} />
            <p className="disabled-input">{t('NSS_FOR_FIREWALL_AND_EC')}</p>
          </div>
          <div className="checkbox-container half-width nss-servers-deployment-users">
            <FormFieldLabel
              tooltip={t('TOOLTIP_NSS_SERVER_DEPLOYMENT_NUMBER_OF_USERS')}
              text={t('NSS_SERVER_DEPLOYMENT_NUMBER_OF_USERS')}
              styleClass={`${hasErrorUsers ? 'invalid' : ''}`}
              error={hasErrorUsers ? t(usersError) : null} />
            <Field
              name="users"
              id="users"
              component={Input}
              styleClass={hasErrorUsers ? 'error' : ''}
              type="text"
              placeholder={t('ENTER_TEXT')}
              validate={[
                required,
                isInteger,
                noOfUserRange,
              ]} />
          </div>
          <div className="checkbox-container half-width nss-servers-deployment-sessions">
            <FormFieldLabel
              tooltip={t('TOOLTIP_NSS_SERVER_DEPLOYMENT_PEAK_TRANSACTIONS_PER_HOUR')}
              text={t('NSS_SERVER_DEPLOYMENT_PEAK_SESSIONS_PER_HOUR')}
              styleClass={`${hasErrorSessions ? 'invalid' : ''}`}
              error={hasErrorSessions ? t(sessionsError) : null} />
            <Field
              name="sessions"
              id="sessions"
              component={Input}
              styleClass={hasErrorSessions ? 'error' : ''}
              type="text"
              placeholder={t('ENTER_TEXT')}
              validate={[
                isInteger,
                peakSessionsRange,
              ]} />
          </div>
          <div className="checkbox-container full-width nss-servers-deployment-dns">
            <FormFieldLabel
              tooltip={t('TOOLTIP_NSS_SERVER_DEPLOYMENT_PEAK_DNS_REQUESTS_PER_HOUR')}
              text={t('NSS_SERVER_DEPLOYMENT_PEAK_DNS_REQUESTS_PER_HOUR')}
              styleClass={`${hasErrorDns ? 'invalid' : ''}`}
              error={hasErrorDns ? t(dnsError) : null} />
            <Field
              name="dns"
              id="dns"
              component={Input}
              styleClass={hasErrorDns ? 'error' : ''}
              type="text"
              placeholder={t('ENTER_TEXT')}
              validate={[
                isInteger,
                peakDnsReqsRange,
              ]} />
          </div>
          <div className="radio-button-container full-width nss-servers-deployment-platform">
            <FormFieldLabel
              tooltip={t('TOOLTIP_NSS_SERVER_DEPLOYMENT_PLATFORM')}
              text={t('NSS_SERVER_DEPLOYMENT_PLATFORM')} />
            <ECRadioGroup
              id="nss-servers-state"
              name="status"
              styleClass="full-width"
              onChange={nssDeploymentPlatformChange}
              options={[{
                name: 'platform', value: 'vmSpecs', checked: selectedPlatform === 'vmSpecs', label: t('VMWARE'),
              },
              {
                name: 'platform', value: 'amiSpecs', checked: selectedPlatform === 'amiSpecs', label: t('AMAZON_WEB_SERVICES'),
              },
              {
                name: 'platform', value: 'gcpstorage', checked: selectedPlatform === 'gcpstorage', label: t('GCP'),
              },
              {
                name: 'platform', value: 'msAzure', checked: selectedPlatform === 'msAzure', label: t('MICROSOFT_AZURE'),
              }]} />
          </div>
          <div className="half-width nss-servers-deployment-submit">
            <button type="submit" className={`custom-compute-button ${!computeButtonActive ? 'disabled' : ''}`}>{t('COMPUTE')}</button>
          </div>
        </div>
      </div>

      {computed
        && (
          <>
            {selectedPlatform === 'amiSpecs'
            && (
              <>
                <div className="form-sections-container nss-servers-deployment-form-config-specs">
                  <FormSectionLabel text={t('COMPUTE_RECOMMENDED_EC2_INSTANCE_TYPE')} />
                  <div className="form-section">
                    <div className="half-width marginBottom15">
                      <FormFieldLabel
                        tooltip={t('TOOLTIP_EC2_INSTANCE_TYPE')}
                        text={t('EC2_INSTANCE_TYPE')} />
                      <p>{amiSpecsResponse.vmName || '--'}</p>
                    </div>
                    <div className="half-width marginBottom15">
                      <FormFieldLabel
                        tooltip={t('TOOLTIP_EBS_STORAGE')}
                        text={t('EBS_STORAGE')} />
                      <p>{amiSpecsResponse.storageSize || '--'}</p>
                    </div>
                  </div>
                </div>
                <div className="form-sections-container nss-servers-deployment-form-config-specs">
                  <FormSectionLabel text={t('CONFIGURATION_INFO')} />
                  <div className="form-section">
                    <div className="half-width">
                      <ExternalLinkButton
                        targetURL="https://aws.amazon.com/console/"
                        label={t('AMAZON_WEB_SERVICES_CONSOLE')} />
                    </div>
                    <div className="half-width">
                      <ExternalLinkButton
                        targetURL="https://help.zscaler.com/zia/nss-deployment-guide-aws"
                        label={t('LEARN_TO_SETUP_EC2_INSTANCE')} />
                    </div>
                  </div>
                </div>
              </>
            )}

            {selectedPlatform === 'vmSpecs'
              && (
                <>
                  <div className="form-sections-container nss-servers-deployment-form-config-specs">
                    <FormSectionLabel text={t('RECOMMENDED_VM_SPECS')} />
                    <div className="form-section">
                      <div className="half-width">
                        <FormFieldLabel
                          tooltip={t('TOOLTIP_RAM')}
                          text={t('RAM')} />
                        <p>{vmSpecsResponse.ram || '--'}</p>
                      </div>
                      <div className="half-width">
                        <FormFieldLabel
                          tooltip={t('TOOLTIP_DISK_STORAGE')}
                          text={t('DISK_STORAGE')} />
                        <p>{vmSpecsResponse.disk || '--'}</p>
                      </div>
                    </div>
                  </div>
                  <div className="form-sections-container nss-servers-deployment-form-config-specs">
                    <FormSectionLabel text={t('RECOMMENDED_HYPERVISOR_SPECS')} />
                    <div className="form-section">
                      <div className="half-width marginBottom15">
                        <FormFieldLabel
                          tooltip={t('TOOLTIP_CPU')}
                          text={t('CPU')} />
                        <p>{vmSpecsResponse.cpu || '--'}</p>
                      </div>
                      <div className="half-width marginBottom15">
                        <FormFieldLabel
                          tooltip={t('TOOLTIP_NUMBER_OF_CORES')}
                          text={t('NUMBER_OF_CORES')} />
                        <p>{vmSpecsResponse.cores || '--'}</p>
                      </div>
                      <div className="half-width">
                        <FormFieldLabel
                          tooltip={t('TOOLTIP_DEDICATED_BANDWIDTH')}
                          text={t('DEDICATED_BANDWIDTH')} />
                        <p>{vmSpecsResponse.bandwidth || '--'}</p>
                      </div>
                      <div className="half-width">
                        <FormFieldLabel
                          tooltip={t('TOOLTIP_NSS_VIRTUAL_MACHINE')}
                          text={t('NSS_VIRTUAL_MACHINE')} />
                        <p role="none" className="downloadVirtualApplianceVm" onClick={() => handleDownloadNssVirtualApplianceVm(vmSpecsResponse.vmName)}>{i18n.t('NSS_SERVER_DOWNLOAD_NSS_DEPLOYMENT_APPLICANCE')}</p>
                      </div>
                    </div>
                  </div>
                </>
              )}
          </>
        )}

      {(selectedPlatform === 'msAzure')
        && (
          <div className="form-sections-container">
            <FormSectionLabel text={t('CONFIGURATION_INFO')} />
            <div className="form-section">
              <div className="full-width">
                <ExternalLinkButton
                  targetURL="https://help.zscaler.com/zia/nss-deployment-guide-microsoft-azure"
                  label={t('MS_AZURE_DEPLOYMENT_GUIDE_FOR_NSS')} />
              </div>
            </div>
          </div>
        )}

      {(selectedPlatform === 'gcpstorage')
        && (
          <div className="form-sections-container">
            <FormSectionLabel text={t('CONFIGURATION_INFO')} />
            <div className="form-section">
              <div className="full-width">
                <ExternalLinkButton
                  targetURL="https://help.zscaler.com/zia/nss-deployment-guide-google-cloud-platform"
                  label={t('NSS_GCP_DEPLOYMENT_GUIDE')} />
              </div>
            </div>
          </div>
        )}

      <div className="dialog-footer">
        <div className="dialog-footer-left">
          <button type="button" className="cancel" onClick={() => cancelHandle(false)}>{t('CANCEL')}</button>
        </div>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    compute: PropTypes.func,
    cancelHandle: PropTypes.func,
    handleNssServersStatusChange: PropTypes.func,
    autoFillFormValues: PropTypes.func,
    nssDeploymentPlatformChange: PropTypes.func,
    handleDownloadNssVirtualApplianceVm: PropTypes.func,
    autoFillReduxForm: PropTypes.func,
  }),
  t: PropTypes.func,
  handleSubmit: PropTypes.func,
  selectedPlatform: PropTypes.string,
  vmSpecsResponse: PropTypes.shape(),
  amiSpecsResponse: PropTypes.shape(),
  computed: PropTypes.bool,
  noOfUsers: PropTypes.string,
  peakSessions: PropTypes.string,
  peakDnsReqs: PropTypes.string,
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: null,
    saveLocationTemplate: null,
    autoFillFormValues: null,
  },
  t: (str) => str,
  computed: false,
  handleSubmit: null,
  vmSpecsResponse: {},
  amiSpecsResponse: {},
  formMeta: {},
  formSyncErrors: {},
};

const NssDeploymentForm = reduxForm({
  form: 'addEditNssDeploymentForm',
})(BasicCustomAppForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleDeploymentForm,
    compute: computeVirtualAppliance,
    nssDeploymentPlatformChange: handleNssDeploymentPlatformChange,
    handleDownloadNssVirtualApplianceVm: downloadNssVirtualApplianceVm,
    autoFillReduxForm: autofill,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  ...state.error,
  noOfUsers: NssServersSelectors.noOfUsersSelector(state),
  peakSessions: NssServersSelectors.peakSessionsSelector(state),
  peakDnsReqs: NssServersSelectors.peakDnsReqSelector(state),
  selectedPlatform: NssServersSelectors.platformSelector(state),
  amiSpecsResponse: NssServersSelectors.computeAmiSpecsSelector(state),
  vmSpecsResponse: NssServersSelectors.computeVmSpecsSelector(state),
  computed: NssServersSelectors.isComputed(state),
  formMeta: NssServersSelectors.formMetaSelector(state),
  formSyncErrors: NssServersSelectors.formSyncErrorsSelector(state),
});

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(NssDeploymentForm));

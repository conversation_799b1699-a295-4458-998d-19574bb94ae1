// @flow
import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm, autofill } from 'redux-form';
import Loading from 'components/spinner/Loading';
import { FormSectionLabel, FormFieldLabel } from 'components/label';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';

import {
  saveForm,
  toggleViewModal,
  handleNssStatusChange,
} from 'ducks/nssSettingsServers';
import * as NssServersSelectors from 'ducks/nssSettingsServers/selectors';
 
export function BasicCustomAppForm({
  actions,
  t,
  initialValues,
  modalLoading,
}) {
  const {
    cancelHandle,
  } = actions;
  
  return (
    <form className="add-custom-app-form add-edit-gateways">
      <Loading loading={modalLoading} />
      <div className="form-sections-container">
        <FormSectionLabel text={t('NSS_SERVER')} />
        <div className="form-section">
          <div className="checkbox-container half-width">
            <FormFieldLabel
              tooltip={t('TOOLTIP_NSS_SERVER_NAME')}
              text={t('SERVER_NAME')} />
            <p className="disabled-input">{t(initialValues.name)}</p>
          </div>
          <div className="checkbox-container half-width">
            <FormFieldLabel
              tooltip={t('TOOLTIP_NSS_TYPE')}
              text={t('NSS_TYPE')} />
            <p className="disabled-input">{t(initialValues.type)}</p>
          </div>
          <div className="radio-button-container half-width">
            <FormFieldLabel
              tooltip={t('TOOLTIP_NSS_SERVER_STATUS')}
              text={t('STATUS')} />
            <p className="disabled-input">{t(initialValues.status)}</p>
          </div>
          <div className="checkbox-container half-width">
            <FormFieldLabel
              tooltip={t('TOOLTIP_NSS_SERVER_STATE')}
              text={t('STATE')} />
            <p className="disabled-input">{t(initialValues.state)}</p>
          </div>
        </div>
      </div>
      <div className="dialog-footer">
        <div className="dialog-footer-left">
          <button type="button" className="cancel" onClick={() => cancelHandle(false)}>{t('CANCEL')}</button>
        </div>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
    addNss: PropTypes.func,
    handleNssServersStatusChange: PropTypes.func,
  }),
  t: PropTypes.func,
  initialValues: PropTypes.shape(),
  modalLoading: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
    saveLocationTemplate: noop,
  },
  t: (str) => str,
  initialValues: {},
  modalLoading: false,
};

const NssServersForm = reduxForm({
  form: 'addEditNssServersForm',
})(BasicCustomAppForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleViewModal,
    addNss: saveForm,
    handleNssStatusChange,
    autoFillReduxForm: autofill,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  initialValues: NssServersSelectors.appDataSelector(state),
  modalLoading: NssServersSelectors.modalLoadingSelector(state),
  selectedStatus: NssServersSelectors.statusSelector(state),
  isFailCloseEnabled: NssServersSelectors.failCloseSelector(state),
});

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(NssServersForm));

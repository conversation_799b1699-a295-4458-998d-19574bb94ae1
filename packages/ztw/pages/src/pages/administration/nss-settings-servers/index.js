import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import { HELP_ARTICLES, BASE_LAYOUT } from 'config';
import HelpArticle from 'components/HelpArticle';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faShareSquare } from '@fortawesome/pro-solid-svg-icons';
import AddNewButton from 'components/addNewButton';
import Download from 'components/button/Download';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import NavTabs from 'components/navTabs';

import RBAC from 'components/rbac';
import ServerError from 'components/errors/ServerError';
import * as constants from 'ducks/login/constants';
import * as NssServersSelectors from 'ducks/nssSettingsServers/selectors';
import { NSS_SERVERS_TABLE_CONFIGS } from 'ducks/nssSettingsServers/constants';
import ConfigTable from 'components/configTable';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasNSSsku, getReadOnly } from 'utils/helpers';
import * as loginSelectors from 'ducks/login/selectors';

import {
  loadNssServersData,
  toggleForm,
  downloadMIBFiles,
  downloadNssServer,
  toggleDeploymentForm,
  toggleDeleteForm,
  deleteNssServers,
  toggleViewModal,
} from 'ducks/nssSettingsServers';
import {
  NssServersForm,
  NssDeploymentForm,
  NssServersViewModal,
} from './components';

export class NssSettingsServers extends Component {
  static propTypes = {
    t: PropTypes.func,
    accessPrivileges: PropTypes.shape({
      EDGE_CONNECTOR_NSS_CONFIGURATION: PropTypes.string,
    }),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    cloudNssProvisioned: PropTypes.bool,
    authType: PropTypes.string,
    nssServersTableData: PropTypes.arrayOf(PropTypes.shape()),
    nssServerStatus: PropTypes.string,
    toggleNssServerForm: PropTypes.func,
    toggleNssDeploymentForm: PropTypes.func,
    showForm: PropTypes.bool,
    formTitle: PropTypes.string,
    showNssDeploymentForm: PropTypes.bool,
    showDeleteForm: PropTypes.bool,
    selectedRowID: PropTypes.string,
    handleDelete: PropTypes.func,
    toggleDeleteConfirmationForm: PropTypes.func,
    handleViewNssServersForm: PropTypes.func,
    handleDownloadMIBFiles: PropTypes.func,
    handleDownloadNssServer: PropTypes.func,
    modalLoading: PropTypes.bool,
    load: PropTypes.func,
    showViewForm: PropTypes.bool,
  };

  static defaultProps = {
    t: (str) => str,
    accessPrivileges: {},
    accessSubscriptions: [],
    cloudNssProvisioned: false,
    authType: '',
    nssServersTableData: [],
    nssServerStatus: '',
    showDeleteForm: false,
    modalLoading: false,
    selectedRowID: null,
    handleDelete: noop,
    toggleDeleteConfirmationForm: noop,
    load: noop,
    showForm: false,
    showViewForm: false,
    formTitle: '',
    showNssDeploymentForm: false,
    toggleNssServerForm: noop,
    toggleNssDeploymentForm: noop,
    handleViewNssServersForm: noop,
    handleDownloadMIBFiles: noop,
    handleDownloadNssServer: noop,
  };

  componentDidMount() {
    const { load, accessSubscriptions } = this.props;
    const hasNssSubscription = hasNSSsku(accessSubscriptions);

    if (hasNssSubscription) {
      load();
    }
  }

  getTableData = () => {
    const { nssServersTableData, accessPrivileges, authType } = this.props;
    const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_NSS_CONFIGURATION, authType);

    const tableData = nssServersTableData.map((row) => {
      return {
        id: row.id,
        name: row.name,
        type: row.type,
        status: row.status,
        state: row.state,
        download: 'Download',
        failClosed: row.failClosed,
        description: row.description,
        // isReadOnly: row.name === 'Default ZIA Gateway',
        isReadOnly,
        isDeletable: !isReadOnly,
        isEditable: !isReadOnly,
      };
    });
    return tableData;
  };

  render() {
    const {
      t,
      accessPrivileges,
      accessSubscriptions,
      cloudNssProvisioned,
      authType,
      showDeleteForm,
      modalLoading,
      selectedRowID,
      handleDelete,
      toggleDeleteConfirmationForm,
      showForm,
      showViewForm,
      formTitle,
      showNssDeploymentForm,
      toggleNssServerForm,
      handleDownloadMIBFiles,
      handleDownloadNssServer,
      toggleNssDeploymentForm,
      handleViewNssServersForm,
    } = this.props;

    const hasNssSubscription = hasNSSsku(accessSubscriptions);
    const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_NSS_CONFIGURATION, authType);

    if (!hasNssSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPrivileges.EDGE_CONNECTOR_NSS_CONFIGURATION === 'NONE') {
      return <PermissionRequired accessPermissions={accessPrivileges} />;
    }

    const tabConfiguration = [
      {
        id: 'nss-settings-servers',
        title: t('NSS_SERVERS'),
        to: `${BASE_LAYOUT}/administration/nss-settings-servers`,
      },
      {
        id: 'nss-settings-feeds',
        title: t('NSS_FEEDS'),
        to: `${BASE_LAYOUT}/administration/nss-settings-feeds`,
      },
      ...(cloudNssProvisioned ? [{
        id: 'nss-settings-cloud-feeds',
        title: t('NSS_CLOUD_FEEDS'),
        to: `${BASE_LAYOUT}/administration/nss-settings-cloud-feeds`,
      }] : []),
    ];

    return (
      <>
        <HelpArticle article={HELP_ARTICLES.NSS_SERVERS} />
        <div className="main-container">
          <div className="nss-title">
            <NavTabs
              isPageHeader
              tabConfiguration={tabConfiguration} />
          </div>
          <div className="nss-servers-page">
            {!isReadOnly && (
              <>
                <AddNewButton label={t('ADD_NSS_SERVER')} clickCallback={() => toggleNssServerForm(null, true, 'ADD_EC_NSS_SERVER')} />
                <div className="nss-deployment-button">
                  <FontAwesomeIcon icon={faShareSquare} />
                  <span role="none" onKeyUp={() => toggleNssDeploymentForm(null, true)} onClick={() => toggleNssDeploymentForm(null, true)}>{t('DEPLOY_NSS_VIRTUAL_APPLIANCE')}</span>
                </div>
              </>
            )}
            <div className="download-mib-files">
              <Download label={t('DOWNLOAD_MIB_FILES')} onActionCb={() => handleDownloadMIBFiles()} />
            </div>
          </div>
          <div className="nss-servers-wrapper nss-servers">
            <RBAC privilege={constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_NSS_CONFIGURATION}>
              <Loading {...this.props}>
                <ServerError {...this.props}>
                  <ConfigTable
                    {...NSS_SERVERS_TABLE_CONFIGS}
                    onHandleRowEdit={toggleNssServerForm}
                    onHandleRowDelete={toggleDeleteConfirmationForm}
                    onHandleRowView={handleViewNssServersForm}
                    onHandleRowDownload={handleDownloadNssServer}
                    data={this.getTableData()} />
                
                  <Modal
                    title={t(formTitle)}
                    isOpen={showForm}
                    styleClass="nss-servers-modal"
                    closeModal={() => toggleNssServerForm(null, false, 'ADD_EC_NSS_SERVER')}>
                    <NssServersForm />
                  </Modal>
                  <Modal
                    title={t('NSS_VIRTUAL_APPLIANCE_DEPLOYMENT_CC')}
                    isOpen={showNssDeploymentForm}
                    styleClass="nss-servers-modal"
                    closeModal={() => toggleNssDeploymentForm(null, false, 'DEPLOY_EC_NSS_VIRTUAL_APPLIANCE')}>
                    <NssDeploymentForm />
                  </Modal>
                  <Modal
                    title={t('VIEW_NSS_SERVER')}
                    isOpen={showViewForm}
                    closeModal={() => handleViewNssServersForm(false)}>
                    <NssServersViewModal />
                  </Modal>
               
                  <Modal
                    title={t('DELETE_CONFIRMATION')}
                    isOpen={showDeleteForm}
                    closeModal={() => toggleDeleteConfirmationForm(null, false)}>
                    <DeleteConfirmationForm
                      modalLoading={modalLoading}
                      selectedRowID={selectedRowID}
                      handleCancel={toggleDeleteConfirmationForm}
                      handleDelete={handleDelete} />
                  </Modal>
                </ServerError>
              </Loading>
            </RBAC>
          </div>
        </div>
      </>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    ...NssServersSelectors.baseSelector(state),
    modalLoading: NssServersSelectors.modalLoadingSelector(state),
    authType: loginSelectors.authTypeSelector(state),
    accessPrivileges: loginSelectors.accessPermissionsSelector(state),
    accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
    cloudNssProvisioned: loginSelectors.cloudNssProvisionedSelector(state),
  };
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    // load: localData,
    load: loadNssServersData,
    toggleNssServerForm: toggleForm,
    handleDownloadMIBFiles: downloadMIBFiles,
    handleDownloadNssServer: downloadNssServer,
    toggleNssDeploymentForm: toggleDeploymentForm,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deleteNssServers,
    handleViewNssServersForm: toggleViewModal,
  }, dispatch);
  return actions;
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(NssSettingsServers));

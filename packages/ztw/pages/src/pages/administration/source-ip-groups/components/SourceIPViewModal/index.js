// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import withRouter from 'layout/withRouter';
import { reduxForm } from 'redux-form';
import { noop } from 'utils/lodash';
import { FormFieldLabel } from 'components/label';

import { toggleViewModal } from 'ducks/sourceIPGroups';

import * as SourceIPGroupsSelectors from 'ducks/sourceIPGroups/selectors';

export function BasicCustomAppForm({
  actions,
  t,
  appData,
}) {
  const {
    name,
    description,
    ipAddresses,
  } = appData;

  const handleCancelClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    const { cancelHandle } = actions;
    
    cancelHandle(null, false);
  };
  
  return (
    <form className="wizard-form configure-provisioning-template sig-form">
      <div className="form-sections-container">
        <div className="form-section provisioning-url">

          <div className="input-container review full-width">
            <FormFieldLabel text={t('NAME')} styleClass="no-margin-top" />
            <p className="disabled-input">{name}</p>
          </div>
          <div className="input-container review full-width">
            <FormFieldLabel text={t('IP_ADDRESSES')} styleClass="no-margin-top" />
            <p className="disabled-input">{ipAddresses && ipAddresses.length > 0 ? ipAddresses.join(', ') : '---'}</p>
          </div>

          <p className="review-section-title">{t('DESCRIPTION')}</p>
          <div className="input-container review desc full-width">
            <p className="disabled-input full-width">{description || '---'}</p>
          </div>
        </div>
      </div>
      <div className="modal-footer">
        <button type="submit" onClick={handleCancelClick} className="next primary-button">{t('CANCEL')}</button>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
  }),
  t: PropTypes.func,
  appData: PropTypes.shape(),
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
  },
  t: (str) => str,
  appData: {},
};

const SourceIPGroupsViewModal = reduxForm({
  form: 'SourceIpViewForm',
})(BasicCustomAppForm);

const mapStateToProps = (state) => ({
  ...SourceIPGroupsSelectors.default(state),
  appData: SourceIPGroupsSelectors.appDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleViewModal,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(SourceIPGroupsViewModal)));

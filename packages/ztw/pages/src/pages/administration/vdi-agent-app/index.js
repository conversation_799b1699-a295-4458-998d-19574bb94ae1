/* eslint-disable react/jsx-handler-names */
// @flow
import React, { useEffect } from 'react';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { Field, reduxForm } from 'redux-form';
import { hasBsku, hasCsku } from 'utils/helpers';
import { HELP_ARTICLES, BASE_LAYOUT } from 'config';
import { useLocation } from 'react-router-dom';
import { withTranslation } from 'react-i18next';
import * as constants from 'ducks/login/constants';
import * as loginSelectors from 'ducks/login/selectors';
import * as vdiDeviceManagementSelector from 'ducks/vdiDeviceManagement/selectors';
import HelpArticle from 'components/HelpArticle';
import PageTabs from 'components/navTabs/PageTabs';
import PermissionRequired from 'components/PermissionRequired';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import PropTypes from 'prop-types';
import SearchBox from 'components/searchBox';
import ServerError from 'components/errors/ServerError';
import SubscriptionRequired from 'components/subscriptionRequired';

import {
  handleSearchText,
  tabConfiguration,
} from 'ducks/vdiAgentApp';
import {
  VdiTableAgentAppGA,
  VdiTableAgentAppLA,
} from './components';

function BasicCustomAppForm(props) {
  const {
    actions,
    t,
    accessPrivileges,
    accessSubscriptions,
    showAddForm,
    showViewForm,
    showEditForm,
  } = props;
  const location = useLocation();
  const { pathname } = location;
  let isGAPage = false;

  useEffect(() => {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.BRANCH_AND_CLOUD_GROUP });
    // dispatch(handleLoadFilters(t));
    // dispatch(toggleAdminPanel(false));
    // return dispatch(toggleForm(false));
  }, [pathname]);

  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);

  if (!hasBSubscription && !hasCSubscription) {
    return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
  }

  if (accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
    return <PermissionRequired accessPermissions={accessPrivileges} />;
  }
  const permKey = [constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_CLOUD_PROVISIONING];

  isGAPage = (pathname === `${BASE_LAYOUT}/administration/vdi/agent-app/ga`);
    
  return (
    <ServerError {...props}>
      {/* <Loading {...props} /> */}
      {(!showAddForm && !showEditForm && !showViewForm) && (
        <div className="main-container vdi-device-managenent">
          <HelpArticle article={HELP_ARTICLES.BRANCH_CONNECTOR_GROUP} />
          <div className="page-title header-3">
            {t('VDI_AGENT_APP')}
          </div>
          <Field
            id="pageTabs"
            name="pageTabs"
            component={PageTabs} />
          <div className="table-actions-container">
            <div className="actions-row">
              <div className="actions-items">
                <div className={`${accessPrivileges[permKey] === 'READ_ONLY' ? 'hide' : ''}`}>
                </div>
              </div>

              <div className="search-container">
                <SearchBox
                  placeholder={t('SEARCH')}
                  clickCallback={(value) => actions.handleSearchText(value, isGAPage)} />
              </div>
            </div>

            <div className="container-row-cc-group vdi-table-container">
              {isGAPage && <VdiTableAgentAppGA {...props} />}
              {!isGAPage && <VdiTableAgentAppLA {...props} />}
            </div>
          </div>
        </div>
      )}
    </ServerError>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    load: PropTypes.func,
    handleToggleForm: PropTypes.func,
    handleToggleDeleteForm: PropTypes.func,
    deleteBCGroupRowData: PropTypes.func,
  }),
  authType: PropTypes.string,
  t: PropTypes.func,
  accessPrivileges: PropTypes.shape({}),
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  showAddForm: PropTypes.bool,
  showViewForm: PropTypes.bool,
  showEditForm: PropTypes.bool,
};

BasicCustomAppForm.defaultProps = {
  actions: {
    load: null,
    handleToggleForm: null,
    handleToggleDeleteForm: null,
    deleteBCGroupRowData: null,
  },
  authType: '',
  t: (str) => str,
  accessPrivileges: {},
  accessSubscriptions: [],
  showAddForm: false,
  showViewForm: false,
  showEditForm: false,
};

const BasicCustomForm = reduxForm({
  form: 'vdiDeviceManagementMainForm',
  destroyOnUnmount: true,
  forceUnregisterOnUnmount: true,
})(BasicCustomAppForm);

const PartnerIntegrations = connect((state) => {
  const accessSubscriptions = loginSelectors.accessSubscriptionSelector(state);
  const hasBSubscription = hasBsku(accessSubscriptions);
  const hasCSubscription = hasCsku(accessSubscriptions);

  return ({
    initialValues: {
      pageTabs: `${BASE_LAYOUT}/administration/vdi/agent-app/ga`,
      tabConfiguration: tabConfiguration(hasBSubscription, hasCSubscription),
    },
  });
})(BasicCustomForm);

const mapStateToProps = (state) => ({
  ...vdiDeviceManagementSelector.baseSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    handleSearchText,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(PartnerIntegrations));

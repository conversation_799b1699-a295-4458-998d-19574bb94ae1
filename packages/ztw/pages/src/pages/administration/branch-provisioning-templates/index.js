import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import Modal from 'components/modal';
import EditConfirmationForm from 'components/EditConfirmationForm';
import AddNewButton from 'components/addNewButton';
import Loading from 'components/spinner/Loading';
import RBAC from 'components/rbac';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import ServerError from 'components/errors/ServerError';
import NavTabs from 'components/navTabs';
import SearchBox from 'components/searchBox';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import StatusConfirmationForm from 'components/DeleteConfirmationForm/provInfoStatusUpdateForm';
import * as constants from 'ducks/login/constants';
import * as ProvisioningTemplatesSelectors from 'ducks/provisioningTemplatesBranch/selectors';
import * as provisioningTemplateWizardBranchSelectors from 'ducks/provisioningTemplateWizardBranch/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import { HELP_ARTICLES, BASE_LAYOUT } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import { hasBsku, getReadOnly } from 'utils/helpers';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRefresh } from '@fortawesome/pro-regular-svg-icons';

import {
  toggleWizard,
  toggleEditAlertModal,
} from 'ducks/provisioningTemplateWizardBranch';

import {
  loadProvisioningTemplates,
  deleteProvisioningTemplate,
  toggleViewModal,
  handleCopyText,
  toggleDeleteForm,
  handleStatusChange,
  toggleStatusUpdateForm,
} from 'ducks/provisioningTemplatesBranch';
import {
  ProvisioningTemplatesForm,
  ProvisioningTemplateViewModal,
} from './components';

import ProvisioningTemplatesTable from './components/ProvisioningTemplatesTable';

function BranchProvisioningTemplates(props) {
  const {
    t,
    provisioningTemplatesData,
    load,
    toggleProvTemplateWizard,
    handleViewProvForm,
    formTitle,
    showViewForm,
    handleProvUrlCopyText,
    showDeleteForm,
    showEditAlert,
    showStatusUpdateForm,
    modalLoading,
    selectedRowID,
    selectedStatus,
    handleDelete,
    handleStatus,
    toggleStatusForm,
    toggleDeleteConfirmationForm,
    toggleEditAlertModalForm,
    accessPrivileges,
    authType,
    accessSubscriptions,
    history,
    hasNextPage,
    moreItemsLoading,
  } = props;
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const [isReadOnly, setIsReadOnly] = useState(false);
  const [permKey, setPermKey] = useState([]);
  const [hasBSubscription, setHasBSubscription] = useState(false);
  const [tabConfiguration, setTabConfiguration] = useState([]);

  useEffect(() => {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.BRANCH_PROVISIONING_TEMPLATES });
    load(true);
  }, []);

  const openModal = (event) => {
    event.preventDefault();
    toggleProvTemplateWizard(null, true, 'NEW');
  };

  useEffect(() => {
    if (params.get('URI')) {
      toggleProvTemplateWizard(null, true, 'NEW');
    }
  }, [location.search]);

  const isReadOnlyCheck = () => {
    setIsReadOnly((accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE' && accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING !== 'NONE') || getReadOnly(accessPrivileges.EDGE_CONNECTOR_TEMPLATE, authType));
  };

  const permKeyCheck = () => {
    setPermKey([constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_TEMPLATE, constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_CLOUD_PROVISIONING]);
  };

  const hasBSubscriptionCheck = () => {
    setHasBSubscription(hasBsku(accessSubscriptions));
  };

  const tabConfigurationCheck = () => {
    if (params.get('filter') === 'BC') {
      setTabConfiguration([
        {
          id: 'branch-provisioning-templates',
          title: t('BRANCH_CONFIGURATION'),
          to: `${BASE_LAYOUT}/administration/branch-provisioning-templates?filter=BC`,
        },
      ]);
    } else {
      setTabConfiguration([
        {
          id: 'branch-provisioning-templates',
          title: t('BRANCH_CONFIGURATION'),
          to: `${BASE_LAYOUT}/administration/branch-provisioning-templates`,
        },
        {
          id: 'provisioning-templates',
          title: t('CLOUD_PROVISIONING'),
          to: `${BASE_LAYOUT}/administration/provisioning-templates`,
        },
      ]);
    }
  };

  useEffect(() => {
    isReadOnlyCheck();
    permKeyCheck();
    hasBSubscriptionCheck();
    tabConfigurationCheck();
  }, [accessPrivileges, accessSubscriptions, t]);

  if (accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE' && accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
    return <PermissionRequired accessPermissions={accessPrivileges} />;
  }

  return (
    <div className="provisioning-template-page-container">
      <h1 className="page-title">
        {t('PROVISIONING_AND_CONFIGUATION')}
      </h1>
      <div className="main-container provisioning-template-header">
        <div className="configuration-nav-tab">
          <NavTabs
            tabConfiguration={tabConfiguration} />
        </div>
        <div className="actions-row refresh">
          <button
            type="button"
            className="refresh-button"
            onClick={() => load(true)}>
            <FontAwesomeIcon className="cloud-formation-options-link-icon fa-external-link fa-xs" icon={faRefresh} size="xs" />
            {t('REFRESH')}
          </button>
          <div className="search-container">
            <SearchBox placeholder={t('SEARCH')} clickCallback={() => load(true)} />
          </div>
        </div>
      </div>
      {!hasBSubscription ? <SubscriptionRequired accessSubscriptions={accessSubscriptions} />
        : (
          <div className="main-container provisioning-template-table">
            <div className={`sipg-fragment ${accessPrivileges[permKey] === 'READ_ONLY' ? 'hide' : ''}`}>
              {!isReadOnly && <AddNewButton label={t('ADD_BRANCH_CONNECTOR_PROV_TEMPLATE')} clickCallback={openModal} />}
            </div>
            <div className="cloud-provider-wrapper branch-templates">
              <RBAC privilege={permKey}>
                <Loading {...props}>
                  <HelpArticle article={HELP_ARTICLES.BRANCH_PROVISIONING_TEMPLATES} />
                  <ServerError {...props}>
                    <ProvisioningTemplatesTable
                      authType={authType}
                      accessPrivileges={accessPrivileges}
                      toggleDeleteConfirmationForm={toggleDeleteConfirmationForm}
                      handleViewProvForm={handleViewProvForm}
                      provisioningTemplatesData={provisioningTemplatesData}
                      toggleProvTemplateWizard={toggleProvTemplateWizard}
                      handleProvUrlCopyText={handleProvUrlCopyText}
                      moreItemsLoading={moreItemsLoading}
                      hasNextPage={hasNextPage}
                      toggleStatusForm={toggleStatusForm}
                      loadMore={load} />
                    <ProvisioningTemplatesForm />
                    <Modal
                      title={t('VIEW_BRANCH_PROVISIONING_TEMPLATE')}
                      isOpen={showViewForm}
                      styleClass="view-branch-connector provisioning-view-modal"
                      closeModal={() => handleViewProvForm(null, false)}>
                      <ProvisioningTemplateViewModal />
                    </Modal>
                    <Modal
                      title={t('DELETE_CONFIRMATION')}
                      isOpen={showDeleteForm}
                      closeModal={() => toggleDeleteConfirmationForm(null, false)}>
                      <DeleteConfirmationForm
                        modalLoading={modalLoading}
                        selectedRowID={selectedRowID}
                        handleCancel={toggleDeleteConfirmationForm}
                        handleDelete={handleDelete} />
                    </Modal>
                    <Modal
                      title={t('STATUS_UPDATE_CONFIRMATION')}
                      isOpen={showStatusUpdateForm}
                      closeModal={() => toggleStatusForm(false)}>
                      <StatusConfirmationForm
                        modalLoading={modalLoading}
                        selectedRowID={selectedRowID}
                        provisioningTemplatesData={provisioningTemplatesData}
                        selectedStatus={selectedStatus}
                        handleCancel={toggleStatusForm}
                        handleUpdate={handleStatus} />
                    </Modal>
                    <Modal
                      title={t('EDIT_CONFIRMATION')}
                      isOpen={showEditAlert}
                      styleClass="edit-accounts"
                      closeModal={() => toggleEditAlertModalForm(false)}>
                      <EditConfirmationForm
                        // modalLoading={modalLoading}
                        // selectedRowID={selectedRowID}
                        message={t('EDIT_CONFIRMATION_DEPLOYED_GATEWAY')}
                        handleCancel={() => toggleEditAlertModalForm(false)}
                        handleProceed={() => toggleProvTemplateWizard('PROCEED', true, 'EDIT')} />
                    </Modal>
                  </ServerError>
                </Loading>
              </RBAC>
            </div>
          </div>
        )}
    </div>
  );
}

BranchProvisioningTemplates.propTypes = {
  t: PropTypes.func,
  provisioningTemplatesData: PropTypes.arrayOf(PropTypes.shape()),
  load: PropTypes.func,
  toggleProvTemplateWizard: PropTypes.func,
  handleViewProvForm: PropTypes.func,
  formTitle: PropTypes.string,
  showViewForm: PropTypes.bool,
  handleProvUrlCopyText: PropTypes.func,
  showDeleteForm: PropTypes.bool,
  showEditAlert: PropTypes.bool,
  showStatusUpdateForm: PropTypes.bool,
  modalLoading: PropTypes.bool,
  selectedRowID: PropTypes.string,
  selectedStatus: PropTypes.string,
  handleDelete: PropTypes.func,
  handleStatus: PropTypes.func,
  toggleStatusForm: PropTypes.func,
  toggleDeleteConfirmationForm: PropTypes.func,
  toggleEditAlertModalForm: PropTypes.func,
  accessPrivileges: PropTypes.shape({
    EDGE_CONNECTOR_CLOUD_PROVISIONING: PropTypes.string,
    EDGE_CONNECTOR_TEMPLATE: PropTypes.string,
  }),
  authType: PropTypes.string,
  accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  history: PropTypes.shape(),
  hasNextPage: PropTypes.bool,
  moreItemsLoading: PropTypes.bool,
};

BranchProvisioningTemplates.defaultProps = {
  t: (str) => str,
  provisioningTemplatesData: null,
  load: noop,
  toggleProvTemplateWizard: noop,
  handleViewProvForm: noop,
  formTitle: '',
  showViewForm: false,
  handleProvUrlCopyText: noop,
  showDeleteForm: false,
  showEditAlert: false,
  showStatusUpdateForm: false,
  modalLoading: false,
  selectedRowID: null,
  selectedStatus: null,
  handleDelete: noop,
  handleStatus: noop,
  toggleStatusForm: noop,
  toggleDeleteConfirmationForm: noop,
  toggleEditAlertModalForm: noop,
  accessPrivileges: {},
  authType: '',
  accessSubscriptions: [],
  history: {},
  hasNextPage: false,
  moreItemsLoading: false,
};

const stateProps = (state) => ({
  ...ProvisioningTemplatesSelectors.default(state),
  modalLoading: ProvisioningTemplatesSelectors.modalLoadingSelector(state),
  selectedRowID: ProvisioningTemplatesSelectors.selectedRowIDSelector(state),
  selectedStatus: ProvisioningTemplatesSelectors.selectedRowProvInfoStatusSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  showEditAlert: provisioningTemplateWizardBranchSelectors.editAlertSelector(state),
});

const dispatchProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loadProvisioningTemplates,
    toggleProvTemplateWizard: toggleWizard,
    handleViewProvForm: toggleViewModal,
    handleProvUrlCopyText: handleCopyText,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    toggleEditAlertModalForm: toggleEditAlertModal,
    handleDelete: deleteProvisioningTemplate,
    handleStatus: handleStatusChange,
    toggleStatusForm: toggleStatusUpdateForm,
  }, dispatch);
  return actions;
};

export default connect(stateProps, dispatchProps)(withTranslation()(BranchProvisioningTemplates));

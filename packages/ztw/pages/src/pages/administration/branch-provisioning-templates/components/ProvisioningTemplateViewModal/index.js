// @flow

import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import withRouter from 'layout/withRouter';
import { reduxForm } from 'redux-form';
import { noop, isEmpty } from 'utils/lodash';
import { isHardwareAppliance } from 'utils/helpers';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import { FormFieldLabel } from 'components/label';

import { toggleViewModal, setManagementIpType } from 'ducks/provisioningTemplatesBranch';

import * as ProvisioningTemplatesSelectors from 'ducks/provisioningTemplatesBranch/selectors';

import {
  Name,
  Hypervisor,
  Description,
  ExistingLocation,
  LocationName,
  LocationCountry,
  LocationTemplate,
  ExistingGroup,
  ApplianceName,
  ApplianceSN,
  ApplianceDescription,
  GroupDescription,
  GroupName,
  GroupSize,
  HaDeviceStatus,
  CellularConfiguration,
  CellularDeploymentMode,
  CellularDeployment,
  ManagementIpAddress,
  ManagementDefaultGateway,
  ManagementPrimaryDNS,
  ManagementSecondaryDNS,
  HwManagementIpAddress,
  HwManagementDefaultGateway,
  HwManagementPrimaryDNS,
  HwManagementSecondaryDNS,
  ForwardDefaultGateway,
  ServiceVirtualIpAddress,
  LoadBalancerlIpAddress,
  ForwardPrimaryDNS,
  ForwardSecondaryDNS,
  ServiceIPAddress1,
  ServiceIPAddress2,
  ServiceIPAddress3,
  
  AppConnectorProvKey,
  AppConnectorProvKeyName,
  AppConnectorIpAddress,
  AppConnectorGateway,
  AppConnectorDNS1,
  AppConnectorDNS2,
} from '../ProvisioningTemplatesForm/components/GenericFilters';

export function BasicCustomAppForm(props) {
  const {
    actions,
    t,
    appData,
  } = props;
  const {
    provUrl,
    provUrlData,
    location,
    locationTemplate,
    deviceTemplate,
  } = appData;
  const { modelType } = deviceTemplate || {};
  const newLocation = location && location.id === 0;
  let formValues;
  if (location && newLocation) {
    formValues = { ...appData, locationName: location.name };
    formValues.country = location.extensions
      ? { id: location.extensions.country, name: location.extensions.country }
      : {};
    formValues.locationTemplateList = { name: locationTemplate && locationTemplate.name };
  }
  const isAppliance = isHardwareAppliance(modelType);
  const hasAppConnectorDetails = !isEmpty(appData.appConnectorDetails);

  useEffect(() => {
    actions.setManagementIpType(provUrlData);
  }, []);
  
  const handleCancelClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    const { cancelHandle } = actions;
    
    cancelHandle(null, false);
  };
   
  return (
    <form className="wizard-form bc-details">
      <div className="form-sections-container">
        <Loading {...props}>
          <ServerError {...props} size="small">
            <div className="form-section provisioning-url">
              <div className="input-container review prov-url full-width">
                <FormFieldLabel text={t('PROVISIONING_URL')} styleClass="no-margin-top" />
                <p className="disabled-input full-width">
                  <span>{provUrl}</span>
                </p>
              </div>
            </div>
            <div className="form-section">
              <div className="full-width">
                <div className="section">
                  <Name isProvisioningUrl {...props} />
                  <Hypervisor isProvisioningUrl {...props} />
                  <Description isProvisioningUrl {...props} />
                </div>
              </div>
            </div>
            
            {isAppliance && (
              <div className="form-section">
                <p className="review-section-title">{t('DEVICE_SELECTION')}</p>
                <div className="full-width">
                  <div className="section">
                    <ApplianceSN isProvisioningUrl {...props} />
                    <ApplianceName isProvisioningUrl {...props} />
                    <ApplianceDescription isProvisioningUrl {...props} />
                    <div className="input-container review half-width">
                      <FormFieldLabel
                        text={t('DEVICE_MODEL')}
                        styleClass="model-number" />
                      <p className="disabled-input">{appData?.provUrlData?.deviceTemplate?.modelType}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="form-section">
              <p className="review-section-title">{t('LOCATION')}</p>
              <div className="full-width">
                <div className="section">
                  <ExistingLocation
                    isProvisioningUrl
                    {...props}
                    showAddLocation={newLocation}
                    formValues={formValues} />
                  <LocationName
                    isProvisioningUrl
                    {...props}
                    showAddLocation={newLocation}
                    formValues={formValues} />
                  <LocationCountry
                    isProvisioningUrl
                    {...props}
                    showAddLocation={newLocation}
                    formValues={formValues} />
                  <LocationTemplate
                    isProvisioningUrl
                    {...props}
                    showAddLocation={newLocation}
                    formValues={formValues} />
                </div>
              </div>
            </div>
            
            <div className="form-section">
              <p className="review-section-title">{t('BC_GROUP_DETAILS')}</p>
              <div className="full-width">
                <div className="section">
                  <ExistingGroup isProvisioningUrl {...props} />
                  <GroupName isProvisioningUrl {...props} />
                  <GroupSize isProvisioningUrl {...props} />
                  <GroupDescription isProvisioningUrl {...props} />

                  <CellularConfiguration isProvisioningUrl {...props} />
                  <CellularDeploymentMode isProvisioningUrl {...props} />
                  <CellularDeployment isProvisioningUrl {...props} />
                </div>
              </div>
            </div>

            <div className="form-section">
              <p className="review-section-title">{t('BC_DETAILS')}</p>
              <div className="full-width">
                <div className="section">
                  <div className="section-inner-title">{t('MANAGEMENT_INTERFACE')}</div>
                  <HwManagementIpAddress isProvisioningUrl {...props} />
                  <HwManagementDefaultGateway isProvisioningUrl {...props} />
                  <HwManagementPrimaryDNS isProvisioningUrl {...props} />
                  <HwManagementSecondaryDNS isProvisioningUrl {...props} />

                  <ManagementIpAddress isProvisioningUrl {...props} />
                  <ManagementDefaultGateway isProvisioningUrl {...props} />
                  <ManagementPrimaryDNS isProvisioningUrl {...props} />
                  <ManagementSecondaryDNS isProvisioningUrl {...props} />
              
                  <div className="section-inner-title">{t('FORWARDING_INTERFACE')}</div>
                  <ForwardPrimaryDNS isProvisioningUrl {...props} />
                  <ForwardSecondaryDNS isProvisioningUrl {...props} />
                  <ForwardDefaultGateway isProvisioningUrl {...props} />
                  <LoadBalancerlIpAddress isProvisioningUrl {...props} />

                  <HaDeviceStatus isProvisioningUrl {...props} />
                  <ServiceVirtualIpAddress isProvisioningUrl {...props} />
                  
                  {/* <div className="separator-line" /> */}
                  <ServiceIPAddress1 isProvisioningUrl {...props} />
                  <ServiceIPAddress2 isProvisioningUrl {...props} />
                  <ServiceIPAddress3 isProvisioningUrl {...props} />
                </div>
              </div>
            </div>

            {hasAppConnectorDetails && (
              <div className="form-section">
                <p className="review-section-title">{t('APP_CONNECTOR')}</p>
                <div className="full-width">
                  <div className="section">
                    <div className="section-inner-title">{t('APP_CONNECTOR')}</div>
                    <AppConnectorProvKeyName isViewOnly {...props} />
                    <AppConnectorProvKey isViewOnly {...props} />
                    <AppConnectorIpAddress isProvisioningUrl {...props} />
                    <AppConnectorGateway isProvisioningUrl {...props} />
                    <AppConnectorDNS1 isProvisioningUrl {...props} />
                    <AppConnectorDNS2 isProvisioningUrl {...props} />
                  </div>
                </div>
              </div>
            )}

          </ServerError>
        </Loading>
      </div>
      <div className="modal-footer">
        <button type="submit" onClick={handleCancelClick} className="next primary-button">{t('CANCEL')}</button>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
    setManagementIpType: PropTypes.func,
  }),
  t: PropTypes.func,
  appData: PropTypes.shape(),
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
  },
  t: (str) => str,
  appData: {},
};

const ProvisioningTemplateViewModal = reduxForm({
  form: 'ProvisioningTemplateViewForm',
})(BasicCustomAppForm);

const mapStateToProps = (state) => {
  const appData = ProvisioningTemplatesSelectors.appDataSelector(state) || {};
  const { provUrlData = {} } = appData;
  const { bcGroup = {}, hyperVisors = '' } = provUrlData;
  const { cellularDeployment, cellularDeploymentMode } = bcGroup;
  const formValues = {
    ...appData,
    ...provUrlData,
    bcGroupName: bcGroup.name,
    onpremise: hyperVisors,
    cellularConfiguration: (cellularDeployment && cellularDeploymentMode) ? 'ENABLED' : 'DISABLED',
    cellularDeployment,
    cellularDeploymentMode,
  };

  return ({
    ...ProvisioningTemplatesSelectors.default(state),
    appData: formValues,
    formValues,
  });
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleViewModal,
    setManagementIpType,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(ProvisioningTemplateViewModal)));

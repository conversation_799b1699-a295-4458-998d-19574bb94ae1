// @flow

import React, { PureComponent } from 'react';
import { reduxForm } from 'redux-form';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel, FormSectionLabel } from 'components/label';
import { toUpper, noop } from 'utils/lodash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCopy } from '@fortawesome/pro-solid-svg-icons';
import {
  handleCopyText,
} from 'ducks/provisioningTemplatesBranch';
import * as provisioningTemplatesSelector from 'ducks/provisioningTemplatesBranch/selectors';

import {
  Name,
  Hypervisor,
  Description,
  ExistingLocation,
  LocationName,
  LocationCountry,
  LocationTemplate,
  ExistingGroup,
  GroupDescription,
  GroupName,
  GroupSize,
  HaDeviceStatus,
  ManagementIpAddress,
  ManagementDefaultGateway,
  ManagementPrimaryDNS,
  ManagementSecondaryDNS,
  ForwardDefaultGateway,
  ServiceVirtualIpAddress,
  LoadBalancerlIpAddress,
  ForwardPrimaryDNS,
  ForwardSecondaryDNS,
  ServiceIPAddress1,
  ServiceIPAddress2,
  ServiceIPAddress3,
} from '../GenericFilters';

export class ConfigureEdgeconnectorForm extends PureComponent {
  static propTypes = {
    handleSubmit: PropTypes.func,
    t: PropTypes.func,
    formValues: PropTypes.shape({
      platform: PropTypes.string,
    }),
    cloud: PropTypes.bool,
    cloudName: PropTypes.string,
    premiseName: PropTypes.string,
    provisioningTemplateData: PropTypes.shape(),
    handleCopyText1: PropTypes.func,
  };

  static defaultProps = {
    handleSubmit: (str) => str,
    t: (str) => str,
    formValues: {},
    cloud: false,
    cloudName: '',
    premiseName: '',
    provisioningTemplateData: {},
    handleCopyText1: null,
  };

  handleCopyProvUrlClick = (e) => {
    e.preventDefault();
    const { handleCopyText1 } = this.props;
    const parentElementText = e.currentTarget.parentElement.children[0].innerText;
    
    handleCopyText1(e.currentTarget.parentElement, parentElementText);
  };

  formatIpValues = (ip) => {
    return ip === '0.0.0.0' || ip === '***************' ? '---' : ip;
  };

  render() {
    const {
      handleSubmit,
      t,
      cloudName,
      cloud,
      premiseName,
      provisioningTemplateData,
      formValues,
    } = this.props;
  
    const { provUrl } = provisioningTemplateData;
  
    // update wizard values based on cloud / premise selection
    formValues.platform = cloud ? toUpper(cloudName) : toUpper(premiseName);
    
    return (
      <form onSubmit={handleSubmit} className="wizard-form bc-details">
        <div className="form-sections-container">
          <Loading {...this.props}>
            <ServerError {...this.props} size="small">
              <div className="review-changes-heading">
                <FormSectionLabel text={t('PROVISIONING_URL')} />
              </div>
              <div className="form-section provisioning-url">
                <div className="input-container review prov-url full-width">
                  <FormFieldLabel text={t('PROVISIONING_URL')} styleClass="no-margin-top" />
                  <p className="disabled-input">
                    <span>{provUrl}</span>
                    <span
                      className="copy-prov-url"
                      tabIndex="0"
                      onKeyDown={noop}
                      role="button"
                      onClick={this.handleCopyProvUrlClick}>
                      <FontAwesomeIcon icon={faCopy} size="lg" />
                      {t('COPY_PROVISIONING_URL')}
                    </span>
                  </p>
                </div>
              </div>
              <div className="form-section">
                <div className="full-width">
                  <div className="section">
                    <Name isProvisioningUrl {...this.props} />
                    <Hypervisor isProvisioningUrl {...this.props} />
                    <Description isProvisioningUrl {...this.props} />
                  </div>
                </div>
              </div>
            
              <div className="form-section">
                <p className="review-section-title">{t('LOCATION')}</p>
                <div className="full-width">
                  <div className="section">
                    <ExistingLocation isProvisioningUrl {...this.props} />
                    <LocationName isProvisioningUrl {...this.props} />
                    <LocationCountry isProvisioningUrl {...this.props} />
                    <LocationTemplate isProvisioningUrl {...this.props} />
                  </div>
                </div>
              </div>
            
              <div className="form-section">
                <p className="review-section-title">{t('BC_GROUP_DETAILS')}</p>
                <div className="full-width">
                  <div className="section">
                    <ExistingGroup isProvisioningUrl {...this.props} />
                    <GroupName isProvisioningUrl {...this.props} />
                    <GroupSize isProvisioningUrl {...this.props} />
                    <GroupDescription isProvisioningUrl {...this.props} />
                  </div>
                </div>
              </div>

              <div className="form-section">
                <p className="review-section-title">{t('BC_DETAILS')}</p>
                <div className="full-width">
                  <div className="section">
                    <div className="section-inner-title">{t('MANAGEMENT_INTERFACE')}</div>
                    <ManagementIpAddress isProvisioningUrl {...this.props} />
                    <ManagementDefaultGateway isProvisioningUrl {...this.props} />
                    <ManagementPrimaryDNS isProvisioningUrl {...this.props} />
                    <ManagementSecondaryDNS isProvisioningUrl {...this.props} />
              
                    <div className="section-inner-title">{t('FORWARDING_INTERFACE')}</div>
                    <ForwardPrimaryDNS isProvisioningUrl {...this.props} />
                    <ForwardSecondaryDNS isProvisioningUrl {...this.props} />
                    <ForwardDefaultGateway isProvisioningUrl {...this.props} />
                    <LoadBalancerlIpAddress isProvisioningUrl {...this.props} />
                    <HaDeviceStatus isProvisioningUrl {...this.props} />
                    <ServiceVirtualIpAddress isProvisioningUrl {...this.props} />
                  
                    <div className="separator-line" />
                    <ServiceIPAddress1 isProvisioningUrl {...this.props} />
                    <ServiceIPAddress2 isProvisioningUrl {...this.props} />
                    <ServiceIPAddress3 isProvisioningUrl {...this.props} />
                  </div>
                </div>
              </div>
            </ServerError>
          </Loading>
        </div>
        <div className="modal-footer">
          <button type="submit" className="next primary-button">{t('DONE')}</button>
        </div>
      </form>
    );
  }
}

let ProvisioningUrl = reduxForm({ // eslint-disable-line import/no-mutable-exports
  form: 'provisioningTemplateWizard',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(withTranslation()(ConfigureEdgeconnectorForm));

const mapStateToProps = (state) => ({
  ...provisioningTemplatesSelector.default(state),
  formValues: provisioningTemplatesSelector.formValuesSelector(state),
  formMeta: provisioningTemplatesSelector.formMetaSelector(state),
  formSyncErrors: provisioningTemplatesSelector.formSyncErrorsSelector(state),
  cloud: provisioningTemplatesSelector.cloudSelector(state),
  cloudName: provisioningTemplatesSelector.cloudNameSelector(state),
  premiseName: provisioningTemplatesSelector.premiseNameSelector(state),
  provisioningTemplateData: provisioningTemplatesSelector.provisioningTemplateDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    handleCopyText1: handleCopyText,
  }, dispatch);

  return actions;
};

ProvisioningUrl = connect(mapStateToProps, mapDispatchToProps)(ProvisioningUrl);

export default ProvisioningUrl;

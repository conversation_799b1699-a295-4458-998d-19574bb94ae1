// @flow

import React from 'react';
import { getFormValues, reduxForm } from 'redux-form';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { get, isEmpty, noop } from 'utils/lodash';
import * as provisioningTemplatesSelector from 'ducks/provisioningTemplatesBranch/selectors';
import * as provisioningTemplateWizardSelectors from 'ducks/provisioningTemplateWizardBranch/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import {
  reviewItems,
  getBCGroups,
  handleDnsIpTypeChange,
  handleDnsIpTypeChange2,
  handleLocationDropDownChangeChange,
} from 'ducks/provisioningTemplatesBranch';
import { updateWizardMode } from 'ducks/provisioningTemplateWizardBranch';
import Loading from 'components/spinner/Loading';
import ReviewGroup from 'components/reviewGroup';
import ServerError from 'components/errors/ServerError';
import { has5Gsku, isHardwareAppliance } from 'utils/helpers';
import portMapZthwHaImg from 'images/portMapZTHW_HA.png';
import portMapZthwNoHaImg from 'images/portMapZTHW_NO_HA.png';
import portMapZthwZPAHaImg from 'images/portMapZTHW_ZPA_HA.png';
import portMapZthwZPANoHaImg from 'images/portMapZTHW_ZPA_NO_HA.png';

import {
  Name,
  Hypervisor,
  Description,
  DeviceGroupType,
  // DevicePort,
  ExistingLocation,
  LocationName,
  LocationCountry,
  LocationTemplate,
  ExistingGroup,
  ApplianceName,
  ApplianceSN,
  ApplianceDescription,
  DeviceModel,
  GroupDescription,
  GroupName,
  GroupSize,
  HaDeviceStatus,
  CellularConfiguration,
  CellularDeploymentMode,
  CellularDeployment,
  ManagementIpAddress,
  ManagementDefaultGateway,
  ManagementPrimaryDNS,
  ManagementSecondaryDNS,
  HwManagementIpAddress,
  HwManagementDefaultGateway,
  HwManagementPrimaryDNS,
  HwManagementSecondaryDNS,
  ForwardDefaultGateway,
  ServiceVirtualIpAddress,
  LoadBalancerlIpAddress,
  ForwardPrimaryDNS,
  ForwardSecondaryDNS,
  ServiceIPAddress1,
  ServiceIPAddress2,
  ServiceIPAddress3,
  
  AppConnectorProvKey,
  AppConnectorProvKeyName,
  AppConnectorIpAddress,
  AppConnectorGateway,
  AppConnectorDNS1,
  AppConnectorDNS2,
} from '../GenericFilters';
import ReviewAsGateway from './ReviewAsGateway';

function ConfigureEdgeconnectorForm(props) {
  const {
    closeModal,
    handleSubmit,
    onSubmit,
    t,
    handleSetPage,
    previousPage,
    wizardFieldValues,
    existingBCGroup,
    existingLocation,
    premiseName,
    actions,
    initialValues,
    formValues,
    bcAutoType,
    bcHwAutoType,
    bcFwdType,
    bcDnsAutoType,
    bcDnsAutoType2,
    haDeployment,
    subscriptions,
  } = props;
  const { onpremise, hasAppConnector } = formValues || {};
  const isAppliance = isHardwareAppliance(onpremise);

  // update wizard values based on location type selection
  wizardFieldValues.existingLocation = existingLocation;
  wizardFieldValues.existingBCGroup = existingBCGroup;
  wizardFieldValues.bcAutoType = bcAutoType;
  wizardFieldValues.bcHwAutoType = bcHwAutoType;
  wizardFieldValues.haDeployment = haDeployment;
  wizardFieldValues.bcFwdType = bcFwdType;
  wizardFieldValues.bcDnsAutoType = bcDnsAutoType;
  wizardFieldValues.bcDnsAutoType2 = bcDnsAutoType2;
  wizardFieldValues.premiseName = premiseName;

  if (isEmpty(initialValues)) {
    actions.updateMode('EDIT');
  }

  const deployAsGateway = get(formValues, 'provUrlData.deviceTemplate.deployAsGateway', 'NO');
  if (isAppliance && deployAsGateway === 'YES') {
    return <ReviewAsGateway {...props} />;
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="wizard-form bc-details">
      <div className="form-sections-container">
        <Loading {...props}>
          <ServerError {...props} size="small">
            <div className="form-section">
              <div className="bc-provisioning-general-info-title">
                {t('REVIEW')}
                <div className="bc-provisioning-general-info-description">
                  {t('REVIEW_ENSURE_INFORMATION')}
                </div>
              </div>
            </div>
            <ReviewGroup title={t('GENERAL_INFORMATION')} handleEdit={() => handleSetPage(0)}>
              <Name isProvisioningUrl {...props} />
              <Hypervisor isProvisioningUrl {...props} />
              <DeviceModel isProvisioningUrl {...props} />
              <Description isProvisioningUrl {...props} />
            </ReviewGroup>
            {isAppliance && (
              <div className="portMap-container">
                <div className="separator-line" />
                {haDeployment && hasAppConnector === 'DISABLED' && <img src={portMapZthwHaImg} className="portMap-image" alt="portMap ZT600 ZT800 with HA" />}
                {!haDeployment && hasAppConnector === 'DISABLED' && <img src={portMapZthwNoHaImg} className="portMap-image" alt="portMap ZT600 ZT800 without HA" />}
                {haDeployment && hasAppConnector !== 'DISABLED' && <img src={portMapZthwZPAHaImg} className="portMap-image" alt="portMap ZT600 ZT800 with HA" />}
                {!haDeployment && hasAppConnector !== 'DISABLED' && <img src={portMapZthwZPANoHaImg} className="portMap-image" alt="portMap ZT600 ZT800 without HA" />}
                <div className="separator-line" />
              </div>
            )}
    
            <ReviewGroup title={t('LOCATION')} handleEdit={() => handleSetPage(1)}>
              <DeviceGroupType isProvisioningUrl {...props} />
              <ExistingLocation isProvisioningUrl {...props} />
              <LocationName isProvisioningUrl {...props} />
              <LocationCountry isProvisioningUrl {...props} />
              <LocationTemplate isProvisioningUrl {...props} />
            </ReviewGroup>

            <ReviewGroup title={t('BC_GROUP_DETAILS')} handleEdit={() => handleSetPage(2)}>
              <ExistingGroup isProvisioningUrl {...props} />
              <GroupName isProvisioningUrl {...props} />
              <GroupSize isProvisioningUrl {...props} />
              <GroupDescription isProvisioningUrl {...props} />

              {has5Gsku(subscriptions) && (
                <>
                  <CellularConfiguration isProvisioningUrl {...props} />
                  <CellularDeploymentMode isProvisioningUrl {...props} />
                  <CellularDeployment isProvisioningUrl {...props} />
                </>
              )}

            </ReviewGroup>

            <ReviewGroup title={isAppliance ? t('DEVICE_DETAILS') : t('BC_DETAILS')} handleEdit={() => handleSetPage(3)}>

              {isAppliance && (
                <>
                  <ApplianceSN isProvisioningUrl {...props} />
                  <ApplianceName isProvisioningUrl {...props} />
                  <ApplianceDescription isProvisioningUrl {...props} />
                </>
              )}
              <div className="review-section-title">{t('MANAGEMENT_INTERFACE')}</div>
              {/* <DevicePort isProvisioningUrl {...props} port="Port0-MGT" /> */}
              <HwManagementIpAddress isProvisioningUrl {...props} />
              <HwManagementDefaultGateway isProvisioningUrl {...props} />
              <HwManagementPrimaryDNS isProvisioningUrl {...props} />
              <HwManagementSecondaryDNS isProvisioningUrl {...props} />

              <ManagementIpAddress isProvisioningUrl {...props} />
              <ManagementDefaultGateway isProvisioningUrl {...props} />
              <ManagementPrimaryDNS isProvisioningUrl {...props} />
              <ManagementSecondaryDNS isProvisioningUrl {...props} />
              
              <div className="review-section-title">{t('FORWARDING_INTERFACE')}</div>
              {/* <DevicePort isProvisioningUrl {...props} port="Port1-LAN" /> */}
              <ForwardPrimaryDNS isProvisioningUrl {...props} />
              <ForwardSecondaryDNS isProvisioningUrl {...props} />
              <ForwardDefaultGateway isProvisioningUrl {...props} />

              <LoadBalancerlIpAddress isProvisioningUrl {...props} />
              <HaDeviceStatus isProvisioningUrl {...props} />
              <ServiceVirtualIpAddress isProvisioningUrl {...props} />
                  
              <div className="review-section-title">{t('SERVICE_IP_ADDRESS')}</div>
              <ServiceIPAddress1 isProvisioningUrl {...props} />
              {/* <DevicePort isProvisioningUrl {...props} port="Port1-WAN" portNum="1" /> */}
              <ServiceIPAddress2 isProvisioningUrl {...props} />
              {/* <DevicePort isProvisioningUrl {...props} port="Port1-WAN" portNum="2" /> */}
              <ServiceIPAddress3 isProvisioningUrl {...props} />
              {/* <DevicePort isProvisioningUrl {...props} port="Port1-WAN" portNum="3" /> */}
            </ReviewGroup>
            
            <ReviewGroup title={t('APP_CONNECTOR')} handleEdit={() => handleSetPage(isAppliance ? 5 : 4)}>
              <AppConnectorProvKeyName isProvisioningUrl {...props} />
              <AppConnectorProvKey isProvisioningUrl {...props} />
              {hasAppConnector === 'ENABLED' && (
                <>
                  <AppConnectorIpAddress isProvisioningUrl {...props} />
                  <AppConnectorGateway isProvisioningUrl {...props} />
                  <AppConnectorDNS1 isProvisioningUrl {...props} />
                  <AppConnectorDNS2 isProvisioningUrl {...props} />
                </>
              )}
            </ReviewGroup>
          </ServerError>
        </Loading>
      </div>
      <div className="modal-footer">
        <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
        <div>
          <button type="button" className="previous secondary-button" onClick={previousPage}>{t('BACK')}</button>
          <button type="submit" className="next primary-button">{t('SAVE')}</button>
        </div>
      </div>
    </form>
  );
}

ConfigureEdgeconnectorForm.propTypes = {
  actions: PropTypes.shape({
    setFormData: PropTypes.func,
    updateMode: PropTypes.func,
  }),
  bcAutoType: PropTypes.bool,
  bcHwAutoType: PropTypes.bool,
  bcDnsAutoType: PropTypes.bool,
  bcDnsAutoType2: PropTypes.bool,
  bcFwdType: PropTypes.bool,
  closeModal: PropTypes.func,
  existingBCGroup: PropTypes.bool,
  existingLocation: PropTypes.bool,
  formValues: PropTypes.shape({}),
  haDeployment: PropTypes.bool,
  handleSetPage: PropTypes.func,
  handleSubmit: PropTypes.func,
  initialValues: PropTypes.shape(),
  loading: PropTypes.bool,
  onSubmit: PropTypes.func,
  premiseName: PropTypes.string,
  previousPage: PropTypes.func,
  subscriptions: PropTypes.arrayOf(PropTypes.string),
  t: PropTypes.func,
  wizardFieldValues: PropTypes.shape(),
  
};

ConfigureEdgeconnectorForm.defaultProps = {
  actions: {
    setFormData: noop,
    updateMode: noop,
  },
  closeModal: (str) => str,
  existingBCGroup: false,
  existingLocation: false,
  formValues: {},
  haDeployment: true,
  handleSetPage: (str) => str,
  handleSubmit: (str) => str,
  loading: false,
  onSubmit: noop,
  premiseName: '',
  previousPage: (str) => str,
  t: (str) => str,
  wizardFieldValues: {},
};

const mapStateToProps = (state) => ({
  ...provisioningTemplatesSelector.default(state),
  formValues: provisioningTemplatesSelector.formValuesSelector(state),
  formMeta: provisioningTemplatesSelector.formMetaSelector(state),
  formSyncErrors: provisioningTemplatesSelector.formSyncErrorsSelector(state),
  wizardFieldValues: getFormValues('provisioningTemplateWizard')(state),
  existingBCGroup: provisioningTemplatesSelector.bcGroupTypeSelector(state),
  existingLocation: provisioningTemplatesSelector.locationTypeSelector(state),
  bcAutoType: provisioningTemplatesSelector.bcAutoTypeSelector(state),
  bcHwAutoType: provisioningTemplatesSelector.bcHwAutoTypeSelector(state),
  bcFwdType: provisioningTemplatesSelector.bcFwdTypeSelector(state),
  bcDnsAutoType: provisioningTemplatesSelector.bcDnsAutoTypeSelector(state),
  bcDnsAutoType2: provisioningTemplatesSelector.bcDnsAutoTypeSelector2(state),
  premiseName: provisioningTemplatesSelector.premiseNameSelector(state),
  initialValues: provisioningTemplateWizardSelectors.dataSelectorForReview(state),
  configData: loginSelectors.configDataSelector(state),
  subscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateMode: updateWizardMode,
    reviewItems,
    getBCGroups,
    handleDnsIpTypeChange,
    handleDnsIpTypeChange2,
    locationOnChange: handleLocationDropDownChangeChange,
  }, dispatch);

  return {
    actions,
  };
};

const Review = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'provisioningTemplateWizard',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(withTranslation()(ConfigureEdgeconnectorForm)));

export default Review;

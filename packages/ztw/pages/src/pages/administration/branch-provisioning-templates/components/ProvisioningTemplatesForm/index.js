/* eslint-disable react/jsx-handler-names */
// @flow

import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useLocation } from 'react-router-dom';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import Modal from 'react-modal';
import { withTranslation } from 'react-i18next';
import { getFormValues, change, updateSyncErrors } from 'redux-form';
import withRouter from 'layout/withRouter';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';

import { HELP_ARTICLES, isOneUI } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import { isHardwareAppliance } from 'utils/helpers';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import {
  toggleWizard,
  saveProvisioningTemplate,
  setWizardActiveNavPage,
  handleProvisioningPageClick,
} from 'ducks/provisioningTemplateWizardBranch';

import {
  resetFormValues,
} from 'ducks/provisioningTemplatesBranch';

import * as provisioningTemplateWizardSelectors from 'ducks/provisioningTemplateWizardBranch/selectors';
import * as provisioningTemplatesSelectors from 'ducks/provisioningTemplatesBranch/selectors';

import {
  // ProvName,
  AppConnector,
  BCDetails,
  BCGroupDetails,
  Cloud,
  Location,
  Review,
  WizardNav,
} from './components';

const getWizardNavConfig = (isAppliance) => {
  if (isAppliance) {
    return [
      'GENERAL_INFORMATION',
      'LOCATION',
      'BC_GROUP',
      'DEVICE_DETAILS',
      'APP_CONNECTOR',
      'REVIEW',
    ];
  }
  return [
    'GENERAL_INFORMATION',
    'LOCATION',
    'BC_GROUP',
    'BC_DETAILS',
    'APP_CONNECTOR',
    'REVIEW',
  ];
};

function ProvisioningTemplatesForm(props) {
  const {
    activePage,
    enableGoToPage,
    isProvTemplateWizardOpen,
    mode,
    onpremise,
    deployAsGateway,
    t,
  } = props;
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const redirectURL = params.get('URI');
  const isAppliance = isHardwareAppliance(onpremise);
  const wizardNavConfig = getWizardNavConfig(isAppliance);

  useEffect(() => {
    PersistentStorage.setItem({
      [LS_HELP_ARTICLE]: HELP_ARTICLES.ADD_BRANCH_PROVISIONING_TEMPLATE,
    });
    Modal.setAppElement('#r-app');
    return () => {
      const { actions: { toggleWizardModal } } = props;
  
      if (isProvTemplateWizardOpen) {
        toggleWizardModal(false, null, null);
      }
    };
  }, []);
   
  const closeModal = () => {
    const { actions: { toggleWizardModal, resetReduxFormValues } } = props;
    resetReduxFormValues();
    // toggleWizardModal(false, null, null);
    // navigate to appliance / locations page based on param
    if (isOneUI) {
      console.log('this is one ui');
    }
    const searchParam = window.location.href.split('?')[1];
    if (searchParam) {
      const uriComponents = searchParam.split('&');

      if (searchParam.indexOf('location-id') > -1 && searchParam.indexOf('location-name') > -1) {
        const locationID = uriComponents[0] && uriComponents[0].split('=')[1];
        const locationName = uriComponents[1] && uriComponents[1].split('=')[1];
        const assignUrl = `/locations/${locationID}?name=${locationName}&tab=appliances`;
        window.location.assign(assignUrl);
      } else if (searchParam.indexOf('URI') > -1 && searchParam.includes('appliances')) {
        window.location.assign('/appliances');
      } else {
        toggleWizardModal(false, null, null);
      }
    } else {
      toggleWizardModal(false, null, null);
    }
  };

  const handleSetPage = (pg) => {
    // eslint-disable-next-line
    const { actions: { setProvTemplateWizardActive } } = props;

    setProvTemplateWizardActive(pg);
  };

  const handleNextPage = () => {
    // eslint-disable-next-line
    const { actions: { setProvTemplateWizardActive }, activePage } = props;

    return setProvTemplateWizardActive(activePage + 1);
  };

  const previousPage = async () => {
    // eslint-disable-next-line
    const { actions: { updateFormErrors, setProvTemplateWizardActive }, activePage } = props;
    
    setProvTemplateWizardActive(activePage - 1);
    return updateFormErrors('provisioningTemplateWizard', {});
  };

  const goToPage = (pageNum) => {
    // eslint-disable-next-line
    const { actions: { setProvTemplateWizardActive }, enableGoToPage } = props;

    if (!enableGoToPage) {
      return;
    }

    setProvTemplateWizardActive(pageNum);
  };

  const handleSubmit = (values) => {
    // eslint-disable-next-line no-shadow
    const { actions: { saveProvTemplate }, mode } = props;

    saveProvTemplate(mode, redirectURL);
  };

  // const handleClickDone = (values) => {
  //   const { actions: { handleDoneClick, resetReduxFormValues } } = props;
  //   resetReduxFormValues();
  //   handleDoneClick(values);
  // };
  
  const renderForm = () => {
    if (isAppliance) {
      switch (activePage) {
      case 0:
        return (
          <Cloud
            onSubmit={handleNextPage}
            closeModal={closeModal} />
        );
      // case 1:
      //   return (
      //     <ApplianceDetails
      //       onSubmit={handleNextPage}
      //       previousPage={previousPage}
      //       closeModal={closeModal} />
      //   );
      case 1:
        return (
          <Location
            onSubmit={handleNextPage}
            previousPage={previousPage}
            closeModal={closeModal} />
        );
      case 2:
        return (
          <BCGroupDetails
            onSubmit={handleNextPage}
            previousPage={previousPage}
            closeModal={closeModal} />
        );

      case 3:
        return (
          <BCDetails
            onSubmit={handleNextPage}
            previousPage={previousPage}
            closeModal={closeModal} />
        );
      case 4:
        return (
          <AppConnector
            onSubmit={handleNextPage}
            previousPage={previousPage}
            closeModal={closeModal} />
        );
      case 5:
        return (
          <Review
            onSubmit={handleSubmit}
            handleSetPage={handleSetPage}
            previousPage={previousPage}
            closeModal={closeModal} />
        );

      default:
        return null;
      }
    } else {
      switch (activePage) {
      case 0:
        return (
          <Cloud
            onSubmit={handleNextPage}
            closeModal={closeModal} />
        );
      case 1:
        return (
          <Location
            onSubmit={handleNextPage}
            previousPage={previousPage}
            closeModal={closeModal} />
        );
      case 2:
        return (
          <BCGroupDetails
            onSubmit={handleNextPage}
            previousPage={previousPage}
            closeModal={closeModal} />
        );
      case 3:
        return (
          <BCDetails
            onSubmit={handleNextPage}
            previousPage={previousPage}
            closeModal={closeModal} />
        );
      case 4:
        return (
          <AppConnector
            onSubmit={handleNextPage}
            previousPage={previousPage}
            closeModal={closeModal} />
        );
      case 5:
        return (
          <Review
            onSubmit={handleSubmit}
            handleSetPage={handleSetPage}
            previousPage={previousPage}
            closeModal={closeModal} />
        );
  
      default:
        return null;
      }
    }
  };

  let label;

  switch (mode) {
  case 'NEW':
  case 'PRESET':
    label = t('ADD_BC_PROVISIONING_TEMPLATE');
    break;

  case 'EDIT':
    label = t('EDIT_BC_PROVISIONING_TEMPLATE');
    break;

  default:
    label = '';
    break;
  }
  if (isProvTemplateWizardOpen) {
    return (
      <div className="edgeconnector-page">
        {
          !isOneUI && (
            <div className={`back-to-ec ${enableGoToPage ? '' : 'hide'}`}>
              <FontAwesomeIcon icon={faArrowLeft} size="lg" onClick={closeModal} />
              {label}
            </div>
          )
        }
        <div className="edgeconnector-modal">
          <div className="modal-content modal-body branch-provisioning-modal-content">
            <Loading {...props}>
              <HelpArticle article={HELP_ARTICLES.ADD_BRANCH_PROVISIONING_TEMPLATE} />
              <ServerError {...props}>
                <WizardNav
                  activePage={activePage}
                  deployAsGateway={deployAsGateway}
                  goToPage={goToPage}
                  wizardNavConfig={wizardNavConfig} />
                {renderForm(activePage)}
              </ServerError>
            </Loading>
          </div>
        </div>
      </div>
    );
  }
  return '';
}

ProvisioningTemplatesForm.propTypes = {
  actions: PropTypes.shape(),
  activePage: PropTypes.number,
  deployAsGateway: PropTypes.bool,
  enableGoToPage: PropTypes.bool,
  history: PropTypes.shape(),
  isProvTemplateWizardOpen: PropTypes.bool,
  match: PropTypes.shape(),
  mode: PropTypes.string,
  name: PropTypes.string,
  onpremise: PropTypes.string,
  t: PropTypes.func,
};

ProvisioningTemplatesForm.defaultProps = {
  actions: null,
  activePage: 0,
  history: {},
  match: {},
  name: '',
  mode: 'NEW',
  isProvTemplateWizardOpen: false,
  t: (str) => str,
};

const mapStateToProps = (state) => ({
  ...provisioningTemplateWizardSelectors.default(state),
  ...getFormValues('provisioningTemplateWizard')(state),
  enableGoToPage: provisioningTemplatesSelectors.enableGoToPageSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleWizardModal: toggleWizard,
    updateFormState: change,
    updateFormErrors: updateSyncErrors,
    saveProvTemplate: saveProvisioningTemplate,
    setProvTemplateWizardActive: setWizardActiveNavPage,
    handleDoneClick: handleProvisioningPageClick,
    resetReduxFormValues: resetFormValues,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(ProvisioningTemplatesForm)));

// @flow

import React from 'react';
import { getFormValues, reduxForm } from 'redux-form';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { get, isEmpty, noop } from 'utils/lodash';
import * as provisioningTemplatesSelector from 'ducks/provisioningTemplatesBranch/selectors';
import * as provisioningTemplateWizardSelectors from 'ducks/provisioningTemplateWizardBranch/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import {
  reviewItems,
  getBCGroups,
  handleDnsIpTypeChange,
  handleDnsIpTypeChange2,
  handleLocationDropDownChangeChange,
  onShowClick,
} from 'ducks/provisioningTemplatesBranch';
import { updateWizardMode } from 'ducks/provisioningTemplateWizardBranch';
import Loading from 'components/spinner/Loading';
import ReviewGroup from 'components/reviewGroup';
import ServerError from 'components/errors/ServerError';
import { has5Gsku } from 'utils/helpers';

import {
  AppConnectorProvKey,
  AppConnectorProvKeyName,
  ApplianceDescription,
  ApplianceName,
  ApplianceSN,
  CellularConfiguration,
  CellularDeployment,
  CellularDeploymentMode,
  Description,
  DeviceGroupType,
  DeviceModel,
  DHCP,
  ExistingGroup,
  ExistingLocation,
  GroupDescription,
  GroupName,
  GroupSize,
  HwManagementDefaultGateway,
  HwManagementIpAddress,
  HwManagementPrimaryDNS,
  HwManagementSecondaryDNS,
  Hypervisor,
  LocationCountry,
  LocationName,
  LocationTemplate,
  Name,
  NwInterface,
  NwInterfacePrimaryDNS,
  NwInterfaceSecondaryDNS,
  Shutdown,
  ShutdownSubInterface,
  StaticRoute,
  SubInterfaceMTU,
  SubInterfaceReview,
  TrafficDistribution,
  UseWanDnsServer,
} from '../GenericFilters';

function ConfigureEdgeconnectorForm(props) {
  const {
    closeModal,
    handleSubmit,
    onSubmit,
    t,
    handleSetPage,
    previousPage,
    wizardFieldValues,
    existingBCGroup,
    existingLocation,
    premiseName,
    actions,
    initialValues,
    formValues,
    bcAutoType,
    bcHwAutoType,
    bcFwdType,
    bcDnsAutoType,
    bcDnsAutoType2,
    haDeployment,
    subscriptions,
  } = props;
  const { onpremise } = formValues || {};
  const modelType = onpremise;

  // update wizard values based on location type selection
  wizardFieldValues.existingLocation = existingLocation;
  wizardFieldValues.existingBCGroup = existingBCGroup;
  wizardFieldValues.bcAutoType = bcAutoType;
  wizardFieldValues.bcHwAutoType = bcHwAutoType;
  wizardFieldValues.haDeployment = haDeployment;
  wizardFieldValues.bcFwdType = bcFwdType;
  wizardFieldValues.bcDnsAutoType = bcDnsAutoType;
  wizardFieldValues.bcDnsAutoType2 = bcDnsAutoType2;
  wizardFieldValues.premiseName = premiseName;

  if (isEmpty(initialValues)) {
    actions.updateMode('EDIT');
  }

  const deployGateway = get(formValues, 'provUrlData.deployGateway', null);
  const numInterfaces = deployGateway?.netInterface?.length || 2;
  const staticRoute = deployGateway?.staticRoute || [];

  const loadInterface = (type) => {
    let node;
    let childnode;
      
    for (let i = 0; i < numInterfaces; i += 1) {
      const isShowMore = get(formValues, `provUrlData.deployGateway.netInterface[${i}].isShowMore`, '');
      if (deployGateway?.netInterface && deployGateway?.netInterface[i]?.type === type) {
        const { subInterface } = deployGateway?.netInterface[i] || {};
        // const isPort = get(formValues, `${fieldName}.vlanId`, []);
        childnode = (
          ((type === 'WAN') || (type === 'LAN')
          ) && (
            <div className="bc-details-panel-container">
              {/* <div className="bc-details-panel-left nw-interface">
                <p className="disabled-input">
                  {t('PORT_DETAILS')}
                </p>
              </div> */}
              <div className="bc-details-panel-right inline-block full-width">
                <NwInterface
                  {...props}
                  isProvisioningUrl
                  key={i}
                  fieldName={`provUrlData.deployGateway.netInterface[${i}].name`}
                  showMore={`provUrlData.deployGateway.netInterface[${i}].isShowMore`}
                  type={type}
                  index={i} />
                {isShowMore && type === 'LAN' && (<ShutdownSubInterface {...props} isProvisioningUrl fieldName={`provUrlData.deployGateway.netInterface[${i}].shutdown`} />)}
                {isShowMore && <SubInterfaceMTU {...props} isPort isReview fieldName={`provUrlData.deployGateway.netInterface[${i}].mtu`} />}

                <div className="port-no-separator-line" />
                {isShowMore && subInterface && subInterface.map((x, j) => (
                  <SubInterfaceReview
                    {...props}
                    isProvisioningUrl
                    // eslint-disable-next-line react/no-array-index-key
                    key={`Interface[${i}]SubInterface[${j}]`}
                    fieldName={`provUrlData.deployGateway.netInterface[${i}].subInterface[${j}]`}
                    index={i}
                    netIfIndex={i}
                    subIfIndex={j}
                    type={type} />
                ))}
              </div>
            </div>
          )
        );
        node = [node, childnode];
      }
    }
    return <>{node}</>;
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="wizard-form bc-details">
      <div className="form-sections-container">
        <Loading {...props}>
          <ServerError {...props} size="small">
            <div className="form-section">
              <div className="bc-provisioning-general-info-title">
                {t('REVIEW')}
                <div className="bc-provisioning-general-info-description">
                  {t('REVIEW_ENSURE_INFORMATION')}
                </div>
              </div>
            </div>
            <ReviewGroup title={t('GENERAL_INFORMATION')} handleEdit={() => handleSetPage(0)}>
              <Name isProvisioningUrl {...props} />
              <Hypervisor isProvisioningUrl {...props} />
              <DeviceModel isProvisioningUrl {...props} />
              <Description isProvisioningUrl {...props} />
            </ReviewGroup>
     
            <ReviewGroup title={t('LOCATION')} handleEdit={() => handleSetPage(1)}>
              <DeviceGroupType isProvisioningUrl {...props} />
              <ExistingLocation isProvisioningUrl {...props} />
              <LocationName isProvisioningUrl {...props} />
              <LocationCountry isProvisioningUrl {...props} />
              <LocationTemplate isProvisioningUrl {...props} />
            </ReviewGroup>

            <ReviewGroup title={t('BC_GROUP_DETAILS')} handleEdit={() => handleSetPage(2)}>
              <ExistingGroup isProvisioningUrl {...props} />
              <GroupName isProvisioningUrl {...props} />
              <GroupSize isProvisioningUrl {...props} />
              <GroupDescription isProvisioningUrl {...props} />

              {has5Gsku(subscriptions) && (
                <>
                  <CellularConfiguration isProvisioningUrl {...props} />
                  <CellularDeploymentMode isProvisioningUrl {...props} />
                  <CellularDeployment isProvisioningUrl {...props} />
                </>
              )}

            </ReviewGroup>

            <ReviewGroup title={t('DEVICE_DETAILS')} handleEdit={() => handleSetPage(3)}>

              <ReviewGroup title={t('SYSTEM_SETTINGS')} styleClass="sub-review-group">
                <div className="review-sub-section-title">{t('DEVICE_MODEL')}</div>
                <ApplianceSN isProvisioningUrl {...props} />
                <ApplianceName isProvisioningUrl {...props} />
                <ApplianceDescription isProvisioningUrl {...props} />

                <div className="review-sub-section-title">{`${t('MANAGEMENT_INTERFACE')} (${modelType === 'ZT800' ? t('GE3') : t('GE1')})`}</div>
                <Shutdown {...props} isProvisioningUrl isOptionGroup isMVP1 />
                <DHCP {...props} isProvisioningUrl isOptionGroup isMVP1 />
                <HwManagementIpAddress {...props} isProvisioningUrl isMVP1 />
                <HwManagementDefaultGateway {...props} isProvisioningUrl isMVP1 />
                <HwManagementPrimaryDNS {...props} isProvisioningUrl isMVP1 />
                <HwManagementSecondaryDNS {...props} isProvisioningUrl isMVP1 />

              </ReviewGroup>

              <ReviewGroup title={t('WAN')} styleClass="sub-review-group">
                {loadInterface('WAN')}
                <TrafficDistribution
                  {...props}
                  isProvisioningUrl
                  fieldName="provUrlData.deployGateway.wanTrafficDistribution" />
              </ReviewGroup>
              
              <ReviewGroup title={t('LAN')} styleClass="sub-review-group">
                {loadInterface('LAN')}
                <UseWanDnsServer {...props} isProvisioningUrl fieldName="provUrlData.deployGateway.lan.useWanDns" />
                <NwInterfacePrimaryDNS {...props} isProvisioningUrl fieldName="provUrlData.deployGateway.lan.dns[0]" />
                <NwInterfaceSecondaryDNS {...props} isProvisioningUrl fieldName="provUrlData.deployGateway.lan.dns[1]" />
              </ReviewGroup>

              {!isEmpty(staticRoute[0].route) && (
                <ReviewGroup title={t('ROUTING')} styleClass="sub-review-group">
                  {staticRoute && staticRoute.map((item, i) => (
                    <StaticRoute
                      {...props}
                      isProvisioningUrl
                      // eslint-disable-next-line react/no-array-index-key
                      key={`staticRoute[${i}]`}
                      fieldName={`provUrlData.deployGateway.staticRoute[${i}]`}
                      index={i} />
                  ))}
                </ReviewGroup>
              )}
              
            </ReviewGroup>
            
            <ReviewGroup title={t('APP_CONNECTOR')} handleEdit={() => handleSetPage(5)}>
              <AppConnectorProvKeyName isProvisioningUrl {...props} />
              <AppConnectorProvKey isProvisioningUrl {...props} />
            </ReviewGroup>
          </ServerError>
        </Loading>
      </div>
      <div className="modal-footer">
        <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
        <div>
          <button type="button" className="previous secondary-button" onClick={previousPage}>{t('BACK')}</button>
          <button type="submit" className="next primary-button">{t('SAVE')}</button>
        </div>
      </div>
    </form>
  );
}

ConfigureEdgeconnectorForm.propTypes = {
  actions: PropTypes.shape({
    setFormData: PropTypes.func,
    updateMode: PropTypes.func,
  }),
  bcAutoType: PropTypes.bool,
  bcHwAutoType: PropTypes.bool,
  bcDnsAutoType: PropTypes.bool,
  bcDnsAutoType2: PropTypes.bool,
  bcFwdType: PropTypes.bool,
  closeModal: PropTypes.func,
  existingBCGroup: PropTypes.bool,
  existingLocation: PropTypes.bool,
  formValues: PropTypes.shape({}),
  haDeployment: PropTypes.bool,
  handleSetPage: PropTypes.func,
  handleSubmit: PropTypes.func,
  initialValues: PropTypes.shape(),
  loading: PropTypes.bool,
  onSubmit: PropTypes.func,
  premiseName: PropTypes.string,
  previousPage: PropTypes.func,
  subscriptions: PropTypes.arrayOf(PropTypes.string),
  t: PropTypes.func,
  wizardFieldValues: PropTypes.shape(),
  
};

ConfigureEdgeconnectorForm.defaultProps = {
  actions: {
    setFormData: noop,
    updateMode: noop,
  },
  closeModal: (str) => str,
  existingBCGroup: false,
  existingLocation: false,
  formValues: {},
  haDeployment: true,
  handleSetPage: (str) => str,
  handleSubmit: (str) => str,
  loading: false,
  onSubmit: noop,
  premiseName: '',
  previousPage: (str) => str,
  t: (str) => str,
  wizardFieldValues: {},
};

const mapStateToProps = (state) => ({
  ...provisioningTemplatesSelector.default(state),
  formValues: provisioningTemplatesSelector.formValuesSelector(state),
  formMeta: provisioningTemplatesSelector.formMetaSelector(state),
  formSyncErrors: provisioningTemplatesSelector.formSyncErrorsSelector(state),
  wizardFieldValues: getFormValues('provisioningTemplateWizard')(state),
  existingBCGroup: provisioningTemplatesSelector.bcGroupTypeSelector(state),
  existingLocation: provisioningTemplatesSelector.locationTypeSelector(state),
  bcAutoType: provisioningTemplatesSelector.bcAutoTypeSelector(state),
  bcHwAutoType: provisioningTemplatesSelector.bcHwAutoTypeSelector(state),
  bcFwdType: provisioningTemplatesSelector.bcFwdTypeSelector(state),
  bcDnsAutoType: provisioningTemplatesSelector.bcDnsAutoTypeSelector(state),
  bcDnsAutoType2: provisioningTemplatesSelector.bcDnsAutoTypeSelector2(state),
  premiseName: provisioningTemplatesSelector.premiseNameSelector(state),
  initialValues: provisioningTemplateWizardSelectors.dataSelectorForReview(state),
  configData: loginSelectors.configDataSelector(state),
  subscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    updateMode: updateWizardMode,
    reviewItems,
    getBCGroups,
    handleDnsIpTypeChange,
    handleDnsIpTypeChange2,
    locationOnChange: handleLocationDropDownChangeChange,
    onShowClick,
  }, dispatch);

  return {
    actions,
  };
};

const Review = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'provisioningTemplateWizard',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(withTranslation()(ConfigureEdgeconnectorForm)));

export default Review;

/* eslint-disable react/jsx-handler-names */
// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { noop } from 'utils/lodash';
import Modal from 'react-modal';
import { withTranslation } from 'react-i18next';
import { getFormValues, change, autofill } from 'redux-form';
import withRouter from 'layout/withRouter';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';

import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';

import {
  resetFormValues,
  toggleWizard,
  saveVDITemplate,
  handleAuthTypeChange,
} from 'ducks/vdiTemplates';

import * as VDITemplatesSelectors from 'ducks/vdiTemplates/selectors';

import VDIAgentForm from './Form';

class VDITemplatesForm extends React.Component {
  componentDidMount() {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.ADD_PROVISIONING_TEMPLATE });
    Modal.setAppElement('#r-app');
  }

  componentWillUnmount() {
    const { actions: { toggleWizardModal }, isProvTemplateWizardOpen } = this.props;

    if (isProvTemplateWizardOpen) {
      toggleWizardModal(false, null, null);
    }
  }

  closeModal = () => {
    const { actions: { toggleWizardModal, resetReduxFormValues } } = this.props;
    resetReduxFormValues();
    toggleWizardModal(false, null, null);
  };

  render() {
    const {
      t,
      isProvTemplateWizardOpen,
      mode,
      modalLoading,
      actions,
      // enableGoToPage,
    } = this.props;
    let label;

    switch (mode) {
    case 'NEW':
    case 'PRESET':
      label = t('ADD_VDI_TEMPLATE');
      break;

    case 'EDIT':
      label = t('EDIT_VDI_TEMPLATE');
      break;

    default:
      label = '';
      break;
    }
    if (isProvTemplateWizardOpen) {
      return (
        <div className="edgeconnector-page">
          <div className="back-to-ec">
            <FontAwesomeIcon icon={faArrowLeft} size="lg" onClick={this.closeModal} />
            {label}
          </div>
          <div className="edgeconnector-modal vdi-template-modal">
            <div className="modal-content modal-body cc-provisioning-modal-content">
              <Loading loading={modalLoading}>
                <HelpArticle article={HELP_ARTICLES.ADD_PROVISIONING_TEMPLATE} />
                <ServerError {...this.props}>
                  <div className="vdi-form-wrapper">
                    <VDIAgentForm
                      {...this.props}
                      handleAuthType={actions.handleAuthType}
                      closeModal={this.closeModal} />
                  </div>
                </ServerError>
              </Loading>
            </div>
          </div>
        </div>
      );
    }
    return '';
  }
}

VDITemplatesForm.propTypes = {
  actions: PropTypes.shape(),
  activePage: PropTypes.number,
  history: PropTypes.shape(),
  match: PropTypes.shape(),
  name: PropTypes.string,
  mode: PropTypes.string,
  isProvTemplateWizardOpen: PropTypes.bool,
  enableGoToPage: PropTypes.bool,
  t: PropTypes.func,
  modalLoading: PropTypes.bool,
  handleAuthType: PropTypes.func,
};

VDITemplatesForm.defaultProps = {
  actions: null,
  activePage: 0,
  history: {},
  match: {},
  name: '',
  mode: 'NEW',
  isProvTemplateWizardOpen: false,
  t: (str) => str,
  modalLoading: false,
  handleAuthType: noop,
};

const mapStateToProps = (state) => ({
  ...VDITemplatesSelectors.default(state),
  ...getFormValues('vdiAgentTemplateForm')(state),
  enableGoToPage: VDITemplatesSelectors.enableGoToPageSelector(state),
  modalLoading: VDITemplatesSelectors.modalLoadingSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleWizardModal: toggleWizard,
    updateFormState: change,
    saveVDITemplates: saveVDITemplate,
    resetReduxFormValues: resetFormValues,
    handleAuthType: handleAuthTypeChange,
    autofillReduxForm: autofill,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(VDITemplatesForm)));

// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import withRouter from 'layout/withRouter';
import { reduxForm } from 'redux-form';
import { noop } from 'utils/lodash';
import { FormFieldLabel } from 'components/label';

import { toggleViewModal } from 'ducks/vdiTemplates';

import * as VDITemplatesSelectors from 'ducks/vdiTemplates/selectors';

export function BasicCustomAppForm({
  actions,
  t,
  appData,
}) {
  const {
    name,
    desc,
    templateData,
    authType,
    vdiUser,
    idp,
  } = appData;
  const handleCancelClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    const { cancelHandle } = actions;
    
    cancelHandle(appData, false);
  };

  return (
    <form className="wizard-form configure-provisioning-template">
      <div className="form-sections-container cc-provisoning">
        <div className="form-section provisioning-url">
          <div className="input-container review half-width">
            <FormFieldLabel text={t('NAME')} styleClass="no-margin-top" />
            <p className="disabled-input">{name}</p>
          </div>

          <div className="input-container review half-width">
            <FormFieldLabel text={t('DESCRIPTION')} styleClass="no-margin-top" />
            <p className="disabled-input">{desc || '--'}</p>
          </div>
        </div>

        <div className="form-section provisioning-url">
          <div className="input-container review half-width">
            <FormFieldLabel text={t('DOMAINS')} styleClass="no-margin-top" />
            <p className="disabled-input">{((templateData && templateData.zsCloudDomain) || '---')}</p>
          </div>
          <div className="input-container review half-width">
            <FormFieldLabel text={t('AUTH_TYPE')} styleClass="no-margin-top" />
            <p className="disabled-input">{(authType || '---')}</p>
          </div>
          {
            authType === 'IDP'
              ? (
                <div className="input-container review half-width">
                  <FormFieldLabel text={t('IDP_NAME')} styleClass="no-margin-top" />
                  <p className="disabled-input">{((idp && idp.name) || '---')}</p>
                </div>
              )
              : ''
          }
          <div className="input-container review half-width">
            <FormFieldLabel text={t('SYSTEM_USER')} styleClass="no-margin-top" />
            <p className="disabled-input">{((vdiUser && vdiUser.name) || '---')}</p>
          </div>
        </div>
 
      </div>
      <div className="modal-footer">
        <button type="submit" onClick={handleCancelClick} className="next primary-button">{t('CANCEL')}</button>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
  }),
  t: PropTypes.func,
  appData: PropTypes.shape(),
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
  },
  t: (str) => str,
  appData: {},
};

const VDITemplateViewModal = reduxForm({
  form: 'ProvisioningTemplateViewForm',
})(BasicCustomAppForm);

const mapStateToProps = (state) => ({
  ...VDITemplatesSelectors.default(state),
  appData: VDITemplatesSelectors.appDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleViewModal,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(VDITemplateViewModal)));

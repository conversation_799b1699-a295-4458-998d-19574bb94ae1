// @flow

import React, { useEffect } from 'react';
import { Field, reduxForm } from 'redux-form';
// import { useSelector } from 'react-redux';
import { connect } from 'react-redux';
import { get, noop, isEmpty } from 'utils/lodash';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import * as provisioningTemplatesSelector from 'ducks/vdiTemplates/selectors';
import {
  required,
  noWhiteSpacesAllowed,
  maxLength,
  // ipAddressOnly,
} from 'utils/validations';
import { FormFieldLabel } from 'components/label';
import Input from 'components/Input';
import ECRadioGroup from 'components/ecRadioGroup';

import IdPNameDropdown from 'commonConnectedComponents/dropdown/IdPNameDropdown';
import SystemUserDropdown from 'commonConnectedComponents/dropdown/SystemUserDropdown';
// import idpNameSelectors from 'ducks/dropdowns/idp-name/selectors';
// import systemUserSelectors from 'ducks/dropdowns/system-user/selectors';

const maxLength10240 = maxLength(10240);

function ConfigureEdgeconnectorForm(props) {
  // const idpNameSelector = useSelector(state => idpNameSelectors(state).dropdown);
  // const systemUserSelector = useSelector(state => systemUserSelectors(state).dropdown);
  useEffect(() => {
    const { initialValues, actions } = props;
    const { systemUser, row = {}, authType } = initialValues;
    const { templateData = {}, idp, vdiUser } = row;
    
    // if (!isEmpty(idpNameSelector.data) && !isEmpty(templateData)) {
    //   idpNameSelector.data.forEach((item) => {
    //     if (item.id === templateData.idpId) {
    //       idpData.id = item.id;
    //       idpData.name = item.idpName;
    //     }
    //   });
    // }
    // can be used once api removed the systemUserName field from template data
    // if (!isEmpty(systemUserSelector.data) && !isEmpty(templateData)) {
    //   systemUserSelector.data.forEach((item) => {
    //     if (item.id === templateData.systemUserId) {
    //       systemUserData.id = item.id;
    //       systemUserData.name = item.name;
    //     }
    //   });
    // }

    // authType
    actions.autofillReduxForm('vdiAgentTemplateForm', 'authType', authType);

    if (systemUser) {
      actions.autofillReduxForm('vdiAgentTemplateForm', 'idp', idp);
      actions.autofillReduxForm('vdiAgentTemplateForm', 'vdiUser', vdiUser);
      actions.autofillReduxForm('vdiAgentTemplateForm', 'templateData.connectorIp', templateData.connectorIp || '');
    }
  }, []);

  const {
    closeModal,
    handleSubmit,
    t,
    formMeta,
    formSyncErrors,
    actions,
    valid,
    initialValues,
    handleAuthType,
  } = props;
  let {
    authType,
  } = isEmpty(initialValues) ? props : initialValues;
  
  const { id, name, templateData } = initialValues;
  const fieldName = 'name';
  const meta = get(formMeta, fieldName, null);
  const error = get(formSyncErrors, fieldName, null);
  const hasError = meta && meta.touched && !!error;

  const fieldName2 = 'desc';
  const meta2 = get(formMeta, fieldName2, null);
  const error2 = get(formSyncErrors, fieldName2, null);
  const hasError2 = meta2 && meta2.touched && !!error2;

  const fieldName3 = 'vdiUser';
  const meta3 = get(formMeta, fieldName3, null);
  const error3 = get(formSyncErrors, fieldName3, null);
  const hasError3 = meta3 && meta3.touched && !!error3;

  const fieldName4 = 'idp';
  const meta4 = get(formMeta, fieldName4, null);
  const error4 = get(formSyncErrors, fieldName4, null);
  const hasError4 = meta4 && meta4.touched && !!error4;

  if (authType === '' && initialValues.authType) {
    // eslint-disable-next-line prefer-destructuring
    authType = initialValues.authType;
  } else if (authType === '') {
    authType = 'IDP';
  }

  return (
    <form onSubmit={handleSubmit(actions.saveVDITemplates)} className="vdi-form configure-vdi-template">
      <div className="form-sections-container">
        <div className="form-section">
          <div className="form-width">
            <div className="input-container">
              <FormFieldLabel
                styleClass={`${hasError ? 'invalid' : ''}`}
                error={hasError ? t(error) : null}
                text={t('NAME')}
                tooltip={t('TOOLTIP_VDI_TEMPLATE_NAME')} />
              {
                !id && (
                  <Field
                    id="name"
                    name="name"
                    component={Input}
                    placeholder={t('ENTER_TEXT')}
                    validate={[
                      required,
                      noWhiteSpacesAllowed,
                    ]}
                    styleClass="max-width" />
                )
              }
              {
                id && (
                  <p className="disabled-input">{name}</p>
                )
              }
            </div>
            <FormFieldLabel
              error={hasError2 ? t(error2) : null}
              styleClass={`${hasError2 ? 'invalid' : ''}`}
              text={t('DESCRIPTION')}
              tooltip={t('TOOLTIP_POLICY_FIREWALL_DESCRIPTION')} />
            <Field
              id="desc"
              name="desc"
              component="textarea"
              placeholder={t('VDI_AGENT_DESCRIPTION_PLACEHOLDER_TEXT')}
              validate={[maxLength10240]}
              className="cc-provisioning-description" />

            <div className="section-divider"></div>

            <div className="form-input-row">
              <FormFieldLabel styleClass="criteria-title-text" text={t('END_USER_AUTHENDICATION')} />
              <div className="disabled-input criteria-content">{t('VDI_AGENT_TEMPLATE_END_USER_AUTHENTICATION_TEXT')}</div>
            </div>

            {/* authType */}
            <div className="dropdown-container full-width">
              <FormFieldLabel text={t('AUTH_TYPE')} tooltip={t('TOOLTIP_VDI_AGENT_TEMPLATE_AUTH_TYPE')} />
              <ECRadioGroup
                id="authType"
                name="authType"
                styleClass="auth-type"
                onChange={handleAuthType}
                options={[{
                  name: 'authType', value: 'IDP', checked: authType === 'IDP', label: t('IDP'),
                },
                {
                  name: 'authType', value: 'HOSTED_DB', checked: authType === 'HOSTED_DB', label: t('HOSTED_DB'),
                },
                ]} />
            </div>
            {/* Domains */}
            {
              authType === 'IDP' && (
                <div className="input-container full-width">
                  <FormFieldLabel
                    tooltip={t('')}
                    text={t('DOMAINS')} />
                  <p className="disabled-input">{(templateData && templateData.zsCloudDomain) || '---'}</p>
                </div>
              )
            }
            {
              authType === 'IDP' && (
                <div className="input-container full-width">
                  <FormFieldLabel
                    styleClass={`${hasError4 ? 'invalid' : ''}`}
                    error={hasError4 ? t(error4) : null}
                    tooltip={t('TOOLTIP_VDI_TEMPLATE_IDP_NAME')}
                    text={t('IDP_NAME')} />
                  <Field
                    id="idp"
                    name="idp"
                    component={IdPNameDropdown}
                    className="select-item"
                    styleClass="idp-dropdown"
                    onChange={() => noop}
                    required
                    validate={[
                      required,
                    ]} />
                </div>
              )
            }

            {/* System User */}
            <div className="input-container">
              <FormFieldLabel
                styleClass={`${hasError3 ? 'invalid' : ''}`}
                error={hasError3 ? t(error3) : null}
                tooltip={t('TOOLTIP_VDI_TEMPLATE_SYSTEM_USER')}
                text={t('SYSTEM_USER')} />
              <Field
                id="vdiUser"
                name="vdiUser"
                styleClass="vdi-user-dropdown"
                component={SystemUserDropdown}
                className="select-item"
                onChange={() => noop}
                required
                validate={[
                  required,
                ]} />
            </div>
            
            {/* <div className="input-container">
              <FormFieldLabel text={t('CONNECTOR_IP')} tooltip={t('TOOLTIP_VDI_CONNECTOR_IP')} />
              <Field
                id="templateData.connectorIp"
                name="templateData.connectorIp"
                component={Input}
                styleClass="zia"
                validate={[
                  // ipAddressOnly,
                ]}
                placeholder={t('ENTER_IP')} />
            </div> */}
          </div>
        </div>
      </div>
      <div className="modal-footer">
        <div>
          <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
          <button type="submit" disabled={!valid} className="next primary-button">{t('SUBMIT')}</button>
        </div>
      </div>
    </form>
  );
}

ConfigureEdgeconnectorForm.propTypes = {
  closeModal: PropTypes.func,
  handleSubmit: PropTypes.func,
  t: PropTypes.func,
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  actions: PropTypes.shape(PropTypes.func),
  valid: PropTypes.bool,
  initialValues: PropTypes.shape(),
  authType: PropTypes.string,
  handleAuthType: PropTypes.func,
};

ConfigureEdgeconnectorForm.defaultProps = {
  closeModal: (str) => str,
  handleSubmit: (str) => str,
  t: (str) => str,
  formMeta: {},
  formSyncErrors: {},
  valid: true,
  authType: 'IDP',
};

const mapStateToProps = (state) => ({
  ...provisioningTemplatesSelector.default(state),
  initialValues: provisioningTemplatesSelector.dataSelector(state),
  // wizardMode: provisioningTemplateWizardSelectors.wizardModeSelector(state),
  formValues: provisioningTemplatesSelector.formValuesSelector(state),
  formMeta: provisioningTemplatesSelector.formMetaSelector(state),
  formSyncErrors: provisioningTemplatesSelector.formSyncErrorsSelector(state),
  authType: provisioningTemplatesSelector.authTypeSelector(state),
});

const VDIAgentForm = connect(mapStateToProps, null)(reduxForm({
  form: 'vdiAgentTemplateForm',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(withTranslation()(ConfigureEdgeconnectorForm)));

export default VDIAgentForm;

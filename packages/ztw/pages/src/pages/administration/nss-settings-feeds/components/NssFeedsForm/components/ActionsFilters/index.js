// @flow

import React, { PureComponent } from 'react';
import { Field } from 'redux-form';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import {
  get, noop,
} from 'utils/lodash';

import { FormFieldLabel } from 'components/label';
import ForwardingMethodDropdown from 'commonConnectedComponents/dropdown/ForwardingMethodDropdown';
import ForwardingRuleDropdown from 'commonConnectedComponents/dropdown/ForwardingRuleDropdown';

const parseDropdownValues = (value) => get(value, 'original', value);

export class ActionsFilters extends PureComponent {
  static propTypes = {
    actions: PropTypes.shape({
      load: PropTypes.func,
    }),
    t: PropTypes.func,
  };

  static defaultProps = {
    actions: {
      load: noop,
    },
    t: (str) => str,
  };

  render() {
    const {
      t,
    } = this.props;
    return (
      <div className="full-width">
        <div className="checkbox-container half-width">
          <FormFieldLabel
            tooltip={t('TOOLTIP_NSS_FEED_SESSION_FILTERS_POLICY_ACTION')}
            text={t('NSS_FEED_SESSION_FILTERS_POLICY_ACTION')} />
          <Field
            id="fwdType"
            className="session-policy-action"
            name="fwdType"
            component={ForwardingMethodDropdown}
            onChange={noop}
            parse={(values = []) => values.map(parseDropdownValues)} />
        </div>
        <div className="checkbox-container half-width">
          <FormFieldLabel
            tooltip={t('TOOLTIP_NSS_FEED_SESSION_FILTERS_RULE_NAME')}
            text={t('NSS_FEED_SESSION_FILTERS_RULE_NAME')} />
          <Field
            id="session-rule-name"
            class="session-rule-name"
            name="rules"
            component={ForwardingRuleDropdown}
            props={{ place: 'right' }}
            onChange={noop} />
        </div>
      </div>
    );
  }
}

export default (withTranslation()(ActionsFilters));

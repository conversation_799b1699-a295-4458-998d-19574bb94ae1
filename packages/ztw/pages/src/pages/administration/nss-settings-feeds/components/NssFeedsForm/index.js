// @flow
import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm, Field, autofill } from 'redux-form';
import Input from 'components/Input';
import Loading from 'components/spinner/Loading';
import ECRadioGroup from 'components/ecRadioGroup';

import { FormSectionLabel, FormFieldLabel } from 'components/label';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import {
  noop, isArray, set, each, get,
} from 'utils/lodash';
import { required, maxLength, validatecustomEscapedCharacter } from 'utils/validations';
import TabSwitch from 'components/tabSwitch';

import {
  FILTERS_SESSION_TAB_LIST, FILTERS_DNS_TAB_LIST, FILTERS_METRICS_TAB_LIST,
} from 'ducks/nssSettingsFeeds/constants';
import {
  saveForm,
  toggleForm,
  loadNssServersData,
  handleNssFeedsNssServerChange,
  handleNssFeedsStatusChange,
  handleNssFeedsNssLogTypeChange,
  setMetricsRecordTypeTabFilter,
  handleMetricsRecordTypeChange,
  handleNssFeedsFirewallLoggingModeChange,
  handleNssFeedsSiemRateChange,
  handleNssFeedsSiemDestTypeChange,
  handleNssFeedsFeedOutputTypeChange,
  handleNssFeedOutputFormatChange,
  handleMetricsVmFeedOutputFormatChange,
  handleMetricsInstanceFeedOutputFormatChange,
  handleMetricsDeviceFeedOutputFormatChange,
  handleNssFeedsTimezoneChange,
  handleNssFeedsDuplicateLogsChange,
  setSessionTabFilter,
  setDnsTabFilter,
} from 'ducks/nssSettingsFeeds';
import * as NssFeedsSelectors from 'ducks/nssSettingsFeeds/selectors';
import EcMetricsRecordTypesDropdown from 'commonConnectedComponents/dropdown/EcMetricsRecordTypesDropdown';
import {
  NssFeedsModalDropdown, ActionsFilters, SourceFilters, DestinationFilters,
  GatewayFilters, DnsActionsFilters, DnsSourceFilters, DnsDestinationFilters, DnsTransactionFilters,
  MetricsFilters,
} from './components';

const parseDropdownValues = (value) => get(value, 'original', value);

const maxLength50 = maxLength(255);
const maxLength4 = maxLength(4);
const maxLength10240 = maxLength(10240);
const getObjectArray = (array) => {
  const objArray = [];
  each(array, (el) => {
    objArray.push({
      id: el,
      name: el,
    });
  });
  return objArray;
};
export class BasicCustomAppForm extends PureComponent {
  static propTypes = {
    actions: PropTypes.shape({
      addNssFeed: PropTypes.func,
      autoFillFormValues: PropTypes.func,
      cancelHandle: PropTypes.func,
      handleMetricsDeviceFeedOutputFormatChange: PropTypes.func,
      handleMetricsInstanceFeedOutputFormatChange: PropTypes.func,
      handleMetricsRecordTypeChange: PropTypes.func,
      handleMetricsVmFeedOutputFormatChange: PropTypes.func,
      handleNssFeedOutputFormatChange: PropTypes.func,
      handleNssFeedsDuplicateLogsChange: PropTypes.func,
      handleNssFeedsFeedOutputTypeChange: PropTypes.func,
      handleNssFeedsFirewallLoggingModeChange: PropTypes.func,
      handleNssFeedsNssLogTypeChange: PropTypes.func,
      handleNssFeedsNssServerChange: PropTypes.func,
      handleNssFeedsSiemDestTypeChange: PropTypes.func,
      handleNssFeedsSiemRateChange: PropTypes.func,
      handleNssFeedsStatusChange: PropTypes.func,
      handleNssFeedsTimezoneChange: PropTypes.func,
      loadNssServersData: PropTypes.func,
      setDnsTabFilter: PropTypes.func,
      setMetricsRecordTypeTabFilter: PropTypes.func,
      setMetricsTabFilter: PropTypes.func,
      setSessionTabFilter: PropTypes.func,
    }),
    valid: PropTypes.bool,
    t: PropTypes.func,
    handleSubmit: PropTypes.func,
    selectedStatus: PropTypes.string,
    selectedLogType: PropTypes.string,
    metricsRecordTypesList: PropTypes.shape(),
    selectedMetricsRecordTypesTab: PropTypes.shape(),
    selectedFirewallLoggingMode: PropTypes.string,
    selectedSiemRate: PropTypes.string,
    selectedDestType: PropTypes.string,
    selectedFeedOutputFormat: PropTypes.string,
    allFeedOutputFormats: PropTypes.shape(),
    selectedVmGblMetricsfeedOutputFormatSelector: PropTypes.shape(),
    selectedInstGblMetricsfeedOutputFormatSelector: PropTypes.shape(),
    selectedDeviceMetricsfeedOutputFormatSelector: PropTypes.shape(),
    initialValues: PropTypes.shape(),
    modalLoading: PropTypes.bool,
    nssType: PropTypes.string,
    selectedSessionFilterTab: PropTypes.shape(),
    selectedDnsFilterTab: PropTypes.shape(),
    selectedMetricsFilterTab: PropTypes.shape(),
    selectedFeedOutputType: PropTypes.shape(),
    selectedCustomEscapedCharacter: PropTypes.string,
  };

  static defaultProps = {
    actions: {
      cancelHandle: noop,
      saveLocationTemplate: noop,
      autoFillFormValues: noop,
    },
    valid: true,
    t: (str) => str,
    handleSubmit: noop,
    initialValues: {},
    modalLoading: false,
    nssType: '',
    metricsRecordTypesList: [],
    selectedMetricsRecordTypesTab: {},
    selectedSessionFilterTab: {},
    selectedDnsFilterTab: {},
    selectedMetricsFilterTab: {},
    selectedFeedOutputType: {},
    selectedCustomEscapedCharacter: '',
  };

  vmGblFilters = () => {
    const { actions } = this.props;
    return (
      <div className="form-section textarea-wrapper nss-feeds-text-area marginTop13">
        <Field
          name="feedOutputFormatMetricsVm"
          id="feedOutputFormatMetricsVm"
          customErrorText="DESCRIPTION_MAX_LIMIT_ERROR"
          component={Input}
          // styleClass={feedOutputFormatHasError ? 'error' : ''}
          type="textarea"
          className="form-textarea"
          onChange={actions.handleMetricsVmFeedOutputFormatChange}
          validate={[
            maxLength10240,
          ]} />
      </div>
    );
  };

  InstanceGblFilters = () => {
    const { actions } = this.props;
    return (
      <div className="form-section textarea-wrapper nss-feeds-text-area marginTop13">
        <Field
          name="feedOutputFormatMetricsInstance"
          id="feedOutputFormatMetricsInstance"
          customErrorText="DESCRIPTION_MAX_LIMIT_ERROR"
          component={Input}
          // styleClass={feedOutputFormatHasError ? 'error' : ''}
          type="textarea"
          className="form-textarea"
          onChange={actions.handleMetricsInstanceFeedOutputFormatChange}
          validate={[
            maxLength10240,
          ]} />
      </div>
    );
  };

  DeviceMetricsFilters = () => {
    const { actions } = this.props;
    return (
      <div className="form-section textarea-wrapper nss-feeds-text-area marginTop13">
        <Field
          name="feedOutputFormatMetricsDevice"
          id="feedOutputFormatMetricsDevice"
          customErrorText="DESCRIPTION_MAX_LIMIT_ERROR"
          component={Input}
          // styleClass={feedOutputFormatHasError ? 'error' : ''}
          type="textarea"
          className="form-textarea"
          onChange={actions.handleMetricsDeviceFeedOutputFormatChange}
          validate={[
            maxLength10240,
          ]} />
      </div>
    );
  };

  render() {
    const {
      valid,
      actions,
      t,
      handleSubmit,
      initialValues,
      nssType,
      selectedSessionFilterTab,
      selectedDnsFilterTab,
      selectedMetricsFilterTab,
      modalLoading,
      selectedStatus,
      selectedLogType,
      metricsRecordTypesList,
      selectedMetricsRecordTypesTab,
      selectedFirewallLoggingMode,
      selectedFeedOutputType,
      selectedCustomEscapedCharacter,
      selectedSiemRate,
      selectedDestType,
      selectedFeedOutputFormat,
      allFeedOutputFormats,
      selectedVmGblMetricsfeedOutputFormatSelector,
      selectedInstGblMetricsfeedOutputFormatSelector,
      selectedDeviceMetricsfeedOutputFormatSelector,
    } = this.props;
    const {
      cancelHandle,
      addNssFeed,
      autoFillFormValues,
    } = actions;

    if (selectedLogType === 'MULTIFEEDLOG') {
      if (selectedVmGblMetricsfeedOutputFormatSelector) {
        autoFillFormValues('addEditNssFeedsForm', 'feedOutputFormatMetricsVm', selectedVmGblMetricsfeedOutputFormatSelector);
      } else if (allFeedOutputFormats) {
        autoFillFormValues('addEditNssFeedsForm', 'feedOutputFormatMetricsVm', allFeedOutputFormats.VM_GBL_METRICS);
      }

      if (selectedInstGblMetricsfeedOutputFormatSelector) {
        autoFillFormValues('addEditNssFeedsForm', 'feedOutputFormatMetricsInstance', selectedInstGblMetricsfeedOutputFormatSelector);
      } else if (allFeedOutputFormats) {
        autoFillFormValues('addEditNssFeedsForm', 'feedOutputFormatMetricsInstance', allFeedOutputFormats.INST_GBL_METRICS);
      }

      if (selectedDeviceMetricsfeedOutputFormatSelector) {
        autoFillFormValues('addEditNssFeedsForm', 'feedOutputFormatMetricsDevice', selectedDeviceMetricsfeedOutputFormatSelector);
      } else if (allFeedOutputFormats) {
        autoFillFormValues('addEditNssFeedsForm', 'feedOutputFormatMetricsDevice', allFeedOutputFormats.DEVICE_METRICS);
      }
    } else if (selectedFeedOutputFormat) {
      autoFillFormValues('addEditNssFeedsForm', 'feedOutputFormat', selectedFeedOutputFormat);
    }

    let custEscCharEditable = true;
    let renderCustChar = '';

    if (selectedFeedOutputType) {
      // if (selectedFeedOutputType.id === 'CUSTOM' || selectedFeedOutputType.id === 'JSON') {
      if (selectedFeedOutputType === 'CUSTOM' || selectedFeedOutputType === 'JSON') {
        custEscCharEditable = true;
        autoFillFormValues('addEditNssFeedsForm', 'customEscapedCharacter', selectedCustomEscapedCharacter);
      } else {
        custEscCharEditable = false;
        renderCustChar = selectedCustomEscapedCharacter;
      }
    } else if (initialValues && (initialValues.customEscapedCharacter
      || initialValues.nssFeedType)) {
      const tempCustChar = initialValues.customEscapedCharacter;
      if (isArray(tempCustChar) && tempCustChar.length === 1) {
        const temp = tempCustChar[0].match(/\d+/g)[0];
        renderCustChar = String.fromCharCode(temp).trim();
      } else if (isArray(tempCustChar)) {
        for (let i = 0; i < tempCustChar.length; i += 1) {
          const temp = tempCustChar[i].match(/\d+/g)[0];
          const decimalVal = String.fromCharCode(temp).trim();
          renderCustChar += decimalVal;
        }
      }

      if (initialValues.nssFeedType === 'CUSTOM' || initialValues.nssFeedType === 'JSON') {
        custEscCharEditable = true;
        autoFillFormValues('addEditNssFeedsForm', 'customEscapedCharacter', renderCustChar);
      } else {
        custEscCharEditable = false;
      }
    }
  
    return (
      <form onSubmit={handleSubmit(addNssFeed)} className="add-custom-app-form add-edit-nss-feeds">
        <Loading loading={modalLoading} />
        <div className="form-sections-nss-container-parent">
          <div className="form-sections-container">
            <FormSectionLabel text={t('NSS_FEED_GENERAL')} />
            <div className="form-section">
              <div className="checkbox-container half-width">
                <FormFieldLabel
                  tooltip={t('TOOLTIP_NSS_FEED_NAME')}
                  text={t('NSS_FEED_NAME')} />
                <Field
                  name="name"
                  id="name"
                  component={Input}
                  type="text"
                  placeholder={t('TYPE_NSS_FEED_NAME')}
                  validate={[
                    required,
                    maxLength50,
                  ]} />
              </div>
              <div className="checkbox-container half-width">
                <FormFieldLabel
                  tooltip={t('TOOLTIP_NSS_TYPE')}
                  text={t('NSS_TYPE')} />
                <p className="disabled-input">{t(nssType)}</p>
              </div>
              <div className="checkbox-container half-width marginTop13">
                <FormFieldLabel
                  tooltip={t('TOOLTIP_NSS_FEED_SERVER')}
                  text={t('NSS_FEED_SERVER')} />
                <Field
                  id="nssServer"
                  name="nssServer"
                  component={NssFeedsModalDropdown}
                  onChange={actions.handleNssFeedsNssServerChange} />
              </div>
              <div className="radio-button-container nss-feeds-dialog-radio-container half-width marginTop13">
                <FormFieldLabel
                  tooltip={t('TOOLTIP_NSS_FEED_STATUS')}
                  text={t('STATUS')} />
                <ECRadioGroup
                  id="nss-feeds-state"
                  name="feedStatus"
                  styleClass="half-width"
                  onChange={actions.handleNssFeedsStatusChange}
                  options={[{
                    name: 'feedStatus', value: 'ENABLED', checked: selectedStatus === 'ENABLED', label: t('ENABLED'),
                  },
                  {
                    name: 'feedStatus', value: 'DISABLED', checked: selectedStatus === 'DISABLED', label: t('DISABLED'),
                  }]} />
              </div>
            </div>
          </div>

          <div className="form-sections-container">
            <FormSectionLabel text={t('NSS_FEED_SIEM_CONNECTIVITY')} />
            <div className="form-section">
              <div className="radio-button-container nss-feeds-dialog-radio-container half-width">
                <FormFieldLabel
                  tooltip={t('TOOLTIP_NSS_FEED_SIEM_DESTINATION_TYPE')}
                  text={t('NSS_FEED_SIEM_DESTINATION_TYPE')} />
                <ECRadioGroup
                  id="nss-feeds-siem-destination-type"
                  name="type"
                  styleClass="half-width"
                  onChange={actions.handleNssFeedsSiemDestTypeChange}
                  options={[{
                    name: 'type', value: 'DSTN_IP', checked: selectedDestType === 'DSTN_IP', label: t('IP_ADDRESS'),
                  },
                  {
                    name: 'type', value: 'DSTN_FQDN', checked: selectedDestType === 'DSTN_FQDN', label: t('FQDN'),
                  }]} />
              </div>
              {selectedDestType === 'DSTN_IP'
                ? (
                  <div className="checkbox-container half-width">
                    <FormFieldLabel
                      tooltip={t('TOOLTIP_NSS_FEED_SIEM_IP_ADDRESS')}
                      text={t('NSS_FEED_SIEM_IP_ADDRESS')} />
                    <Field
                      name="serviceIp"
                      id="siemIpAddress"
                      component={Input}
                      type="text"
                      placeholder={t('TYPE_SIEM_IP_ADDRESS')}
                      validate={[
                        required,
                        maxLength50,
                      ]} />
                  </div>
                ) : (
                  <div className="checkbox-container half-width">
                    <FormFieldLabel
                      tooltip={t('TOOLTIP_NSS_FEED_SIEM_FQDN')}
                      text={t('NSS_FEED_SIEM_FQDN')} />
                    <Field
                      name="serviceFQDN"
                      id="siemFqdn"
                      component={Input}
                      type="text"
                      placeholder={t('TYPE_SIEM_FQDN')}
                      validate={[
                        required,
                        maxLength50,
                      ]} />
                  </div>
                )}
              <div className="checkbox-container half-width marginTop13">
                <FormFieldLabel
                  tooltip={t('TOOLTIP_NSS_FEED_SIEM_TCP_PORT')}
                  text={t('NSS_FEED_SIEM_TCP_PORT')} />
                <Field
                  name="servicePort"
                  id="siemTCPPort"
                  component={Input}
                  type="text"
                  placeholder={t('TYPE_SIEM_TCP_PORT')}
                  validate={[
                    required,
                    maxLength50,
                  ]} />
              </div>
              <div className="radio-button-container nss-feeds-dialog-radio-container half-width marginTop13">
                <FormFieldLabel
                  tooltip={t('TOOLTIP_NSS_FEED_SIEM_RATE')}
                  text={t('NSS_FEED_SIEM_RATE')} />
                <ECRadioGroup
                  id="nss-feeds-siem-rate"
                  name="siemRate"
                  styleClass="half-width"
                  onChange={actions.handleNssFeedsSiemRateChange}
                  options={[{
                    name: 'siemRate', value: 'UNLIMITED', checked: selectedSiemRate === 'UNLIMITED', label: t('UNLIMITED'),
                  },
                  {
                    name: 'siemRate', value: 'LIMITED', checked: selectedSiemRate === 'LIMITED', label: t('LIMITED'),
                  }]} />
              </div>
              {selectedSiemRate === 'LIMITED'
                  && (
                    <div className="checkbox-container half-width marginTop13">
                      <FormFieldLabel
                        tooltip={t('TOOLTIP_NSS_FEED_SIEM_RATE_LIMIT')}
                        text={t('NSS_FEED_SIEM_RATE_LIMIT')} />
                      <Field
                        name="epsRateLimit"
                        id="siemRateLimit"
                        component={Input}
                        type="text"
                        placeholder={t('TYPE_SIEM_RATE_LIMIT')}
                        validate={[
                          required,
                          maxLength50,
                        ]} />
                    </div>
                  )}
            </div>
          </div>

          <div className="form-sections-container nss-formatting-block">
            <FormSectionLabel text={t('NSS_FEED_FORMATTING')} />
            <div className="form-section">
              <div className="checkbox-container half-width marginTop13">
                <FormFieldLabel
                  tooltip={t('TOOLTIP_NSS_FEED_LOG_TYPE')}
                  text={t('NSS_FEED_LOG_TYPE')} />
                <Field
                  id="nssLogType"
                  name="nssLogType"
                  component={NssFeedsModalDropdown}
                  autoFillReduxForm={autoFillFormValues}
                  onChange={actions.handleNssFeedsNssLogTypeChange} />
              </div>
              {(selectedLogType && selectedLogType === 'MULTIFEEDLOG')
                  && (
                    <div className="radio-button-container half-width">
                      <FormFieldLabel
                        tooltip={t('TOOLTIP_NSS_FEED_EC_METRICS_RECORD_TYPE')}
                        text={t('NSS_FEED_EC_METRICS_RECORD_TYPE')} />
                      <Field
                        id="metricsRecordType"
                        className="metrics-record-type"
                        name="metricsRecordType"
                        autoFillReduxForm={autoFillFormValues}
                        component={EcMetricsRecordTypesDropdown}
                        onChange={actions.handleMetricsRecordTypeChange}
                        parse={(values = []) => values.map(parseDropdownValues)} />
                    </div>
                  )}
              {selectedLogType === 'ECLOG'
                  && (
                    <div className="radio-button-container full-width marginTop13">
                      <FormFieldLabel
                        tooltip={t('TOOLTIP_NSS_FEED_SESSION_LOG_TYPE')}
                        text={t('NSS_FEED_SESSION_LOG_TYPE')} />
                      <ECRadioGroup
                        id="nss-feeds-firewall-log-type"
                        name="firewallLoggingMode"
                        styleClass="full-width"
                        onChange={actions.handleNssFeedsFirewallLoggingModeChange}
                        options={[{
                          name: 'firewallLoggingMode', value: 'SESSION', checked: selectedFirewallLoggingMode === 'SESSION', label: t('NSS_FEEDS_FULL_SESSION_LOGS'),
                        },
                        {
                          name: 'firewallLoggingMode', value: 'AGGREGATE', checked: selectedFirewallLoggingMode === 'AGGREGATE', label: t('NSS_FEEDS_AGGREGATE_LOGS'),
                        },
                        {
                          name: 'firewallLoggingMode', value: 'ALL', checked: selectedFirewallLoggingMode === 'ALL', label: t('NSS_FEEDS_BOTH_SESSION_AND_AGGREGATE_LOGS'),
                        }]} />
                    </div>
                  )}
              <div className="checkbox-container half-width marginTop13">
                <FormFieldLabel
                  tooltip={t('TOOLTIP_NSS_FEED_OUTPUT_TYPE')}
                  text={t('NSS_FEED_OUTPUT_TYPE')} />
                <Field
                  id="nssFeedType"
                  name="nssFeedType"
                  component={NssFeedsModalDropdown}
                  autoFillReduxForm={autoFillFormValues}
                  onChange={actions.handleNssFeedsFeedOutputTypeChange} />
              </div>
              <div className="checkbox-container half-width marginTop13">
                <FormFieldLabel
                  tooltip={t('TOOLTIP_NSS_FEED_ESCAPE_CHARACTER')}
                  text={t('NSS_FEED_ESCAPE_CHARACTER')} />
                {!custEscCharEditable
                    && <p className="disabled-input">{renderCustChar}</p>}
                {custEscCharEditable
                      && (
                        <Field
                          name="customEscapedCharacter"
                          id="customEscapedCharacter"
                          component={Input}
                          type="text"
                          placeholder={t('TYPE_FEED_ESCAPE_CHARACTER')}
                          validate={[
                            maxLength4,
                            validatecustomEscapedCharacter,
                          ]} />
                      )}
              </div>

              {/* Tabs for Different FeedOutputFormat for different EC_METRICS_RECORD_TYPES */}
              {(selectedLogType === 'MULTIFEEDLOG')
                  && (
                    <div className="full-width marginTop22">
                      <FormFieldLabel
                        tooltip={t('TOOLTIP_NSS_FEED_OUTPUT_FORMAT')}
                        text={t('NSS_FEED_OUTPUT_FORMAT')} />
                      <div className="full-width">
                        <TabSwitch
                          items={metricsRecordTypesList}
                          setValue={actions.setMetricsRecordTypeTabFilter}
                          currentValue={selectedMetricsRecordTypesTab}
                          t={t} />
                        {selectedMetricsRecordTypesTab.label === 'VM_GBL_METRICS' && this.vmGblFilters()}
                        {selectedMetricsRecordTypesTab.label === 'INST_GBL_METRICS' && this.InstanceGblFilters()}
                        {selectedMetricsRecordTypesTab.label === 'DEVICE_METRICS' && this.DeviceMetricsFilters()}
                      </div>
                    </div>
                  )}

              {/* REGULAR FeedOutputFormat for other logTypes (Session and DNS) */}
              {(selectedLogType && selectedLogType !== 'MULTIFEEDLOG')
                  && (
                    <div className="form-section textarea-wrapper nss-feeds-text-area marginTop13">
                      <FormFieldLabel
                        tooltip={t('TOOLTIP_NSS_FEED_OUTPUT_FORMAT')}
                        text={t('NSS_FEED_OUTPUT_FORMAT')} />
                      <Field
                        name="feedOutputFormat"
                        id="feedOutputFormat"
                        customErrorText="DESCRIPTION_MAX_LIMIT_ERROR"
                        component={Input}
                        type="textarea"
                        className="form-textarea"
                        onChange={actions.handleNssFeedOutputFormatChange}
                        validate={[
                          maxLength10240,
                        ]} />
                    </div>
                  )}
              <div className="checkbox-container half-width marginTop13">
                <FormFieldLabel
                  tooltip={t('TOOLTIP_NSS_FEED_TIMEZONE')}
                  text={t('NSS_FEED_TIMEZONE')} />
                <Field
                  id="timeZone"
                  name="timeZone"
                  component={NssFeedsModalDropdown}
                  autoFillReduxForm={autoFillFormValues}
                  onChange={actions.handleNssFeedsTimezoneChange}
                  validate={[
                    required,
                  ]} />
              </div>
              <div className="checkbox-container half-width marginTop13">
                <FormFieldLabel
                  tooltip={t('TOOLTIP_NSS_FEED_DUPLICATE_LOGS')}
                  text={t('NSS_FEED_DUPLICATE_LOGS')} />
                <Field
                  id="duplicateLogs"
                  name="duplicateLogs"
                  component={NssFeedsModalDropdown}
                  autoFillReduxForm={autoFillFormValues}
                  onChange={actions.handleNssFeedsDuplicateLogsChange}
                  validate={[
                    required,
                  ]} />
              </div>
            </div>
          </div>

          <div className="form-sections-container nss-feeds-filters-section">
            <FormSectionLabel text={t('NSS_FEED_FILTERS')} />
            <div className="form-section nss-feeds-filters-container">
              {selectedLogType === 'ECLOG'
                    && (
                      <TabSwitch
                        items={FILTERS_SESSION_TAB_LIST}
                        setValue={actions.setSessionTabFilter}
                        currentValue={selectedSessionFilterTab}
                        t={t} />
                    )}
              {selectedLogType === 'ECLOG'
                    && (
                      <>
                        {selectedSessionFilterTab.label === 'ACTIONS' && <ActionsFilters />}
                        {selectedSessionFilterTab.label === 'SOURCE' && <SourceFilters /> }
                        {selectedSessionFilterTab.label === 'DESTINATION' && <DestinationFilters />}
                        {selectedSessionFilterTab.label === 'GATEWAY' && <GatewayFilters />}
                      </>
                    )}
              {selectedLogType === 'EC_DNSLOG'
                    && (
                      <TabSwitch
                        items={FILTERS_DNS_TAB_LIST}
                        setValue={actions.setDnsTabFilter}
                        currentValue={selectedDnsFilterTab}
                        t={t} />
                    )}
              {selectedLogType === 'EC_DNSLOG'
                    && (
                      <>
                        {selectedDnsFilterTab.label === 'DNS_ACTIONS' && <DnsActionsFilters />}
                        {selectedDnsFilterTab.label === 'DNS_SOURCE' && <DnsSourceFilters />}
                        {selectedDnsFilterTab.label === 'DNS_DESTINATION' && <DnsDestinationFilters />}
                        {selectedDnsFilterTab.label === 'DNS_TRANSACTION' && <DnsTransactionFilters />}
                      </>
                    )}
              {(selectedLogType && selectedLogType === 'MULTIFEEDLOG')
                    && (
                      <TabSwitch
                        items={FILTERS_METRICS_TAB_LIST}
                        setValue={actions.setMetricsTabFilter}
                        currentValue={selectedMetricsFilterTab}
                        t={t} />
                    )}
              {(selectedLogType && selectedLogType === 'MULTIFEEDLOG')
                    && (
                      <>
                        {selectedMetricsFilterTab.label === 'SOURCE' && <MetricsFilters />}
                      </>
                    )}
              {(selectedLogType && selectedLogType === 'EC_EVENTLOG')
                    && (
                      <TabSwitch
                        items={FILTERS_METRICS_TAB_LIST}
                        setValue={actions.setMetricsTabFilter}
                        currentValue={selectedMetricsFilterTab}
                        t={t} />
                    )}
              {(selectedLogType && selectedLogType === 'EC_EVENTLOG')
                    && (
                      <>
                        {selectedMetricsFilterTab.label === 'SOURCE' && <MetricsFilters />}
                      </>
                    )}
            </div>
          </div>
        </div>
        <div className="dialog-footer">
          <button type="submit" className={`submit ${!valid ? 'disabled' : ''}`}>{t('SAVE')}</button>
          <button type="button" className="cancel" onClick={() => cancelHandle(false)}>{t('CANCEL')}</button>
        </div>
      </form>
    );
  }
}

const nssFeedsFormSubmissionFailed = () => {
  // console.log('failed and errors', errors);
};

const validateNssFeedsForm = () => {
  // console.log('reaches here');
};

const NssFeedsForm = reduxForm({
  form: 'addEditNssFeedsForm',
  validate: validateNssFeedsForm,
  onSubmitFail: nssFeedsFormSubmissionFailed,
})(BasicCustomAppForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleForm,
    addNssFeed: saveForm,
    loadNssServersData,
    handleNssFeedsNssServerChange,
    handleNssFeedsStatusChange,
    handleNssFeedsNssLogTypeChange,
    setMetricsRecordTypeTabFilter,
    handleMetricsRecordTypeChange,
    handleNssFeedsFirewallLoggingModeChange,
    handleNssFeedsSiemDestTypeChange,
    handleNssFeedsSiemRateChange,
    handleNssFeedsTimezoneChange,
    handleNssFeedsFeedOutputTypeChange,
    handleNssFeedOutputFormatChange,
    handleMetricsVmFeedOutputFormatChange,
    handleMetricsInstanceFeedOutputFormatChange,
    handleMetricsDeviceFeedOutputFormatChange,
    handleNssFeedsDuplicateLogsChange,
    setSessionTabFilter,
    setDnsTabFilter,
    autoFillFormValues: autofill,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => {
  const appData = NssFeedsSelectors.appDataSelector(state);
  const {
    dnsActions,
    fwdType,
    dnsResponseTypes,
    dnsRequestTypes,
  } = appData;
  if (isArray(dnsActions) && typeof dnsActions[0] === 'string') {
    set(appData, 'dnsActions', getObjectArray(dnsActions));
  }

  if (isArray(fwdType) && typeof fwdType[0] === 'string') {
    set(appData, 'fwdType', getObjectArray(fwdType));
  }

  if (isArray(dnsResponseTypes) && typeof dnsResponseTypes[0] === 'string') {
    set(appData, 'dnsResponseTypes', getObjectArray(dnsResponseTypes));
  }

  if (isArray(dnsRequestTypes) && typeof dnsRequestTypes[0] === 'string') {
    set(appData, 'dnsRequestTypes', getObjectArray(dnsRequestTypes));
  }
  return ({
    initialValues: appData,
    nssType: NssFeedsSelectors.nssTypeSelector(state),
    tabConfigurationSession: NssFeedsSelectors.sessionFiltersSelector(state),
    tabConfigurationDns: NssFeedsSelectors.dnsFiltersSelector(state),
    selectedTab: NssFeedsSelectors.currentFilterTabSelector(state),
    selectedSessionFilterTab: NssFeedsSelectors.currentSessionFilterTabSelector(state),
    selectedDnsFilterTab: NssFeedsSelectors.currentDnsFilterTabSelector(state),
    selectedMetricsFilterTab: NssFeedsSelectors.currentMetricsFilterTabSelector(state),
    modalLoading: NssFeedsSelectors.modalLoadingSelector(state),
    selectedStatus: NssFeedsSelectors.statusSelector(state),
    selectedLogType: NssFeedsSelectors.logTypeSelector(state),
    metricsRecordTypesList: NssFeedsSelectors.metricsRecordTypeTabListSelector(state),
    selectedMetricsRecordTypesTab: NssFeedsSelectors.currentMetricsRecordTypeTabSelector(state),
    selectedFeedOutputType: NssFeedsSelectors.feedOutputTypeSelector(state),
    selectedCustomEscapedCharacter: NssFeedsSelectors.feedEscapeCharacterSelector(state),
    selectedFirewallLoggingMode: NssFeedsSelectors.firewallLoggingModeSelector(state),
    selectedSiemRate: NssFeedsSelectors.siemRateSelector(state),
    selectedDestType: NssFeedsSelectors.destTypeSelector(state),
    selectedFeedOutputFormat: NssFeedsSelectors.feedOutputFormatSelector(state),
    allFeedOutputFormats: NssFeedsSelectors.feedOutputFormatAllValuesSelector(state),
    selectedVmGblMetricsfeedOutputFormatSelector:
      NssFeedsSelectors.vmGblMetricsfeedOutputFormatSelector(state),
    selectedInstGblMetricsfeedOutputFormatSelector:
      NssFeedsSelectors.instGblMetricsFeedOutputFormatSelector(state),
    selectedDeviceMetricsfeedOutputFormatSelector:
      NssFeedsSelectors.deviceMetricsFeedOutputFormatSelector(state),
  });
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(NssFeedsForm));

// @flow
import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm } from 'redux-form';
import Loading from 'components/spinner/Loading';
import { FormSectionLabel, FormFieldLabel } from 'components/label';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import TabSwitch from 'components/tabSwitch';
import { FILTERS_SESSION_TAB_LIST, FILTERS_DNS_TAB_LIST } from 'ducks/nssSettingsFeeds/constants';
import {
  toggleViewModal,
  loadNssServersData,
  handleNssFeedsNssServerChange,
  handleNssFeedsStatusChange,
  handleNssFeedsNssLogTypeChange,
  handleNssFeedsFirewallLoggingModeChange,
  handleNssFeedsSiemRateChange,
  handleNssFeedsSiemDestTypeChange,
  handleNssFeedsFeedOutputTypeChange,
  handleNssFeedsTimezoneChange,
  handleNssFeedsDuplicateLogsChange,
  setSessionTabFilter,
  setDnsTabFilter,
} from 'ducks/nssSettingsFeeds';
import * as NssFeedsSelectors from 'ducks/nssSettingsFeeds/selectors';
import {
  ActionsFilters, SourceFilters, DestinationFilters, GatewayFilters,
  DnsActionsFilters, DnsSourceFilters, DnsDestinationFilters, DnsTransactionFilters,
} from '../NssFeedsForm/components';

export class BasicCustomAppForm extends PureComponent {
  static propTypes = {
    actions: PropTypes.shape({
      cancelHandle: PropTypes.func,
      loadNssServersData: PropTypes.func,
      setDnsTabFilter: PropTypes.func,
      setSessionTabFilter: PropTypes.func,
    }),
    t: PropTypes.func,
    selectedStatus: PropTypes.string,
    selectedLogType: PropTypes.string,
    selectedFirewallLoggingMode: PropTypes.string,
    selectedSiemRate: PropTypes.string,
    selectedDestType: PropTypes.string,
    initialValues: PropTypes.shape(),
    modalLoading: PropTypes.bool,
    selectedSessionFilterTab: PropTypes.shape(),
    selectedDnsFilterTab: PropTypes.shape(),
    nssType: PropTypes.string,
  };

  static defaultProps = {
    actions: {
      cancelHandle: noop,
    },
    t: (str) => str,
    initialValues: {},
    modalLoading: false,
    selectedSessionFilterTab: {},
    selectedDnsFilterTab: {},
    nssType: '',
  };

  render() {
    const {
      actions,
      t,
      initialValues,
      nssType,
      selectedSessionFilterTab,
      selectedDnsFilterTab,
      modalLoading,
      selectedStatus,
      selectedLogType,
      selectedFirewallLoggingMode,
      selectedSiemRate,
      selectedDestType,
    } = this.props;
    const {
      cancelHandle,
    } = actions;

    return (
      <form className="add-custom-app-form add-edit-nss-feeds">
        <Loading loading={modalLoading} />
        <div className="form-sections-container">
          <FormSectionLabel text={t('NSS_FEED')} />
          <div className="form-section">
            <div className="half-width">
              <FormFieldLabel
                tooltip={t('TOOLTIP_NSS_FEED_NAME')}
                text={t('NSS_FEED_NAME')} />
              <p className="disabled-input">{t(initialValues.name)}</p>
            </div>
            <div className="half-width">
              <FormFieldLabel
                tooltip={t('TOOLTIP_NSS_TYPE')}
                text={t('NSS_TYPE')} />
              <p className="disabled-input">{t(nssType)}</p>
            </div>
            <div className="half-width">
              <FormFieldLabel
                tooltip={t('TOOLTIP_NSS_FEED_SERVER')}
                text={t('NSS_FEED_SERVER')} />
              <p className="disabled-input">{t(initialValues.nssServer.name)}</p>
            </div>
            <div className="radio-button-container nss-feeds-dialog-radio-container half-width">
              <FormFieldLabel
                tooltip={t('TOOLTIP_NSS_FEED_STATUS')}
                text={t('STATUS')} />
              <p className="disabled-input">{t(selectedStatus)}</p>
            </div>
          </div>
        </div>

        <div className="form-sections-container">
          <FormSectionLabel text={t('NSS_FEED_SIEM_CONNECTIVITY')} />
          <div className="form-section">
            <div className="radio-button-container nss-feeds-dialog-radio-container half-width">
              <FormFieldLabel
                tooltip={t('TOOLTIP_NSS_FEED_SIEM_DESTINATION_TYPE')}
                text={t('NSS_FEED_SIEM_DESTINATION_TYPE')} />
              <p className="disabled-input">{t(selectedDestType)}</p>
            </div>
            {selectedDestType === 'DSTN_IP'
              ? (
                <div className="half-width">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_FEED_SIEM_IP_ADDRESS')}
                    text={t('NSS_FEED_SIEM_IP_ADDRESS')} />
                  <p className="disabled-input">{t(initialValues.serviceIp)}</p>
                </div>
              ) : (
                <div className="half-width">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_FEED_SIEM_FQDN')}
                    text={t('NSS_FEED_SIEM_FQDN')} />
                  <p className="disabled-input">{t(initialValues.serviceFQDN)}</p>
                </div>
              )}
            <div className="half-width marginTop13">
              <FormFieldLabel
                tooltip={t('TOOLTIP_NSS_FEED_SIEM_TCP_PORT')}
                text={t('NSS_FEED_SIEM_TCP_PORT')} />
              <p className="disabled-input">{t(initialValues.servicePort)}</p>
            </div>
            <div className="radio-button-container nss-feeds-dialog-radio-container half-width marginTop13">
              <FormFieldLabel
                tooltip={t('TOOLTIP_NSS_FEED_SIEM_RATE')}
                text={t('NSS_FEED_SIEM_RATE')} />
              <p className="disabled-input">{t(selectedSiemRate)}</p>
            </div>
            {selectedSiemRate === 'LIMITED'
              && (
                <div className="half-width marginTop13">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_FEED_SIEM_RATE_LIMIT')}
                    text={t('NSS_FEED_SIEM_RATE_LIMIT')} />
                  <p className="disabled-input">{t(initialValues.epsRateLimit)}</p>

                </div>
              )}
          </div>
        </div>

        <div className="form-sections-container nss-formatting-block">
          <FormSectionLabel text={t('NSS_FEED_FORMATTING')} />
          <div className="form-section">
            <div className="radio-button-container full-width">
              <FormFieldLabel
                tooltip={t('TOOLTIP_NSS_FEED_LOG_TYPE')}
                text={t('NSS_FEED_LOG_TYPE')} />
              <p className="disabled-input">{t(selectedLogType)}</p>
            </div>
            {selectedLogType === 'ECLOG'
              && (
                <div className="radio-button-container full-width marginTop13">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_FEED_SESSION_LOG_TYPE')}
                    text={t('NSS_FEED_SESSION_LOG_TYPE')} />
                  <p className="disabled-input">{t(selectedFirewallLoggingMode)}</p>
                </div>
              )}
            <div className="half-width marginTop13">
              <FormFieldLabel
                tooltip={t('TOOLTIP_NSS_FEED_OUTPUT_TYPE')}
                text={t('NSS_FEED_OUTPUT_TYPE')} />
              <p className="disabled-input">{t(initialValues.nssFeedType)}</p>
            </div>
            <div className="half-width marginTop13">
              <FormFieldLabel
                tooltip={t('TOOLTIP_NSS_FEED_ESCAPE_CHARACTER')}
                text={t('NSS_FEED_ESCAPE_CHARACTER')} />
              <p className="disabled-input">{t(initialValues.customEscapedCharacter)}</p>
            </div>
            {/* <div className="form-section textarea-wrapper nss-feeds-text-area marginTop13">
                <FormFieldLabel
                  tooltip={t('TOOLTIP_NSS_FEED_OUTPUT_FORMAT')}
                  text={t('NSS_FEED_OUTPUT_FORMAT')} />
                <p className="disabled-input">{t(initialValues.feedOutputFormat)}</p>
              </div> */}
            <div className="half-width marginTop13">
              <FormFieldLabel
                tooltip={t('TOOLTIP_NSS_FEED_TIMEZONE')}
                text={t('NSS_FEED_TIMEZONE')} />
              <p className="disabled-input">{t(initialValues.timeZone)}</p>
            </div>
            <div className="half-width marginTop13">
              <FormFieldLabel
                tooltip={t('TOOLTIP_NSS_FEED_DUPLICATE_LOGS')}
                text={t('NSS_FEED_DUPLICATE_LOGS')} />
              <p className="disabled-input">{t(initialValues.duplicateLogs)}</p>
            </div>
          </div>
        </div>

        <div className="form-sections-container nss-feeds-filters-section">
          <FormSectionLabel text={t('NSS_FEED_FILTERS')} />
          <div className="form-section nss-feeds-filters-container">
            {selectedLogType === 'ECLOG'
                && (
                  <TabSwitch
                    items={FILTERS_SESSION_TAB_LIST}
                    setValue={actions.setSessionTabFilter}
                    currentValue={selectedSessionFilterTab}
                    t={t} />
                )}
            {selectedLogType === 'ECLOG'
                && (
                  <>
                    {selectedSessionFilterTab.label === 'ACTIONS' && <ActionsFilters />}
                    {selectedSessionFilterTab.label === 'SOURCE' && <SourceFilters /> }
                    {selectedSessionFilterTab.label === 'DESTINATION' && <DestinationFilters />}
                    {selectedSessionFilterTab.label === 'GATEWAY' && <GatewayFilters />}
                  </>
                )}

            {selectedLogType === 'EC_DNSLOG'
                && (
                  <TabSwitch
                    items={FILTERS_DNS_TAB_LIST}
                    setValue={actions.setDnsTabFilter}
                    currentValue={selectedDnsFilterTab}
                    t={t} />
                )}
            {selectedLogType === 'EC_DNSLOG'
                && (
                  <>
                    {selectedDnsFilterTab.label === 'DNS_ACTIONS' && <DnsActionsFilters />}
                    {selectedDnsFilterTab.label === 'DNS_SOURCE' && <DnsSourceFilters />}
                    {selectedDnsFilterTab.label === 'DNS_DESTINATION' && <DnsDestinationFilters />}
                    {selectedDnsFilterTab.label === 'DNS_TRANSACTION' && <DnsTransactionFilters />}
                  </>
                )}
          </div>
        </div>

        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="button" className="cancel" onClick={() => cancelHandle(undefined, false)}>{t('CANCEL')}</button>
          </div>
        </div>
      </form>
    );
  }
}

const validateNssFeedsForm = () => {
  // console.log('reaches here');
};

const NssFeedsForm = reduxForm({
  form: 'addEditNssFeedsForm',
  validate: validateNssFeedsForm,
})(BasicCustomAppForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleViewModal,
    loadNssServersData,
    handleNssFeedsNssServerChange,
    handleNssFeedsStatusChange,
    handleNssFeedsNssLogTypeChange,
    handleNssFeedsFirewallLoggingModeChange,
    handleNssFeedsSiemDestTypeChange,
    handleNssFeedsSiemRateChange,
    handleNssFeedsTimezoneChange,
    handleNssFeedsFeedOutputTypeChange,
    handleNssFeedsDuplicateLogsChange,
    setSessionTabFilter,
    setDnsTabFilter,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  initialValues: NssFeedsSelectors.appDataSelector(state),
  nssType: NssFeedsSelectors.nssTypeSelector(state),
  tabConfigurationSession: NssFeedsSelectors.sessionFiltersSelector(state),
  tabConfigurationDns: NssFeedsSelectors.dnsFiltersSelector(state),
  selectedTab: NssFeedsSelectors.currentFilterTabSelector(state),
  selectedSessionFilterTab: NssFeedsSelectors.currentSessionFilterTabSelector(state),
  selectedDnsFilterTab: NssFeedsSelectors.currentDnsFilterTabSelector(state),
  modalLoading: NssFeedsSelectors.modalLoadingSelector(state),
  selectedStatus: NssFeedsSelectors.statusSelector(state),
  selectedLogType: NssFeedsSelectors.logTypeSelector(state),
  selectedFeedOutputType: NssFeedsSelectors.feedOutputTypeSelector(state),
  selectedCustomEscapedCharacter: NssFeedsSelectors.feedEscapeCharacterSelector(state),
  selectedFirewallLoggingMode: NssFeedsSelectors.firewallLoggingModeSelector(state),
  selectedSiemRate: NssFeedsSelectors.siemRateSelector(state),
  selectedDestType: NssFeedsSelectors.destTypeSelector(state),
});

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(NssFeedsForm));

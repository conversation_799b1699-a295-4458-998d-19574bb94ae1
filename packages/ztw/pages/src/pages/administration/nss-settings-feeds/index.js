import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import { HELP_ARTICLES, BASE_LAYOUT } from 'config';
import HelpArticle from 'components/HelpArticle';
import AddNewButton from 'components/addNewButton';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import NavTabs from 'components/navTabs';

import RBAC from 'components/rbac';
import ServerError from 'components/errors/ServerError';
import * as constants from 'ducks/login/constants';
import * as NssFeedsSelectors from 'ducks/nssSettingsFeeds/selectors';
import { NSS_FEEDS_TABLE_CONFIGS } from 'ducks/nssSettingsFeeds/constants';
import ConfigTable from 'components/configTable';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasNSSsku, getReadOnly } from 'utils/helpers';
import * as loginSelectors from 'ducks/login/selectors';

import {
  loadNssFeedsData,
  toggleForm,
  toggleDeleteForm,
  deleteNssFeeds,
  toggleViewModal,
} from 'ducks/nssSettingsFeeds';
import {
  NssFeedsForm,
  NssFeedsViewModal,
} from './components';

export class NssSettingsFeeds extends Component {
  static propTypes = {
    t: PropTypes.func,
    accessPrivileges: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    cloudNssProvisioned: PropTypes.bool,
    authType: PropTypes.string,
    nssFeedsTableData: PropTypes.arrayOf(PropTypes.shape()),
    nssServerStatus: PropTypes.string,
    toggleNssFeedsGenericForm: PropTypes.func,
    toggleGatewayForm: PropTypes.func,
    showForm: PropTypes.bool,
    formTitle: PropTypes.string,
    showNssDeploymentForm: PropTypes.bool,
    showDeleteForm: PropTypes.bool,
    selectedRowID: PropTypes.string,
    handleDelete: PropTypes.func,
    toggleDeleteConfirmationForm: PropTypes.func,
    handleViewNssFeedsForm: PropTypes.func,
    modalLoading: PropTypes.bool,
    load: PropTypes.func,
    showViewForm: PropTypes.bool,
  };

  static defaultProps = {
    t: (str) => str,
    accessPrivileges: {},
    accessSubscriptions: [],
    cloudNssProvisioned: false,
    authType: '',
    nssFeedsTableData: [],
    nssServerStatus: '',
    showDeleteForm: false,
    modalLoading: false,
    selectedRowID: null,
    handleDelete: noop,
    toggleDeleteConfirmationForm: noop,
    handleViewNssFeedsForm: noop,
    load: noop,
    showForm: false,
    showViewForm: false,
    formTitle: '',
    showNssDeploymentForm: false,
    toggleNssFeedsGenericForm: noop,
  };

  componentDidMount() {
    const { load } = this.props;
    load();
  }

  getTableData = () => {
    const { nssFeedsTableData, accessPrivileges, authType } = this.props;
    const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_NSS_CONFIGURATION, authType);

    const tableData = nssFeedsTableData.map((row) => {
      return {
        ...row,
        failClosed: row.failClosed,
        isReadOnly: isReadOnly || row.name === 'Default ZIA Gateway',
        isDeletable: !isReadOnly && row.name !== 'Default ZIA Gateway',
        isEditable: !isReadOnly && row.name !== 'Default ZIA Gateway',
      };
    });
    return tableData;
  };

  render() {
    const {
      t,
      accessPrivileges,
      accessSubscriptions,
      cloudNssProvisioned,
      authType,
      showDeleteForm,
      modalLoading,
      selectedRowID,
      handleDelete,
      toggleDeleteConfirmationForm,
      showForm,
      showViewForm,
      formTitle,
      toggleNssFeedsGenericForm,
      handleViewNssFeedsForm,
    } = this.props;

    const hasNssSubscription = hasNSSsku(accessSubscriptions);
    const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_NSS_CONFIGURATION, authType);

    if (!hasNssSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPrivileges.EDGE_CONNECTOR_NSS_CONFIGURATION === 'NONE') {
      return <PermissionRequired accessPermissions={accessPrivileges} />;
    }

    const tabConfiguration = [
      {
        id: 'nss-settings-servers',
        title: t('NSS_SERVERS'),
        to: `${BASE_LAYOUT}/administration/nss-settings-servers`,
      },
      {
        id: 'nss-settings-feeds',
        title: t('NSS_FEEDS'),
        to: `${BASE_LAYOUT}/administration/nss-settings-feeds`,
      },
      ...(cloudNssProvisioned ? [{
        id: 'nss-settings-cloud-feeds',
        title: t('NSS_CLOUD_FEEDS'),
        to: `${BASE_LAYOUT}/administration/nss-settings-cloud-feeds`,
      }] : []),
    ];
    
    return (
      <>
        <HelpArticle article={HELP_ARTICLES.NSS_FEEDS} />
        <div className="main-container">
          <div className="nss-title">
            <NavTabs
              isPageHeader
              tabConfiguration={tabConfiguration} />
          </div>
          <div className="nss-feeds-page">
            {!isReadOnly && <AddNewButton label={t('ADD_NSS_FEED')} clickCallback={() => toggleNssFeedsGenericForm(null, true, 'ADD_EC_NSS_FEED')} />}
          </div>
          <div className="nss-feeds-wrapper nss-feeds">
            <RBAC privilege={constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_NSS_CONFIGURATION}>
              <Loading {...this.props}>
                <ServerError {...this.props}>
                  <ConfigTable
                    {...NSS_FEEDS_TABLE_CONFIGS}
                    onHandleRowEdit={toggleNssFeedsGenericForm}
                    onHandleRowDelete={toggleDeleteConfirmationForm}
                    selectedRowID={selectedRowID}
                    onHandleRowView={handleViewNssFeedsForm}
                    data={this.getTableData()} />
                  <Modal
                    title={t(formTitle)}
                    isOpen={showForm}
                    styleClass="nss-feeds-modal"
                    closeModal={() => toggleNssFeedsGenericForm(null, false, 'ADD_EC_NSS_FEED')}>
                    <NssFeedsForm />
                  </Modal>
                  <Modal
                    title={t('VIEW_NSS_FEEDS')}
                    isOpen={showViewForm}
                    closeModal={() => handleViewNssFeedsForm(false)}>
                    <NssFeedsViewModal />
                  </Modal>
               
                  <Modal
                    title={t('DELETE_CONFIRMATION')}
                    isOpen={showDeleteForm}
                    closeModal={() => toggleDeleteConfirmationForm(null, false)}>
                    <DeleteConfirmationForm
                      modalLoading={modalLoading}
                      selectedRowID={selectedRowID}
                      handleCancel={toggleDeleteConfirmationForm}
                      handleDelete={handleDelete} />
                  </Modal>
                </ServerError>
              </Loading>
            </RBAC>
          </div>
        </div>
      </>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    ...NssFeedsSelectors.baseSelector(state),
    selectedRowID: NssFeedsSelectors.selectedRowIDSelector(state),
    modalLoading: NssFeedsSelectors.modalLoadingSelector(state),
    authType: loginSelectors.authTypeSelector(state),
    accessPrivileges: loginSelectors.accessPermissionsSelector(state),
    accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
    cloudNssProvisioned: loginSelectors.cloudNssProvisionedSelector(state),
  };
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    // load: localData,
    load: loadNssFeedsData,
    toggleNssFeedsGenericForm: toggleForm,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deleteNssFeeds,
    handleViewNssFeedsForm: toggleViewModal,
  }, dispatch);
  return actions;
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(NssSettingsFeeds));

// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { handleOnSearchFilter, toggleAddForm } from 'ducks/roleManagement';
import { withTranslation } from 'react-i18next';
import AddNewButton from 'components/addNewButton';
import SimpleSearchInput from 'components/Input/SimpleSearchInput';

export function RoleManagementSearch({ t, isReadOnly, actions }) {
  return (
    <div className="table-layout-header">
      <div className="controls-container">
        <div className="grid-toolbar-left">
          {!isReadOnly && <AddNewButton label={t('ADD_CLOUD_CONNECTOR_ROLE')} clickCallback={() => actions.toggleAddForm(true, {}, 'ADD_CLOUD_CONNECTOR_ROLE')} />}
        </div>
        <div className="grid-toolbar-right">
          <SimpleSearchInput
            withButton
            showCancel={actions.showCancel}
            onKeyPressCb={actions.handleOnSearchFilter} />
        </div>
      </div>
    </div>
  );
}

RoleManagementSearch.propTypes = {
  t: PropTypes.func,
  actions: PropTypes.shape(),
  isReadOnly: PropTypes.bool,
};

RoleManagementSearch.defaultProps = {
  t: null,
  actions: {},
  isReadOnly: true,
};

const mapStateToProps = (state, ownProps) => {
  return {
    ...ownProps,
    ...state.locations,
  };
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleAddForm,
    handleOnSearchFilter,
  }, dispatch);
  
  return {
    actions,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(
  withTranslation()(RoleManagementSearch),
);

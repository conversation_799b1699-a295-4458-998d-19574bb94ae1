/* eslint-disable max-len */
// @flow
import React, { PureComponent, Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm, Field, autofill } from 'redux-form';
import Input from 'components/Input';
import KeyValueInputs from 'components/KeyValueInputs';
import Loading from 'components/spinner/Loading';
import ECRadioGroup from 'components/ecRadioGroup';
import { ToggleCheckBox } from 'components/ecToggle';
import { FormSectionLabel, FormFieldLabel } from 'components/label';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import {
  noop, isArray, set, each, get,
} from 'utils/lodash';

import { faPlusCircle, faEye, faEyeSlash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import {
  required, maxLength, isSiemURL, validateCloudCustomEscapedCharacter, validateUnitBatchSize,
} from 'utils/validations';
import TabSwitch from 'components/tabSwitch';

import {
  FILTERS_SESSION_TAB_LIST, FILTERS_DNS_TAB_LIST, FILTERS_METRICS_TAB_LIST, METRICS_RECORD_TYPE_TAB_LIST,
} from 'ducks/nssSettingsCloudFeeds/constants';

import {
  saveForm,
  toggleForm,
  handleStatusChange,
  handleSiemTypeChange,
  handleMaxBatchSizeChange,
  handleSizeUnitChange,
  handleLogTypeChange,
  setMetricsRecordTypeTabFilter,
  handleMetricsRecordTypeChange,
  handleFirewallLoggingModeChange,
  handleSiemRateChange,
  handleNssFeedTypeChange,
  handleFeedOutputFormatChange,
  handleMetricsVmFeedOutputFormatChange,
  handleMetricsInstanceFeedOutputFormatChange,
  handleMetricsDeviceFeedOutputFormatChange,
  handleTimezoneChange,
  handleHttpHeadersChange,
  setObfuscatedState,
  setSessionTabFilter,
  setDnsTabFilter,
  setMetricsTabFilter,
} from 'ducks/nssSettingsCloudFeeds';
import * as NssCloudFeedsSelectors from 'ducks/nssSettingsCloudFeeds/selectors';
import EcMetricsRecordTypesDropdown from 'commonConnectedComponents/dropdown/EcMetricsRecordTypesDropdown';
import {
  Dropdown, ActionsFilters, SourceFilters, DestinationFilters,
  GatewayFilters, DnsActionsFilters, DnsSourceFilters, DnsDestinationFilters, DnsTransactionFilters,
  MetricsFilters,
} from './components';

const parseDropdownValues = (value) => get(value, 'original', value);

const maxLength50 = maxLength(255);
const maxLength4 = maxLength(4);
const maxLength10240 = maxLength(10240);
const getObjectArray = (array) => {
  const objArray = [];
  each(array, (el) => {
    objArray.push({
      id: el,
      name: el,
    });
  });
  return objArray;
};

export class BasicCustomAppForm extends Component {
  static propTypes = {
    actions: PropTypes.shape({
      cancelHandle: PropTypes.func,
      addNssFeed: PropTypes.func,
      autoFillFormValues: PropTypes.func,
    }),
    valid: PropTypes.bool,
    t: PropTypes.func,
    handleSubmit: PropTypes.func,
    selectedStatus: PropTypes.string,
    selectedLogType: PropTypes.string,
    selectedMaxBatchSize: PropTypes.number,
    metricsRecordTypesList: PropTypes.shape(),
    selectedMetricsRecordTypesTab: PropTypes.shape(),
    selectedFirewallLoggingMode: PropTypes.string,
    selectedSiemRate: PropTypes.string,
    selectedFeedOutputFormat: PropTypes.string,
    allFeedOutputFormats: PropTypes.shape(),
    selectedVmGblMetricsfeedOutputFormatSelector: PropTypes.shape(),
    selectedInstGblMetricsfeedOutputFormatSelector: PropTypes.shape(),
    selectedDeviceMetricsfeedOutputFormatSelector: PropTypes.shape(),
    isSecretObfuscated: PropTypes.bool,
    initialValues: PropTypes.shape(),
    modalLoading: PropTypes.bool,
    nssType: PropTypes.string,
    selectedSessionFilterTab: PropTypes.shape(),
    selectedDnsFilterTab: PropTypes.shape(),
    selectedMetricsFilterTab: PropTypes.shape(),
    selectedNssFeedType: PropTypes.shape(),
    selectedCustomEscapedCharacter: PropTypes.string,
    selectedConnectionHeaders: PropTypes.arrayOf(),
    selectedSiemType: PropTypes.string,
    formMeta: PropTypes.shape(),
    formValues: PropTypes.shape(),
    formSyncErrors: PropTypes.shape(),
  };

  static defaultProps = {
    actions: {
      cancelHandle: noop,
      saveLocationTemplate: noop,
      autoFillFormValues: noop,
    },
    valid: true,
    t: (str) => str,
    handleSubmit: noop,
    initialValues: {},
    modalLoading: false,
    nssType: '',
    metricsRecordTypesList: [],
    selectedMetricsRecordTypesTab: {},
    selectedSessionFilterTab: {},
    selectedDnsFilterTab: {},
    selectedMetricsFilterTab: {},
    selectedNssFeedType: {},
    selectedConnectionHeaders: [],
    isSecretObfuscated: true,
    selectedSiemType: 'SPLUNK',
    formMeta: {},
    formValues: {},
    formSyncErrors: {},
  };

  updateConnectionHeaders = (value, index) => {
    const { actions } = this.props;
    actions.handleHttpHeadersChange('UPDATE', value, index);
  };

  addNewKeyValuePair = (e) => {
    const { actions, selectedConnectionHeaders } = this.props;
    if (selectedConnectionHeaders[0] === ':' || selectedConnectionHeaders[0] === undefined || selectedConnectionHeaders[0] === '') {
      return undefined;
    }
    actions.handleHttpHeadersChange('ADD', null, undefined);
  };

  deleteItem = (index) => {
    const { actions } = this.props;
    actions.handleHttpHeadersChange('DELETE', null, index);
  };

  inputPairFunction = () => {
    const { t, selectedConnectionHeaders } = this.props;
    if (selectedConnectionHeaders[0] === '' || selectedConnectionHeaders[0] === undefined) {
      return (
        <Field
          name="http-headers"
          id="http-headers"
          key="placeholder-header"
          props={{
            t,
            disabled: false,
            index: 0,
            keyValue: '',
            valValue: '',
            placeholderKey: t('TYPE_KEY'),
            placeholderValue: t('TYPE_VALUE'),
            styleConfig: { inputMarginRight: 10 },
            deleteKeyValuePair: this.deleteItem,
            updateConnectionHeaders: this.updateConnectionHeaders,
            // validation: isInteger,
            inputClass: 'half-width',
          }}
          component={KeyValueInputs}
          type="text"
          placeholder={t('TYPE_NSS_FEED_NAME')} />
      );
    }

    return (
      <>
        {
          selectedConnectionHeaders.map((c, index) => {
            return (
              <Field
                name="http-headers"
                id="http-headers"
                // eslint-disable-next-line react/no-array-index-key
                key={`kv-${index}`}
                props={{
                  t,
                  disabled: false,
                  index,
                  keyValue: c.length ? c.split(':')[0] : '',
                  valValue: c.length ? c.split(':')[1] : '',
                  placeholderKey: t('TYPE_KEY'),
                  placeholderValue: t('TYPE_VALUE'),
                  styleConfig: { inputMarginRight: 10 },
                  deleteKeyValuePair: this.deleteItem,
                  updateConnectionHeaders: this.updateConnectionHeaders,
                  // validation: isInteger,
                  inputClass: 'half-width',
                }}
                component={KeyValueInputs}
                type="text"
                placeholder={t('TYPE_NSS_FEED_NAME')} />
            );
          })
        }
      </>
    );
  };

  enableObfuscation = () => {
    const { actions, formValues, selectedSiemType } = this.props;
    const stringActionValueObfuscated = formValues.clientSecret.replace(/./g, '*');
    if (selectedSiemType === 'S3') {
      actions.autoFillFormValues('addEditCloudNssFeedsForm', 'readOnlyClientSecretS3', stringActionValueObfuscated);
    } else if (selectedSiemType === 'AZURE_SENTINEL') {
      actions.autoFillFormValues('addEditCloudNssFeedsForm', 'readOnlyClientSecretSentinel', stringActionValueObfuscated);
    }
    actions.setObfuscatedState(true);
  };

  disableObfuscation = () => {
    const { actions } = this.props;
    actions.setObfuscatedState(false);
  };
  
  handleShowValue = () => {
    const { actions } = this.props;
    actions.setObfuscatedState(false);
  };

  handleHideValue = () => {
    const { actions } = this.props;
    actions.setObfuscatedState(true);
  };

  handleSiemTypeChange = (value) => {
    const { actions } = this.props;
    actions.autoFillFormValues('addEditCloudNssFeedsForm', 'clientId', '');
    actions.autoFillFormValues('addEditCloudNssFeedsForm', 'clientSecret', '');
    if (value && value.id !== 'AZURE_SENTINEL') {
      actions.autoFillFormValues('addEditCloudNssFeedsForm', 'scope', '');
      actions.autoFillFormValues('addEditCloudNssFeedsForm', 'grantType', '');
      actions.autoFillFormValues('addEditCloudNssFeedsForm', 'grantType', '');
      actions.autoFillFormValues('addEditCloudNssFeedsForm', 'authenticationUrl', '');
    }
    if (value && value.id === 'AZURE_SENTINEL') {
      actions.autoFillFormValues('addEditCloudNssFeedsForm', 'oauthAuthentication', true);
    }
    actions.handleSiemTypeChange(value);
  };

  vmGblFilters = () => {
    const { actions, t } = this.props;
    return (
      <div className="form-section textarea-wrapper nss-feeds-text-area marginTop13">
        <Field
          name="feedOutputFormatMetricsVm"
          id="feedOutputFormatMetricsVm"
          customErrorText="DESCRIPTION_MAX_LIMIT_ERROR"
          component={Input}
          // styleClass={feedOutputFormatHasError ? 'error' : ''}
          type="textarea"
          className="form-textarea"
          onChange={actions.handleMetricsVmFeedOutputFormatChange}
          validate={[
            maxLength10240,
          ]} />
      </div>
    );
  };

  InstanceGblFilters = () => {
    const { actions, t } = this.props;
    return (
      <div className="form-section textarea-wrapper nss-feeds-text-area marginTop13">
        <Field
          name="feedOutputFormatMetricsInstance"
          id="feedOutputFormatMetricsInstance"
          customErrorText="DESCRIPTION_MAX_LIMIT_ERROR"
          component={Input}
          // styleClass={feedOutputFormatHasError ? 'error' : ''}
          type="textarea"
          className="form-textarea"
          onChange={actions.handleMetricsInstanceFeedOutputFormatChange}
          validate={[
            maxLength10240,
          ]} />
      </div>
    );
  };

  DeviceMetricsFilters = () => {
    const { actions, t } = this.props;
    return (
      <div className="form-section textarea-wrapper nss-feeds-text-area marginTop13">
        <Field
          name="feedOutputFormatMetricsDevice"
          id="feedOutputFormatMetricsDevice"
          customErrorText="DESCRIPTION_MAX_LIMIT_ERROR"
          component={Input}
          // styleClass={feedOutputFormatHasError ? 'error' : ''}
          type="textarea"
          className="form-textarea"
          onChange={actions.handleMetricsDeviceFeedOutputFormatChange}
          validate={[
            maxLength10240,
          ]} />
      </div>
    );
  };

  render() {
    const {
      valid,
      actions,
      t,
      formMeta,
      formSyncErrors,
      handleSubmit,
      initialValues,
      nssType,
      selectedSessionFilterTab,
      selectedDnsFilterTab,
      selectedMetricsFilterTab,
      modalLoading,
      selectedConnectionHeaders,
      selectedStatus,
      selectedSiemType,
      selectedLogType,
      selectedMaxBatchSize,
      metricsRecordTypesList,
      selectedMetricsRecordTypesTab,
      selectedFirewallLoggingMode,
      selectedNssFeedType,
      selectedCustomEscapedCharacter,
      selectedSiemRate,
      selectedFeedOutputFormat,
      allFeedOutputFormats,
      selectedVmGblMetricsfeedOutputFormatSelector,
      selectedInstGblMetricsfeedOutputFormatSelector,
      selectedDeviceMetricsfeedOutputFormatSelector,
      isSecretObfuscated,
    } = this.props;
    const {
      cancelHandle,
      addNssFeed,
      autoFillFormValues,
    } = actions;

    const nameHasError = formSyncErrors.name && formMeta.name && formMeta.name.touched;
    const epsRateLimitHasError = formSyncErrors.epsRateLimit && formMeta.epsRateLimit && formMeta.epsRateLimit.touched;
    const maxBatchSizeHasError = formSyncErrors.maxBatchSize && formMeta.maxBatchSize && formMeta.maxBatchSize.touched;
    const connectionURLHasError = formSyncErrors.connectionURL && formMeta.connectionURL && formMeta.connectionURL.touched;
    const customEscapedCharacterHasError = formSyncErrors.customEscapedCharacter && formMeta.customEscapedCharacter && formMeta.customEscapedCharacter.touched;

    // need to check whether the API is returning proper error after validating feed and then display in the field
    const feedOutputFormatHasError = formSyncErrors.feedOutputFormat && formMeta.feedOutputFormat && formMeta.feedOutputFormat.touched;

    const clientIdHasError = formSyncErrors.clientId && formMeta.clientId && formMeta.clientId.touched;
    const clientSecretHasError = formSyncErrors.clientSecret && formMeta.clientSecret && formMeta.clientSecret.touched;
    const awsAccessIdHasError = formSyncErrors.awsAccessId && formMeta.awsAccessId && formMeta.awsAccessId.touched;
    const awsSecretKeyHasError = formSyncErrors.awsSecretKey && formMeta.awsSecretKey && formMeta.awsSecretKey.touched;
    const grantTypeHasError = formSyncErrors.grantType && formMeta.grantType && formMeta.grantType.touched;
    const scopeHasError = formSyncErrors.scope && formMeta.scope && formMeta.scope.touched;
    const authenticationURLHasError = formSyncErrors.authenticationUrl && formMeta.authenticationUrl && formMeta.authenticationUrl.touched;

    if (selectedLogType === 'MULTIFEEDLOG') {
      if (selectedVmGblMetricsfeedOutputFormatSelector) {
        autoFillFormValues('addEditCloudNssFeedsForm', 'feedOutputFormatMetricsVm', selectedVmGblMetricsfeedOutputFormatSelector);
      } else if (allFeedOutputFormats) {
        autoFillFormValues('addEditCloudNssFeedsForm', 'feedOutputFormatMetricsVm', allFeedOutputFormats.VM_GBL_METRICS);
      }

      if (selectedInstGblMetricsfeedOutputFormatSelector) {
        autoFillFormValues('addEditCloudNssFeedsForm', 'feedOutputFormatMetricsInstance', selectedInstGblMetricsfeedOutputFormatSelector);
      } else if (allFeedOutputFormats) {
        autoFillFormValues('addEditCloudNssFeedsForm', 'feedOutputFormatMetricsInstance', allFeedOutputFormats.INST_GBL_METRICS);
      }

      if (selectedDeviceMetricsfeedOutputFormatSelector) {
        autoFillFormValues('addEditCloudNssFeedsForm', 'feedOutputFormatMetricsDevice', selectedDeviceMetricsfeedOutputFormatSelector);
      } else if (allFeedOutputFormats) {
        autoFillFormValues('addEditCloudNssFeedsForm', 'feedOutputFormatMetricsDevice', allFeedOutputFormats.DEVICE_METRICS);
      }
    } else if (selectedFeedOutputFormat) {
      autoFillFormValues('addEditCloudNssFeedsForm', 'feedOutputFormat', selectedFeedOutputFormat);
    }

    let custEscCharEditable = true;
    let renderCustChar = '';

    if (initialValues && initialValues.customEscapedCharacter && initialValues.customEscapedCharacter.length) {
      const tempCustChar = initialValues.customEscapedCharacter;
      if (isArray(tempCustChar) && tempCustChar.length === 1) {
        const temp = tempCustChar[0].match(/\d+/g)[0];
        renderCustChar = String.fromCharCode(temp).trim();
      } else if (isArray(tempCustChar)) {
        for (let i = 0; i < tempCustChar.length; i += 1) {
          const temp = tempCustChar[i].match(/\d+/g)[0];
          const decimalVal = String.fromCharCode(temp).trim();
          renderCustChar += decimalVal;
        }
      }

      if ((initialValues.siemType === 'SPLUNK' || selectedSiemType === 'SPLUNK')
          && ((initialValues.nssFeedType === 'CUSTOM' || initialValues.nssFeedType === 'JSON')
            || (selectedNssFeedType === 'JSON' || selectedNssFeedType === 'CUSTOM'))) {
        custEscCharEditable = true;
        autoFillFormValues('addEditNssFeedsForm', 'customEscapedCharacter', renderCustChar);
      } else {
        custEscCharEditable = false;
        renderCustChar = selectedCustomEscapedCharacter;
      }
    }

    autoFillFormValues('addEditCloudNssFeedsForm', 'maxBatchSize', selectedMaxBatchSize);
  
    return (
      <form onSubmit={handleSubmit(addNssFeed)} className="add-custom-app-form add-edit-cloud-nss-feeds">
        <Loading loading={modalLoading} />
        <div className="form-sections-nss-container-parent">
          <div className="form-sections-container">
            <FormSectionLabel text={t('NSS_CLOUD_FEED_GENERAL')} />
            <div className="form-section">
              <>
                <div className="half-width">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_FEED_NAME')}
                    text={t('NSS_FEED_NAME')}
                    styleClass={`${nameHasError ? 'invalid' : ''}`}
                    error={nameHasError ? t(formSyncErrors.name) : ''} />
                  <Field
                    name="name"
                    id="name"
                    component={Input}
                    styleClass={nameHasError ? 'error' : ''}
                    type="text"
                    placeholder={t('TYPE_NSS_FEED_NAME')}
                    validate={[
                      required,
                      maxLength50,
                    ]} />
                </div>
                <div className="half-width">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_TYPE')}
                    text={t('NSS_TYPE')} />
                  <p className="disabled-input">{t(nssType)}</p>
                </div>
                <div className="radio-button-container nss-feeds-dialog-radio-container marginTop13">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_FEED_STATUS')}
                    text={t('STATUS')} />
                  <ECRadioGroup
                    id="nss-feeds-state"
                    name="feedStatus"
                    styleClass="half-width"
                    onChange={actions.handleStatusChange}
                    options={[{
                      name: 'feedStatus', value: 'ENABLED', checked: selectedStatus === 'ENABLED', label: t('ENABLED'),
                    },
                    {
                      name: 'feedStatus', value: 'DISABLED', checked: selectedStatus === 'DISABLED', label: t('DISABLED'),
                    }]} />
                </div>
                <div className="radio-button-container nss-feeds-dialog-radio-container half-width marginTop13">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_FEED_SIEM_RATE')}
                    text={t('NSS_FEED_SIEM_RATE')} />
                  <ECRadioGroup
                    id="siemRate"
                    name="siemRate"
                    styleClass="half-width"
                    onChange={actions.handleSiemRateChange}
                    options={[{
                      name: 'siemRate', value: 'UNLIMITED', checked: selectedSiemRate === 'UNLIMITED', label: t('UNLIMITED'),
                    },
                    {
                      name: 'siemRate', value: 'LIMITED', checked: selectedSiemRate === 'LIMITED', label: t('LIMITED'),
                    }]} />
                </div>
                {selectedSiemRate === 'LIMITED'
                  && (
                    <div className="half-width marginTop13">
                      <FormFieldLabel
                        tooltip={t('TOOLTIP_NSS_FEED_SIEM_RATE_LIMIT')}
                        text={t('NSS_FEED_SIEM_RATE_LIMIT')}
                        styleClass={`${epsRateLimitHasError ? 'invalid' : ''}`}
                        error={epsRateLimitHasError ? t(formSyncErrors.epsRateLimit) : ''} />
                      <Field
                        name="epsRateLimit"
                        id="siemRateLimit"
                        component={Input}
                        styleClass={epsRateLimitHasError ? 'error' : ''}
                        type="text"
                        placeholder={t('TYPE_SIEM_RATE_LIMIT')}
                        validate={[
                          required,
                          maxLength50,
                        ]} />
                    </div>
                  )}
              </>
            </div>
          </div>

          <div className="form-sections-container">
            <FormSectionLabel text={t('NSS_FEED_SIEM_CONNECTIVITY')} />
            <div className="form-section">
              <>
                <div className="half-width">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_CLOUD_FEED_SIEM_TYPE')}
                    text={t('NSS_CLOUD_FEED_SIEM_TYPE')} />
                  <Field
                    id="siemType"
                    name="siemType"
                    component={Dropdown}
                    autoFillReduxForm={autoFillFormValues}
                    onChange={this.handleSiemTypeChange} />
                </div>
                <div className={`half-width ${selectedSiemType === 'S3' ? 'hide-oauth2-flag' : ''}`}>
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_CLOUD_FEED_OAUTH2_AUTHENTICATION')}
                    text={t('NSS_CLOUD_FEED_OAUTH2_AUTHENTICATION')} />
                  <Field
                    id="oauthAuthentication"
                    name="oauthAuthentication"
                    disabled={selectedSiemType === 'AZURE_SENTINEL'}
                    component={ToggleCheckBox}
                    type="checkbox"
                    styleClass="ec-toggle-checkbox" />
                </div>

                {/* -------------------- S3 Attributes -------------------- */}
                {selectedSiemType === 'S3'
                  && (
                    <>
                      <div className="half-width marginTop13">
                        <FormFieldLabel
                          tooltip={t('TOOLTIP_NSS_CLOUD_FEED_AWS_ACCESS_ID')}
                          text={t('NSS_CLOUD_FEED_AWS_ACCESS_ID')}
                          styleClass={`${awsAccessIdHasError ? 'invalid' : ''}`}
                          error={awsAccessIdHasError ? t(formSyncErrors.awsAccessId) : ''} />
                        <Field
                          name="clientId"
                          id="clientId"
                          component={Input}
                          styleClass={awsAccessIdHasError ? 'error' : ''}
                          type="text"
                          placeholder={t('TYPE_NSS_CLOUD_FEED_AWS_ACCESS_ID')}
                          validate={[
                            required,
                            maxLength50,
                          ]} />
                      </div>
                      <div className="half-width marginTop13">
                        <FormFieldLabel
                          tooltip={t('TOOLTIP_NSS_CLOUD_FEED_AWS_SECRET_KEY')}
                          text={t('NSS_CLOUD_FEED_AWS_SECRET_KEY')}
                          styleClass={`${awsSecretKeyHasError ? 'invalid' : ''}`}
                          error={awsSecretKeyHasError ? t(formSyncErrors.awsSecretKey) : ''} />
                        {!isSecretObfuscated ? (
                          <Field
                            name="clientSecret"
                            id="clientSecret"
                            component={Input}
                            styleClass={awsSecretKeyHasError ? 'error' : ''}
                            type="text"
                            placeholder={t('TYPE_NSS_CLOUD_FEED_AWS_SECRET_KEY')}
                            onBlur={() => this.enableObfuscation()}
                            onFocus={() => this.disableObfuscation()}
                            validate={[
                              required,
                              maxLength50,
                            ]} />
                        ) : (
                          <Field
                            name="readOnlyClientSecretS3"
                            id="readOnlyClientSecretS3"
                            component={Input}
                            type="text"
                            placeholder={t('TYPE_NSS_CLOUD_FEED_CLIENT_SECRET')}
                            onFocus={() => this.disableObfuscation()} />
                        )}
                        {isSecretObfuscated ? (
                          <span className="form-link-text reveal-show-hide-icon cloud-nss-action-string-value-show">
                            <FontAwesomeIcon
                              icon={faEye}
                              className="delete-icon"
                              onClick={this.handleShowValue} />
                          </span>
                        ) : (
                          <span className="form-link-text reveal-show-hide-icon cloud-nss-action-string-value-hide">
                            <FontAwesomeIcon
                              icon={faEyeSlash}
                              className="delete-icon"
                              onClick={this.handleHideValue} />
                          </span>
                        )}
                      </div>
                    </>
                  )}

                {/* -------------------- Sentinel Attributes -------------------- */}
                {selectedSiemType === 'AZURE_SENTINEL'
                  && (
                    <>
                      <div className="half-width marginTop13">
                        <FormFieldLabel
                          tooltip={t('TOOLTIP_NSS_CLOUD_FEED_CLIENT_ID')}
                          text={t('NSS_CLOUD_FEED_CLIENT_ID')}
                          styleClass={`${clientIdHasError ? 'invalid' : ''}`}
                          error={clientIdHasError ? t(formSyncErrors.clientId) : ''} />
                        <Field
                          name="clientId"
                          id="clientId"
                          component={Input}
                          styleClass={clientIdHasError ? 'error' : ''}
                          type="text"
                          placeholder={t('TYPE_NSS_CLOUD_FEED_CLIENT_ID')}
                          validate={[
                            required,
                            maxLength50,
                          ]} />
                      </div>
                      <div className="half-width marginTop13">
                        <FormFieldLabel
                          tooltip={t('TOOLTIP_NSS_CLOUD_FEED_CLIENT_SECRET')}
                          text={t('NSS_CLOUD_FEED_CLIENT_SECRET')}
                          styleClass={`${clientSecretHasError ? 'invalid' : ''}`}
                          error={clientSecretHasError ? t(formSyncErrors.clientSecret) : ''} />
                        {!isSecretObfuscated ? (
                          <Field
                            name="clientSecret"
                            id="clientSecret"
                            component={Input}
                            styleClass={clientSecretHasError ? 'error' : ''}
                            type="text"
                            placeholder={t('TYPE_NSS_CLOUD_FEED_CLIENT_SECRET')}
                            onBlur={() => this.enableObfuscation()}
                            onFocus={() => this.disableObfuscation()}
                            validate={[
                              required,
                              maxLength50,
                            ]} />
                        ) : (
                          <Field
                            name="readOnlyClientSecretSentinel"
                            id="readOnlyClientSecretSentinel"
                            component={Input}
                            type="text"
                            placeholder={t('TYPE_NSS_CLOUD_FEED_CLIENT_SECRET')}
                            onFocus={() => this.disableObfuscation()} />
                        )}
                        {isSecretObfuscated ? (
                          <span className="form-link-text reveal-show-hide-icon cloud-nss-action-string-value-show">
                            <FontAwesomeIcon
                              icon={faEye}
                              className="delete-icon"
                              onClick={this.handleShowValue} />
                          </span>
                        ) : (
                          <span className="form-link-text reveal-show-hide-icon cloud-nss-action-string-value-hide">
                            <FontAwesomeIcon
                              icon={faEyeSlash}
                              className="delete-icon"
                              onClick={this.handleHideValue} />
                          </span>
                        )}
                      </div>
                      <div className="half-width marginTop13">
                        <FormFieldLabel
                          tooltip={t('TOOLTIP_NSS_CLOUD_FEED_SCOPE')}
                          text={t('NSS_CLOUD_FEED_SCOPE')}
                          styleClass={`${scopeHasError ? 'invalid' : ''}`}
                          error={nameHasError ? t(formSyncErrors.scope) : ''} />
                        <Field
                          name="scope"
                          id="scope"
                          component={Input}
                          styleClass={scopeHasError ? 'error' : ''}
                          type="text"
                          placeholder="TYPE_NSS_CLOUD_FEED_SCOPE"
                          validate={[
                            required,
                            maxLength50,
                          ]} />
                      </div>
                      <div className="half-width marginTop13">
                        <FormFieldLabel
                          tooltip={t('TOOLTIP_NSS_CLOUD_FEED_GRANT_TYPE')}
                          text={t('NSS_CLOUD_FEED_GRANT_TYPE')}
                          styleClass={`${grantTypeHasError ? 'invalid' : ''}`}
                          error={grantTypeHasError ? t(formSyncErrors.grantType) : ''} />
                        <Field
                          name="grantType"
                          id="grantType"
                          component={Input}
                          styleClass={grantTypeHasError ? 'error' : ''}
                          type="text"
                          placeholder={t('TYPE_NSS_CLOUD_FEED_GRANT_TYPE')}
                          validate={[
                            required,
                            maxLength50,
                          ]} />
                      </div>
                      <div className="full-width marginTop13 marginTop13">
                        <FormFieldLabel
                          tooltip={t('TOOLTIP_NSS_CLOUD_FEED_AUTHENTICATION_URL')}
                          text={t('NSS_CLOUD_FEED_AUTHENTICATION_URL')}
                          styleClass={`${authenticationURLHasError ? 'invalid' : ''}`}
                          error={authenticationURLHasError ? t(formSyncErrors.authenticationUrl) : ''} />
                        <Field
                          name="authenticationUrl"
                          id="authenticationUrl"
                          component={Input}
                          styleClass={authenticationURLHasError ? 'error' : ''}
                          type="text"
                          placeholder={t('TYPE_NSS_CLOUD_FEED_AUTHENTICATION_URL')}
                          validate={[
                            required,
                            isSiemURL,
                          ]} />
                      </div>
                    </>
                  )}

                <div className="half-width marginTop13">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_CLOUD_FEED_MAX_BATCH_SIZE')}
                    text={t('NSS_CLOUD_FEED_MAX_BATCH_SIZE')}
                    styleClass={`${maxBatchSizeHasError ? 'invalid' : ''}`}
                    error={maxBatchSizeHasError ? t(formSyncErrors.maxBatchSize) : ''} />
                  <Field
                    name="maxBatchSize"
                    id="maxBatchSize"
                    component={Input}
                    styleClass={maxBatchSizeHasError ? 'error' : ''}
                    type="text"
                    autoFillReduxForm={autoFillFormValues}
                    onChange={actions.handleMaxBatchSizeChange}
                    placeholder={t('TYPE_NSS_CLOUD_FEED_MAX_BATCH_SIZE')}
                    validate={[
                      required,
                      validateUnitBatchSize,
                    ]} />
                </div>
                <div className="half-width cloud-nss-max-batch-size-unit">
                  <FormFieldLabel
                    text={t('')} />
                  <Field
                    id="sizeUnit"
                    name="sizeUnit"
                    component={Dropdown}
                    autoFillReduxForm={autoFillFormValues}
                    onChange={actions.handleSizeUnitChange} />
                </div>
                <div className="full-width marginTop13">
                  {selectedSiemType === 'S3' ? (
                    <FormFieldLabel
                      tooltip={t('TOOLTIP_NSS_CLOUD_FEEDS_S3_FOLDER_URL')}
                      text={t('NSS_CLOUD_FEEDS_S3_FOLDER_URL')}
                      styleClass={`${connectionURLHasError ? 'invalid' : ''}`}
                      error={connectionURLHasError ? t(formSyncErrors.connectionURL) : ''} />
                  ) : (
                    <FormFieldLabel
                      tooltip={t('TOOLTIP_NSS_CLOUD_FEED_API_URL')}
                      text={t('NSS_CLOUD_FEED_API_URL')}
                      styleClass={`${connectionURLHasError ? 'invalid' : ''}`}
                      error={connectionURLHasError ? t(formSyncErrors.connectionURL) : ''} />
                  )}
                  <Field
                    name="connectionURL"
                    id="connectionURL"
                    component={Input}
                    styleClass={connectionURLHasError ? 'error' : ''}
                    type="text"
                    placeholder={selectedSiemType === 'S3' ? t('TYPE_NSS_CLOUD_FEED_S3_FOLDER_URL') : t('TYPE_NSS_CLOUD_FEED_API_URL')}
                    validate={[
                      required,
                      isSiemURL,
                    ]} />
                </div>
                {/* This below is for the http-headers component */}
                <div className="full-width marginTop22">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_CLOUD_NSS_HTTP_HEADERS')}
                    text={t('CLOUD_NSS_HTTP_HEADERS')} />
                </div>
                {this.inputPairFunction()}
                <div className="full-width">
                  <span
                    role="button"
                    tabIndex="0"
                    className="control-button table-layout-control-buttons add-new-key-value-pair-button"
                    onClick={() => this.addNewKeyValuePair()}
                    onKeyDown={() => this.addNewKeyValuePair()}>
                    <FontAwesomeIcon
                      icon={faPlusCircle}
                      className="add-http-header-icon" />
                    <span>{`${t('ADD_HTTP_HEADER')}`}</span>
                  </span>
                </div>
              </>
            </div>
          </div>

          <div className="form-sections-container nss-formatting-block">
            <FormSectionLabel text={t('NSS_FEED_FORMATTING')} />
            <div className="form-section">
              <>
                <div className="checkbox-container half-width">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_FEED_LOG_TYPE')}
                    text={t('NSS_FEED_LOG_TYPE')} />
                  <Field
                    id="nssLogType"
                    name="nssLogType"
                    component={Dropdown}
                    autoFillReduxForm={autoFillFormValues}
                    onChange={actions.handleLogTypeChange} />
                </div>
                {(selectedLogType && selectedLogType === 'MULTIFEEDLOG')
                  && (
                    <div className="radio-button-container half-width">
                      <FormFieldLabel
                        tooltip={t('TOOLTIP_NSS_FEED_EC_METRICS_RECORD_TYPE')}
                        text={t('NSS_FEED_EC_METRICS_RECORD_TYPE')} />
                      <Field
                        id="metricsRecordType"
                        className="metrics-record-type"
                        name="metricsRecordType"
                        autoFillReduxForm={autoFillFormValues}
                        component={EcMetricsRecordTypesDropdown}
                        onChange={actions.handleMetricsRecordTypeChange}
                        validate={[
                          required,
                        ]}
                        parse={(values = []) => values.map(parseDropdownValues)} />
                    </div>
                  )}
                {(selectedLogType && selectedLogType === 'ECLOG')
                  && (
                    <div className="radio-button-container full-width marginTop13">
                      <FormFieldLabel
                        tooltip={t('TOOLTIP_NSS_FEED_SESSION_LOG_TYPE')}
                        text={t('NSS_FEED_SESSION_LOG_TYPE')} />
                      <ECRadioGroup
                        id="nss-feeds-firewall-log-type"
                        name="firewallLoggingMode"
                        styleClass="full-width"
                        onChange={actions.handleFirewallLoggingModeChange}
                        options={[{
                          name: 'firewallLoggingMode', value: 'SESSION', checked: selectedFirewallLoggingMode === 'SESSION', label: t('NSS_FEEDS_FULL_SESSION_LOGS'),
                        },
                        {
                          name: 'firewallLoggingMode', value: 'AGGREGATE', checked: selectedFirewallLoggingMode === 'AGGREGATE', label: t('NSS_FEEDS_AGGREGATE_LOGS'),
                        },
                        {
                          name: 'firewallLoggingMode', value: 'ALL', checked: selectedFirewallLoggingMode === 'ALL', label: t('NSS_FEEDS_BOTH_SESSION_AND_AGGREGATE_LOGS'),
                        }]} />
                    </div>
                  )}
                <div className="half-width marginTop13">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_FEED_OUTPUT_TYPE')}
                    text={t('NSS_FEED_OUTPUT_TYPE')} />
                  <Field
                    id="nssFeedType"
                    name="nssFeedType"
                    component={Dropdown}
                    autoFillReduxForm={autoFillFormValues}
                    onChange={actions.handleNssFeedTypeChange} />
                </div>
                {(selectedNssFeedType === 'JSON')
                  && (
                    <div className="half-width marginTop13">
                      <FormFieldLabel
                        tooltip={t('TOOLTIP_NSS_CLOUD_FEED_JSON_ARRAY_NOTATION')}
                        text={t('NSS_CLOUD_FEED_JSON_ARRAY_NOTATION')} />
                      <Field
                        id="jsonArrayToggle"
                        name="jsonArrayToggle"
                        component={ToggleCheckBox}
                        type="checkbox"
                        styleClass="ec-toggle-checkbox" />
                    </div>
                  )}
                <div className="half-width marginTop13">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_FEED_ESCAPE_CHARACTER')}
                    text={t('NSS_FEED_ESCAPE_CHARACTER')}
                    styleClass={`${customEscapedCharacterHasError ? 'invalid' : ''}`}
                    error={customEscapedCharacterHasError ? t(formSyncErrors.customEscapedCharacter) : ''} />
                  {!custEscCharEditable
                    && <p className="disabled-input marginTop13">{renderCustChar}</p>}
                  {custEscCharEditable
                      && (
                        <Field
                          name="customEscapedCharacter"
                          id="customEscapedCharacter"
                          component={Input}
                          type="text"
                          placeholder={t('TYPE_FEED_ESCAPE_CHARACTER')}
                          validate={[
                            maxLength4,
                            validateCloudCustomEscapedCharacter,
                          ]} />
                      )}
                </div>

                {/* Tabs for Different FeedOutputFormat for different EC_METRICS_RECORD_TYPES */}
                {(selectedLogType === 'MULTIFEEDLOG')
                  && (
                    <div className="full-width marginTop22">
                      <FormFieldLabel
                        tooltip={t('TOOLTIP_NSS_FEED_OUTPUT_FORMAT')}
                        text={t('NSS_FEED_OUTPUT_FORMAT')} />
                      <div className="full-width">
                        <TabSwitch
                          items={metricsRecordTypesList}
                          setValue={actions.setMetricsRecordTypeTabFilter}
                          currentValue={selectedMetricsRecordTypesTab}
                          t={t} />
                        {selectedMetricsRecordTypesTab.label === 'VM_GBL_METRICS' && this.vmGblFilters()}
                        {selectedMetricsRecordTypesTab.label === 'INST_GBL_METRICS' && this.InstanceGblFilters()}
                        {selectedMetricsRecordTypesTab.label === 'DEVICE_METRICS' && this.DeviceMetricsFilters()}
                      </div>
                    </div>
                  )}

                {/* REGULAR FeedOutputFormat for other logTypes (Session and DNS) */}
                {(selectedLogType && selectedLogType !== 'MULTIFEEDLOG')
                  && (
                    <div className="form-section textarea-wrapper nss-feeds-text-area marginTop13">
                      <FormFieldLabel
                        tooltip={t('TOOLTIP_NSS_FEED_OUTPUT_FORMAT')}
                        text={t('NSS_FEED_OUTPUT_FORMAT')}
                        styleClass={`${feedOutputFormatHasError ? 'invalid' : ''}`}
                        error={feedOutputFormatHasError ? t(formSyncErrors.feedOutputFormat) : ''} />
                      <Field
                        name="feedOutputFormat"
                        id="feedOutputFormat"
                        customErrorText="DESCRIPTION_MAX_LIMIT_ERROR"
                        component={Input}
                        styleClass={feedOutputFormatHasError ? 'error' : ''}
                        type="textarea"
                        className="form-textarea"
                        onChange={actions.handleFeedOutputFormatChange}
                        validate={[
                          maxLength10240,
                        ]} />
                    </div>
                  )}
                <div className="half-width marginTop13">
                  <FormFieldLabel
                    tooltip={t('TOOLTIP_NSS_FEED_TIMEZONE')}
                    text={t('NSS_FEED_TIMEZONE')} />
                  <Field
                    id="timeZone"
                    name="timeZone"
                    component={Dropdown}
                    autoFillReduxForm={autoFillFormValues}
                    onChange={actions.handleTimezoneChange}
                    validate={[
                      required,
                    ]} />
                </div>
              </>
            </div>
          </div>

          <div className="form-sections-container nss-cloud-feeds-filters-section">
            <FormSectionLabel text={t('NSS_FEED_FILTERS')} />
            <div className="form-section nss-feeds-filters-container">
              <>
                {(selectedLogType && selectedLogType === 'ECLOG')
                    && (
                      <TabSwitch
                        items={FILTERS_SESSION_TAB_LIST}
                        setValue={actions.setSessionTabFilter}
                        currentValue={selectedSessionFilterTab}
                        t={t} />
                    )}
                {(selectedLogType && selectedLogType === 'ECLOG')
                    && (
                      <>
                        {selectedSessionFilterTab.label === 'ACTIONS' && <ActionsFilters />}
                        {selectedSessionFilterTab.label === 'SOURCE' && <SourceFilters /> }
                        {selectedSessionFilterTab.label === 'DESTINATION' && <DestinationFilters />}
                        {selectedSessionFilterTab.label === 'GATEWAY' && <GatewayFilters />}
                      </>
                    )}
                {(selectedLogType && selectedLogType === 'EC_DNSLOG')
                    && (
                      <TabSwitch
                        items={FILTERS_DNS_TAB_LIST}
                        setValue={actions.setDnsTabFilter}
                        currentValue={selectedDnsFilterTab}
                        t={t} />
                    )}
                {(selectedLogType && selectedLogType === 'EC_DNSLOG')
                    && (
                      <>
                        {selectedDnsFilterTab.label === 'DNS_ACTIONS' && <DnsActionsFilters />}
                        {selectedDnsFilterTab.label === 'DNS_SOURCE' && <DnsSourceFilters />}
                        {selectedDnsFilterTab.label === 'DNS_DESTINATION' && <DnsDestinationFilters />}
                        {selectedDnsFilterTab.label === 'DNS_TRANSACTION' && <DnsTransactionFilters />}
                      </>
                    )}
                {(selectedLogType && selectedLogType === 'MULTIFEEDLOG')
                    && (
                      <TabSwitch
                        items={FILTERS_METRICS_TAB_LIST}
                        setValue={actions.setMetricsTabFilter}
                        currentValue={selectedMetricsFilterTab}
                        t={t} />
                    )}
                {(selectedLogType && selectedLogType === 'MULTIFEEDLOG')
                    && (
                      <>
                        {selectedMetricsFilterTab.label === 'SOURCE' && <MetricsFilters />}
                      </>
                    )}
                {(selectedLogType && selectedLogType === 'EC_EVENTLOG')
                    && (
                      <TabSwitch
                        items={FILTERS_METRICS_TAB_LIST}
                        setValue={actions.setMetricsTabFilter}
                        currentValue={selectedMetricsFilterTab}
                        t={t} />
                    )}
                {(selectedLogType && selectedLogType === 'EC_EVENTLOG')
                    && (
                      <>
                        {selectedMetricsFilterTab.label === 'SOURCE' && <MetricsFilters />}
                      </>
                    )}
              </>
            </div>
          </div>
        </div>
        <div className="dialog-footer">
          <button type="submit" className={`submit ${!valid ? 'disabled' : ''}`}>{t('SAVE')}</button>
          <button type="button" className="cancel" onClick={() => cancelHandle(false)}>{t('CANCEL')}</button>
        </div>
      </form>
    );
  }
}

const NssCloudFeedsForm = reduxForm({
  form: 'addEditCloudNssFeedsForm',
})(BasicCustomAppForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleForm,
    addNssFeed: saveForm,
    handleStatusChange,
    handleLogTypeChange,
    setMetricsRecordTypeTabFilter,
    handleMetricsRecordTypeChange,
    handleFirewallLoggingModeChange,
    handleSiemRateChange,
    handleTimezoneChange,
    handleSiemTypeChange,
    handleMaxBatchSizeChange,
    handleSizeUnitChange,
    handleFeedOutputFormatChange,
    handleMetricsVmFeedOutputFormatChange,
    handleMetricsInstanceFeedOutputFormatChange,
    handleMetricsDeviceFeedOutputFormatChange,
    handleNssFeedTypeChange,
    handleHttpHeadersChange,
    setSessionTabFilter,
    setDnsTabFilter,
    setMetricsTabFilter,
    setObfuscatedState,
    autoFillFormValues: autofill,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => {
  const appData = NssCloudFeedsSelectors.appDataSelector(state);
  const {
    dnsActions,
    fwdType,
    dnsResponseTypes,
    dnsRequestTypes,
  } = appData;
  if (isArray(dnsActions) && typeof dnsActions[0] === 'string') {
    set(appData, 'dnsActions', getObjectArray(dnsActions));
  }

  if (isArray(fwdType) && typeof fwdType[0] === 'string') {
    set(appData, 'fwdType', getObjectArray(fwdType));
  }

  if (isArray(dnsResponseTypes) && typeof dnsResponseTypes[0] === 'string') {
    set(appData, 'dnsResponseTypes', getObjectArray(dnsResponseTypes));
  }

  if (isArray(dnsRequestTypes) && typeof dnsRequestTypes[0] === 'string') {
    set(appData, 'dnsRequestTypes', getObjectArray(dnsRequestTypes));
  }
  return ({
    initialValues: appData,
    formSyncErrors: NssCloudFeedsSelectors.formSyncErrorsSelector(state),
    formMeta: NssCloudFeedsSelectors.formMetaSelector(state),
    formValues: NssCloudFeedsSelectors.formValuesSelector(state),
    nssType: NssCloudFeedsSelectors.nssTypeSelector(state),
    selectedSessionFilterTab: NssCloudFeedsSelectors.currentSessionFilterTabSelector(state),
    selectedDnsFilterTab: NssCloudFeedsSelectors.currentDnsFilterTabSelector(state),
    selectedMetricsFilterTab: NssCloudFeedsSelectors.currentMetricsFilterTabSelector(state),
    modalLoading: NssCloudFeedsSelectors.modalLoadingSelector(state),
    selectedStatus: NssCloudFeedsSelectors.statusSelector(state),
    selectedConnectionHeaders: NssCloudFeedsSelectors.httpHeadersSelector(state),
    selectedSiemType: NssCloudFeedsSelectors.siemTypeSelector(state),
    selectedLogType: NssCloudFeedsSelectors.nssLogTypeSelector(state),
    selectedMaxBatchSize: NssCloudFeedsSelectors.maxBatchSizeSelector(state),
    metricsRecordTypesList: NssCloudFeedsSelectors.metricsRecordTypeTabListSelector(state),
    selectedMetricsRecordTypesTab: NssCloudFeedsSelectors.currentMetricsRecordTypeTabSelector(state),
    selectedNssFeedType: NssCloudFeedsSelectors.nssFeedTypeSelector(state),
    selectedCustomEscapedCharacter: NssCloudFeedsSelectors.feedEscapeCharacterSelector(state),
    selectedFirewallLoggingMode: NssCloudFeedsSelectors.firewallLoggingModeSelector(state),
    selectedSiemRate: NssCloudFeedsSelectors.siemRateSelector(state),
    selectedFeedOutputFormat: NssCloudFeedsSelectors.feedOutputFormatSelector(state),
    allFeedOutputFormats: NssCloudFeedsSelectors.feedOutputFormatAllValuesSelector(state),
    selectedVmGblMetricsfeedOutputFormatSelector: NssCloudFeedsSelectors.vmGblMetricsfeedOutputFormatSelector(state),
    selectedInstGblMetricsfeedOutputFormatSelector: NssCloudFeedsSelectors.instGblMetricsFeedOutputFormatSelector(state),
    selectedDeviceMetricsfeedOutputFormatSelector: NssCloudFeedsSelectors.deviceMetricsFeedOutputFormatSelector(state),
    isSecretObfuscated: NssCloudFeedsSelectors.isSecretObfuscatedSelector(state),
  });
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(NssCloudFeedsForm));

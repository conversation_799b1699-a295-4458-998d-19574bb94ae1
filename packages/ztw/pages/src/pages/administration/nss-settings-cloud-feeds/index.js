import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import { HELP_ARTICLES, BASE_LAYOUT } from 'config';
import HelpArticle from 'components/HelpArticle';
import AddNewButton from 'components/addNewButton';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import NavTabs from 'components/navTabs';

import RBAC from 'components/rbac';
import ServerError from 'components/errors/ServerError';
import * as constants from 'ducks/login/constants';
import * as NssCloudFeedsSelectors from 'ducks/nssSettingsCloudFeeds/selectors';
import { NSS_CLOUD_FEEDS_TABLE_CONFIGS } from 'ducks/nssSettingsCloudFeeds/constants';
import ConfigTable from 'components/configTable';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasCloudNSSsku, getReadOnly } from 'utils/helpers';
import * as loginSelectors from 'ducks/login/selectors';

import {
  loadData,
  toggleForm,
  toggleDeleteForm,
  deleteFeed,
  toggleViewModal,
  handleTestConnectivity,
} from 'ducks/nssSettingsCloudFeeds';
import {
  NssCloudFeedsForm,
  NssCloudFeedsViewModal,
} from './components';

export class NssSettingsCloudFeeds extends Component {
  static propTypes = {
    t: PropTypes.func,
    accessPrivileges: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    authType: PropTypes.string,
    tableData: PropTypes.arrayOf(PropTypes.shape()),
    nssServerStatus: PropTypes.string,
    toggleNssCloudFeedsGenericForm: PropTypes.func,
    toggleGatewayForm: PropTypes.func,
    showForm: PropTypes.bool,
    formTitle: PropTypes.string,
    showNssDeploymentForm: PropTypes.bool,
    showDeleteForm: PropTypes.bool,
    selectedRowID: PropTypes.string,
    handleDelete: PropTypes.func,
    toggleDeleteConfirmationForm: PropTypes.func,
    handleViewForm: PropTypes.func,
    testConnectivityInProgress: PropTypes.bool,
    modalLoading: PropTypes.bool,
    load: PropTypes.func,
    showViewForm: PropTypes.bool,
    handleTestConnectivity: PropTypes.func,
  };

  static defaultProps = {
    t: (str) => str,
    accessPrivileges: {},
    accessSubscriptions: [],
    authType: '',
    tableData: [],
    nssServerStatus: '',
    showDeleteForm: false,
    modalLoading: false,
    selectedRowID: null,
    handleDelete: noop,
    toggleDeleteConfirmationForm: noop,
    handleViewForm: noop,
    testConnectivityInProgress: false,
    load: noop,
    showForm: false,
    showViewForm: false,
    formTitle: '',
    showNssDeploymentForm: false,
    toggleNssCloudFeedsGenericForm: noop,
    handleTestConnectivity: noop,
  };

  componentDidMount() {
    const { load } = this.props;
    load();
  }

  getTableData = () => {
    const { tableData, accessPrivileges, authType } = this.props;
    const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_NSS_CONFIGURATION, authType);

    const tableDataConfig = tableData.map((row) => {
      return {
        ...row,
        failClosed: row.failClosed,
        isReadOnly: isReadOnly || row.name === 'Default ZIA Gateway',
        isDeletable: !isReadOnly && row.name !== 'Default ZIA Gateway',
        isEditable: !isReadOnly && row.name !== 'Default ZIA Gateway',
      };
    });
    return tableDataConfig;
  };

  render() {
    const {
      t,
      accessPrivileges,
      accessSubscriptions,
      authType,
      showDeleteForm,
      modalLoading,
      selectedRowID,
      handleDelete,
      toggleDeleteConfirmationForm,
      showForm,
      showViewForm,
      formTitle,
      toggleNssCloudFeedsGenericForm,
      handleViewForm,
      testConnectivityInProgress,
    } = this.props;

    const hasNssSubscription = hasCloudNSSsku(accessSubscriptions);
    const isReadOnly = getReadOnly(accessPrivileges.EDGE_CONNECTOR_NSS_CONFIGURATION, authType);

    if (!hasNssSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPrivileges.EDGE_CONNECTOR_NSS_CONFIGURATION === 'NONE') {
      return <PermissionRequired accessPermissions={accessPrivileges} />;
    }
    return (
      <>
        <HelpArticle article={HELP_ARTICLES.NSS_CLOUD_FEEDS} />
        <div className="main-container">
          <div className="nss-title">
            <NavTabs
              isPageHeader
              tabConfiguration={[{
                id: 'nss-settings-servers',
                title: t('NSS_SERVERS'),
                to: `${BASE_LAYOUT}/administration/nss-settings-servers`,
              },
              {
                id: 'nss-settings-feeds',
                title: t('NSS_FEEDS'),
                to: `${BASE_LAYOUT}/administration/nss-settings-feeds`,
              },
              {
                id: 'nss-settings-cloud-feeds',
                title: t('NSS_CLOUD_FEEDS'),
                to: `${BASE_LAYOUT}/administration/nss-settings-cloud-feeds`,
              }]} />
          </div>
          <div className="nss-feeds-page">
            {!isReadOnly && <AddNewButton label={t('ADD_CLOUD_NSS_FEED')} clickCallback={() => toggleNssCloudFeedsGenericForm(null, true, 'ADD_EC_NSS_CLOUD_FEED')} />}
          </div>
          <div className="nss-feeds-wrapper nss-feeds">
            <RBAC privilege={constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_NSS_CONFIGURATION}>
              <Loading {...this.props}>
                <ServerError {...this.props}>
                  <ConfigTable
                    {...NSS_CLOUD_FEEDS_TABLE_CONFIGS}
                    onHandleRowEdit={toggleNssCloudFeedsGenericForm}
                    onHandleRowDelete={toggleDeleteConfirmationForm}
                    onHandleRowTestConnectivity={handleTestConnectivity}
                    selectedRowID={selectedRowID}
                    onHandleRowView={handleViewForm}
                    isTestConnectivityInProgress={testConnectivityInProgress}
                    data={this.getTableData()} />
                  <Modal
                    title={t(formTitle)}
                    isOpen={showForm}
                    styleClass="nss-feeds-modal"
                    closeModal={() => toggleNssCloudFeedsGenericForm(null, false, 'ADD_EC_NSS_FEED')}>
                    <NssCloudFeedsForm />
                  </Modal>
                  <Modal
                    title={t('VIEW_CLOUD_NSS_FEED')}
                    isOpen={showViewForm}
                    closeModal={() => handleViewForm(false)}>
                    <NssCloudFeedsViewModal />
                  </Modal>
               
                  <Modal
                    title={t('DELETE_CONFIRMATION')}
                    isOpen={showDeleteForm}
                    closeModal={() => toggleDeleteConfirmationForm(null, false)}>
                    <DeleteConfirmationForm
                      modalLoading={modalLoading}
                      selectedRowID={selectedRowID}
                      handleCancel={toggleDeleteConfirmationForm}
                      handleDelete={handleDelete} />
                  </Modal>
                </ServerError>
              </Loading>
            </RBAC>
          </div>
        </div>
      </>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    ...NssCloudFeedsSelectors.baseSelector(state),
    selectedRowID: NssCloudFeedsSelectors.selectedRowIDSelector(state),
    modalLoading: NssCloudFeedsSelectors.modalLoadingSelector(state),
    authType: loginSelectors.authTypeSelector(state),
    accessPrivileges: loginSelectors.accessPermissionsSelector(state),
    accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  };
};

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loadData,
    toggleNssCloudFeedsGenericForm: toggleForm,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deleteFeed,
    handleViewForm: toggleViewModal,
    handleTestConnectivity,
  }, dispatch);
  return actions;
};

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(NssSettingsCloudFeeds));

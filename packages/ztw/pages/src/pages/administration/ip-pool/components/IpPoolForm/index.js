// @flow
import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm, Field } from 'redux-form';
// import { Field } from 'redux-form';
import Input from 'components/Input';
import Loading from 'components/spinner/Loading';
import { FormFieldLabel } from 'components/label';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import { required, maxLength, ipAddressesOrRanges } from 'utils/validations';
import ListBuilder from 'components/listBuilder';

import {
  saveForm,
  toggleForm,
  handleIPAddresses,
} from 'ducks/ipPool';
import * as IPGroupsSelectors from 'ducks/ipPool/selectors';

const maxLength255 = maxLength(255);
export class BasicCustomAppForm extends PureComponent {
  static propTypes = {
    actions: PropTypes.shape({
      cancelHandle: PropTypes.func,
      saveSourceIP: PropTypes.func,
      onipAddresses: PropTypes.func,
    }),
    valid: PropTypes.bool,
    t: PropTypes.func,
    handleSubmit: PropTypes.func,
    initialValues: PropTypes.shape(),
    modalLoading: PropTypes.bool,
    ipAddressesList: PropTypes.arrayOf(PropTypes.string),
  };

  static defaultProps = {
    actions: {
      cancelHandle: noop,
      saveSourceIP: noop,
      onipAddresses: noop,
    },
    valid: true,
    t: (str) => str,
    handleSubmit: noop,
    initialValues: {},
    modalLoading: false,
    ipAddressesList: [],
  };

  componentDidMount() {
    const {
      actions,
      initialValues,
    } = this.props;

    const { onipAddresses } = actions;
    const { ipAddresses } = initialValues;
    // ipAddresses loaded from initial form load
    onipAddresses(ipAddresses);
  }

  render() {
    const {
      valid,
      actions,
      t,
      handleSubmit,
      modalLoading,
      ipAddressesList,
    } = this.props;

    const {
      cancelHandle,
      saveSourceIP,
      onipAddresses,
    } = actions;

    const toolTipIPPoolAdresses = t('TOOLTIP_SOURCE_IP_ADDRESSES').split(/{[0-9]}/g);
    // <ul class='tooltip-text-list'><li></li><li></li><li></li></ul><b></b>
    const toolTipJSX = (
      <>
        {toolTipIPPoolAdresses[0]}
        <ul className="tooltip-text-list">
          <li>{toolTipIPPoolAdresses[1]}</li>
          <li>{toolTipIPPoolAdresses[2]}</li>
          <li>{toolTipIPPoolAdresses[3]}</li>
        </ul>
        {toolTipIPPoolAdresses[4]}
        <b>
          {toolTipIPPoolAdresses[5]}
        </b>
        {toolTipIPPoolAdresses[6]}
      </>
    );

    return (
      <form onSubmit={handleSubmit(saveSourceIP)} className="add-custom-app-form add-edit-ip-pool">
        <Loading loading={modalLoading} />
        <div className="form-sections-container">
          <div className="form-section">
            <div className="full-width source-ip-row">
              <Field
                name="name"
                id="name"
                component={Input}
                type="text"
                placeholder={t('TYPE_IP_POOL_NAME')}
                styleClass="half-width"
                label={t('NAME')}
                tooltip={t('TOOLTIP_IP_POOL_NAME')}
                validate={[
                  required,
                  maxLength255,
                ]} />
            </div>
            <div className="full-width source-ip-row">
              <FormFieldLabel
                text={t('IP_ADDRESSES')}
                tooltip={toolTipJSX} />
              <Field
                id="ipAddresses"
                name="ipAddresses"
                attribute="ipAddresses"
                props={{
                  t,
                  hasSearch: true,
                  value: ipAddressesList,
                  action: onipAddresses,
                  disabled: false,
                  removeItemsText: t('REMOVE_ALL'),
                  addButtonText: t('ADD_ITEMS'),
                  placeholder: t('ADD_ITEMS'),
                  styleConfig: { inputMarginRight: 10 },
                  validation: ipAddressesOrRanges,
                }}
                component={ListBuilder} />
            </div>
            <FormFieldLabel
              text={t('DESCRIPTION')}
              tooltip={t('TOOLTIP_GENERAL_DESCRIPTION')} />
            <Field
              name="description"
              id="description"
              component="textarea"
              className="full-width form-textarea"
              label={t('DESCRIPTION')} />
          </div>
        </div>
        <div className="dialog-footer">
          <div className="dialog-footer-left">
            <button type="submit" className={`submit ${!valid ? 'disabled' : ''}`}>{t('SAVE')}</button>
            <button type="button" className="cancel" onClick={() => cancelHandle(null, false)}>{t('CANCEL')}</button>
          </div>
        </div>
      </form>
    );
  }
}

const IPGroupForm = reduxForm({
  form: 'ipGroupForm',
})(BasicCustomAppForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleForm,
    saveSourceIP: saveForm,
    onipAddresses: handleIPAddresses,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  initialValues: IPGroupsSelectors.appDataSelector(state),
  modalLoading: IPGroupsSelectors.modalLoadingSelector(state),
  // ipAddressesList: IPGroupsSelectors.ipAddressesSelector(state),
});

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(IPGroupForm));

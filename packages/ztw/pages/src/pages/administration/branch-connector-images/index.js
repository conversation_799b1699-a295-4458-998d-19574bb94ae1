/* eslint-disable react/jsx-handler-names */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import ReviewGroup from 'components/reviewGroup';
import RBAC from 'components/rbac';
import Modal from 'components/modal';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasBsku, verifyConfigData } from 'utils/helpers';
import * as BranchCloudConnectorImagesSelectors from 'ducks/branchCloudConnectorImages/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import { loadLocalData, downloadImage, loader } from 'ducks/branchCloudConnectorImages';
import * as constants from 'ducks/login/constants';
import { HELP_ARTICLES } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';

import { BRANCH_IMAGES_TABLE_CONFIGS } from 'ducks/branchCloudConnectorImages/constants';
import ConfigTable from 'components/configTable';

import { DownloadProgress } from './components';

export class BranchCloudConnectorImages extends Component {
  static propTypes = {
    actions: PropTypes.shape({
      downloadImage: PropTypes.func,
      loader: PropTypes.func,
      loadlocal: PropTypes.func,
    }),
    appData: PropTypes.shape({}),
    showDownloadForm: PropTypes.bool,
    loaded: PropTypes.number,
    totalDownload: PropTypes.number,
    imagestabledata: PropTypes.arrayOf(PropTypes.shape()),
    imagesAppConnectorTableData: PropTypes.arrayOf(PropTypes.shape()),
    t: PropTypes.func,
    accessPermissions: PropTypes.shape({
      EDGE_CONNECTOR_CLOUD_PROVISIONING: PropTypes.string,
    }),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    esxiLink: PropTypes.string,
    kvmLink: PropTypes.string,
    hyperVLink: PropTypes.string,
  };

  static defaultProps = {
    actions: {},
    appData: {},
    showDownloadForm: false,
    loaded: 0,
    totalDownload: 0,
    imagestabledata: [],
    imagesAppConnectorTableData: [],
    t: (str) => str,
    accessPermissions: {},
    accessSubscriptions: [],
  };

  componentDidMount() {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.CLOUD_AUTOMATION_SCRIPTS });
    const { actions } = this.props;
    actions.loadlocal();
    actions.loader();
  }

  getDownloadURL = (hypervisorName) => {
    const { kvmLink, esxiLink, hyperVLink } = this.props;
    if (hypervisorName === 'KVM') return kvmLink;
    if (hypervisorName === 'ESXI') return esxiLink;
    if (hypervisorName === 'HYPER_V') return hyperVLink;
    return '';
  };

  getTableData = (data, isHybrid) => {
    const tableData = data.map((row) => {
      return {
        id: row.id,
        name: row.name,
        hypervisor: row.hypervisor,
        version: row.version,
        isHybrid,
        isReadOnly: false,
        isDeletable: false,
        isEditable: false,
        isDownloadable: true,
        downloadUrl: this.getDownloadURL(row?.name),
        disableDownload: row.name === 'CENTOS',
      };
    });
    return tableData;
  };

  render() {
    const {
      actions,
      showDownloadForm,
      appData = {},
      loaded,
      totalDownload,
      t,
      accessPermissions,
      accessSubscriptions,
      imagestabledata,
    } = this.props;
    const hasBSubscription = hasBsku(accessSubscriptions);

    if (!hasBSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPermissions.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
      return <PermissionRequired accessPermissions={accessPermissions} />;
    }

    const branchImagesText = t('BC_IMAGES_DESCRIPTION').split(/{[0-9]}/g);
    const branchImagesJSX = (
      <>
        {branchImagesText[0]}
        <a
          target="_blank"
          rel="noopener noreferrer"
          href="https://github.com/zscaler"
          className="tooltip-navlink">
          <b>{branchImagesText[1]}</b>
        </a>
        {branchImagesText[2]}
        <a
          target="_blank"
          rel="noopener noreferrer"
          href="https://help.zscaler.com/cloud-branch-connector/deployment-templates-zscaler-branch-connector-app-connector"
          className="tooltip-navlink">
          <b>{branchImagesText[3]}</b>
        </a>
      </>
    );

    const branchImagesDetailText = t('BC_IMAGES_DETAIL2').split(/{[0-9]}/g);
    const branchImagesDetailJSX = (
      <>
        {branchImagesDetailText[0]}
        <a
          target="_blank"
          rel="noopener noreferrer"
          href="https://help.zscaler.com/cloud-branch-connector/deployment-management-virtual-devices"
          className="tooltip-navlink">
          <b>{branchImagesDetailText[1]}</b>
        </a>
        {branchImagesDetailText[2]}
      </>
    );
    const local = localStorage.getItem('configData');
    const configData = (local && local.length) ? JSON.parse(local) : {};
    const enableMicrosoftHyperV = verifyConfigData({ configData, key: 'enableMicrosoftHyperV' });

    return (
      <div className="bc-images-main-container">
        <div className="page-title">
          {t('BRANCH_CONNECTOR_IMAGES')}
          <div className="bc-images-general-info-description">
            {branchImagesJSX}
          </div>
        </div>
        <RBAC privilege={constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_CLOUD_PROVISIONING}>
          <Loading {...this.props}>
            <HelpArticle article={HELP_ARTICLES.BRANCH_AND_CLOUD_IMAGES} />
            <ServerError {...this.props}>
              <ReviewGroup title={t('BC_CONNECTOR')} itemsNumber={imagestabledata.length}>
                <div className="bc-images-general-info-detail">
                  {t('BC_IMAGES_DETAIL1')}
                  <br />
                  <br />
                  {branchImagesDetailJSX}
                </div>
                <div className="dashboard-row table-content">
                  <ConfigTable
                    {...BRANCH_IMAGES_TABLE_CONFIGS}
                    onHandleRowDownload={actions.downloadImage}
                    data={this.getTableData(
                      imagestabledata.filter((x) => enableMicrosoftHyperV || x.name !== 'HYPER_V'),
                      false,
                    )} />
                </div>
              </ReviewGroup>

            </ServerError>
          </Loading>
        </RBAC>
        <Modal
          title={t('DOWNLOAD_PROGRESS')}
          styleClass="download-progress-modal"
          isOpen={showDownloadForm}>
          <DownloadProgress
            fileName={appData.hypervisor}
            loaded={loaded}
            totalDownload={totalDownload} />
        </Modal>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...BranchCloudConnectorImagesSelectors.baseSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    loadlocal: loadLocalData,
    loader,
    downloadImage,
  }, dispatch);
  return { actions };
};

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(BranchCloudConnectorImages));

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import Modal from 'components/modal';
import AddNewButton from 'components/addNewButton';
import Loading from 'components/spinner/Loading';
import RBAC from 'components/rbac';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import ServerError from 'components/errors/ServerError';
import NavTabs from 'components/navTabs';
import SearchBox from 'components/searchBox';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import StatusConfirmationForm from 'components/DeleteConfirmationForm/provInfoStatusUpdateForm';
import * as constants from 'ducks/login/constants';
import * as ProvisioningTemplatesSelectors from 'ducks/provisioningTemplates/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import { HELP_ARTICLES, BASE_LAYOUT } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import { hasCsku, getReadOnly } from 'utils/helpers';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRefresh } from '@fortawesome/pro-regular-svg-icons';

import {
  toggleWizard,
} from 'ducks/provisioningTemplateWizard';

import {
  loadProvisioningTemplates,
  deleteProvisioningTemplate,
  toggleViewModal,
  handleCopyText,
  toggleDeleteForm,
  handleStatusChange,
  toggleStatusUpdateForm,
} from 'ducks/provisioningTemplates';
import {
  ProvisioningTemplatesTable,
  ProvisioningTemplatesForm,
  ProvisioningTemplateViewModal,
} from './components';

export class ProvisioningTemplates extends Component {
  static propTypes = {
    t: PropTypes.func,
    provisioningTemplatesData: PropTypes.arrayOf(PropTypes.shape()),
    load: PropTypes.func,
    toggleProvTemplateWizard: PropTypes.func,
    handleViewProvForm: PropTypes.func,
    formTitle: PropTypes.string,
    showViewForm: PropTypes.bool,
    handleProvUrlCopyText: PropTypes.func,
    showDeleteForm: PropTypes.bool,
    showStatusUpdateForm: PropTypes.bool,
    modalLoading: PropTypes.bool,
    selectedRowID: PropTypes.string,
    selectedStatus: PropTypes.string,
    handleDelete: PropTypes.func,
    handleStatus: PropTypes.func,
    toggleStatusForm: PropTypes.func,
    toggleDeleteConfirmationForm: PropTypes.func,
    accessPrivileges: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    history: PropTypes.shape(),
    authType: PropTypes.string,
    hasNextPage: PropTypes.bool,
    moreItemsLoading: PropTypes.bool,
  };

  static defaultProps = {
    t: (str) => str,
    provisioningTemplatesData: null,
    load: noop,
    toggleProvTemplateWizard: noop,
    handleViewProvForm: noop,
    formTitle: '',
    showViewForm: false,
    handleProvUrlCopyText: noop,
    showDeleteForm: false,
    showStatusUpdateForm: false,
    modalLoading: false,
    selectedRowID: null,
    selectedStatus: null,
    handleDelete: noop,
    handleStatus: noop,
    toggleStatusForm: noop,
    toggleDeleteConfirmationForm: noop,
    accessPrivileges: {},
    accessSubscriptions: [],
    history: {},
    authType: '',
    hasNextPage: false,
    moreItemsLoading: false,
  };

  constructor(props) {
    super(props);
    this.state = {
      queryParams: {},
    };
  }

  componentDidMount() {
    const urlParams = new URLSearchParams(window.location.search);
    const queryParams = {};
    // eslint-disable-next-line no-restricted-syntax
    for (const [key, value] of urlParams) {
      queryParams[key] = value;
    }
    this.setState({ queryParams });
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.PROVISIONING_TEMPLATES });
    const { load } = this.props;
    load(true);
  }

  openModal = (event) => {
    event.preventDefault();
    // eslint-disable-next-line
    const { toggleProvTemplateWizard } = this.props;
    toggleProvTemplateWizard(null, true, 'NEW');
  };

  render() {
    const {
      provisioningTemplatesData,
      toggleProvTemplateWizard,
      handleViewProvForm,
      showViewForm,
      handleProvUrlCopyText,
      showDeleteForm,
      showStatusUpdateForm,
      modalLoading,
      selectedRowID,
      selectedStatus,
      handleDelete,
      handleStatus,
      toggleStatusForm,
      toggleDeleteConfirmationForm,
      t,
      accessPrivileges,
      accessSubscriptions,
      authType,
      load,
      moreItemsLoading,
      hasNextPage,
    } = this.props;
    const isReadOnly = (accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE' && accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING !== 'NONE') || getReadOnly(accessPrivileges.EDGE_CONNECTOR_TEMPLATE, authType);
    const permKey = [constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_TEMPLATE,
      constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_CLOUD_PROVISIONING];
    const hasCSubscription = hasCsku(accessSubscriptions);
    const tabConfiguration = [];

    // eslint-disable-next-line react/destructuring-assignment
    if (this.state?.queryParams?.filter === 'CC') {
      tabConfiguration.push({
        id: 'provisioning-templates',
        title: t('CLOUD_PROVISIONING'),
        to: `${BASE_LAYOUT}/administration/provisioning-templates?filter=CC`,
      });
    } else {
      tabConfiguration.push({
        id: 'branch-provisioning-templates',
        title: t('BRANCH_CONFIGURATION'),
        to: `${BASE_LAYOUT}/administration/branch-provisioning-templates`,
      });
      tabConfiguration.push({
        id: 'provisioning-templates',
        title: t('CLOUD_PROVISIONING'),
        to: `${BASE_LAYOUT}/administration/provisioning-templates`,
      });
    }

    if (accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE' && accessPrivileges.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
      return <PermissionRequired accessPermissions={accessPrivileges} />;
    }

    return (
      <div className="provisioning-template-page-container">
        <h2 className="page-title">
          {t('PROVISIONING_AND_CONFIGUATION')}
        </h2>
        <div className="main-container provisioning-template-header">
          <div className="configuration-nav-tab">
            <NavTabs tabConfiguration={tabConfiguration} />
          </div>
          <div className="actions-row refresh">
            <button
              type="button"
              className="refresh-button"
              onClick={() => load(true)}>
              <FontAwesomeIcon className="cloud-formation-options-link-icon fa-external-link fa-xs" icon={faRefresh} size="xs" />
              {t('REFRESH')}
            </button>
            <div className="search-container">
              <SearchBox placeholder={t('SEARCH')} clickCallback={() => load(true)} />
            </div>
          </div>
        </div>
        {!hasCSubscription ? <SubscriptionRequired accessSubscriptions={accessSubscriptions} />
          : (
            <div className="main-container provisioning-template-table">
              <div className={`sipg-fragment ${accessPrivileges[permKey] === 'READ_ONLY' ? 'hide' : ''}`}>
                {!isReadOnly && <AddNewButton label={t('ADD_CLOUD_PROVISIONING_TEMPLATE')} clickCallback={this.openModal} />}
              </div>
              <div className="cloud-provider-wrapper prov-templates">
                <RBAC privilege={permKey}>
                  <Loading {...this.props}>
                    <HelpArticle article={HELP_ARTICLES.PROVISIONING_TEMPLATES} />
                    <ServerError {...this.props}>
                      <ProvisioningTemplatesTable
                        authType={authType}
                        accessPrivileges={accessPrivileges}
                        toggleDeleteConfirmationForm={toggleDeleteConfirmationForm}
                        handleViewProvForm={handleViewProvForm}
                        provisioningTemplatesData={provisioningTemplatesData}
                        toggleProvTemplateWizard={toggleProvTemplateWizard}
                        handleProvUrlCopyText={handleProvUrlCopyText}
                        hasNextPage={hasNextPage}
                        moreItemsLoading={moreItemsLoading}
                        toggleStatusForm={toggleStatusForm}
                        loadMore={load} />
                      <ProvisioningTemplatesForm />
                      <Modal
                        title={t('VIEW_PROVISIONING_TEMPLATE')}
                        styleClass="view-cloudconnector-provisioning-modal"
                        isOpen={showViewForm}
                        closeModal={() => handleViewProvForm(null, false)}>
                        <ProvisioningTemplateViewModal />
                      </Modal>
                      <Modal
                        title={t('DELETE_CONFIRMATION')}
                        isOpen={showDeleteForm}
                        closeModal={() => toggleDeleteConfirmationForm(null, false)}>
                        <DeleteConfirmationForm
                          modalLoading={modalLoading}
                          selectedRowID={selectedRowID}
                          handleCancel={toggleDeleteConfirmationForm}
                          handleDelete={handleDelete} />
                      </Modal>
                      <Modal
                        title={t('STATUS_UPDATE_CONFIRMATION')}
                        isOpen={showStatusUpdateForm}
                        closeModal={() => toggleStatusForm(false)}>
                        <StatusConfirmationForm
                          modalLoading={modalLoading}
                          selectedRowID={selectedRowID}
                          selectedStatus={selectedStatus}
                          handleCancel={toggleStatusForm}
                          handleUpdate={handleStatus} />
                      </Modal>
                    </ServerError>
                  </Loading>
                </RBAC>
              </div>
            </div>
          )}
        
      </div>
    );
  }
}

const stateProps = (state) => ({
  ...ProvisioningTemplatesSelectors.default(state),
  modalLoading: ProvisioningTemplatesSelectors.modalLoadingSelector(state),
  selectedRowID: ProvisioningTemplatesSelectors.selectedRowIDSelector(state),
  selectedStatus: ProvisioningTemplatesSelectors.selectedRowProvInfoStatusSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const dispatchProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loadProvisioningTemplates,
    toggleProvTemplateWizard: toggleWizard,
    handleViewProvForm: toggleViewModal,
    handleProvUrlCopyText: handleCopyText,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deleteProvisioningTemplate,
    handleStatus: handleStatusChange,
    toggleStatusForm: toggleStatusUpdateForm,
  }, dispatch);
  return actions;
};

export default connect(stateProps, dispatchProps)(withTranslation()(ProvisioningTemplates));

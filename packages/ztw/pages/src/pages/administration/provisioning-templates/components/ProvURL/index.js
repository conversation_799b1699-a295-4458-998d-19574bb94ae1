import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import ServerError from 'components/errors/ServerError';
import * as ProvisioningTemplatesSelectors from 'ducks/provisioningTemplates/selectors';
import { withTranslation } from 'react-i18next';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCopy } from '@fortawesome/pro-solid-svg-icons';

export class ProvURL extends PureComponent {
  static propTypes = {
    row: PropTypes.shape(),
    t: PropTypes.func,
    handleClick: PropTypes.func,
    isNukeLoading: PropTypes.bool,
    children: PropTypes.oneOfType([
      PropTypes.element,
      PropTypes.arrayOf(PropTypes.element),
      PropTypes.node,
      PropTypes.arrayOf(PropTypes.node),
    ]),
  };

  static defaultProps = {
    row: {},
    t: (str) => str,
    handleClick: null,
    children: null,
  };

  handleCopyClick = (e) => {
    e.preventDefault();
    const parentElementText = e.currentTarget.parentElement.querySelector('.prov-url-text').innerText;

    const { handleClick } = this.props;
    handleClick(e.currentTarget.parentElement, parentElementText);
  };

  render() {
    const {
      row, t, children,
    } = this.props;
    return (
      <ServerError {...this.props}>
        <div className="ec-page prov-url-conatiner">
          <div className="prov-url-wrapper">
            <p className="title-text">{t('URL')}</p>
            <p className="url-text">
              <span className="prov-url-text">{row.original.provUrl}</span>
              <FontAwesomeIcon icon={faCopy} size="lg" onClick={this.handleCopyClick} />
            </p>
          </div>
          <div className="prov-children-wrapper">
            {children}
          </div>
        </div>
      </ServerError>
    );
  }
}

// eslint-disable-next-line max-len
const mapStateToProps = (state) => ({ ...ProvisioningTemplatesSelectors.baseSelector(state) });

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
  }, dispatch);

  return { actions };
};

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(ProvURL));

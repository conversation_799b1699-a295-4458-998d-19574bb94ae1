// @flow

import React from 'react';
import { getFormValues, reduxForm } from 'redux-form';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { FormFieldLabel } from 'components/label';
import { toUpper, isEmpty, noop } from 'utils/lodash';
import * as provisioningTemplatesSelector from 'ducks/provisioningTemplates/selectors';
import * as provisioningTemplateWizardSelectors from 'ducks/provisioningTemplateWizard/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import { setProvisioningTemplateData, updateWizardMode } from 'ducks/provisioningTemplateWizard';
import Loading from 'components/spinner/Loading';
import ReviewGroup from 'components/reviewGroup';
import ServerError from 'components/errors/ServerError';
import { verifyConfigData } from 'utils/helpers';

function ConfigureEdgeconnectorForm(props) {
  const {
    closeModal,
    handleSubmit,
    t,
    handleSetPage,
    previousPage,
    wizardFieldValues,
    cloudName,
    cloud,
    actions,
    initialValues,
    configData,
  } = props;

  // update wizard values based on cloud selection
  wizardFieldValues.platform = cloud ? toUpper(cloudName) : '';
  wizardFieldValues.cloudName = cloudName;

  if (isEmpty(initialValues)) {
    actions.updateMode('EDIT');
    actions.setFormData(wizardFieldValues);
  }
  const { provUrlData = {} } = initialValues || {};
  const { cloudProviderType } = provUrlData || {};

  // const { capacity = {} } = autoScaleDetails;
  // const { min, max, desired } = capacity;
  // const cloudName = cloudProviderType
  const enableASG = (verifyConfigData({ configData, key: 'enableASG' }) && ((cloudName || cloudProviderType) === 'AWS'))
  || (verifyConfigData({ configData, key: 'enableAzureASG' }) && (cloudName || cloudProviderType) === 'AZURE');

  return (
    <form onSubmit={handleSubmit} className="wizard-form configure-provisioning-template">
      <div className="form-sections-container">
        <Loading {...props}>
          <ServerError {...props} size="small">
            <div className="cc-provisioning-general-info-title">
              {t('REVIEW')}
              <div className="cc-provisioning-general-info-description full-width">
                {t('ENSURE_ALL_INFORMATION_IS_CORRECT')}
              </div>
            </div>
            <div className="full-width">
              <ReviewGroup title={t('GENERAL_INFORMATION')} handleEdit={() => handleSetPage(0)}>
                <div className="input-container review half-width">
                  <FormFieldLabel text={t('NAME')} styleClass="no-margin-top" />
                  <div className="review-item">
                    <p className="disabled-input">{wizardFieldValues.name}</p>
                  </div>
                </div>
                <div className="input-container review half-width">
                  <FormFieldLabel text={t('DESCRIPTION')} styleClass="no-margin-top" />
                  <p className="disabled-input break-spaces">{wizardFieldValues.desc}</p>
                </div>
              </ReviewGroup>
              <ReviewGroup title={t('CLOUD_PROVIDER')} handleEdit={() => handleSetPage(1)}>
                <div className="input-container review half-width">
                  <FormFieldLabel text={t('CLOUD_PROVIDER')} styleClass="no-margin-top" />
                  <p className="disabled-input">{wizardFieldValues.cloudName || wizardFieldValues.provUrlData.cloudProviderType}</p>
                </div>
              </ReviewGroup>
              <ReviewGroup title={t('LOCATION')} handleEdit={() => handleSetPage(2)}>
                <div className="input-container review half-width">
                  <FormFieldLabel text={t('LOCATION_CREATION')} styleClass="no-margin-top" />
                  <p className="disabled-input">{t('AUTOMATIC')}</p>
                </div>
                <div className="input-container review half-width">
                  <FormFieldLabel text={t('LOCATION_TEMPLATE')} styleClass="no-margin-top" />
                  <p className="disabled-input">{wizardFieldValues.locationTemplate ? wizardFieldValues.locationTemplate.name : t('NONE')}</p>
                </div>
              </ReviewGroup>
              <ReviewGroup title={t('GROUP_INFORMATION')} handleEdit={() => handleSetPage(3)}>
                <div className="input-container review half-width">
                  <FormFieldLabel text={t('NO_OF_CLOUD_CONNECTOR_GROUPS')} styleClass="no-margin-top" />
                  <p className="disabled-input">{t('AUTOMATIC')}</p>
                </div>
                <div className="input-container review half-width">
                  <FormFieldLabel text={t('EDGE_CONNECTOR_VM_SIZE')} styleClass="no-margin-top" />
                  <p className="disabled-input">{t(wizardFieldValues.formFactor ? wizardFieldValues.formFactor : wizardFieldValues.provUrlData.formFactor)}</p>
                </div>
                {enableASG && (
                  <div className="input-container review half-width">
                    <FormFieldLabel text={t('AUTO_SCALING')} styleClass="no-margin-top" />
                    <p className="disabled-input">{t((wizardFieldValues.provUrlData.autoScaleDetails && wizardFieldValues.provUrlData.autoScaleDetails.autoScale) || false)}</p>
                  </div>
                )}
                {
                  (wizardFieldValues.provUrlData.autoScaleDetails) && (
                    <>
                      
                      {/* <div className="input-container review half-width">
                        <FormFieldLabel text={t('MIN_CAPACITY')} styleClass="no-margin-top" />
                        <p className="disabled-input">{
                          t(wizardFieldValues.provUrlData.autoScaleDetails.capacity.min
                           ? wizardFieldValues.provUrlData.autoScaleDetails.capacity.min : min)}</p>
                      </div>
                      <div className="input-container review half-width">
                        <FormFieldLabel text={t('MAX_CAPACITY')} styleClass="no-margin-top" />
                        <p className="disabled-input">{
                          t(wizardFieldValues.provUrlData.autoScaleDetails.capacity.max
                           ? wizardFieldValues.provUrlData.autoScaleDetails.capacity.max : max)}</p>
                      </div>
                      <div className="input-container review half-width">
                        <FormFieldLabel text={t('DESIRED_CAPACITY')} styleClass="no-margin-top" />
                        <p className="disabled-input">{
                          t(wizardFieldValues.provUrlData.autoScaleDetails.capacity.desired
                           ? wizardFieldValues.provUrlData.autoScaleDetails.capacity.desired
                            : desired)}</p>
                      </div> */}
                    </>
                  )
                }
              </ReviewGroup>
            </div>
          </ServerError>
        </Loading>
      </div>
      <div className="modal-footer">
        <button type="button" className="quit-wizard" onClick={closeModal}>{t('CANCEL')}</button>
        <div>
          <button type="button" className="previous secondary-button" onClick={previousPage}>{t('BACK')}</button>
          <button type="submit" className="next primary-button">{t('SAVE')}</button>
        </div>
      </div>
    </form>
  );
}

ConfigureEdgeconnectorForm.propTypes = {
  closeModal: PropTypes.func,
  handleSubmit: PropTypes.func,
  t: PropTypes.func,
  wizardFieldValues: PropTypes.shape(),
  handleSetPage: PropTypes.func,
  previousPage: PropTypes.func,
  cloud: PropTypes.bool,
  cloudName: PropTypes.string,
  loading: PropTypes.bool,
  initialValues: PropTypes.shape(),
  actions: PropTypes.shape({
    setFormData: PropTypes.func,
    updateMode: PropTypes.func,
  }),
  subscriptions: PropTypes.arrayOf(PropTypes.string),
  configData: PropTypes.shape(),
};

ConfigureEdgeconnectorForm.defaultProps = {
  closeModal: (str) => str,
  handleSubmit: (str) => str,
  t: (str) => str,
  wizardFieldValues: {},
  handleSetPage: (str) => str,
  previousPage: (str) => str,
  cloud: false,
  cloudName: '',
  loading: false,
  actions: {
    setFormData: noop,
    updateMode: noop,
  },
  configData: {},
};

const mapStateToProps = (state) => ({
  ...provisioningTemplateWizardSelectors.default(state),
  wizardFieldValues: getFormValues('provisioningTemplateWizard')(state),
  formValues: getFormValues('provisioningTemplateWizard')(state),
  cloud: provisioningTemplatesSelector.cloudSelector(state),
  cloudName: provisioningTemplatesSelector.cloudNameSelector(state),
  initialValues: provisioningTemplateWizardSelectors.dataSelectorForReview(state),
  subscriptions: loginSelectors.accessSubscriptionSelector(state),
  configData: loginSelectors.configDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    setFormData: setProvisioningTemplateData,
    updateMode: updateWizardMode,
  }, dispatch);

  return {
    actions,
  };
};

const Review = connect(mapStateToProps, mapDispatchToProps)(reduxForm({
  form: 'provisioningTemplateWizard',
  destroyOnUnmount: false,
  forceUnregisterOnUnmount: true,
})(withTranslation()(ConfigureEdgeconnectorForm)));

export default Review;

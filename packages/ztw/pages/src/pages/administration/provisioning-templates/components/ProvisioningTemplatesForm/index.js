/* eslint-disable react/jsx-handler-names */
// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import Modal from 'react-modal';
import { withTranslation } from 'react-i18next';
import { getFormValues, change } from 'redux-form';
import withRouter from 'layout/withRouter';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';
import { HELP_ARTICLES, isOneUI } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import {
  toggleWizard,
  saveProvisioningTemplate,
  setWizardActiveNavPage,
  handleProvisioningPageClick,
} from 'ducks/provisioningTemplateWizard';

import {
  resetFormValues,
} from 'ducks/provisioningTemplates';

import * as provisioningTemplateWizardSelectors from 'ducks/provisioningTemplateWizard/selectors';
import * as provisioningTemplatesSelectors from 'ducks/provisioningTemplates/selectors';

import {
  Cloud,
  Location,
  WizardNav,
  EdgeConnectorGroups,
  Review,
  ProvName,
  ProvisioningUrl,
} from './components';

class ProvisioningTemplatesForm extends React.Component {
  state = {
    wizardNavConfig: [
      'GENERAL_INFORMATION',
      'CLOUD_PROVIDER',
      'LOCATION',
      'GROUP_INFORMATION',
      'REVIEW',
    ],
  };

  componentDidMount() {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.ADD_PROVISIONING_TEMPLATE });
    Modal.setAppElement('#r-app');
  }

  componentWillUnmount() {
    const { actions: { toggleWizardModal }, isProvTemplateWizardOpen } = this.props;

    if (isProvTemplateWizardOpen) {
      toggleWizardModal(false, null, null);
    }
  }

  closeModal = () => {
    const { actions: { toggleWizardModal, resetReduxFormValues } } = this.props;
    resetReduxFormValues();
    toggleWizardModal(false, null, null);
  };

  handleNextPage = () => {
    // eslint-disable-next-line
    const { actions: { setProvTemplateWizardActive }, activePage } = this.props;

    setProvTemplateWizardActive(activePage + 1);
  };

  previousPage = () => {
    // eslint-disable-next-line
    const { actions: { setProvTemplateWizardActive }, activePage } = this.props;

    setProvTemplateWizardActive(activePage - 1);
  };

  goToPage = (pageNum) => {
    // eslint-disable-next-line
    const { actions: { setProvTemplateWizardActive }, enableGoToPage } = this.props;

    if (!enableGoToPage) {
      return;
    }
    setProvTemplateWizardActive(pageNum);
  };

  handleSetPage = (pg) => {
    // eslint-disable-next-line
    const { actions: { setProvTemplateWizardActive } } = this.props;

    setProvTemplateWizardActive(pg);
  };

  onSubmit = (values) => {
    const { actions: { saveProvTemplate } } = this.props;

    saveProvTemplate(values);
  };

  onClickDone = (values) => {
    const { actions: { handleDoneClick, resetReduxFormValues } } = this.props;
    resetReduxFormValues();
    handleDoneClick(values);
  };

  renderForm = (activePage) => {
    switch (activePage) {
    case 0:
      return (
        <ProvName
          onSubmit={this.handleNextPage}
          closeModal={this.closeModal} />
      );
    case 1:
      return (
        <Cloud
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal} />
      );
    case 2:
      return (
        <Location
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal} />
      );
    case 3:
      return (
        <EdgeConnectorGroups
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal} />
      );
    case 4:
      return (
        <Review
          onSubmit={this.onSubmit}
          previousPage={this.previousPage}
          handleSetPage={this.handleSetPage}
          closeModal={this.closeModal} />
      );
    case 5:
      return (
        <ProvisioningUrl
          onSubmit={this.onClickDone}
          closeModal={this.closeModal} />
      );
    default:
      return null;
    }
  };

  render() {
    const {
      t,
      activePage,
      isProvTemplateWizardOpen,
      mode,
      enableGoToPage,
    } = this.props;
    const { wizardNavConfig } = this.state;
    let label;

    switch (mode) {
    case 'NEW':
    case 'PRESET':
      label = t('ADD_PROVISIONING_TEMPLATE');
      break;

    case 'EDIT':
      label = t('EDIT_PROVISIONING_TEMPLATE');
      break;

    default:
      label = '';
      break;
    }
    if (isProvTemplateWizardOpen) {
      return (
        <div className="edgeconnector-page">
          {
            !isOneUI && (
              <div className={`back-to-ec ${enableGoToPage ? '' : 'hide'}`}>
                <FontAwesomeIcon icon={faArrowLeft} size="lg" onClick={this.closeModal} />
                {label}
              </div>
            )
          }
          
          <div className="edgeconnector-modal">
            <div className="modal-content modal-body cc-provisioning-modal-content">
              <Loading {...this.props}>
                <HelpArticle article={HELP_ARTICLES.ADD_PROVISIONING_TEMPLATE} />
                <ServerError {...this.props}>
                  <WizardNav
                    activePage={activePage}
                    goToPage={this.goToPage}
                    wizardNavConfig={wizardNavConfig} />
                  {this.renderForm(activePage)}
                </ServerError>
              </Loading>
            </div>
          </div>
        </div>
      );
    }
    return '';
  }
}

ProvisioningTemplatesForm.propTypes = {
  actions: PropTypes.shape(),
  activePage: PropTypes.number,
  history: PropTypes.shape(),
  match: PropTypes.shape(),
  name: PropTypes.string,
  mode: PropTypes.string,
  isProvTemplateWizardOpen: PropTypes.bool,
  enableGoToPage: PropTypes.bool,
  t: PropTypes.func,
};

ProvisioningTemplatesForm.defaultProps = {
  actions: null,
  activePage: 0,
  history: {},
  match: {},
  name: '',
  mode: 'NEW',
  isProvTemplateWizardOpen: false,
  t: (str) => str,
};

const mapStateToProps = (state) => ({
  ...provisioningTemplateWizardSelectors.default(state),
  ...getFormValues('provisioningTemplateWizard')(state),
  enableGoToPage: provisioningTemplatesSelectors.enableGoToPageSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleWizardModal: toggleWizard,
    updateFormState: change,
    saveProvTemplate: saveProvisioningTemplate,
    setProvTemplateWizardActive: setWizardActiveNavPage,
    handleDoneClick: handleProvisioningPageClick,
    resetReduxFormValues: resetFormValues,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(ProvisioningTemplatesForm)));

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import NavTabs from 'components/navTabs';
import SearchBox from 'components/searchBox';
import AddNewButton from 'components/addNewButton';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import RBAC from 'components/rbac';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasBsku, hasCsku, getReadOnly } from 'utils/helpers';
import ServerError from 'components/errors/ServerError';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import * as constants from 'ducks/login/constants';
import * as LocationTemplatesSelectors from 'ducks/locationTemplates/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import { HELP_ARTICLES, BASE_LAYOUT } from 'config';
import HelpArticle from 'components/HelpArticle';
import PersistentStorage, { LS_HELP_ARTICLE } from 'utils/persistentStorage';
import { LOCATION_TEMPLATE_TABLE_CONFIGS } from 'ducks/locationTemplates/constants';
import ConfigTable from 'components/configTable';

import {
  toggleForm,
  loadLocationTemplateData,
  toggleDeleteForm,
  deleteLocationTemplate,
  toggleViewModal,
} from 'ducks/locationTemplates';
import {
  LocationTemplatesForm,
} from './components';

export class LocationTemplates extends Component {
  static propTypes = {
    t: PropTypes.func,
    locationTemplateData: PropTypes.arrayOf(PropTypes.shape()),
    toggleCloudProvidersForm: PropTypes.func,
    showForm: PropTypes.bool,
    load: PropTypes.func,
    formTitle: PropTypes.string,
    showDeleteForm: PropTypes.bool,
    modalLoading: PropTypes.bool,
    selectedRowID: PropTypes.string,
    handleDelete: PropTypes.func,
    showViewForm: PropTypes.bool,
    toggleDeleteConfirmationForm: PropTypes.func,
    handleLocationTemplateView: PropTypes.func,
    accessPrivileges: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    authType: PropTypes.string,
    moreItemsLoading: PropTypes.bool,
    hasNextPage: PropTypes.bool,
  };

  static defaultProps = {
    t: (str) => str,
    locationTemplateData: null,
    toggleCloudProvidersForm: noop,
    showForm: false,
    load: noop,
    formTitle: '',
    showDeleteForm: false,
    modalLoading: false,
    selectedRowID: null,
    handleDelete: noop,
    showViewForm: false,
    toggleDeleteConfirmationForm: noop,
    handleLocationTemplateView: noop,
    accessPrivileges: {},
    accessSubscriptions: [],
    authType: '',
    moreItemsLoading: false,
    hasNextPage: false,
  };

  componentDidMount() {
    PersistentStorage.setItem({ [LS_HELP_ARTICLE]: HELP_ARTICLES.LOCATION_TEMPLATES });
    const { load } = this.props;
    load(null, null, null, true); // passing fourth arg true when page loads for the first time
  }

  getTableData = () => {
    const {
      locationTemplateData, accessPrivileges, t, authType,
    } = this.props;
    const isReadOnly = (accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE' && accessPrivileges.EDGE_CONNECTOR_LOCATION_MANAGEMENT !== 'NONE') || getReadOnly(accessPrivileges.EDGE_CONNECTOR_TEMPLATE, authType);

    const tableData = locationTemplateData.map((row) => {
      return {
        id: row.id,
        name: row.name,
        desc: row.desc,
        lastModTime: row.lastModTime,
        lastModUid: (row.lastModUid && row.lastModUid.name) || t('NONE'),
        isDeletable: (row.editable && row.name !== 'Default Location Template') && !isReadOnly,
        isReadOnly: !row.editable || row.name === 'Default Location Template' || isReadOnly,
        isEditable: (row.editable && row.name !== 'Default Location Template') && !isReadOnly,
        template: row.template,
      };
    });
    return tableData;
  };

  render() {
    const {
      t,
      toggleCloudProvidersForm,
      showForm,
      formTitle,
      showDeleteForm,
      modalLoading,
      selectedRowID,
      handleDelete,
      toggleDeleteConfirmationForm,
      showViewForm,
      handleLocationTemplateView,
      accessPrivileges,
      authType,
      accessSubscriptions,
      load,
      moreItemsLoading,
      hasNextPage,
    } = this.props;

    const isReadOnly = (accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE'
                        && accessPrivileges.EDGE_CONNECTOR_LOCATION_MANAGEMENT !== 'NONE')
                        || getReadOnly(accessPrivileges.EDGE_CONNECTOR_TEMPLATE, authType);

    const permKey = [constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_TEMPLATE,
      constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_LOCATION_MANAGEMENT];
      
    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    // if (!hasBSubscription && !hasCSubscription) {
    //   return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    // }

    // if (accessPrivileges.EDGE_CONNECTOR_TEMPLATE === 'NONE') {
    //   return<PermissionRequired accessPermissions={accessPermissions} />;
    // }
    const hasLocation = accessPrivileges.EDGE_CONNECTOR_LOCATION_MANAGEMENT !== 'NONE';

    return (
      <div className="main-container locations-main-container">
        <div className="location-header">
          <div className="configuration-nav-tab">
            <div className="page-title  header-3">
              {t('LOCATION_TEMPLATES')}
            </div>
          </div>
        </div>
        <div className={`location-header fragment ${accessPrivileges[permKey] === 'READ_ONLY' ? 'hide' : ''}`}>
          {!isReadOnly && <AddNewButton label={t('ADD_LOCATION_TEMPLATE')} clickCallback={() => toggleCloudProvidersForm(null, true, 'ADD_LOCATION_TEMPLATE')} />}
          <div className="search-container">
            <SearchBox placeholder={t('SEARCH')} clickCallback={() => load(null, null, null, true)} />
          </div>
        </div>
        <div className="container-row locations">
          <RBAC privilege={permKey}>
            <Loading {...this.props}>
              <HelpArticle article={HELP_ARTICLES.LOCATION_TEMPLATES} />
              <ServerError {...this.props}>
                <ConfigTable
                  {...LOCATION_TEMPLATE_TABLE_CONFIGS}
                  permission={accessPrivileges[permKey]}
                  onHandleRowEdit={toggleCloudProvidersForm}
                  onHandleRowDelete={toggleDeleteConfirmationForm}
                  onHandleRowView={handleLocationTemplateView}
                  moreItemsLoading={moreItemsLoading}
                  hasNextPage={hasNextPage}
                  loadMore={load}
                  pagination
                  data={this.getTableData()} />
                
                <Modal
                  title={t(formTitle)}
                  isOpen={showForm}
                  styleClass="locationTemplate-view-modal"
                  closeModal={() => toggleCloudProvidersForm(null, false)}>
                  <LocationTemplatesForm editForm />
                </Modal>
                <Modal
                  title={t('VIEW_LOCATION_TEMPLATE')}
                  isOpen={showViewForm}
                  styleClass="locationTemplate-view-modal"
                  closeModal={() => handleLocationTemplateView(null, false)}>
                  <LocationTemplatesForm editForm={false} />
                </Modal>
                <Modal
                  title={t('DELETE_CONFIRMATION')}
                  isOpen={showDeleteForm}
                  closeModal={() => toggleDeleteConfirmationForm(null, false)}>
                  <DeleteConfirmationForm
                    modalLoading={modalLoading}
                    selectedRowID={selectedRowID}
                    handleCancel={toggleDeleteConfirmationForm}
                    handleDelete={handleDelete} />
                </Modal>
              </ServerError>
            </Loading>
          </RBAC>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...LocationTemplatesSelectors.baseSelector(state),
  modalLoading: LocationTemplatesSelectors.modalLoadingSelector(state),
  selectedRowID: LocationTemplatesSelectors.selectedRowIDSelector(state),
  accessPrivileges: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loadLocationTemplateData,
    toggleCloudProvidersForm: toggleForm,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deleteLocationTemplate,
    handleLocationTemplateView: toggleViewModal,
  }, dispatch);
  return actions;
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(LocationTemplates));

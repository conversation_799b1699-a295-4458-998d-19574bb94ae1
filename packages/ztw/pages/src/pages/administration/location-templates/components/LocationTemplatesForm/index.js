/* eslint-disable max-len */
/* eslint-disable eqeqeq */
// @flow
import React from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { reduxForm } from 'redux-form';
import Loading from 'components/spinner/Loading';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import {
  noop, isEmpty, isEqual, omit,
} from 'utils/lodash';
import { convertFromMinutesToTimeUnit } from 'utils/helpers';
import * as loginSelectors from 'ducks/login/selectors';

import {
  saveForm,
  toggleViewModal,
  handleBWChange,
  handleAUPChange,
  handleOFWChange,
  handleIPSChange,
  handleXFFChange,
  handleAuthChange,
  handleCautionChange,
} from 'ducks/locationTemplates';
import * as LocationTemplatesSelectors from 'ducks/locationTemplates/selectors';
import { GatewayFragment } from './components/GatewayFragment';

export function BasicCustomAppForm(props) {
  const {
    valid,
    actions,
    initialValues,
    t,
    handleSubmit,
    modalLoading,
    editForm,
    formValues,
    formMeta,
    formSyncErrors,
    accessDetailSubscriptions,
  } = props;
  const {
    cancelHandle,
    saveLocationTemplate,
  } = actions;

  let { template: formValuesTemplate } = formValues || {};
  let { template: appData } = initialValues || {};
  if (appData.bandwidthEnabled === 'false') {
    formValuesTemplate = omit(formValuesTemplate, ['dnBandwidth', 'upBandwidth']);
    appData = omit(appData, ['dnBandwidth', 'upBandwidth']);
  }
  if (!appData.aupEnabled) {
    formValuesTemplate = omit(formValuesTemplate, ['aupTimeoutInDays']);
    appData = omit(appData, ['aupTimeoutInDays']);
  }
  if (!appData.ofwEnabled) {
    formValuesTemplate = omit(formValuesTemplate, ['ipsControl']);
    appData = omit(appData, ['ipsControl']);
  }

  const disableSave = !isEmpty(formSyncErrors);

  const noChange = isEqual(formValuesTemplate, appData)
  && initialValues.name === formValues.name
  && initialValues.templatePrefix === formValues.templatePrefix
  && initialValues.desc === formValues.desc;

  return (
    <form onSubmit={handleSubmit(saveLocationTemplate)} className="wizard-form wizard-form-location">
      <Loading loading={modalLoading} />
      <div className="form-sections-container">
        <GatewayFragment
          accessDetailSubscriptions={accessDetailSubscriptions}
          t={t}
          editForm={editForm}
          formValues={formValues}
          formMeta={formMeta}
          formSyncErrors={formSyncErrors} />
      </div>
      <div className="modal-footer">
        {editForm && <button type="submit" disabled={disableSave || noChange || !valid} className="submit primary-button">{t('SAVE')}</button>}
        <button type="button" className="cancel" onClick={() => cancelHandle(null, false)}>{t('CANCEL')}</button>
      </div>
    </form>
  );
}

BasicCustomAppForm.propTypes = {
  actions: PropTypes.shape({
    cancelHandle: PropTypes.func,
    saveLocationTemplate: PropTypes.func,
  }),
  valid: PropTypes.bool,
  t: PropTypes.func,
  handleSubmit: PropTypes.func,
  modalLoading: PropTypes.bool,
  editForm: PropTypes.bool,
  initialValues: PropTypes.shape({}),
  formValues: PropTypes.shape({}),
  formMeta: PropTypes.shape({}),
  formSyncErrors: PropTypes.shape({}),
  accessDetailSubscriptions: PropTypes.arrayOf(PropTypes.shape()),
};

BasicCustomAppForm.defaultProps = {
  actions: {
    cancelHandle: noop,
    saveLocationTemplate: noop,
  },
  valid: true,
  t: (str) => str,
  handleSubmit: noop,
  modalLoading: false,
  editForm: false,
  initialValues: {},
  formValues: {},
  formMeta: {},
  formSyncErrors: {},
  accessDetailSubscriptions: [],
};

const BasicCustomForm = reduxForm({
  form: 'LocationTemplateForm',
})(BasicCustomAppForm);

const LocationTemplatesForm = connect(
  (state) => {
    const {
      id,
      name,
      desc,
      template,
    } = LocationTemplatesSelectors.appDataSelector(state);
    const {
      aupEnabled,
      aupTimeoutInDays,
      authRequired,
      cautionEnabled,
      displayTimeUnit,
      dnBandwidth,
      enforceBandwidthControl,
      idleTimeInMinutes,
      ipsControl,
      ofwEnabled,
      upBandwidth,
      surrogateIP,
      surrogateIPEnforcedForKnownBrowsers,
      surrogateRefreshTimeInMinutes,
      surrogateRefreshTimeUnit,
      templatePrefix,
      xffForwardEnabled,
    } = template;
  
    return ({
      initialValues: {
        id,
        name,
        templatePrefix,
        desc,
        template: {
          authRequired,
          bandwidthEnabled: enforceBandwidthControl || (dnBandwidth && dnBandwidth !== 0) || (upBandwidth && upBandwidth !== 0) ? 'true' : 'false',
          cautionEnabled,
          xffForwardEnabled,
          aupEnabled,
          aupTimeoutInDays: (aupTimeoutInDays && aupTimeoutInDays !== 0) ? aupTimeoutInDays.toString() : '1',
          // aupBlockInternetUntilAccepted,
          // aupForceSslInspection,
          ofwEnabled,
          ipsControl,
          dnBandwidth: (dnBandwidth && dnBandwidth !== 0) ? (dnBandwidth / 1000).toString() : '',
          upBandwidth: (upBandwidth && upBandwidth !== 0) ? (upBandwidth / 1000).toString() : '',
          surrogateIP,
          surrogateIPEnforcedForKnownBrowsers,
          surrogateRefreshTimeInMinutes: surrogateRefreshTimeInMinutes
            ? String(convertFromMinutesToTimeUnit(surrogateRefreshTimeUnit, surrogateRefreshTimeInMinutes)) : '1',
          surrogateRefreshTimeUnit: surrogateRefreshTimeUnit || 'HOUR',
          idleTimeInMinutes: idleTimeInMinutes ? String(convertFromMinutesToTimeUnit(displayTimeUnit, idleTimeInMinutes)) : '2',
          displayTimeUnit: displayTimeUnit || 'HOUR',
        },
      },
    });
  },
)(BasicCustomForm);

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    cancelHandle: toggleViewModal,
    saveLocationTemplate: saveForm,
    handleBWToggle: handleBWChange,
    handleAUPToggle: handleAUPChange,
    handleOFWChange,
    handleIPSChange,
    handleXFFChange,
    handleAuthChange,
    handleCautionChange,
  }, dispatch);

  return {
    actions,
  };
};

const mapStateToProps = (state) => ({
  isFirewallEnabled: LocationTemplatesSelectors.enableOFWSelector(state),
  isIPSEnabled: LocationTemplatesSelectors.enableIPSSelector(state),
  isXFFEnabled: LocationTemplatesSelectors.enableXFFSelector(state),
  isAuthEnabled: LocationTemplatesSelectors.enableAuthSelector(state),
  isCautionEnabled: LocationTemplatesSelectors.enableCautionSelector(state),
  isEnableAup: LocationTemplatesSelectors.enableAUPSelector(state),
  enforceBW: LocationTemplatesSelectors.enforceBWSelector(state),
  surrogateIP: LocationTemplatesSelectors.enableSurrogateIPSelector(state),
  idleTimeInMinutes: LocationTemplatesSelectors.idleTimeInMinutesSelector(state),
  displayTimeUnit: LocationTemplatesSelectors.displayTimeUnitelector(state),
  initialValues: LocationTemplatesSelectors.appDataSelector(state),
  modalLoading: LocationTemplatesSelectors.modalLoadingSelector(state),
  formValues: LocationTemplatesSelectors.formValuesSelector(state),
  formMeta: LocationTemplatesSelectors.formMetaSelector(state),
  formSyncErrors: LocationTemplatesSelectors.formSyncErrorsSelector(state),
  accessDetailSubscriptions: loginSelectors.accessDetailSubscriptionSelector(state),
});

export default
connect(mapStateToProps, mapDispatchToProps)(withTranslation()(LocationTemplatesForm));

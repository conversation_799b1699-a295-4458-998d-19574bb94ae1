// @flow
import React from 'react';
import PropTypes from 'prop-types';
import { noop } from 'utils/lodash';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import AddNewButton from 'components/addNewButton';
import { toggleWizard } from 'ducks/edgeconnectorWizard';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import { hasBsku, hasCsku } from 'utils/helpers';
import * as edgeconnectorSelector from 'ducks/edgeconnectors/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import Loading from 'components/spinner/Loading';
import {
  loadEcGroups,
  updateMenu,
} from 'ducks/edgeconnectors';

import TableWrapper from 'components/tablePro/TableWrapper';
import CustomizeColumns from 'components/tablePro/CustomizeColumns';
import CustomizeColsHeader from 'components/tablePro/CustomizeColsHeader';
import RowActions from 'components/tablePro/RowActions';
import { ECAddForm } from './components';

export class EdgeConnectorGroups extends React.Component {
  static propTypes = {
    actions: PropTypes.shape({
      load: PropTypes.func,
    }),
    ectabledata: PropTypes.arrayOf(PropTypes.shape()),
    showForm: PropTypes.bool,
    ecColumns: PropTypes.arrayOf(PropTypes.shape()),
    accessPermissions: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
  };

  static defaultProps = {
    actions: {
      load: noop,
    },
    ectabledata: [],
    showForm: null,
    ecColumns: [],
    accessPermissions: {},
    accessSubscriptions: [],
  };

  state = {
    showComponent: false,
  };

  componentDidMount() {
    const { actions } = this.props;
    const { load } = actions;
    load();
  }

  openModal = (event) => {
    event.preventDefault();
    // eslint-disable-next-line
    const { actions: { toggleWizard } } = this.props;
    toggleWizard(true, 'NEW', null);
  };

  handleOnMouseOverCb = () => this.setState(() => ({ showComponent: true }));

  onLeave = () => {
    this.setState({ showComponent: false });
  };

  getColumns = () => {
    const {
      ecColumns,
      actions,
    } = this.props;
    const {
      editRowHandler,
    } = actions;
    const lastColumn = ecColumns[ecColumns.length - 1];
    const { showComponent } = this.state;
    const lastColWithElipsis = {
      ...lastColumn,
      headerRenderer: (
        <CustomizeColsHeader
          props={this}
          handleLeave={this.onLeave}
          showComponent={showComponent}
          onMouseOverCb={this.handleOnMouseOverCb} />
      ),
      formatter: <RowActions showSubEdit />,
      events: {
        onClick(ev, row) {
          editRowHandler(row);
        },
      },
    };
    ecColumns[ecColumns.length - 1] = lastColWithElipsis;
    return [...ecColumns];
  };

  render() {
    const {
      ectabledata,
      actions,
      accessPermissions,
      accessSubscriptions,
    } = this.props;

    const { showComponent } = this.state;
    const columns = this.getColumns();

    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    if (accessPermissions.EDGE_CONNECTOR_CLOUD_PROVISIONING === 'NONE') {
      return <PermissionRequired accessPermissions={accessPermissions} />;
    }

    return (
      <div className="main-container">
        <AddNewButton label="Add Edgeconnector Group" clickCallback={this.openModal} />
        <div className="container-row">
          <Loading {...this.props}>
            <div className="table-layout-header -js-header">
              <TableWrapper
                initialRows={ectabledata}
                initialColumns={columns}
                keysInReturn={['name', 'location.name', 'deployType', 'ecVMs[0].ecInstances[0].serviceNw.ipStart']}
                updateMenu={actions.updateMenu} />
              <CustomizeColumns
                initialItems={columns}
                handleLeave={this.onLeave}
                showComponent={showComponent}
                dragnDrop={actions.updateMenu}
                minHeight="200" />
              <ECAddForm />
            </div>
          </Loading>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...edgeconnectorSelector.default(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    // load: getEdgeConnectorsData,
    load: loadEcGroups,
    toggleWizard,
    updateMenu,
  }, dispatch);
  return { actions };
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(EdgeConnectorGroups));

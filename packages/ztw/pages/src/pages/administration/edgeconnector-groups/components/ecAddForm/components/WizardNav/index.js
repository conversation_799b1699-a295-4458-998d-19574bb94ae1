// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Trans, withTranslation } from 'react-i18next';
import { formValueSelector, isValid } from 'redux-form';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDotCircle, faCircle } from '@fortawesome/pro-solid-svg-icons';

import * as edgeconnectorSelector from 'ducks/edgeconnectors/selectors';

const formValidity = {};
const selector = formValueSelector('edgeconnectorWizard');
let type;

function WizardNav(props) {
  const {
    activePage,
    goToPage,
    typeFromState,
    valid,
    wizardNavConfig,
    cloudName,
    cloud,
    premise,
    premiseName,
  } = props;

  // track form validity for each sub-form
  formValidity[activePage] = valid;

  // if a sub-form is invalid, mark all next sub-forms as invalid
  if (!valid) {
    wizardNavConfig.forEach((item, index) => {
      if (index > activePage) {
        formValidity[index] = false;
      }
    });
  }

  // if the entire wizard form is filled out and user comes back to 1st screen and
  // changes the type, unset validity for all other pages to reflect change
  // in Additional params
  if (activePage === 0 && type !== typeFromState) {
    for (let i = 1, l = wizardNavConfig.length; i < l; i += 1) {
      formValidity[i] = false;
    }

    type = typeFromState;
  }
  
  return (
    <div className="wizard-nav">
      <ul>
        {wizardNavConfig.map((item, index) => {
          let widgetTitle = <span className="name"><Trans>{item}</Trans></span>;
          if (index === 0) {
            if (cloud) {
              widgetTitle = <span className="name"><Trans>CLOUD</Trans></span>;
            } else if (premise) {
              widgetTitle = <span className="name"><Trans>ON_PREMISE</Trans></span>;
            }
          }

          return (
            <li
              className={`${index === activePage ? 'active' : ''} ${formValidity[index] ? 'valid' : ''}`}
              key={item}>
              <button
                type="button"
                onClick={() => goToPage(index)}
                disabled={index !== 0 && !formValidity[index - 1]}>
                <span className="fa-layers fa-fw">
                  {index === activePage ? <FontAwesomeIcon icon={faDotCircle} size="lg" /> : <FontAwesomeIcon icon={faCircle} size="lg" />}
                </span>
                {widgetTitle}
                <span className={index === 0 && (cloud === true || premise === true) ? '' : 'hide'}>: </span>
                {
                  cloud ? <span className={`name ${index === 0 ? '' : 'hide'}`}>{cloudName}</span> : <span className={`name ${index === 0 ? '' : 'hide'}`}><Trans>{premiseName}</Trans></span>
                }
              </button>
            </li>
          );
        })}
      </ul>
    </div>
  );
}

WizardNav.propTypes = {
  activePage: PropTypes.number,
  goToPage: PropTypes.func,
  typeFromState: PropTypes.string,
  valid: PropTypes.bool,
  wizardNavConfig: PropTypes.arrayOf(PropTypes.string),
  cloud: PropTypes.bool,
  premise: PropTypes.bool,
  cloudName: PropTypes.string,
  premiseName: PropTypes.string,
};

WizardNav.defaultProps = {
  activePage: 0,
  goToPage: (str) => str,
  typeFromState: '',
  valid: false,
  wizardNavConfig: {},
  cloud: false,
  premise: false,
  cloudName: '',
  premiseName: '',
};

export default connect((state) => ({
  typeFromState: selector(state, 'type'),
  valid: isValid('edgeconnectorWizard')(state),
  cloud: edgeconnectorSelector.cloudSelector(state),
  premise: edgeconnectorSelector.premiseSelector(state),
  cloudName: edgeconnectorSelector.cloudNameSelector(state),
  premiseName: edgeconnectorSelector.premiseNameSelector(state),
}))(withTranslation()(WizardNav));

export { WizardNav };

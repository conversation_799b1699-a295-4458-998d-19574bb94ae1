import React from 'react';
import PropTypes from 'prop-types';
import { Field } from 'redux-form';
import { noop } from 'utils/lodash';

import RadioButton from '../RadioButton';

function RadioImageGroup(props = {}) {
  const {
    name,
    options,
    disabled,
    styleClass,
    id,
    onChange,
    checked,
  } = props;

  return (
    <div className={`image-radio-button-container ${styleClass}`}>
      <div className={`radio-buttons ${disabled ? 'disabled' : ''}`} id={id}>
        {options.map((item) => (
          <Field
            className={disabled ? 'disabled' : ''}
            key={item.value}
            disabled={disabled}
            appName={item.appName}
            name={name}
            checked={checked}
            component={RadioButton}
            onChange={onChange}
            type="radio"
            value={item.value}
            parse={(value) => value === 'true'} />
        ))}
      </div>
    </div>
  );
}

RadioImageGroup.defaultProps = {
  disabled: false,
  styleClass: '',
  id: null,
  onChange: noop,
};

RadioImageGroup.propTypes = {
  name: PropTypes.string.isRequired,
  checked: PropTypes.bool,
  options: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string,
    name: PropTypes.string,
    value: PropTypes.bool,
    appName: PropTypes.string,
  })).isRequired,
  disabled: PropTypes.bool,
  styleClass: PropTypes.string,
  id: PropTypes.string,
  onChange: PropTypes.func,
};

export default RadioImageGroup;

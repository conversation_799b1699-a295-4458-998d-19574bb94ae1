/* eslint-disable react/jsx-handler-names */
// @flow

import React from 'react';
import PropTypes from 'prop-types';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import Modal from 'react-modal';
import { withTranslation } from 'react-i18next';
import { getFormValues, change } from 'redux-form';
import withRouter from 'layout/withRouter';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';

import Loading from 'components/spinner/Loading';
import ServerError from 'components/errors/ServerError';
import {
  toggleWizard,
  saveEdgeconnector,
  setWizardActiveNavPage,
} from 'ducks/edgeconnectorWizard';

import { toggleAddEcForm } from 'ducks/edgeconnectors';
import * as edgeconnectorWizardSelectors from 'ducks/edgeconnectorWizard/selectors';

import {
  Cloud,
  Location,
  WizardNav,
  EdgeConnectorGroups,
  Review,
} from './components';

class ECAddForm extends React.Component {
  state = {
    wizardNavConfig: [
      'CLOUD_OR_ON_PREMISE',
      'LOCATION',
      'GROUP_INFORMATION',
      'REVIEW',
    ],
  };

  componentDidMount() {
    Modal.setAppElement('body');
  }

  componentWillUnmount() {
    const { actions: { toggleWizardModal }, isMonitorWizardOpen } = this.props;

    if (isMonitorWizardOpen) {
      toggleWizardModal(false, null, null);
    }
  }

  closeModal = () => {
    const { actions: { toggleWizardModal } } = this.props;

    toggleWizardModal(false, null, null);
  };

  handleNextPage = () => {
    // eslint-disable-next-line
    const { actions: { setWizardActiveNavPage }, activePage } = this.props;

    setWizardActiveNavPage(activePage + 1);
  };

  previousPage = () => {
    // eslint-disable-next-line
    const { actions: { setWizardActiveNavPage }, activePage } = this.props;

    setWizardActiveNavPage(activePage - 1);
  };

  goToPage = (pageNum) => {
    // eslint-disable-next-line
    const { actions: { setWizardActiveNavPage } } = this.props;

    setWizardActiveNavPage(pageNum);
  };

  onSubmit = (values) => {
    const { actions: { saveEdgeconnector } } = this.props; // eslint-disable-line no-shadow

    saveEdgeconnector(values);
  };

  renderForm = (activePage) => {
    const { type } = this.props;

    switch (activePage) {
    case 0:
      return (
        <Cloud
          onSubmit={this.handleNextPage}
          closeModal={this.closeModal}
          type={type} />
      );
    case 1:
      return (
        <Location
          onSubmit={this.handleNextPage}
          closeModal={this.closeModal}
          type={type} />
      );
    case 2:
      return (
        <EdgeConnectorGroups
          onSubmit={this.handleNextPage}
          previousPage={this.previousPage}
          closeModal={this.closeModal}
          type={type} />
      );
    
    case 3:
      return (
        <Review
          onSubmit={this.onSubmit}
          previousPage={this.previousPage}
          closeModal={this.closeModal}
          type={type} />
      );

    default:
      return null;
    }
  };

  render() {
    const {
      t,
      activePage,
      isMonitorWizardOpen,
      mode,
    } = this.props;
    const { wizardNavConfig } = this.state;
    let label;

    switch (mode) {
    case 'NEW':
    case 'PRESET':
      label = t('ADD_LOCATION_AND_CCG');
      break;

    case 'EDIT':
      label = t('EDIT_EDGECONNECTOR');
      break;

    case 'COPY':
      label = t('COPY_CLOUD_CONNECTOR');
      break;

    default:
      label = '';
      break;
    }
    if (isMonitorWizardOpen) {
      return (
        <div className="edgeconnector-page">
          <div className="back-to-ec">
            <FontAwesomeIcon icon={faArrowLeft} size="lg" onClick={this.closeModal} />
            {label}
          </div>
          <div className="edgeconnector-modal">
            <div className="modal-content modal-body branch-provisioning-modal-content">
              <Loading {...this.props}>
                <ServerError {...this.props}>
                  <WizardNav
                    activePage={activePage}
                    goToPage={this.goToPage}
                    wizardNavConfig={wizardNavConfig} />
                  {this.renderForm(activePage)}
                </ServerError>
              </Loading>
            </div>
          </div>
        </div>
      );
    }
    return '';
  }
}

ECAddForm.propTypes = {
  actions: PropTypes.shape(),
  activePage: PropTypes.number,
  history: PropTypes.shape(),
  match: PropTypes.shape(),
  name: PropTypes.string,
  type: PropTypes.string,
  mode: PropTypes.string,
  isMonitorWizardOpen: PropTypes.bool,
  t: PropTypes.func,
};

ECAddForm.defaultProps = {
  actions: null,
  activePage: 0,
  history: {},
  match: {},
  name: '',
  type: 'WEB',
  mode: 'NEW',
  isMonitorWizardOpen: false,
  t: (str) => str,
};

const mapStateToProps = (state) => ({
  ...edgeconnectorWizardSelectors.default(state),
  ...getFormValues('edgeconnectorWizard')(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    toggleWizardModal: toggleWizard,
    updateFormState: change,
    saveEdgeconnector,
    setWizardActiveNavPage,
    cancelHandle: toggleAddEcForm,
  }, dispatch);

  return {
    actions,
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(withRouter(ECAddForm)));

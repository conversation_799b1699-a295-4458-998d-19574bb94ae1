import 'core-js/stable';
import 'regenerator-runtime/runtime';
import 'react-app-polyfill/ie11';

import React, { useEffect } from 'react';
import { Provider } from 'react-redux';
import { createRoot } from 'react-dom/client';
import { isOneUI } from 'config';
import Tracker from '@openreplay/tracker';
import { getTheme } from 'utils/helpers';
// eslint-disable-next-line no-unused-vars
import RootRedirect from './RootRedirect';

import AuthRouter from './AuthRouter';
import OneUIAuthRouter from './OneUIAuthRouter';
import useResetReactDndBackend from './useResetReactDndBackend';

import './utils/i18n';
// import './scss/main.scss';

import { configureStore } from './store';

export const store = configureStore();

const usernameFS = localStorage.getItem('username');

if (!isOneUI) {
  const pathName = window.location.hostname;
  const isProd = ['connector.zscaler.net', 'connector.zscalertwo.net', 'connector.zscalerthree.net', 'connector.zscloud.net'].includes(pathName);
  const tracker = new Tracker({
    projectKey: isProd
      ? process.env.REACT_APP_PROD_PROJECT_KEY
      : process.env.REACT_APP_PROJECT_KEY,
    ingestPoint: isProd
      ? process.env.REACT_APP_PROD_INGEST_POINT
      : process.env.REACT_APP_INGEST_POINT,
  });
  tracker.start({
    userID: usernameFS,
    metadata: {
      subscription: '10M',
      configData: 'free',
      permissions: null,
      orgProvisioning: null,
    },
  });
  tracker.setUserID(usernameFS);
  tracker.setMetadata('Dashboard', '1');
}

// eslint-disable-next-line react/prefer-stateless-function
function App() {
  useResetReactDndBackend();
  useEffect(() => {
    document.querySelector('html').setAttribute('class', getTheme());
  }, []);

  if (isOneUI) {
    return (
      <div className="ec-root-page inline-position-relative">
        <Provider store={store}>
          <OneUIAuthRouter />
        </Provider>
      </div>
    );
  }

  return (
    <div className="ec-root-page">
      <RootRedirect />
      <Provider store={store}>
        {/* <RouterProvider router={router} /> */}
        <AuthRouter />
      </Provider>
    </div>
  );
}

export default App;

const root = createRoot(document.getElementById('r-app'));
root.render(<App />);

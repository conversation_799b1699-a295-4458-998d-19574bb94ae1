const fs = require('fs');
const path = require('path');

// Generate TypeScript declaration file
function generateTypeDeclaration() {
  const declarationContent = `// Type definitions for @ztw/pages library
// Auto-generated during build process

declare module '@ztw/pages' {
  import * as React from 'react';
  
  // Main library export
  export interface EdgeUXLibrary {
    [key: string]: any;
  }
  
  // Component exports
  export const AppProvider: React.ComponentType<any>;
  export const store: any;
  export const reducers: any;
  export const sagas: any;
  
  // Re-export everything from lib-entry
  export * from '../src/lib-entry';
  
  const library: EdgeUXLibrary;
  export default library;
}

// UMD global export
declare global {
  interface Window {
    EdgeUXLibrary: any;
  }
}

export = EdgeUXLibrary;
declare const EdgeUXLibrary: any;
`;

  const distDir = path.join(__dirname, '..', 'dist');
  const typeFile = path.join(distDir, 'index.d.ts');
  
  // Ensure dist directory exists
  if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
  }
  
  // Write the declaration file
  fs.writeFileSync(typeFile, declarationContent, 'utf8');
  console.log('✅ TypeScript declaration file generated at:', typeFile);
}

// Custom webpack plugin to generate types
class GenerateTypesPlugin {
  apply(compiler) {
    compiler.hooks.afterEmit.tap('GenerateTypesPlugin', () => {
      generateTypeDeclaration();
    });
  }
}

module.exports = { GenerateTypesPlugin, generateTypeDeclaration };

import { faScaleBalanced } from "@fortawesome/pro-solid-svg-icons/faScaleBalanced";
import { faCode } from "@fortawesome/pro-solid-svg-icons/faCode";
import { faCarSide } from "@fortawesome/pro-solid-svg-icons/faCarSide";
import { faGlobe } from "@fortawesome/pro-solid-svg-icons/faGlobe";
import { faFileSpreadsheet } from "@fortawesome/pro-solid-svg-icons/faFileSpreadsheet";
import { faStethoscope } from "@fortawesome/pro-solid-svg-icons/faStethoscope";
import { faHouse } from "@fortawesome/pro-solid-svg-icons/faHouse";
import { faUsers } from "@fortawesome/pro-solid-svg-icons/faUsers";
import { faFolderGear } from "@fortawesome/pro-solid-svg-icons/faFolderGear";
import { faShieldKeyhole } from "@fortawesome/pro-solid-svg-icons/faShieldKeyhole";
import { type IconDefinition } from "@fortawesome/pro-solid-svg-icons";
import { type SampleMockGenAIDataType } from "./types";

export const BubbleTreeChartsMockDataForGenAI: SampleMockGenAIDataType = {
  doc_categories: {
    "6": {
      "117": 2,
      "118": 600,
    },
    "2": {
      "34": 43,
      "35": 600,
      "36": 300,
    },
    "3": {
      "59": 300,
      "60": 208,
    },
    "10": {
      "181": 59,
      "182": 400,
    },
    "13": {
      "187": 45,
      "188": 400,
    },
    "14": {
      "206": 41,
      "207": 500,
    },
    "4": {
      "80": 5,
      "81": 90,
    },
    "8": {
      "135": 90,
      "136": 2,
    },
  },
};

export const iconMapping: Record<string, IconDefinition> = {
  DMV: faCarSide,
  Financial: faScaleBalanced,
  Technical: faFileSpreadsheet,
  Medical: faStethoscope,
  "Real Estate": faHouse,
  HR: faUsers,
  Invoice: faFolderGear,
  Insurance: faShieldKeyhole,
  Tax: faFolderGear,
  Legal: faScaleBalanced,
  "Court Form": faScaleBalanced,
  "Corporate Legal": faScaleBalanced,
  Immigration: faGlobe,
  "Source Code": faCode,
  Unknown: faFolderGear,
};

export const defaultIcon = faFolderGear;

export const classificationDrawerDataMockData = [
  {
    id: 3,
    data: {
      accounts: {
        AWS: {
          "************": 50,
          "***********": 50,
        },
        AZURE: {
          "qa-automation-434512": 40,
          "qa-automation34512": 40,
        },
        GCP: {
          "qa-automation-434512": 12,
        },
        ONEDRIVE: {
          "***********": 286,
        },
        SLACK: {
          "**********": 8,
        },
        ONPREMISES: {
          "***************": 50,
          "**************": 50,
          "*************": 7,
        },
        Monitor: {
          "*************": 1,
          "************": 1,
          "************": 1,
          "************": 1,
          "************": 1,
          "***********": 1,
          "************": 1,
          "************": 1,
          "************": 1,
        },
      },
    },
  },
  {
    id: 0,
    data: {
      accounts: {
        AWS: {
          "************": 57,
          "***********": 50,
          "***********": 50,
          "**********": 100,
          "***********": 200,
        },
        AZURE: {
          "qa-automation-434512": 6,
          "qa-automati434512": 80,
        },
        GCP: {
          "qa-automation-434512": 100,
          "qa-automation4512": 5,
        },
        ONEDRIVE: {
          "***********": 50,
          "**********": 7,
        },
        SLACK: {},
        ONPREMISES: {
          "**************": 66,
          "*************": 100,
        },
        Monitor: {
          "***********": 2,
          "***********": 50,
          "**********": 20,
        },
      },
    },
  },
  {
    id: 1,
    data: {
      accounts: {
        AWS: {
          "************": 227,
        },
        AZURE: {
          "qa-automation-434512": 20,
          "qa-automati434512": 100,
          "qa-automati44512": 100,
        },
        GCP: {
          "qa-automation-434512": 3,
        },
        ONEDRIVE: {
          "***********": 2,
          "**********": 2,
        },
        SLACK: {
          "**********": 4,
        },
        ONPREMISES: {
          "**************": 35,
        },
        Monitor: {
          "***********": 15,
        },
      },
    },
  },
  {
    id: 5,
    data: {
      accounts: {
        AWS: {
          "************": 72,
          "***********": 100,
        },
        AZURE: {
          "qa-automation-434512": 25,
        },
        GCP: {
          "qa-automation-434512": 2,
        },
        ONEDRIVE: {
          "***********": 10,
          "**********": 100,
        },
        SLACK: {
          "**********": 8,
        },
        ONPREMISES: {
          "**************": 3,
          "*************": 3,
          "*************": 80,
        },
        Monitor: {
          "***********": 2,
          "**********": 2,
          "**********": 2,
          "*********": 50,
        },
      },
    },
  },
  {
    id: 6,
    data: {
      accounts: {
        AWS: {},
        AZURE: {
          "qa-automation-434512": 13,
        },
        GCP: {
          "qa-automation-434512": 190,
        },
        ONEDRIVE: {
          "***********": 168,
        },
        SLACK: {
          "**********": 6,
        },
        ONPREMISES: {
          "**************": 20,
          "*************": 4,
        },
        Monitor: {
          "***********": 2,
          "**********": 2,
          "**********": 20,
          "*********": 10,
          "********": 10,
        },
      },
    },
  },
  {
    id: 7,
    data: {
      accounts: {
        AWS: {},
        AZURE: {},
        GCP: {
          "qa-automation-434512": 50,
          "qa-automation-4512": 7,
        },
        ONEDRIVE: {
          "***********": 16,
        },
        SLACK: {
          "**********": 11,
        },
        ONPREMISES: {},
        Monitor: {
          "***********": 1,
          "************": 1,
          "**********": 1,
          "************": 1,
          "*************": 1,
          "************": 1,
          "************": 1,
          "************": 1,
          "*************": 1,
          "************": 1,
          "**********": 1,
          "*********": 1,
          "**********": 1,
          "*************": 1,
          "************": 1,
          "************": 2,
          "************": 440,
        },
      },
    },
  },
  {
    id: 2,
    data: {
      accounts: {
        AWS: {
          "qa-automation-434512": 30,
        },
        AZURE: {
          "qa-automation-434512": 2,
        },
        GCP: {
          "qa-automation-434512": 1,
          "qa-automation-4512": 10,
        },
        ONEDRIVE: {
          "***********": 7,
        },
        SLACK: {
          "**********": 3,
        },
        ONPREMISES: {
          "**************": 11,
        },
        Monitor: {
          "***********": 1,
          "**********": 30,
        },
      },
    },
  },
  {
    id: 4,
    data: {
      accounts: {
        AWS: {},
        AZURE: {
          "qa-automation-434512": 6,
          "qa-automation512": 10,
          "qa-automation-4312": 3,
        },
        GCP: {},
        ONEDRIVE: {
          "***********": 1,
        },
        SLACK: {
          "**********": 1,
        },
        ONPREMISES: {
          "**************": 50,
        },
        Monitor: {
          "***********": 21,
        },
      },
    },
  },
];

import { useRef, useState } from "react";
import { t } from "i18next";
import { Checkbox } from "@xc/legacy-components";
import {
  SegmentOptions,
  TopSensitiveDataRestConfig,
  type TabTypeProps,
} from "./config";
import { TopSensitiveDataGenAIClassificationContainer } from "./TopSensitiveDataGenAIClassification/TopSensitiveDataGenAIClassificationContainer";
import { TopSensitiveDataDLPEngineContainer } from "./TopSensitiveDataDLPEngine/TopSensitiveDataDLPEngineContainer";
import {
  type DrawerDataProps,
  type FilterOptionsProps,
  type SelectedNodeProps,
} from "./types";
import { TopSensitiveDataRestDrawerContainer } from "./TopSensitiveDataRestDrawer/TopSensitiveDataRestDrawerContainer";
import TopSensitiveDataRestFilters from "./TopSensitiveDataRestFilter/TopSensitiveDataRestFilters";
import { classificationDrawerDataMockData } from "./TopSensitiveDataGenAIClassification/mock.data";
import { dlpEngineDrawerDataMockData } from "./TopSensitiveDataDLPEngine/mock.data";
import { TopSensitiveDataGenAIExactTypeClassificationContainer } from "./TopSensitiveDataGenAIExactTypeClassification/TopSensitiveDataGenAIExactTypeClassificationContainer";
import { classificationAITypeDrawerDataMockData } from "./TopSensitiveDataGenAIExactTypeClassification/mock.data";
import { GEN_AI_CATEGORY_DATATYPES } from "./TopSensitiveDataGenAIExactTypeClassification/config";
import { Card } from "@/components/Analytics/Card";
import SegmentControl from "@/components/Analytics/SegmentedControl/SegmentedControl";
import { getDataTestId } from "@/utils/utils";
import { GENAICLASSIFICATION } from "@/configs/constants/analytics";

export default function TopSensitiveDataRestContainer({
  isMockData,
}: {
  isMockData: boolean;
}) {
  const [selectedTab, setSelectedTab] = useState<string>(GENAICLASSIFICATION);
  const checkboxRef = useRef<HTMLInputElement>(null);
  const [exactAIBreakdown, setExactAIBreakdown] = useState<string>("UNCHECKED");
  const ID = getDataTestId("sensitive-data-rest");
  const [aiFilter, setAIFilter] = useState<FilterOptionsProps[]>([]);
  const [selectedNode, setSelectedNode] = useState<SelectedNodeProps | null>(
    null,
  );
  const [drawerData, setDrawerData] = useState<DrawerDataProps>(
    classificationDrawerDataMockData[0].data,
  );
  const [isCloseDrawer, setIsCloseDrawer] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [channelsFilter, setChannelsFilters] = useState<FilterOptionsProps[]>(
    [],
  );

  const handleExactAI = () => {
    setExactAIBreakdown((prev: string) =>
      prev === "CHECKED" ? "UNCHECKED" : "CHECKED",
    );
    handleCloseDrawer();
    setAIFilter([]);
    aiFilter?.length && setIsLoading(true);
  };

  const handleReset = () => {
    setChannelsFilters([]);
    setAIFilter([]);
    handleCloseDrawer();
    setIsLoading(true);
  };

  const handleSelectedNode = (
    data: SelectedNodeProps | null,
    isClassification: boolean,
  ) => {
    setSelectedNode(data);
    setIsCloseDrawer(false);
    const drawerData =
      isClassification && exactAIBreakdown === "UNCHECKED"
        ? classificationDrawerDataMockData.find(
            (item) => Number(item.id) === Number(data?.id),
          )
        : isClassification && exactAIBreakdown === "CHECKED"
          ? classificationAITypeDrawerDataMockData.find(
              (item) => Number(item.id) === Number(data?.id),
            )
          : dlpEngineDrawerDataMockData.find(
              (item) => Number(item.id) === Number(data?.id),
            );
    setDrawerData(
      drawerData?.data
        ? drawerData?.data
        : classificationDrawerDataMockData[0].data,
    );
  };

  const categoryName =
    selectedTab === GENAICLASSIFICATION && exactAIBreakdown === "CHECKED"
      ? GEN_AI_CATEGORY_DATATYPES.find(
          (item) => Number(item.id) === Number(selectedNode?.id),
        )?.doc_category_name
      : "";

  console.log(categoryName, "categoryNamecategoryNamecategoryName");

  const handleCloseDrawer = () => {
    setSelectedNode(null);
    setIsCloseDrawer(true);
  };

  const handleChannelsFilter = (options: FilterOptionsProps[]) => {
    setChannelsFilters(options);
    handleCloseDrawer();
    setIsLoading(true);
  };

  const handleAIFilter = (options: FilterOptionsProps[]) => {
    setAIFilter(options);
    handleCloseDrawer();
    setIsLoading(true);
  };

  return (
    <Card id={ID} className="h-[850px]">
      <div className="flex justify-between gap-rem-400">
        <div className="w-full">
          <Card.Header>
            <div className="flex justify-between items-baseline">
              <div className="flex flex-col gap-2">
                <Card.Header.Title id={ID}>
                  {t(TopSensitiveDataRestConfig.heading)}
                </Card.Header.Title>
                <Card.Header.Description id={ID}>
                  {t(TopSensitiveDataRestConfig.subHeading)}
                </Card.Header.Description>
              </div>
              <div className="flex flex-col gap-2">
                <SegmentControl
                  value={selectedTab ?? GENAICLASSIFICATION}
                  onChange={(value) => {
                    setSelectedTab(value ?? ("" as TabTypeProps));
                    handleReset();
                    setExactAIBreakdown("UNCHECKED");
                  }}
                  options={SegmentOptions}
                  customClass="flex h-full"
                  id={ID}
                />

                {selectedTab === GENAICLASSIFICATION && (
                  <div className="flex justify-end items-center">
                    <Checkbox
                      className="rounded-[2px]"
                      id="exactAIBreakdown"
                      ref={checkboxRef}
                      status={exactAIBreakdown}
                      onChange={handleExactAI}
                    />
                    <label
                      htmlFor="exactAIBreakdown"
                      className="ml-rem-40 text-semantic-content-base-primary typography-paragraph1"
                    >
                      {t("DP_DASHBOARD_TOP_SENSITIVE_DATA_AT_REST_AI_TYPE")}
                    </label>
                  </div>
                )}
              </div>
            </div>
            <TopSensitiveDataRestFilters
              selectedTab={selectedTab}
              exactAIBreakdown={exactAIBreakdown}
              channelsFilter={channelsFilter}
              aiFilter={aiFilter}
              handleReset={handleReset}
              handleChannelsFilter={handleChannelsFilter}
              handleAIFilter={handleAIFilter}
            />
          </Card.Header>

          <Card.Content className="h-[700px]">
            {selectedTab === GENAICLASSIFICATION &&
            exactAIBreakdown === "UNCHECKED" ? (
              <TopSensitiveDataGenAIClassificationContainer
                id={ID}
                handleSelectedNode={handleSelectedNode}
                isCloseDrawer={isCloseDrawer}
                aiFilter={aiFilter}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
                exactAIBreakdown={exactAIBreakdown}
                channelsFilter={channelsFilter}
                isMockData={isMockData}
              />
            ) : selectedTab === GENAICLASSIFICATION &&
              exactAIBreakdown === "CHECKED" ? (
              <TopSensitiveDataGenAIExactTypeClassificationContainer
                id={ID}
                handleSelectedNode={handleSelectedNode}
                isCloseDrawer={isCloseDrawer}
                aiFilter={aiFilter}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
                exactAIBreakdown={exactAIBreakdown}
                channelsFilter={channelsFilter}
                isMockData={isMockData}
              />
            ) : (
              <TopSensitiveDataDLPEngineContainer
                id={ID}
                handleSelectedNode={handleSelectedNode}
                isCloseDrawer={isCloseDrawer}
                channelsFilter={channelsFilter}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
                isMockData={isMockData}
              />
            )}
          </Card.Content>
        </div>

        {!isCloseDrawer && selectedNode ? (
          <TopSensitiveDataRestDrawerContainer
            isMockData={isMockData}
            handleClose={handleCloseDrawer}
            id={ID}
            selectedNode={selectedNode}
            filters={{
              aiFilter: aiFilter,
              selectedTab: selectedTab === GENAICLASSIFICATION ? 1 : 2,
              channelsFilter: channelsFilter,
            }}
            drawerData={drawerData}
            categoryName={categoryName}
          />
        ) : null}
      </div>
    </Card>
  );
}

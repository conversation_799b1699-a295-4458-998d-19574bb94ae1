import { useEffect } from "react";
import { faFolderGear } from "@fortawesome/pro-solid-svg-icons/faFolderGear";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { type SelectedNodeProps } from "../types";
import {
  type DLPPayloadFilters,
  type SampleMockDataType,
  type BubbleTreeChartDataProps,
  type TopSensitiveDataDLPEngineContainerProps,
} from "./types";

import { TopSensitiveDataDLPEngine } from "./TopSensitiveDataDLPEngine";
import {
  BubbleTreeChartsMockDataForDLPEngines,
  DLP_ENGINE_COMMON_VALUES,
} from "./mock.data";
import { getDataTestId } from "@/utils/utils";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import { useZDXEntitlement } from "@/hooks/useZDXEntitlement";
import { usePostMutation } from "@/utils/apiUtils";
import { DataProtection } from "@/configs/urls/analytics/data-protection";

export const TopSensitiveDataDLPEngineContainer = ({
  id,
  handleSelectedNode,
  isCloseDrawer,
  channelsFilter,
  isLoading,
  setIsLoading,
  isMockData,
}: TopSensitiveDataDLPEngineContainerProps) => {
  const ID = getDataTestId("sensitive-data-risk-dlp", id);

  const containerId = "DataProtection.Dashboard.SensitiveDLPData";
  const HAS_READ_PERMISSION = useZDXEntitlement(containerId);

  const { trigger, data, isMutating, error } = usePostMutation(
    DataProtection.Dashboard.SensitiveDataAtRisk,
    {
      key: containerId,
    },
  );

  const response = isMockData
    ? BubbleTreeChartsMockDataForDLPEngines
    : (data as SampleMockDataType);

  const CommonValueMap: Record<string, string> =
    DLP_ENGINE_COMMON_VALUES?.reduce(
      (acc, { id, displayName }) => {
        acc[id] = displayName;

        return acc;
      },
      {} as Record<string, string>,
    );

  const getDLPEngineLocale = (enginVal: string) =>
    CommonValueMap[enginVal] || enginVal;

  const transformResponse = () => {
    const result: BubbleTreeChartDataProps[] = [];
    response?.data_engines?.forEach((item, index) => {
      result.push({
        id: index.toString(),
        name: getDLPEngineLocale(item?.engine_enum),
        count: item?.total_count || 0,
        icon: <FontAwesomeIcon icon={faFolderGear} />,
      });
    });

    return result;
  };

  const bubbleTreeNodeData = transformResponse();

  const handleClick = (data: SelectedNodeProps | null) => {
    handleSelectedNode(data, false);
  };

  const cloudMap = {
    publicCloud: ["AWS", "AZURE", "GCP"],
    onPremises: ["ONPREMISES"],
  } as const;

  useEffect(() => {
    setIsLoading(false);
    const filters: DLPPayloadFilters = { classification_scope: 2 };

    if (channelsFilter?.length) {
      const cloudKey = channelsFilter[0]?.id as keyof typeof cloudMap;
      if (cloudKey in cloudMap) {
        filters.cloud = cloudMap[cloudKey];
      }
    }
    trigger({ filters });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [trigger, HAS_READ_PERMISSION, channelsFilter]);

  return (
    <WithAnalyticsStates
      loading={isMockData ? undefined : isLoading || isMutating}
      error={isMockData ? undefined : error}
      showBorder={false}
      id={ID}
    >
      <TopSensitiveDataDLPEngine
        id={ID}
        chartData={bubbleTreeNodeData}
        handleSelectedNode={handleClick}
        isCloseDrawer={isCloseDrawer}
      />
    </WithAnalyticsStates>
  );
};

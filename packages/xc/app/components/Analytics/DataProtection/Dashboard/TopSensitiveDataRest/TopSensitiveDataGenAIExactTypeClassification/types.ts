import { type ReactNode } from "react";
import { type FilterOptionsProps, type SelectedNodeProps } from "../types";
import { type BubbleTreeNodeData } from "@/components/Analytics/Charts/BubbleTreeChart/types";

export type TopSensitiveDataGenAIClassificationContainerProps = {
  id?: string;
  handleSelectedNode: (
    data: SelectedNodeProps | null,
    isClassification: boolean,
  ) => void;
  isCloseDrawer: boolean;
  aiFilter: FilterOptionsProps[];
  isLoading: boolean;
  setIsLoading: (value: boolean) => void;
  exactAIBreakdown: string;
  channelsFilter: FilterOptionsProps[];
  isMockData: boolean;
};

export type TopSensitiveDataGenAIClassificationProps = {
  id?: string;
  chartData: BubbleTreeNodeData[];
  handleSelectedNode: (data: SelectedNodeProps | null) => void;
  isCloseDrawer: boolean;
};

export type PayloadProps = {
  startDate: number;
  endDate: number;
};

export type BubbleTreeChartDataProps = {
  id: string;
  name: string;
  count: number;
  icon: ReactNode;
};

export type GenAIPayloadFilters = {
  classification_scope: number;
  doc_categories?: number[];
  cloud?: readonly string[];
};

export type GenAICategoryDataTypes = {
  id: number;
  doc_category_id: number;
  doc_category_name: string;
  doc_type_id?: number;
  doc_type_name: string;
};

export type SampleMockGenAIDataType = {
  doc_categories: Record<string, Record<string, number>>;
};

export type DataTypeObj = Record<string, number>;

export type DocumentTypeDetails = {
  doc_type_name?: string;
  doc_category_name?: string;
};

export type DocType = {
  name: string;
  value: number;
  docTypeId: number;
};

export type PreparedDocCategory = {
  category_name: string;
  category_id: number;
  total_count: number;
  docTypeIds: number[];
  doc_types?: DocType[];
};

import { faScaleBalanced } from "@fortawesome/pro-solid-svg-icons/faScaleBalanced";
import { faCode } from "@fortawesome/pro-solid-svg-icons/faCode";
import { faCarSide } from "@fortawesome/pro-solid-svg-icons/faCarSide";
import { faGlobe } from "@fortawesome/pro-solid-svg-icons/faGlobe";
import { faFileSpreadsheet } from "@fortawesome/pro-solid-svg-icons/faFileSpreadsheet";
import { faStethoscope } from "@fortawesome/pro-solid-svg-icons/faStethoscope";
import { faHouse } from "@fortawesome/pro-solid-svg-icons/faHouse";
import { faUsers } from "@fortawesome/pro-solid-svg-icons/faUsers";
import { faFolderGear } from "@fortawesome/pro-solid-svg-icons/faFolderGear";
import { faShieldKeyhole } from "@fortawesome/pro-solid-svg-icons/faShieldKeyhole";
import { type IconDefinition } from "@fortawesome/pro-solid-svg-icons";
import { type SampleMockGenAIDataType } from "./types";

export const BubbleTreeChartsMockDataForGenAIType: SampleMockGenAIDataType = {
  doc_categories: {
    "116": {
      "117": 88,
      "118": 500,
    },
    "34": {
      "117": 11,
      "118": 500,
    },
    "78": {
      "117": 8,
      "118": 500,
    },
    "165": {
      "117": 59,
      "118": 400,
    },
    "188": {
      "117": 45,
      "118": 400,
    },
    "40": {
      "117": 32,
      "118": 400,
    },
    "209": {
      "117": 61,
      "118": 300,
    },
    "208": {
      "117": 100,
      "118": 80,
    },
    "81": {
      "117": 5,
      "118": 90,
    },
    "144": {
      "117": 2,
      "118": 90,
    },
    "131": {
      "117": 4,
      "118": 10,
    },
  },
};

export const iconMapping: Record<string, IconDefinition> = {
  DMV: faCarSide,
  Financial: faScaleBalanced,
  Technical: faFileSpreadsheet,
  Medical: faStethoscope,
  "Real Estate": faHouse,
  HR: faUsers,
  Invoice: faFolderGear,
  Insurance: faShieldKeyhole,
  Tax: faFolderGear,
  Legal: faScaleBalanced,
  "Court Form": faScaleBalanced,
  "Corporate Legal": faScaleBalanced,
  Immigration: faGlobe,
  "Source Code": faCode,
  Unknown: faFolderGear,
};

export const defaultIcon = faFolderGear;

export const classificationAITypeDrawerDataMockData = [
  {
    id: 4,
    data: {
      accounts: {
        AWS: {
          "qa-automation-434512": 95,
        },
        AZURE: {
          "qa-automation-434512": 10,
          "qa-automation512": 70,
        },
        GCP: {
          "qa-automation512": 12,
        },
        ONEDRIVE: {
          "***********": 284,
        },
        SLACK: {
          "**********": 6,
        },
        ONPREMISES: {
          "**************": 1,
          "*************": 2,
          "***********21": 100,
        },
        Monitor: {
          "***********": 1,
          "**********": 1,
          "**********": 1,
          "**********": 1,
          "********": 1,
          "*********": 1,
          "*********": 1,
          "**********": 1,
        },
      },
    },
  },
  {
    id: 0,
    data: {
      accounts: {
        AWS: {
          "qa-automation-434512": 2,
          "qa-automatio-434512": 70,
          "qa-automation-44512": 300,
        },
        AZURE: {},
        GCP: {
          "qa-automation512": 4,
          "qa-automaton512": 10,
        },
        ONEDRIVE: {
          "***********": 16,
        },
        SLACK: {},
        ONPREMISES: {
          "**************": 5,
          "*************": 50,
        },
        Monitor: {
          "***********": 2,
          "**********": 52,
        },
      },
    },
  },
  {
    id: 2,
    data: {
      accounts: {
        AWS: {
          "qa-automation-434512": 227,
        },
        AZURE: {
          "qa-automation-434512": 10,
          "qa-automation-34512": 10,
          "qa-automtion-434512": 200,
        },
        GCP: {
          "qa-automation512": 3,
        },
        ONEDRIVE: {
          "***********": 2,
          "**********": 2,
        },
        SLACK: {
          "**********": 4,
        },
        ONPREMISES: {
          "*************": 35,
        },
        Monitor: {
          "***********": 15,
        },
      },
    },
  },
  {
    id: 7,
    data: {
      accounts: {
        AWS: {
          "qa-automation-434512": 2,
          "qa-automation-43412": 170,
        },
        AZURE: {
          "qa-automation-434512": 25,
        },
        GCP: {
          "qa-automation512": 2,
        },
        ONEDRIVE: {
          "***********": 10,
          "**********": 100,
        },
        SLACK: {
          "**********": 8,
        },
        ONPREMISES: {
          "*************": 3,
          "************": 3,
          "************": 80,
        },
        Monitor: {
          "***********": 2,
          "**********": 2,
          "**********": 2,
          "**********": 50,
        },
      },
    },
  },
  {
    id: 8,
    data: {
      accounts: {
        AWS: {},
        AZURE: {
          "qa-automation-434512": 13,
        },
        GCP: {
          "qa-automation512": 190,
        },
        ONEDRIVE: {
          "***********": 168,
        },
        SLACK: {
          "**********": 6,
        },
        ONPREMISES: {
          "*************": 4,
          "************": 20,
        },
        Monitor: {
          "***********": 2,
          "**********": 2,
          "**********": 10,
          "**********": 10,
          "*********": 20,
        },
      },
    },
  },
  {
    id: 1,
    data: {
      accounts: {
        AWS: {
          "qa-automation-434512": 5,
          "qa-automation-43412": 80,
        },
        AZURE: {
          "qa-automation-434512": 6,
          "qa-automation-44512": 80,
        },
        GCP: {
          "qa-automation512": 1,
          "qa-automatio512": 90,
        },
        ONEDRIVE: {
          "***********": 1,
          "**********": 40,
        },
        SLACK: {},
        ONPREMISES: {
          "*************": 111,
        },
        Monitor: {
          "***********": 18,
        },
      },
    },
  },
  {
    id: 10,
    data: {
      accounts: {
        AWS: {},
        AZURE: {},
        GCP: {
          "qa-automation512": 40,
        },
        ONEDRIVE: {},
        SLACK: {},
        ONPREMISES: {},
        Monitor: {
          "***********": 2,
          "**********": 1,
          "*********621": 1,
          "************": 1,
          "*************": 1,
          "************": 1,
          "*********": 1,
          "*********": 1,
          "*********": 1,
          "*********": 1,
          "********": 1,
          "********": 1,
          "232323": 8,
          "***********": 300,
        },
      },
    },
  },
  {
    id: 9,
    data: {
      accounts: {
        AWS: {},
        AZURE: {},
        GCP: {
          "qa-automation512": 17,
        },
        ONEDRIVE: {
          "***********": 16,
        },
        SLACK: {
          "***********": 11,
        },
        ONPREMISES: {},
        Monitor: {
          "***********": 3,
          "**********": 3,
          "************": 130,
        },
      },
    },
  },
  {
    id: 3,
    data: {
      accounts: {
        AWS: {
          "qa-automation512": 30,
        },
        AZURE: {
          "qa-automation512": 2,
        },
        GCP: {
          "qa-automation512": 1,
          "qa-automation12": 10,
        },
        ONEDRIVE: {
          "***********": 7,
        },
        SLACK: {
          "***********": 3,
        },
        ONPREMISES: {
          "***********": 11,
        },
        Monitor: {
          "***********": 1,
          "**********": 30,
        },
      },
    },
  },
  {
    id: 6,
    data: {
      accounts: {
        AWS: {},
        AZURE: {
          "qa-automation512": 2,
          "qa-automatin512": 7,
          "qa-automatn512": 10,
        },
        GCP: {},
        ONEDRIVE: {
          "***********": 1,
        },
        SLACK: {
          "***********": 1,
        },
        ONPREMISES: {
          "***********": 50,
        },
        Monitor: {
          "***********": 21,
        },
      },
    },
  },
  {
    id: 5,
    data: {
      accounts: {
        AWS: {
          "qa-automation512": 5,
        },
        AZURE: {},
        GCP: {},
        ONEDRIVE: {
          "***********": 2,
        },
        SLACK: {
          "***********": 2,
        },
        ONPREMISES: {
          "***********": 4,
        },
        Monitor: {
          "***********": 1,
        },
      },
    },
  },
];

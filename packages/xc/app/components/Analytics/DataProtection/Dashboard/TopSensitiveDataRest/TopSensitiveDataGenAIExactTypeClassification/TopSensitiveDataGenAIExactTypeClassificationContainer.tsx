import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { type SelectedNodeProps } from "../types";
import {
  type PreparedDocCategory,
  type SampleMockGenAIDataType,
  type TopSensitiveDataGenAIClassificationContainerProps,
} from "./types";

import {
  iconMapping,
  defaultIcon,
  BubbleTreeChartsMockDataForGenAIType,
} from "./mock.data";
import { GEN_AI_CATEGORY_DATATYPES } from "./config";
import { TopSensitiveDataGenAIExactTypeClassification } from "./TopSensitiveDataGenAIExactTypeClassification";
import { getDataTestId } from "@/utils/utils";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import { usePostMutation } from "@/utils/apiUtils";
import { DataProtection } from "@/configs/urls/analytics/data-protection";

export const TopSensitiveDataGenAIExactTypeClassificationContainer = ({
  id,
  handleSelectedNode,
  isCloseDrawer,
  isLoading,
  isMockData,
}: TopSensitiveDataGenAIClassificationContainerProps) => {
  const ID = getDataTestId("sensitive-data-dlp-engine", id);
  const containerId = "DataProtection.Dashboard.SensitiveGENAIData";

  const handleClick = (data: SelectedNodeProps | null) => {
    handleSelectedNode(data, true);
  };

  const {
    trigger: _trigger,
    data,
    isMutating,
    error,
  } = usePostMutation(DataProtection.Dashboard.SensitiveDataAtRisk, {
    key: containerId,
  });

  const response = isMockData
    ? BubbleTreeChartsMockDataForGenAIType
    : (data as SampleMockGenAIDataType);

  const processResponse = (
    categories: SampleMockGenAIDataType["doc_categories"],
  ): PreparedDocCategory[] => {
    const result: PreparedDocCategory[] = [];

    Object?.entries(categories)?.forEach(([categoryKey, docTypes]) => {
      const docTypeIds = Object.keys(docTypes).map((key) => parseInt(key, 10));
      const totalCount = Object.values(docTypes).reduce(
        (sum, count) => sum + count,
        0,
      );

      const matchedCategory = GEN_AI_CATEGORY_DATATYPES.find(
        (cat) => cat.id === parseInt(categoryKey, 10),
      );

      if (matchedCategory) {
        result.push({
          category_id: matchedCategory.doc_category_id,
          category_name: matchedCategory.doc_type_name,
          docTypeIds: docTypeIds,
          total_count: totalCount,
        });
      }
    });

    return result;
  };

  function transformData(data: PreparedDocCategory[]) {
    return data?.map((item: PreparedDocCategory, index: number) => ({
      id: index.toString(),
      name: item.category_name,
      count: item.total_count,
      icon: (
        <FontAwesomeIcon
          icon={iconMapping[item.category_name] || defaultIcon}
        />
      ),
    }));
  }

  const preparedData = processResponse(response.doc_categories);

  const bubbleTreeNodeData = transformData(preparedData);

  return (
    <WithAnalyticsStates
      loading={isMockData ? undefined : isLoading || isMutating}
      error={isMockData ? undefined : error}
      showBorder={false}
      id={ID}
    >
      <TopSensitiveDataGenAIExactTypeClassification
        id={ID}
        chartData={bubbleTreeNodeData}
        handleSelectedNode={handleClick}
        isCloseDrawer={isCloseDrawer}
      />
    </WithAnalyticsStates>
  );
};

import { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { type SelectedNodeProps } from "../types";
import {
  type DataTypeObj,
  type DocType,
  type PreparedDocCategory,
  type GenAICategoryDataTypes,
  type GenAIPayloadFilters,
  type SampleMockGenAIDataType,
  type TopSensitiveDataGenAIClassificationContainerProps,
} from "./types";

import { TopSensitiveDataGenAIClassification } from "./TopSensitiveDataGenAIClassification";
import {
  iconMapping,
  defaultIcon,
  BubbleTreeChartsMockDataForGenAI,
} from "./mock.data";
import { GEN_AI_CATEGORY_DATATYPES } from "./config";
import { getDataTestId } from "@/utils/utils";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import { useZDXEntitlement } from "@/hooks/useZDXEntitlement";
import { usePostMutation } from "@/utils/apiUtils";
import { DataProtection } from "@/configs/urls/analytics/data-protection";

export const TopSensitiveDataGenAIClassificationContainer = ({
  id,
  handleSelectedNode,
  isCloseDrawer,
  aiFilter,
  isLoading,
  setIsLoading,
  channelsFilter,
  isMockData,
}: TopSensitiveDataGenAIClassificationContainerProps) => {
  const ID = getDataTestId("sensitive-data-dlp-engine", id);
  const containerId = "DataProtection.Dashboard.SensitiveGENAIData";
  const HAS_READ_PERMISSION = useZDXEntitlement(containerId);
  const [childData, setChildData] = useState<PreparedDocCategory[]>([]);
  const [docCategoryId, setDocCategoryId] = useState<number>();
  const [filteredChildData, setFilteredChildData] =
    useState<PreparedDocCategory[]>();

  const handleClick = (data: SelectedNodeProps | null) => {
    handleSelectedNode(data, true);
  };

  const { trigger, data, isMutating, error } = usePostMutation(
    DataProtection.Dashboard.SensitiveDataAtRisk,
    {
      key: containerId,
    },
  );

  const response = isMockData
    ? BubbleTreeChartsMockDataForGenAI
    : (data as SampleMockGenAIDataType);

  useEffect(() => {
    setIsLoading(false);
    const filters: GenAIPayloadFilters = { classification_scope: 1 };
    trigger({ filters });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [trigger, HAS_READ_PERMISSION]);

  useEffect(() => {
    setIsLoading(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [aiFilter, channelsFilter, isLoading]);

  const getDocumentTypeConfig = () => {
    const documentTypesMap: {
      isPrepared: boolean;
      doc_types: Record<number, GenAICategoryDataTypes>;
      doc_categories: Record<
        number,
        {
          doc_category_name: string;
          doc_category_id: number;
          doc_types: GenAICategoryDataTypes[];
        }
      >;
    } = {
      isPrepared: false,
      doc_types: {},
      doc_categories: {},
    };

    const { isPrepared, ...docTypesData } = documentTypesMap;
    if (isPrepared) return docTypesData;

    const documentTypes = GEN_AI_CATEGORY_DATATYPES || [];
    documentTypes.forEach((docType: GenAICategoryDataTypes) => {
      const { id } = docType;
      documentTypesMap.doc_types[id] = docType;

      if (documentTypesMap.doc_categories[docType.doc_category_id]) {
        documentTypesMap.doc_categories[docType.doc_category_id].doc_types.push(
          docType,
        );
      } else {
        documentTypesMap.doc_categories[docType.doc_category_id] = {
          doc_category_name: docType.doc_category_name,
          doc_types: [docType],
          doc_category_id: docType.doc_category_id,
        };
      }
    });

    documentTypesMap.isPrepared = true;

    return documentTypesMap;
  };

  const getDocumentTypeDetails = (
    id: string | number,
    field: "doc_types" | "doc_categories",
    details = false,
  ) => {
    const documentTypesConfig: {
      doc_types: Record<number, GenAICategoryDataTypes>;
      doc_categories: Record<
        number,
        {
          doc_category_name: string;
          doc_category_id: number;
          doc_types: GenAICategoryDataTypes[];
        }
      >;
    } = getDocumentTypeConfig();

    const numericId = typeof id === "string" ? parseInt(id, 10) : id;

    if (documentTypesConfig[field]) {
      const docTypeData = documentTypesConfig[field][numericId];

      if (details) {
        return docTypeData || {};
      } else {
        if (field === "doc_types") {
          return (docTypeData as GenAICategoryDataTypes)?.doc_type_name || id;
        } else if (field === "doc_categories") {
          return (
            (docTypeData as { doc_category_name: string }).doc_category_name ||
            id
          );
        }
      }
    }

    return details ? {} : id;
  };

  const prepareDocCategoryData = (
    docCategories: SampleMockGenAIDataType["doc_categories"],
  ): PreparedDocCategory[] => {
    const getDocTypeWithTotalCount = (
      docTypeObj: DataTypeObj,
    ): [DocType[], number] =>
      Object.keys(docTypeObj).reduce<[DocType[], number]>(
        (acc, docKey) => {
          const docName = getDocumentTypeDetails(docKey, "doc_types", true);

          if (
            typeof docName === "object" &&
            (docName as GenAICategoryDataTypes)?.doc_type_name
          ) {
            acc[0].push({
              name: (docName as GenAICategoryDataTypes).doc_type_name,
              value: docTypeObj[docKey],
              docTypeId: +docKey,
            });
          } else if (
            typeof docName === "object" &&
            (docName as { doc_category_name?: string })?.doc_category_name
          ) {
            acc[0].push({
              name: (docName as { doc_category_name?: string })
                .doc_category_name!,
              value: docTypeObj[docKey],
              docTypeId: +docKey,
            });
          }

          acc[1] += docTypeObj[docKey];

          return acc;
        },
        [[], 0],
      );

    return docCategories
      ? Object.keys(docCategories).reduce<PreparedDocCategory[]>(
          (acc, mlKey) => {
            const categoryName = getDocumentTypeDetails(
              mlKey,
              "doc_categories",
            );
            if (!categoryName) return acc;

            const [doc_types, total_count] = getDocTypeWithTotalCount(
              docCategories[mlKey],
            );
            if (!total_count) return acc;

            acc.push({
              category_name: categoryName as string,
              category_id: +mlKey,
              total_count,
              doc_types,
              docTypeIds: doc_types.map((item) => item.docTypeId),
            });

            return acc;
          },
          [],
        )
      : [];
  };

  const preparedData = prepareDocCategoryData(response?.doc_categories);

  const updateChildData = (childData: PreparedDocCategory[]) => {
    const docTypeMap: Record<number, string> = {};
    const documentTypesConfig: {
      doc_types: Record<number, GenAICategoryDataTypes>;
      doc_categories: Record<
        number,
        {
          doc_category_name: string;
          doc_category_id: number;
          doc_types: GenAICategoryDataTypes[];
        }
      >;
    } = getDocumentTypeConfig();
    Object.values(documentTypesConfig.doc_types).forEach(
      (category: GenAICategoryDataTypes) => {
        if (
          category.doc_category_id === docCategoryId &&
          category.doc_type_id !== undefined
        ) {
          docTypeMap[category.doc_type_id] = category.doc_type_name || "";
        }
      },
    );

    const updatedChildData = childData.map((child) => {
      const updatedDocTypes = child.doc_types.map((docType: DocType) => ({
        ...docType,
        name: docTypeMap[docType.docTypeId] || docType.name,
      }));

      return {
        ...child,
        doc_types: updatedDocTypes,
      };
    });

    return updatedChildData;
  };

  useEffect(() => {
    if (docCategoryId && childData?.length) {
      setIsLoading(false);
      if (childData?.length) {
        const outputChildData = updateChildData(childData);
        setFilteredChildData(outputChildData);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [docCategoryId, childData]);

  useEffect(() => {
    if (aiFilter?.length) {
      const docTypeData = preparedData?.filter(
        (allData: PreparedDocCategory) =>
          Number(aiFilter?.[0]?.id) === Number(allData?.category_id),
      );
      setChildData(docTypeData);
      setDocCategoryId(docTypeData?.[0]?.category_id);
      setIsLoading(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [aiFilter]);

  function transformData(data: PreparedDocCategory[]) {
    return data?.map((item: PreparedDocCategory, index: number) => ({
      id: index.toString(),
      name: item.category_name,
      count: item.total_count,
      icon: (
        <FontAwesomeIcon
          icon={iconMapping[item.category_name] || defaultIcon}
        />
      ),
    }));
  }

  function transformChildrenData(data: DocType[]) {
    return data?.map((item: DocType, index: number) => ({
      id: index.toString(),
      name: item?.name,
      count: item?.value,
      icon: <FontAwesomeIcon icon={iconMapping[item.name] || defaultIcon} />,
    }));
  }

  const bubbleTreeNodeData =
    aiFilter?.length && filteredChildData?.length
      ? transformChildrenData(filteredChildData?.[0]?.doc_types)
      : transformData(preparedData);

  return (
    <WithAnalyticsStates
      loading={isMockData ? undefined : isLoading || isMutating}
      error={isMockData ? undefined : error}
      showBorder={false}
      id={ID}
    >
      <TopSensitiveDataGenAIClassification
        id={ID}
        chartData={bubbleTreeNodeData}
        handleSelectedNode={handleClick}
        isCloseDrawer={isCloseDrawer}
      />
    </WithAnalyticsStates>
  );
};

import { useTranslation } from "react-i18next";
import { datavizColors } from "@zs-nimbus/dataviz-colors";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faClock, faXmark } from "@fortawesome/pro-solid-svg-icons";
import { useEffect } from "react";
import Link from "next/link";
import { TopSensitiveDataRestDrawer } from "./TopSensitiveDataRestDrawer";
import {
  type PayloadFilters,
  type ResponseProps,
  type TopSensitiveDataRestDrawerContainerProps,
  type TopSensitiveDataRestDrawerItemProps,
} from "./types";
import {
  CloudMap,
  topSensitiveDataRestDrawerConfig,
  TransformResponse,
} from "./config";
import { getSystemTheme } from "@/utils/themeUtils";
import { formatNumber, getDataTestId, handleKeyboardDown } from "@/utils/utils";
import { type DonutProps } from "@/components/Analytics/Charts/DonutChart/types";
import { useZDXEntitlement } from "@/hooks/useZDXEntitlement";
import { usePostMutation } from "@/utils/apiUtils";
import { DataProtection } from "@/configs/urls/analytics/data-protection";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";

export const TopSensitiveDataRestDrawerContainer = ({
  id = "",
  handleClose,
  selectedNode,
  filters,
  isMockData,
  drawerData,
}: TopSensitiveDataRestDrawerContainerProps) => {
  const { t } = useTranslation();
  const theme = getSystemTheme();
  const ID = getDataTestId("drawer", id);
  const containerId = "DataProtection.Dashboard.SensitiveDataRestDrawer";
  const HAS_READ_PERMISSION = useZDXEntitlement(containerId);

  const { trigger, data, isMutating, error } = usePostMutation(
    DataProtection.Dashboard.SensitiveDataRestDrawer,
    {
      key: containerId,
    },
  );

  useEffect(() => {
    const payloads: PayloadFilters = {
      classification_scope: filters?.selectedTab === 1 ? 1 : 2,
    };

    if (filters.channelsFilter?.length) {
      const cloudKey = filters.channelsFilter[0]?.id as keyof typeof CloudMap;
      if (cloudKey in CloudMap) {
        payloads.cloud = CloudMap[cloudKey];
      }
    }

    if (filters.aiFilter?.length) {
      payloads.doc_categories = [Number(filters.aiFilter[0].id)];
      payloads.oc_sub_types = [Number(selectedNode?.id)];
    }

    if (!filters.aiFilter?.length) {
      payloads.doc_categories = [Number(selectedNode?.id)];
    }

    trigger({ filters: payloads });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [trigger, HAS_READ_PERMISSION, filters]);

  const transformClassificationResponseData = (
    rawResponse: TopSensitiveDataRestDrawerItemProps | null,
  ) => {
    const colorMap: Record<string, string> = {
      "Public Clouds": datavizColors[theme].dataviz.status.primary.neutral,
      Saas: datavizColors[theme].dataviz.sequential.blue.scale02,
      "On-Premises": datavizColors[theme].dataviz.sequential.blue.scale01,
      Endpoint: datavizColors[theme].dataviz.sequential.blue.scale04,
    };
    let total = 0;
    const updatedData = rawResponse?.subCategory?.length
      ? rawResponse?.subCategory?.map((item) => {
          const count = item.data?.length
            ? item.data.reduce((sum, item) => sum + item.count, 0)
            : 0;
          total += count;
          const category = t(item.name);

          return {
            category: category,
            count: count,
            color: colorMap[category],
          };
        })
      : [];

    return {
      donutChartData: {
        config: {
          ...topSensitiveDataRestDrawerConfig(theme),
          labelProps: {
            ...topSensitiveDataRestDrawerConfig(theme).labelProps,
            centerLabel: {
              ...topSensitiveDataRestDrawerConfig(theme).labelProps
                ?.centerLabel,
              text1: {
                ...topSensitiveDataRestDrawerConfig(theme).labelProps
                  ?.centerLabel?.text1,
                text: `${formatNumber(total)}`,
              },
              text2: {
                ...topSensitiveDataRestDrawerConfig(theme).labelProps
                  ?.centerLabel?.text2,
                text: `${t("TOTAL_FILES")}[/]`,
              },
            },
          },
        },
        dataKeys: ["count", "category"],
        data: updatedData,
      } as DonutProps,
    };
  };

  const responseData = TransformResponse(
    selectedNode?.name ?? "",
    filters?.aiFilter?.length ? filters.aiFilter[0]?.name : "",
    isMockData ? (drawerData as ResponseProps) : (data as ResponseProps),
  );

  const TopSensitiveDataRestDrawerChartData =
    transformClassificationResponseData(responseData);

  return (
    <div
      className="flex w-[440px] border-l border-semantic-border-base-primary"
      data-testid={ID}
    >
      <div className="flex flex-col h-full gap-3 p-6 bg-semantic-surface-inverted-base-primary w-[440px] transform transition-transform duration-1000 ease-out translate-x-0">
        <div className="flex flex-col gap-2">
          <div className="flex flex-row justify-between items-center">
            <div className="typography-header5 text-semantic-content-base-primary">
              {responseData?.name}
            </div>
            <FontAwesomeIcon
              icon={faXmark}
              aria-label={t("CLOSE_ICON")}
              role="button"
              className="text-semantic-brand-default cursor-pointer focus-visible-default block"
              onClick={handleClose}
              onKeyDown={handleKeyboardDown(handleClose)}
              tabIndex={0}
            />
          </div>
          <div className="typography-paragraph2 text-semantic-content-base-tertiary">
            {responseData?.categoryName}
          </div>
        </div>
        <WithAnalyticsStates
          loading={isMockData ? undefined : isMutating}
          error={isMockData ? undefined : error}
          showBorder={false}
          noData={isMockData ? false : !responseData?.subCategory.length}
          id={ID}
        >
          <div className="flex flex-col gap-4">
            <TopSensitiveDataRestDrawer
              {...TopSensitiveDataRestDrawerChartData}
              id={ID}
            />

            {responseData?.subCategory.map((item, index) => (
              <div
                className="flex flex-col border-t border-semantic-border-base-primary"
                key={index}
              >
                <div className="flex flex-col gap-2 mt-4 px-3">
                  <div className="inline-flex justify-between">
                    <div className="typography-paragraph1-strong text-semantic-content-base-primary">
                      {t(item.name)}
                    </div>
                    {!item?.data?.length && (
                      <div className="inline-flex bg-semantic-surface-status-neutral-default border border-semantic-border-status-neutral-default px-2 py-1 rounded-40 gap-1">
                        <div className="typography-paragraph1 text-semantic-content-status-neutral-secondary">
                          <FontAwesomeIcon icon={faClock} />
                        </div>
                        <div className="typography-paragraph1 text-semantic-content-status-neutral-primary">
                          {t("COMING_SOON")}
                        </div>
                      </div>
                    )}
                  </div>

                  {item?.data?.map((data, index) => (
                    <div className="inline-flex justify-between" key={index}>
                      <div className="inline-flex gap-1">
                        <div className="typography-paragraph2">
                          <data.icon height={20} width={20} />
                        </div>
                        <div className="typography-paragraph2 text-semantic-content-base-primary">
                          {data.name}
                        </div>
                      </div>

                      <Link
                        href={data?.link ?? "#"}
                        target={data?.link ? "_blank" : ""}
                      >
                        <div className="typography-paragraph2 text-semantic-content-interactive-primary-default hover:text-semantic-content-interactive-primary-hover active:text-semantic-content-interactive-primary-active">
                          {data.count}
                        </div>
                      </Link>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </WithAnalyticsStates>
      </div>
    </div>
  );
};

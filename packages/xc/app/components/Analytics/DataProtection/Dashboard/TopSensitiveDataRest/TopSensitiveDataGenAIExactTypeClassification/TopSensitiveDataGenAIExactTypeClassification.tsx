import { t } from "i18next";
import { type SelectedNodeProps } from "../types";
import { type TopSensitiveDataGenAIClassificationProps } from "./types";
import BubbleTreeChart from "@/components/Analytics/Charts/BubbleTreeChart/BubbleTreeChart";

export function TopSensitiveDataGenAIExactTypeClassification({
  chartData,
  id,
  handleSelectedNode,
  isCloseDrawer,
}: TopSensitiveDataGenAIClassificationProps) {
  const handleClick = (data: SelectedNodeProps | null) => {
    handleSelectedNode(data);
  };

  return (
    <div className="flex justify-center" id={id}>
      <BubbleTreeChart
        chartsData={chartData}
        onNodeClick={(selectedNode) => handleClick(selectedNode)}
        isUnselectNode={isCloseDrawer}
        supportsPagination
        description={t("DP_DASHBOARD_SENSITIVE_FILES")}
      />
    </div>
  );
}

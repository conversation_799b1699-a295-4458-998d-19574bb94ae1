import { useTranslation } from "react-i18next";
import { getDeviceOSConfig } from "./config";
import { DeviceOS } from "./DeviceOS";
import { type DeviceOSProps, type DeviceOSData } from "./types";
import { generateMAPayload } from "@/components/pages/Analytics/Dashboard/Filters/ModalPillGroupContainer/util";
import { usePostMutation } from "@/utils/apiUtils";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import { Operational } from "@/configs/urls/analytics/operational";
import useFilterEffect from "@/hooks/useFilterEffect";
import { FILTER_DEVICES_CONFIG } from "@/components/pages/Analytics/Dashboard/Filters/ModalPillGroupContainer/config";
import { useProductAccessProvider } from "@/context/ProductAccessProvider";
import { useMAEntitlement } from "@/hooks/useMAEntitlement";
import { getDataTestId, getTopEntries } from "@/utils/utils";
import { getSystemTheme } from "@/utils/themeUtils";

const DeviceOSContainer = ({ id }: { id?: string }) => {
  const ID = getDataTestId("device-os", id);
  const { entitlements } = useProductAccessProvider();
  const containerId = "Operational.Devices.DeviceOS";
  const theme = getSystemTheme();
  const { t } = useTranslation();
  const deviceOSConfig = getDeviceOSConfig(theme, t);

  const {
    trigger: fetchDeviceOSData,
    data: response,
    isMutating,
    error,
  } = usePostMutation(Operational.Devices.DeviceOS, {
    key: containerId,
  });

  //TODO: Update payload with time filters.
  useFilterEffect(
    fetchDeviceOSData,
    generateMAPayload,
    entitlements?.zcc,
    {
      user: [0],
      type: [0],
      osId: [0],
    },
    FILTER_DEVICES_CONFIG,
  );

  const transformResponseData = (rawResponse: DeviceOSData[]) => {
    const data = rawResponse?.map((device) => ({
      category: device.operatingSystem,
      categoryLabel: t(device.operatingSystem),
      value: +device.count,
    }));
    const total = rawResponse?.reduce((prev, curr) => prev + +curr.count, 0);

    return {
      heading: deviceOSConfig.heading,
      total,
      barChartProps: {
        data: getTopEntries(data),
        axisKeys: deviceOSConfig.axisKeys,
        config: deviceOSConfig.barChartProps,
      },
    } as DeviceOSProps;
  };

  const responseData = response as DeviceOSData[];
  const transformedData = transformResponseData(responseData);

  return (
    <WithAnalyticsStates
      loading={isMutating}
      error={error}
      title={deviceOSConfig?.heading}
      noData={!transformedData?.barChartProps?.data?.length}
      unsubscribed={useMAEntitlement()}
      id={ID}
    >
      <DeviceOS {...transformedData} id={ID} />
    </WithAnalyticsStates>
  );
};

export { DeviceOSContainer };

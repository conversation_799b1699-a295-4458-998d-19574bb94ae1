import { useTranslation } from "react-i18next";
import { getDeviceModelConfig } from "./config";
import { type DeviceModelProps, type DeviceModelData } from "./types";
import { DeviceModel } from "./DeviceModel";
import { usePostMutation } from "@/utils/apiUtils";
import { WithAnalyticsStates } from "@/hoc/WithAnalyticsStates";
import { Operational } from "@/configs/urls/analytics/operational";
import useFilterEffect from "@/hooks/useFilterEffect";
import { generateMAPayload } from "@/components/pages/Analytics/Dashboard/Filters/ModalPillGroupContainer/util";
import { FILTER_DEVICES_CONFIG } from "@/components/pages/Analytics/Dashboard/Filters/ModalPillGroupContainer/config";
import { useProductAccessProvider } from "@/context/ProductAccessProvider";
import { useMAEntitlement } from "@/hooks/useMAEntitlement";
import { getDataTestId, getTopEntries } from "@/utils/utils";
import { getSystemTheme } from "@/utils/themeUtils";

const DeviceModelContainer = ({ id }: { id?: string }) => {
  const ID = getDataTestId("device-model", id);
  const { entitlements } = useProductAccessProvider();
  const containerId = "Operational.Devices.DeviceModel";
  const theme = getSystemTheme();
  const { t } = useTranslation();
  const deviceModelConfig = getDeviceModelConfig(theme, t);

  const {
    trigger: fetchDeviceModelData,
    data: response,
    isMutating,
    error,
  } = usePostMutation(Operational.Devices.DeviceModel, {
    key: containerId,
  });

  //TODO: Update payload with time filters.
  useFilterEffect(
    fetchDeviceModelData,
    generateMAPayload,
    entitlements?.zcc,
    {
      user: [0],
      type: [0],
      osId: [0],
    },
    FILTER_DEVICES_CONFIG,
  );

  const transformResponseData = (rawResponse: DeviceModelData[]) => {
    const data = rawResponse?.map((device) => ({
      category: device.model,
      categoryLabel: t(device.model),
      value: +device.count,
    }));
    const total = rawResponse?.reduce((prev, curr) => prev + +curr.count, 0);

    return {
      heading: deviceModelConfig.heading,
      total,
      barChartProps: {
        data: getTopEntries(data),
        axisKeys: deviceModelConfig.axisKeys,
        config: deviceModelConfig.barChartProps,
      },
    } as DeviceModelProps;
  };

  const responseData = response as DeviceModelData[];
  const transformedData = transformResponseData(responseData);

  return (
    <WithAnalyticsStates
      loading={isMutating}
      error={error}
      title={deviceModelConfig?.heading}
      noData={!transformedData?.barChartProps?.data?.length}
      unsubscribed={useMAEntitlement()}
      id={ID}
    >
      <DeviceModel {...transformedData} id={ID} />
    </WithAnalyticsStates>
  );
};

export { DeviceModelContainer };

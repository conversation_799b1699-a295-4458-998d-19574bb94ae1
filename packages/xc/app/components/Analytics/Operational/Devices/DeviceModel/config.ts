import { colors } from "@zs-nimbus/foundations";
import * as am5 from "@amcharts/amcharts5";
import { type DeviceModelConfigType } from "./types";
import { type ThemeTypes } from "@/context/UserPreferenceContext";
import { formatNumber } from "@/utils/utils";

export const getDeviceModelConfig = (
  theme: ThemeTypes,
  t: (s: string) => string,
): DeviceModelConfigType => ({
  heading: "DEVICE_MODEL",
  axisKeys: ["category", "value"],
  barChartProps: {
    containerClass: "min-h-[260px]",
    numberFormatter: {
      numberFormat: "#.#a",
      bigNumberPrefixes: [
        { number: 1e3, suffix: "K" },
        { number: 1e6, suffix: "M" },
        { number: 1e9, suffix: "B" },
      ],
    },
    roundedColumnProps: {
      cornerRadiusBL: 4,
      cornerRadiusTL: 4,
      height: 16,
    },
    chartProps: {
      chartSettings: {
        paddingTop: 0,
        paddingBottom: 0,
        paddingRight: 0,
        paddingLeft: 0,
      },
    },
    axisProps: {
      yAxisProps: { minGridDistance: 1 },
      xAxisProps: { minGridDistance: 90 },
      xGridProps: {
        visible: true,
        stroke: am5.color(colors[theme].content.base.tertiary),
      },
      xLabelProps: {
        visible: true,
        labelColor: colors[theme].content.base.tertiary,
      },
      yLabelProps: {
        centerX: 0,
        labelColor: colors[theme].content.base.primary,
      },
    },
    tooltipProps: {
      tooltipBackgroundSetting: {
        fillColor: colors[theme].surface.fields.default,
      },
      getTooltipHtml: () => `
              <div class="text-semantic-content-base-primary w-full">
                  <div class="typography-header5 mb-xs">${t("DEVICE_MODEL")}</div> 
                  <div class="flex justify-between bg-brand-white">   
                    <div class="typography-paragraph1 mr-xl">{categoryLabel}</div>
                    <div class="typography-paragraph1 text-semantic-content-base-tertiary">{value}</div>
                  </div>
              </div>
        `,
    },
    bulletProps: {
      showBullets: true,
      bulletColor: colors[theme].content.base.primary,
      bulletsLabelFormatter: (val: string) => formatNumber(+val),
    },
    gradientProps: {
      showGradient: true,
      stops: [
        {
          color: "rgba(33, 96, 225, 1)",
          opacity: 1,
        },
        {
          color: "rgba(33, 96, 225, 0.5)",
          opacity: 0.8,
        },
      ],
      rotation: 0,
    },
  },
});

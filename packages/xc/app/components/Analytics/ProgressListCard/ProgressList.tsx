import classNames from "classnames";
import { Tooltip } from "@xc/legacy-components";
import { cn } from "@up/components";
import { ProgressBar } from "../ProgressBar/ProgressBar";
import {
  formatScoreValue,
  getDataTestId,
  getProgressBarColor,
} from "@/utils/utils";

export type ListItemType = {
  label: string;
  score: number;
};

type ListTooltipPosition = "top" | "left" | "right" | "bottom";

export type ProgressListTooltipProps = {
  tooltipPosition?: ListTooltipPosition;
  customClass?: string;
};

type ProgressListProps = {
  listData: ListItemType[];
  size: string;
  isTooltip?: boolean;
  id?: string;
  darkMode?: boolean;
  className?: string;
};

const ProgressList = ({
  listData,
  size,
  isTooltip = false,
  darkMode = false,
  id,
  className,
  tooltipPosition = "top",
  customClass,
}: ProgressListProps & ProgressListTooltipProps) => {
  const ID = getDataTestId("progress-list", id);

  return (
    <div
      className={cn(`h-full ${!isTooltip && "overflow-y-auto"}`, className)}
      data-testid={ID}
    >
      {listData?.map((listItem, index) => (
        <div
          className={classNames(
            "flex typography-paragraph1 text-semantic-content-base-primary gap-m hover:bg-blue-grey-50 ml-m",
            {
              "border-b border-semantic-border-base-primary":
                index != listData.length - 1,
            },
          )}
          key={`${listItem.label}-${index}`}
          data-testid={getDataTestId(index, ID)}
        >
          {(isTooltip && (
            <Tooltip
              text={listItem.label}
              position={tooltipPosition}
              classList={["w-[250px] justify-center"]}
              customClass={customClass}
              darkMode={darkMode}
            >
              <span className="w-[230px] flex h-[50px] justify-center ">
                <span
                  className={classNames(
                    "overflow-hidden text-ellipsis whitespace-nowrap",
                    {
                      "py-l": size == "lg",
                    },
                    {
                      "py-m": size == "sm",
                    },
                  )}
                >
                  {listItem.label}
                </span>
              </span>
            </Tooltip>
          )) || (
            <div
              className={classNames(
                "w-1/2 overflow-hidden block whitespace-nowrap text-ellipsis px-m",
                {
                  "py-l": size == "lg",
                },
                {
                  "py-m": size == "sm",
                },
              )}
            >
              {listItem.label}
            </div>
          )}
          <div
            className={classNames(
              "flex items-center w-1/2 justify-center px-m",
              {
                "py-l": size == "lg",
              },
              {
                "py-m": size == "sm",
              },
            )}
          >
            <span className="mr-m">{formatScoreValue(listItem?.score)}</span>
            <ProgressBar
              size="md"
              color={getProgressBarColor(listItem?.score)}
              value={listItem?.score}
              thickness="sm"
              showCircularDelimiter={false}
              id={getDataTestId(index, ID)}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProgressList;

import { Card } from "@zs-nimbus/core";
import ProgressList, {
  type ListItemType,
  type ProgressListTooltipProps,
} from "./ProgressList";
import { getDataTestId } from "@/utils/utils";

export type ProgressListCardProps = {
  heading: string;
  listData: ListItemType[];
  footerText?: string;
  onFooterClick?: () => void;
  size: string;
  isTooltip?: boolean;
  id?: string;
  darkMode?: boolean;
  className?: string;
} & ProgressListTooltipProps;

const ProgressListCard = ({
  heading,
  listData,
  size,
  isTooltip = false,
  id,
  darkMode = false,
  ...rest
}: ProgressListCardProps) => (
  <Card.Root
    className="h-full w-2/5 !justify-start"
    data-testid={getDataTestId(`card-container`, id)}
  >
    <Card.Header
      className="text-semantic-content-base-primary typography-header5"
      data-testid={getDataTestId(`card-title`, id)}
    >
      {heading}
    </Card.Header>
    <Card.Body>
      <ProgressList
        listData={listData}
        size={size}
        isTooltip={isTooltip}
        id={id}
        darkMode={darkMode}
        {...rest}
      />
    </Card.Body>
  </Card.Root>
);

export { ProgressListCard };

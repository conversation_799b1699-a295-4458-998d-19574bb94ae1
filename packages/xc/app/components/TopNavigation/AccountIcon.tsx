import {
  type ReactElement,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { t } from "i18next";
import { Tooltip } from "@zs-nimbus/core";
import AccountPopover from "../AccountPopover/AccountPopover";
import HelpPopover, { type HelpPopoverProps } from "../HelpPopover/HelpPopover";
import { ActivationContainer } from "@/components/Activation/ActivationContainer";
import { handleKeyboardDown } from "@/utils/utils";
import { type AccountPopoverProps } from "@/types/types";

export type AccountIconProps = AccountPopoverProps & {
  helpPopover: HelpPopoverProps;
  search?: ReactElement;
};

export function AccountIcon({
  options,
  helpPopover,
  search = undefined,
}: AccountIconProps) {
  const userRef = useRef<HTMLDivElement | null>(null);
  const accountPopoverRef = useRef<HTMLDivElement | null>(null);
  const helpIconRef = useRef<HTMLDivElement | null>(null);
  const helpPopoverRef = useRef<HTMLDivElement | null>(null);
  const [isAccountPopOverOpen, setIsAccountPopOverOpen] =
    useState<boolean>(false);
  const [isHelpPopOverOpen, setIsHelpPopOverOpen] = useState<boolean>(false);

  const [isActivationOpen, setIsActivationOpen] = useState<boolean>(false);

  const handleAccountPopover = useCallback(() => {
    setIsAccountPopOverOpen(!isAccountPopOverOpen);
    setIsHelpPopOverOpen(false);
    setIsActivationOpen(false);
  }, [isAccountPopOverOpen]);

  const handleHelpPopover = useCallback(() => {
    setIsHelpPopOverOpen(!isHelpPopOverOpen);
    setIsAccountPopOverOpen(false);
    setIsActivationOpen(false);
  }, [isHelpPopOverOpen]);

  const onActivationClickHandler = useCallback((open: boolean) => {
    setIsActivationOpen(open);
    if (open) {
      setIsAccountPopOverOpen(false);
      setIsHelpPopOverOpen(false);
    }
  }, []);

  const onClickhandler = useCallback((target: EventTarget | null) => {
    const userNotClicked =
      userRef.current && !userRef.current.contains(target as Node);

    const outsideAccountPopoverClicked =
      accountPopoverRef.current &&
      !accountPopoverRef.current.contains(target as Node);

    const helpIconNotClicked =
      helpIconRef.current && !helpIconRef.current.contains(target as Node);

    const outsideHelpPopoverClicked =
      helpPopoverRef.current &&
      !helpPopoverRef.current.contains(target as Node);

    if (outsideAccountPopoverClicked && userNotClicked) {
      setIsAccountPopOverOpen(false);
    }

    if (outsideHelpPopoverClicked && helpIconNotClicked) {
      setIsHelpPopOverOpen(false);
    }
  }, []);

  const handleMouseClick = useCallback(
    (event: MouseEvent) => {
      onClickhandler(event.target);
    },
    [onClickhandler],
  );

  useEffect(() => {
    document.addEventListener("mousedown", handleMouseClick);

    return () => {
      document.removeEventListener("mousedown", handleMouseClick);
    };
  }, [handleMouseClick]);

  const ID = "top-nav-options";

  return (
    <div
      className="text-semantic-content-immutable-white flex pr-l gap-default justify-end items-center"
      data-testid={ID}
    >
      {search && <>{search}</>}

      <div ref={helpIconRef} className="relative">
        <Tooltip
          placement="bottom"
          description={t("HELP")}
          className="min-w-[3.25rem]"
        >
          <span className="typography-header5">
            <button
              type="button"
              aria-label={t("QUESTION_ICON")}
              onClick={handleHelpPopover}
              onKeyDown={handleKeyboardDown(() => handleHelpPopover())}
              tabIndex={0}
              data-testid={"help-icon"}
            >
              <i
                className={`fa-regular p-[.375rem] fa-circle-question font-normal cursor-pointer focus-visible-on-color ${isHelpPopOverOpen ? "bg-semantic-surface-nav-topBar-active rounded-80" : "hover:rounded-80 hover:bg-semantic-surface-nav-topBar-hover"}`}
              />
            </button>
          </span>
        </Tooltip>
        {isHelpPopOverOpen && (
          <div ref={helpPopoverRef}>
            <HelpPopover {...helpPopover} id={ID} />
          </div>
        )}
      </div>
      <ActivationContainer
        onToggle={onActivationClickHandler}
        isOpen={isActivationOpen}
      />
      <div ref={userRef} data-testid="account-popover-container">
        <Tooltip
          placement="left"
          description={t("ACCOUNT_POPOVER_ACCOUNT_SETTINGS")}
          className="min-w-[3.25rem]"
        >
          <span className="typography-header5">
            <button
              className={`${isAccountPopOverOpen ? "bg-semantic-surface-nav-topBar-active rounded-80" : "hover:rounded-80 hover:bg-semantic-surface-nav-topBar-hover"} font-normal p-[.375rem] cursor-pointer focus-visible-on-color`}
              type="button"
              aria-label={t("USER_ICON")}
              onClick={handleAccountPopover}
              onKeyDown={handleKeyboardDown(() => handleAccountPopover())}
              tabIndex={0}
              data-testid="nav-account-popover"
            >
              <i
                className={`fa-user ${isAccountPopOverOpen ? "fa-solid" : "fa-regular"}`}
              />
            </button>
          </span>
        </Tooltip>
        {isAccountPopOverOpen && (
          <>
            <AccountPopover
              ref={accountPopoverRef}
              options={options}
              id={ID}
              onClose={() => setIsAccountPopOverOpen(!isAccountPopOverOpen)}
            />
          </>
        )}
      </div>
    </div>
  );
}

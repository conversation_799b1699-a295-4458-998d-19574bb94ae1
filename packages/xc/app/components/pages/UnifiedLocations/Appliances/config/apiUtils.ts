import { LOCATIONS_DEFAULT_PARAMS } from "../../Locations/config/apiUtils";
import { type FetchApplianceParams } from "../types";
import { type FetchParams } from "@/configs/urls/onboarding/types";
import { API_ENDPOINTS, buildQueryString } from "@/utils/apiHelper";

export const APPLIANCE_ENDPOINTS = {
  applianceList: `${API_ENDPOINTS.ZUXP}/unified-locations/appliances`,
  applianceDetails: `${API_ENDPOINTS.ZUXP}/unified-locations/appliancesID`,
  fetchAppliances: (params: FetchParams) =>
    `${API_ENDPOINTS.ZUXP}/unified-locations/appliances?${buildQueryString({ ...LOCATIONS_DEFAULT_PARAMS.fetchLocations, ...params })}`,
  fetchApplianceById: (id: string, params: FetchApplianceParams) =>
    `${API_ENDPOINTS.ZUXP}/unified-locations/appliance/${id}?${buildQueryString({ ...params })}`,
  updateApplianceOverview: (id: string) =>
    `${API_ENDPOINTS.ZUXP}/unified-locations/appliance/${id}/overview`,
  updateApplianceNetwork: (id: string) =>
    `${API_ENDPOINTS.ZUXP}/unified-locations/appliance/${id}/network`,
  updateApplianceSupportTunnel: (id: string) =>
    `${API_ENDPOINTS.ZUXP}/unified-locations/appliance/${id}/support-tunnel`,
  deleteAppliance: `${API_ENDPOINTS.ZUXP}/unified-locations/appliance-group`,
  updateApplianceUpgradeSchedule: (id: string) =>
    `${API_ENDPOINTS.ZUXP}/unified-locations/appliance/${id}/schedule`,
  updateApplianceInterface: (id: string) =>
    `${API_ENDPOINTS.ZUXP}/unified-locations/appliance/${id}/interface`,
  deleteInterface: (id: string) =>
    `${API_ENDPOINTS.ZUXP}/unified-locations/appliance/${id}/intf`,
  updateAdminStatus: (
    id: string,
    applianceGroupId: string | null,
    status: string,
  ) =>
    `${API_ENDPOINTS.ZUXP}/unified-locations/${applianceGroupId}/appliance/${id}/${status}`,
};

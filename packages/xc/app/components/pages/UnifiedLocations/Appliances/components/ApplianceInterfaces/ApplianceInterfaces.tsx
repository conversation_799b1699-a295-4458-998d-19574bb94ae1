/* eslint-disable @up/unified-platform/max-file-lines */
import {
  ZDataTable,
  ZMenu,
  Zselect,
  ZToggleSwitch,
} from "@xc/legacy-components";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import debounce from "lodash/debounce";
import classNames from "classnames";

import { SimpleDropdownMenu, Button } from "@zs-nimbus/core";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faEllipsisVertical,
  faPlus,
  faTrash,
} from "@fortawesome/pro-regular-svg-icons";
import { cn } from "@up/components";
import {
  type ApplianceInterfacesDataSetProps,
  type ApplianceInterfacesProps,
  type ApplianceInterfacesStatus,
  type ApplianceInterfacesTableRowItemProps,
  type FilterOptionsProps,
  type IPAddressInfoData,
  type LANInterfaceFormDataProps,
} from "../../types";
import LANInterfaceDrawer from "../../Drawers/LANInterfaceDrawer/LANInterfaceDrawer";
import { LAN_INTERFACE_DATA } from "../../Drawers/LANInterfaceDrawer/LANInterfaceDrawer.data";
import WANInterface from "../../Drawers/WANInterface/WANInterface";
import { WAN_INTERFACE_DATA } from "../../Drawers/WANInterface/WANInterface.data";
import MGTDrawer from "../../Drawers/MGTDrawer/MGTDrawer";
import { MGT_DRAWER_DATA } from "../../Drawers/MGTDrawer/MGTDrawer.data";
import DeleteModal from "../../../Components/DeleteModal/DeleteModal";
import {
  DEFAULTGATEWAY,
  DISABLED,
  DNS,
  DOMAINNAME,
  ENABLED,
  DEFAULTLEASETIME,
  DEFAULT_INTERFACE_MTU,
  SUBINTERFACE_MTU_BUFFER,
} from "../../../constants";
import { isEmptyObject } from "../../../utils/utils";
import { type ApplianceDetailFromServer } from "../../ApplianceDetailTransformer";
import SearchBar from "@/components/SearchBar/SearchBar";
import { getDataTestId, handleKeyboardDown } from "@/utils/utils";
import TableLoader from "@/components/TableLoader/TableLoader";

const ApplianceInterfaces = ({
  interfacesOptions,
  tableRowData,
  columnData,
  onSortChange,
  onInterfacesChange,
  apiResponse,
  triggerUpdateAppliance,
  readOnly,
}: ApplianceInterfacesProps) => {
  const [openMgtDrawer, setOpenMgtDrawer] = useState(false);
  const [openWanDrawer, setOpenWanDrawer] = useState(false);
  const [openLanDrawer, setOpenLanDrawer] = useState(false);
  const [interfaceType, setInterfaceType] = useState("");
  const [filterRowData, setFilterRowData] =
    useState<ApplianceInterfacesTableRowItemProps[]>(tableRowData);
  const [searchInterfaceVal, setSearchInterfaceVal] = useState("");

  const wanCount =
    apiResponse?.applianceInterfaceList?.reduce((count, iface) => {
      const total = iface.type === "WAN" ? 1 : 0;

      return count + total;
    }, 0) ?? 0;

  const lanCount =
    apiResponse?.applianceInterfaceList?.reduce((count, iface) => {
      const total = iface.type === "LAN" ? 1 : 0;

      return count + total;
    }, 0) ?? 0;

  const wanSubWanCount =
    apiResponse?.applianceInterfaceList?.reduce((count, iface) => {
      let total = iface.type === "WAN" ? 1 : 0;
      total += (iface.subInterfaces || []).reduce(
        (subCount, sub) => subCount + (sub.type === "WAN" ? 1 : 0),
        0,
      );

      return count + total;
    }, 0) ?? 0;

  const [ids, setIds] = useState<{
    interfaceId: string;
    subInterfaceID: string;
  }>({ interfaceId: "", subInterfaceID: "" });
  const LARGE_CIRCLE = "\u2B24";
  const onClickHandler = (type: string, value: string) => {
    switch (type) {
      case "LAN":
        if (value) {
          setIsSubInterface(true);
        } else {
          setIsSubInterface(false);
        }
        setIsAddDrawer(false);
        setOpenLanDrawer(true);
        break;
      case "WAN":
        if (value) {
          setIsSubInterface(true);
        } else {
          setIsSubInterface(false);
        }
        setIsAddDrawer(false);
        setOpenWanDrawer(true);
        break;
      case "MGT":
        setIsAddDrawer(false);
        setIsSubInterface(false);
        setOpenMgtDrawer(true);
        break;
      default:
        setIsSubInterface(true);
        break;
    }
  };
  const { t } = useTranslation();
  const [interfacesFilters, setInterfacesFilters] = useState<
    FilterOptionsProps[]
  >([]);
  const [subInterfacesToggle, setSubInterfacesToggle] = useState<boolean>(true);

  const handleIterfacesFilter = (options: FilterOptionsProps[]) => {
    setInterfacesFilters(options);
  };

  const handleFilteredResult = debounce((value: string) => {
    setSearchInterfaceVal(value);
  }, 300);

  useEffect(() => {
    const type =
      interfacesFilters?.[0]?.id === "ALL" || interfacesFilters.length === 0
        ? ""
        : interfacesFilters?.[0]?.id;

    const filterTableRowData = tableRowData.filter(
      (row) =>
        (type === "" ? row : row.type?.includes(type)) &&
        row.interfaces.name
          ?.toLowerCase()
          .includes(searchInterfaceVal.toLowerCase()),
    );
    const filterTableRowDataSubInterface = filterTableRowData.map((obj) => {
      const { subInterfaces, ...newObj } = obj;

      return newObj;
    });
    if (subInterfacesToggle) {
      setFilterRowData(filterTableRowData);
    } else {
      setFilterRowData(filterTableRowDataSubInterface);
    }
  }, [
    interfacesFilters,
    tableRowData,
    subInterfacesToggle,
    searchInterfaceVal,
  ]);

  const handleReset = () => {
    setInterfacesFilters([]);
    onInterfacesChange([]);
    setSubInterfacesToggle(true);
  };

  const [deleteInterface, setDeleteInterface] = useState<string | null>(null);
  const [isSubInterface, setIsSubInterface] = useState<boolean>(false);

  const AppliancesInterfacesMapping = ({
    interfaceId,
    subInterfaceID,
    name,
    meta,
    value,
    type = "",
    onClickHandler,
  }: ApplianceInterfacesDataSetProps) => (
    <div className="flex flex-col">
      {name && (
        <span
          className={classNames(`text-semantic-brand-default`, {
            "ml-rem-240": value,
          })}
        >
          <Button
            variant="base"
            className={cn(
              `typography-paragraph2 text-semantic-surface-interactive-primary-default cursor-pointer focus-visible-default flex items-center -ml-rem-80`,
              {
                "text-semantic-content-base-primary pointer-events-none":
                  readOnly,
              },
            )}
            onClick={() => {
              onClickHandler?.(type ?? "LAN", value?.toString() ?? "");
              setIds({
                interfaceId: interfaceId ?? "",
                subInterfaceID: subInterfaceID ?? "",
              });
            }}
            onKeyDown={handleKeyboardDown(() => {
              onClickHandler?.(type ?? "LAN", value?.toString() ?? "");
              setIds({
                interfaceId: interfaceId ?? "",
                subInterfaceID: subInterfaceID ?? "",
              });
            })}
            tabIndex={0}
            data-testid={getDataTestId(
              "appliance-drawer-link",
              "unified-locations",
            )}
          >
            <div className="flex items-center gap-2">
              {name}{" "}
              {value && (
                <>
                  <div className="text-[4px]">{LARGE_CIRCLE}</div>
                  {value}
                </>
              )}
            </div>
          </Button>
        </span>
      )}
      {meta && (
        <span className="typography-paragraph3 text-semantic-content-base-secondary">
          {meta}
        </span>
      )}
    </div>
  );

  const AppliancesMapping = ({
    name,
    meta,
  }: ApplianceInterfacesDataSetProps) => (
    <div className="flex flex-col">
      <span className="typography-paragraph2 text-semantic-content-base-primary">
        {name ? t(name) : "--"}
      </span>
      {meta && (
        <span className="typography-paragraph3 text-semantic-content-base-tertiary">
          {t(meta)}
        </span>
      )}
    </div>
  );

  const AppliancesStatusMapping = ({
    adminValue,
    linkValue,
    type,
  }: ApplianceInterfacesStatus) => {
    const valueKeyMap = {
      link: linkValue,
      admin: adminValue,
    };
    const success =
      ((type === "admin" || type === "link") &&
        adminValue === "Up" &&
        linkValue === "Up") ||
      (type === "admin" && adminValue === "Up" && linkValue === "Down") ||
      (type === "link" && adminValue === "Down" && linkValue === "Up");
    const warning =
      (type === "link" && adminValue === "Up" && linkValue === "Down") ||
      (type === "admin" && adminValue === "Down" && linkValue === "Up");
    const neutral = linkValue === "Down" && adminValue === "Down";

    return valueKeyMap[type] ? (
      <div className="flex flex-wrap">
        <div
          className={classNames(
            `typography-paragraph2 rounded-40 py-rem-40 px-rem-80 border`,
            {
              "text-semantic-content-status-neutral-primary border-semantic-border-status-neutral-default bg-semantic-surface-status-neutral-default":
                neutral,
            },
            {
              "text-semantic-content-status-success-primary border-semantic-border-status-success-default bg-semantic-surface-accent-green-default":
                success,
            },
            {
              "text-semantic-content-status-danger-primary border-semantic-border-status-danger-default bg-semantic-surface-status-danger-default":
                warning,
            },
          )}
        >
          {type === "link" ? linkValue : adminValue}
        </div>
      </div>
    ) : (
      <span className="typography-paragraph2 text-semantic-content-base-primary">
        --
      </span>
    );
  };

  const transformTableData = (
    tableData: ApplianceInterfacesTableRowItemProps[],
    onClickHandler: (type: string, value: string) => void,
  ) =>
    tableData?.map(
      ({
        id,
        interfaces,
        type,
        ipAddress,
        dhcp,
        adminStatus,
        linkStatus,
        interfaceDetail,
        subInterfaces,
      }) => ({
        id,
        interface: (
          <AppliancesInterfacesMapping
            interfaceId={interfaces.name ?? ""}
            name={interfaces.name}
            value={interfaces.value}
            meta={interfaces.meta}
            type={type}
            onClickHandler={onClickHandler}
          />
        ),
        type: <AppliancesMapping name={type} meta={""} />,
        ipAddress: <AppliancesMapping name={ipAddress ?? ""} meta={""} />,
        dhcp: <AppliancesMapping name={dhcp.name} meta={dhcp.meta} />,
        details: (
          <AppliancesMapping
            name={interfaceDetail.name}
            meta={interfaceDetail.meta}
          />
        ),
        adminStatus: (
          <AppliancesStatusMapping
            adminValue={adminStatus.value}
            adminStatus={adminStatus.status}
            linkValue={linkStatus.value}
            linkStatus={linkStatus.status}
            type="admin"
          />
        ),
        linkStatus: (
          <AppliancesStatusMapping
            adminValue={adminStatus.value}
            adminStatus={adminStatus.status}
            linkValue={linkStatus.value}
            linkStatus={linkStatus.status}
            type={"link"}
          />
        ),
        actions: !readOnly ? (
          <div className="flex gap-default justify-end w-full">
            {!interfaces?.value && !type && (
              <SimpleDropdownMenu
                handler={{
                  prefixIcon: <FontAwesomeIcon icon={faEllipsisVertical} />,
                  label: "",
                  variant: "tertiary",
                }}
                id="appliance-interface-menu"
                items={[
                  {
                    id: "add-lan",
                    leftIcon: <FontAwesomeIcon icon={faPlus} />,
                    name: t("locations.appliance.interface.add-lan"),
                  },
                  {
                    id: "add-wan",
                    leftIcon: <FontAwesomeIcon icon={faPlus} />,
                    name: t("locations.appliance.interface.add-wan"),
                    disabled: wanSubWanCount == 2 ? true : false,
                  },
                ]}
                onClick={(item) => {
                  setIds({
                    interfaceId: interfaces.name ?? "",
                    subInterfaceID: "",
                  });
                  setIsSubInterface(false);
                  setIsAddDrawer(true);
                  if (item?.id === "add-lan") {
                    setInterfaceType("LAN");
                    setOpenLanDrawer(true);
                  }
                  if (item?.id === "add-wan") {
                    setInterfaceType("WAN");
                    setOpenWanDrawer(true);
                  }
                }}
                placement="bottom start"
                trigger="press"
              />
            )}
            {!interfaces?.value && type === "WAN" && (
              <SimpleDropdownMenu
                handler={{
                  prefixIcon: <FontAwesomeIcon icon={faEllipsisVertical} />,
                  label: "",
                  variant: "tertiary",
                }}
                id="appliance-interface-menu"
                items={[
                  {
                    id: "add-sub-interface",
                    leftIcon: <FontAwesomeIcon icon={faPlus} />,
                    name: t("locations.appliance.interface.add-sub-interface"),
                    disabled: wanSubWanCount == 2 ? true : false,
                  },
                  {
                    id: "delete-interface",
                    leftIcon: <FontAwesomeIcon icon={faTrash} />,
                    name: t("DELETE"),
                    disabled: wanCount > 1 ? false : true,
                  },
                ]}
                onClick={(item) => {
                  if (item?.id === "delete-interface") {
                    setDeleteInterface(interfaces?.name);
                    setIsSubInterface(false);
                  }
                  if (item?.id === "add-sub-interface") {
                    setIds({
                      interfaceId: interfaces.name ?? "",
                      subInterfaceID: "",
                    });
                    setInterfaceType("WAN");
                    setIsSubInterface(true);
                    setIsAddDrawer(true);
                    setOpenWanDrawer(true);
                  }
                }}
                placement="bottom start"
                trigger="press"
              />
            )}
            {!interfaces?.value && type === "LAN" && (
              <SimpleDropdownMenu
                handler={{
                  prefixIcon: <FontAwesomeIcon icon={faEllipsisVertical} />,
                  label: "",
                  variant: "tertiary",
                }}
                id="appliance-interface-menu"
                items={[
                  {
                    id: "add-sub-interface",
                    leftIcon: <FontAwesomeIcon icon={faPlus} />,
                    name: t("locations.appliance.interface.add-sub-interface"),
                  },
                  {
                    id: "delete-interface",
                    leftIcon: <FontAwesomeIcon icon={faTrash} />,
                    name: t("DELETE"),
                    disabled: lanCount > 1 ? false : true,
                  },
                ]}
                onClick={(item) => {
                  if (item?.id === "delete-interface") {
                    setDeleteInterface(interfaces?.name);
                    setIsSubInterface(false);
                  }
                  if (item?.id === "add-sub-interface") {
                    setIds({
                      interfaceId: interfaces.name ?? "",
                      subInterfaceID: "",
                    });
                    setInterfaceType("LAN");
                    setIsSubInterface(true);
                    setIsAddDrawer(true);
                    setOpenLanDrawer(true);
                  }
                }}
                placement="bottom start"
                trigger="press"
              />
            )}
          </div>
        ) : null,
        isRowExapdable: false,

        children: subInterfaces?.map(
          ({
            id: childId,
            interfaces: childInterfaces,
            type: childType,
            ipAddress: childIpAddress,
            dhcp: childDhcp,
            adminStatus: childAdminStatus,
            linkStatus: childLinkStatus,
            interfaceDetail: childInterfaceDetail,
            name,
          }) => ({
            id: childId,
            interface: (
              <AppliancesInterfacesMapping
                subInterfaceID={name ?? ""}
                name={childInterfaces.name}
                value={childInterfaces.value}
                meta={childInterfaces.meta}
                type={childType}
                onClickHandler={onClickHandler}
              />
            ),
            type: <AppliancesMapping name={childType} meta={""} />,
            ipAddress: (
              <AppliancesMapping name={childIpAddress ?? ""} meta={""} />
            ),
            dhcp: (
              <AppliancesMapping name={childDhcp.name} meta={childDhcp.meta} />
            ),
            details: (
              <AppliancesMapping
                name={childInterfaceDetail.name}
                meta={childInterfaceDetail.meta}
              />
            ),
            adminStatus: (
              <AppliancesStatusMapping
                adminValue={childAdminStatus.value}
                adminStatus={childAdminStatus.status}
                linkValue={childLinkStatus.value}
                linkStatus={childLinkStatus.status}
                type="admin"
              />
            ),
            linkStatus: (
              <AppliancesStatusMapping
                adminValue={childAdminStatus.value}
                adminStatus={childAdminStatus.status}
                linkValue={childLinkStatus.value}
                linkStatus={childLinkStatus.status}
                type={"link"}
              />
            ),
            actions: (
              <div className="flex gap-default justify-end w-full p-2">
                <ZMenu
                  items={[
                    {
                      id: "delete-action",
                      name: "DELETE",
                      iconClass:
                        "fa-regular fa-trash text-semantic-brand-default",
                      ariaLabel: `DELETE-${childInterfaces?.name}`,
                    },
                  ]}
                  onItemSelect={() => {
                    setIsSubInterface(true);
                    setDeleteInterface(name ?? "");
                  }}
                />
              </div>
            ),
          }),
        ),
      }),
    );

  const translatedColumnData = columnData;
  const translatedTableColumnData = translatedColumnData.map((tcd) => {
    if (typeof tcd.name === "string") {
      tcd.name = t(tcd.name);
    }

    return tcd;
  });

  const outputData = transformTableData(filterRowData, onClickHandler);

  const [isAddDrawer, setIsAddDrawer] = useState<boolean>(false);
  useEffect(() => {
    if (apiResponse) {
      setOpenMgtDrawer(false);
      setOpenWanDrawer(false);
      setOpenLanDrawer(false);
    }
  }, [apiResponse]);

  const selectedData = ids.interfaceId
    ? apiResponse?.applianceInterfaceList.find(
        (item) => item.name == ids.interfaceId,
      )
    : ids.subInterfaceID
      ? (() => {
          const parentInterface = apiResponse?.applianceInterfaceList.find(
            (item) =>
              (item.subInterfaces ?? []).some(
                (subInteface) => subInteface.name === ids.subInterfaceID,
              ),
          );
          if (!parentInterface) return null;
          const subInterface = parentInterface?.subInterfaces?.find(
            (sub) => sub.name === ids.subInterfaceID,
          );

          return subInterface
            ? {
                ...subInterface,
                parentMtu: parentInterface.mtu,
              }
            : null;
        })()
      : null;

  const mgtDrawerData = {
    ipAddress: selectedData?.ipAddress ?? "",
    defaultGateway: selectedData?.defaultGateway ?? "",
    primaryDNS: selectedData?.primaryDns ?? "",
    secondaryDNS: selectedData?.secondaryDns ?? "",
    dhcpEnabled: selectedData?.dhcpStatus === ENABLED ? true : false,
  };
  const [initMGTFormData, setMGTInitFormData] = useState<IPAddressInfoData>(
    MGT_DRAWER_DATA.initFormData,
  );

  useEffect(() => {
    setMGTInitFormData((prev) => ({ ...prev, ...mgtDrawerData }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openMgtDrawer]);

  const wanDrawerData = {
    ipAddress: selectedData?.ipAddress ?? "",
    defaultGateway: selectedData?.defaultGateway ?? "",
    primaryDNS: selectedData?.primaryDns ?? "",
    secondaryDNS: selectedData?.secondaryDns ?? "",
    dhcpEnabled: selectedData?.dhcpStatus === ENABLED ? true : false,
    upLinkMode:
      selectedData?.upLinkMode ??
      WAN_INTERFACE_DATA.IPAddressInfoData.upLinkMode.selectedValue,
    mtu: selectedData?.mtu ?? "",
    parentMtu: selectedData?.parentMtu ?? "",
    description: selectedData?.detailsDescription ?? "",
    vlanId: selectedData?.id?.toString() ?? "",
  };

  const addWanDrawerData = {
    ...WAN_INTERFACE_DATA.initFormData,
    mtu: `${
      isSubInterface
        ? DEFAULT_INTERFACE_MTU - SUBINTERFACE_MTU_BUFFER
        : DEFAULT_INTERFACE_MTU
    }`,
  };

  const [initWanFormData, setWanInitFormData] = useState<IPAddressInfoData>(
    WAN_INTERFACE_DATA.initFormData,
  );

  useEffect(() => {
    const drawerData = isAddDrawer ? addWanDrawerData : wanDrawerData;
    setWanInitFormData((prev) => ({ ...prev, ...drawerData }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openWanDrawer]);

  const lanDrawerData = {
    adminStatus:
      selectedData?.adminStatus ?? LAN_INTERFACE_DATA.adminStatus.selectedValue,
    mtu: selectedData?.mtu ?? "",
    parentMtu: selectedData?.parentMtu ?? "",
    ipAddress: selectedData?.ipAddress ?? "",
    description: selectedData?.detailsDescription ?? "",
    customDNSInputVal: {
      primaryDNS: selectedData?.primaryDns ?? "",
      secDNS: selectedData?.secondaryDns ?? "",
    },
    id: selectedData?.haConfig?.id ?? "",
    haVirtualIp: selectedData?.haConfig?.virtualIpAddress ?? "",
    passphrase: selectedData?.haConfig?.passphrase ?? "",
    toggles: {
      dhcpServer: selectedData?.dhcpStatus === ENABLED ? true : false,
      customDNS:
        (selectedData?.primaryDns ?? selectedData?.secondaryDns) ? true : false,
      highAvailability: isEmptyObject(selectedData?.haConfig ?? {}),
      preferred: selectedData?.haConfig?.preferred ?? false,
    },
    peerDhcpIp: selectedData?.lanDhcpConfig?.peerDhcpIp ?? "",
    defaultLeaseTime:
      selectedData?.lanDhcpConfig?.defaultLeaseTime ??
      DEFAULTLEASETIME.DEFAULTLEASETIME,
    maxLeaseTime:
      selectedData?.lanDhcpConfig?.maxLeaseTime ??
      DEFAULTLEASETIME.MAXLEASETIME,
    dhcpServerStaticIp: selectedData?.lanDhcpConfig?.dhcpLease?.length
      ? selectedData?.lanDhcpConfig?.dhcpLease?.map((item, index) => ({
          id: index,
          addressType: item.macAddress,
          ip: item.ipAddress,
        }))
      : LAN_INTERFACE_DATA.initFormData.dhcpServerStaticIp,
    dhcpRangeData: selectedData?.lanDhcpConfig?.dhcpRange?.length
      ? selectedData?.lanDhcpConfig?.dhcpRange?.map((item, index) => ({
          id: index,
          ipStart: item.ipStart,
          ipEnd: item.ipEnd,
        }))
      : LAN_INTERFACE_DATA.initFormData.dhcpRangeData,
    vlanId: selectedData?.id?.toString() ?? "",
    dhcpServerData: LAN_INTERFACE_DATA.initFormData.dhcpServerData?.map(
      (item) => {
        if (item.selectedOption === DEFAULTGATEWAY) {
          item.value =
            selectedData?.lanDhcpConfig?.dhcpOptions?.defaultGateway?.toString() ??
            "";
        }
        if (item.selectedOption === DNS) {
          item.value =
            selectedData?.lanDhcpConfig?.dhcpOptions?.domainNameServersList?.join(
              ", ",
            ) ?? "";
        }

        if (item.selectedOption === DOMAINNAME) {
          item.value =
            selectedData?.lanDhcpConfig?.dhcpOptions?.domainSearch?.join(
              ", ",
            ) ?? "";
        }

        return {
          ...item,
        };
      },
    ),
  };

  const addLanDrawerData = {
    ...LAN_INTERFACE_DATA.initFormData,
    mtu: `${
      isSubInterface
        ? DEFAULT_INTERFACE_MTU - SUBINTERFACE_MTU_BUFFER
        : DEFAULT_INTERFACE_MTU
    }`,
  };

  const [initLanFormData, setLanInitFormData] =
    useState<LANInterfaceFormDataProps>(LAN_INTERFACE_DATA.initFormData);

  useEffect(() => {
    const drawerData = isAddDrawer ? addLanDrawerData : lanDrawerData;
    setLanInitFormData((prev) => ({ ...prev, ...drawerData }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openLanDrawer]);

  const getUpdatedFormData = (formData: Partial<IPAddressInfoData>) => {
    if (isAddDrawer) {
      return apiResponse?.applianceInterfaceList
        ?.filter((item) => item.name === ids.interfaceId)
        .map((item) =>
          !isSubInterface
            ? {
                ...item,
                ...formData,
                id: selectedData?.id,
                name: selectedData?.name,
                type: selectedData?.type ?? interfaceType,
                ...(formData?.dhcpStatus === "Enabled"
                  ? { ipAddress: null }
                  : { ipAddress: formData.ipAddress }),

                ...(formData?.dhcpStatus === "Enabled"
                  ? { defaultGateway: null }
                  : { defaultGateway: formData.defaultGateway }),

                ...(formData?.dhcpStatus === "Enabled"
                  ? formData.primaryDns
                    ? { primaryDns: formData.primaryDns }
                    : { primaryDns: null }
                  : {
                      primaryDns:
                        formData.primaryDns !== "" ? formData.primaryDns : null,
                    }),

                ...(formData?.dhcpStatus === "Enabled"
                  ? formData.secondaryDns
                    ? { secondaryDns: formData.secondaryDns }
                    : { secondaryDns: null }
                  : {
                      secondaryDns:
                        formData.secondaryDns !== ""
                          ? formData.secondaryDns
                          : null,
                    }),
              }
            : {
                ...item,
                subInterfaces: item.subInterfaces?.length
                  ? [
                      ...item.subInterfaces,
                      {
                        ...formData,
                        existingVlanId: "NEW",
                        type: selectedData?.type ?? interfaceType,
                        ...(formData?.dhcpStatus === "Enabled"
                          ? { ipAddress: null }
                          : { ipAddress: formData.ipAddress }),

                        ...(formData?.dhcpStatus === "Enabled"
                          ? { defaultGateway: null }
                          : { defaultGateway: formData.defaultGateway }),

                        ...(formData?.dhcpStatus === "Enabled"
                          ? formData.primaryDns
                            ? { primaryDns: formData.primaryDns }
                            : { primaryDns: null }
                          : {
                              primaryDns:
                                formData.primaryDns !== ""
                                  ? formData.primaryDns
                                  : null,
                            }),

                        ...(formData?.dhcpStatus === "Enabled"
                          ? formData.secondaryDns
                            ? { secondaryDns: formData.secondaryDns }
                            : { secondaryDns: null }
                          : {
                              secondaryDns:
                                formData.secondaryDns !== ""
                                  ? formData.secondaryDns
                                  : null,
                            }),
                      },
                    ]
                  : [
                      {
                        ...formData,
                        existingVlanId: "NEW",
                        type: selectedData?.type ?? interfaceType,
                        ...(formData?.dhcpStatus === "Enabled"
                          ? { ipAddress: null }
                          : { ipAddress: formData.ipAddress }),

                        ...(formData?.dhcpStatus === "Enabled"
                          ? { defaultGateway: null }
                          : { defaultGateway: formData.defaultGateway }),

                        ...(formData?.dhcpStatus === "Enabled"
                          ? formData.primaryDns
                            ? { primaryDns: formData.primaryDns }
                            : { primaryDns: null }
                          : {
                              primaryDns:
                                formData.primaryDns !== ""
                                  ? formData.primaryDns
                                  : null,
                            }),

                        ...(formData?.dhcpStatus === "Enabled"
                          ? formData.secondaryDns
                            ? { secondaryDns: formData.secondaryDns }
                            : { secondaryDns: null }
                          : {
                              secondaryDns:
                                formData.secondaryDns !== ""
                                  ? formData.secondaryDns
                                  : null,
                            }),
                      },
                    ],
              },
        );
    }

    return apiResponse?.applianceInterfaceList
      ?.filter(
        (item) =>
          item.name === ids.interfaceId ||
          item.subInterfaces?.some((sub) => sub.name === ids.subInterfaceID),
      )
      .map((item) => {
        if (item.name === ids.interfaceId) {
          return {
            ...item,
            ...formData,

            ...(formData?.dhcpStatus === "Enabled" && item?.type === "WAN"
              ? { ipAddress: null }
              : { ipAddress: formData.ipAddress }),

            ...(formData?.dhcpStatus === "Enabled"
              ? { defaultGateway: null }
              : { defaultGateway: formData.defaultGateway }),

            ...(formData?.dhcpStatus === "Enabled"
              ? formData.primaryDns
                ? { primaryDns: formData.primaryDns }
                : { primaryDns: null }
              : {
                  primaryDns:
                    formData.primaryDns !== "" ? formData.primaryDns : null,
                }),

            ...(formData?.dhcpStatus === "Enabled"
              ? formData.secondaryDns
                ? { secondaryDns: formData.secondaryDns }
                : { secondaryDns: null }
              : {
                  secondaryDns:
                    formData.secondaryDns !== "" ? formData.secondaryDns : null,
                }),
          };
        }

        if (item?.subInterfaces) {
          return {
            ...item,

            subInterfaces: item.subInterfaces?.map((sub) =>
              sub.name === ids.subInterfaceID
                ? {
                    ...sub,
                    ...formData,
                    existingVlanId: sub?.name,
                    ...(formData?.dhcpStatus === "Enabled" &&
                    sub?.type === "WAN"
                      ? { ipAddress: null }
                      : { ipAddress: formData.ipAddress }),

                    ...(formData?.dhcpStatus === "Enabled"
                      ? { defaultGateway: null }
                      : { defaultGateway: formData.defaultGateway }),

                    ...(formData?.dhcpStatus === "Enabled"
                      ? formData.primaryDns
                        ? { primaryDns: formData.primaryDns }
                        : { primaryDns: null }
                      : {
                          primaryDns:
                            formData.primaryDns !== ""
                              ? formData.primaryDns
                              : null,
                        }),

                    ...(formData?.dhcpStatus === "Enabled"
                      ? formData.secondaryDns
                        ? { secondaryDns: formData.secondaryDns }
                        : { secondaryDns: null }
                      : {
                          secondaryDns:
                            formData.secondaryDns !== ""
                              ? formData.secondaryDns
                              : null,
                        }),
                  }
                : sub,
            ),
          };
        }

        return item;
      });
  };

  const getdeletedFormData = (id: string) =>
    apiResponse?.applianceInterfaceList
      ?.filter((item) => item.name !== id)
      .map((item) => {
        if (!item?.subInterfaces) {
          return item;
        }
        const filteredInterfaces = item.subInterfaces.filter(
          (sub) => sub.name !== id,
        );

        return {
          ...item,
          subInterfaces: filteredInterfaces,
        };
      });

  const onSaveMGThandler = (formData: IPAddressInfoData) => {
    setMGTInitFormData(formData);
    const newFormData = {
      dhcpStatus: formData.dhcpEnabled ? ENABLED : DISABLED,
      ...(formData?.ipAddress && { ipAddress: formData.ipAddress }),
      ...(formData?.defaultGateway && {
        defaultGateway: formData.defaultGateway,
      }),
      ...(formData?.primaryDNS && { primaryDns: formData.primaryDNS }),
      ...(formData?.secondaryDNS && {
        secondaryDns: formData.secondaryDNS,
      }),
      ...(formData?.adminStatus && { adminStatus: formData.adminStatus }),
    };

    const updatedFormData = getUpdatedFormData(newFormData);

    const payload = {
      ...apiResponse!,
      applianceInterfaceList: [...updatedFormData!],
    } as ApplianceDetailFromServer;

    triggerUpdateAppliance?.(payload, "update-interface");
  };

  const onSaveWANhandler = (
    formData: IPAddressInfoData,
    isSubinterface: boolean,
  ) => {
    setWanInitFormData(formData);
    const newFormData = {
      defaultGateway: formData.defaultGateway ?? null,
      detailsDescription: formData.description ?? null,
      dhcpStatus: formData.dhcpEnabled ? ENABLED : DISABLED,
      ipAddress: formData.ipAddress ?? null,
      primaryDns: formData.primaryDNS ?? null,
      secondaryDns: formData.secondaryDNS ?? null,
      ...(formData?.upLinkMode && {
        upLinkMode: formData.upLinkMode,
      }),
      ...(formData?.mtu && {
        mtu: formData.mtu,
      }),
      ...(formData?.vlanId &&
        isSubinterface && {
          id: Number(formData.vlanId),
        }),
      ...(formData?.vlanId &&
        isSubinterface && {
          name: `${selectedData?.name?.split(".")[0]}.${formData?.vlanId}`,
        }),
    };

    const updatedFormData = getUpdatedFormData(newFormData);

    const payload = {
      ...apiResponse!,
      applianceInterfaceList: [...updatedFormData!],
    } as ApplianceDetailFromServer;
    triggerUpdateAppliance?.(payload, "update-interface");
  };

  const onSaveLANhandler = (
    formData: LANInterfaceFormDataProps,
    isSubinterface: boolean,
  ) => {
    setLanInitFormData(formData);
    const dhcpLeaseoptions = formData.dhcpServerStaticIp
      ?.filter((item) => item.addressType?.trim() || item.ip?.trim())
      .map((item) => ({
        macAddress: item.addressType,
        ipAddress: item.ip,
      }));

    const dhcpRangeoptions = formData.dhcpRangeData
      ?.filter((item) => item.ipStart?.trim() || item.ipEnd?.trim())
      .map((item) => ({
        ipStart: item.ipStart,
        ipEnd: item.ipEnd,
      }));

    const newFormData = {
      adminStatus: formData.adminStatus ?? null,
      mtu: formData.mtu,
      ipAddress: formData.ipAddress ?? null,
      detailsDescription: formData.description ?? null,
      primaryDns: formData.customDNSInputVal.primaryDNS ?? null,
      secondaryDns: formData.customDNSInputVal.secDNS ?? null,
      dhcpStatus: formData.toggles.dhcpServer ? ENABLED : DISABLED,
      haConfig: formData.toggles.highAvailability
        ? isEmptyObject({
            id: formData.id,
            passphrase: formData.passphrase,
            virtualIpAddress: formData.haVirtualIp,
            preferred: formData.toggles.preferred,
          })
          ? {
              ...selectedData?.haConfig,
              ...(formData.id && { id: formData.id }),
              ...(formData.passphrase && { passphrase: formData.passphrase }),
              ...(formData.haVirtualIp && {
                virtualIpAddress: formData.haVirtualIp,
              }),
              preferred: formData.toggles.preferred,
            }
          : null
        : null,
      lanDhcpConfig: formData.toggles.dhcpServer
        ? {
            ...selectedData?.lanDhcpConfig,
            ...(formData.peerDhcpIp && { peerDhcpIp: formData.peerDhcpIp }),
            ...(formData.maxLeaseTime && {
              maxLeaseTime: formData.maxLeaseTime,
            }),
            ...(formData.defaultLeaseTime && {
              defaultLeaseTime: formData.defaultLeaseTime,
            }),
            ...(dhcpRangeoptions?.length > 0 && {
              dhcpRange: dhcpRangeoptions,
            }),
            ...(dhcpLeaseoptions?.length > 0 && {
              dhcpLease: dhcpLeaseoptions,
            }),
            dhcpOptions: {
              ...selectedData?.lanDhcpConfig?.dhcpOptions,
              ...(formData?.dhcpServerData?.find(
                (item) => item.selectedOption === DEFAULTGATEWAY,
              )?.value && {
                defaultGateway: formData?.dhcpServerData
                  ?.find((item) => item.selectedOption === DEFAULTGATEWAY)
                  ?.value.split(","),
              }),

              ...(formData?.dhcpServerData?.find(
                (item) => item.selectedOption === DNS,
              )?.value && {
                domainNameServersList: formData?.dhcpServerData
                  ?.find((item) => item.selectedOption === DNS)
                  ?.value.split(","),
              }),
              ...(formData?.dhcpServerData?.find(
                (item) => item.selectedOption === DOMAINNAME,
              )?.value && {
                domainSearch: formData?.dhcpServerData
                  ?.find((item) => item.selectedOption === DOMAINNAME)
                  ?.value.split(","),
              }),
            },
          }
        : null,
      ...(formData?.vlanId &&
        isSubinterface && {
          id: Number(formData.vlanId),
        }),
      ...(formData?.vlanId &&
        isSubinterface && {
          name: `${selectedData?.name?.split(".")[0]}.${formData?.vlanId}`,
        }),
    };
    const updatedFormData = getUpdatedFormData(newFormData);
    const payload = {
      ...apiResponse!,
      applianceInterfaceList: [...updatedFormData!],
    } as ApplianceDetailFromServer;
    triggerUpdateAppliance?.(payload, "update-interface");
  };

  const deleteSelectedInterface = (id: string) => {
    const updatedFormData = getdeletedFormData(id);

    const payload = {
      ...apiResponse!,
      applianceInterfaceList: [...updatedFormData!],
    };

    triggerUpdateAppliance?.(payload, "delete-interface");
  };

  return (
    <div
      className="px-rem-80 flex flex-col w-full"
      data-testid="unified-locations-appliance-interfaces"
    >
      <div className="flex justify-between w-full items-center mb-rem-120">
        <div className="flex gap-rem-120 items-center">
          <div className="flex gap-rem-120 items-center">
            <Zselect
              showSelectedOptions={false}
              idAttr={"id"}
              valueAttr={"name"}
              id="interface-type"
              multiSelect={false}
              enableSelectAll={false}
              flip={false}
              enableCancel={false}
              options={interfacesOptions.map((data) => {
                data.name = t(data.name);

                return data;
              })}
              overrideCollapsedView={(
                ...props: [boolean, FilterOptionsProps[]]
              ) => {
                const show = props?.[0];
                const selectedOption = props?.[1];

                const summaryText = selectedOption
                  .map((elem) => elem.name)
                  .join(", ");

                return (
                  <>
                    <span
                      className={classNames(
                        `text-semantic-surface-base-primary`,
                        { "summary-text": !show },
                      )}
                    >
                      {!!summaryText.length ? `${t("TYPE")} = ` : t("TYPE")}
                    </span>
                    {!!summaryText.length && (
                      <span
                        className={classNames(
                          `text-semantic-surface-base-primary`,
                          { "text-selected": !show },
                        )}
                      >
                        {t(summaryText)}
                      </span>
                    )}
                  </>
                );
              }}
              preSelectedOptions={interfacesFilters}
              enableClearSelection={false}
              showClearSelection={false}
              isPill
              searchOptions={{ enable: false }}
              onSelectionChange={handleIterfacesFilter}
              customClass="fix-padding-initial-render typography-paragraph1 !font-normal !text-semantic-content-base-primary pill [&_span.summary-text]:!text-semantic-content-base-primary [&_i.fa-caret-down]:!text-semantic-content-interactive-primary-default"
              showDropdownIconVariation={false}
              onActiveShowValue={false}
              minWidth="150"
            />
            <div
              className="flex items-center"
              data-testid={`unified-locations-toggle-sub-interfaces`}
            >
              <ZToggleSwitch
                id={"toggle-sub-interfaces-filter"}
                checked={subInterfacesToggle}
                small
                type="secondary"
                onChange={(value: boolean) => {
                  setSubInterfacesToggle(value);
                }}
              />
              <div
                className="typography-paragraph2 text-semantic-content-base-secondary pl-rem-80"
                aria-label={t(
                  "locations.appliance.interface.filters.sub-interfaces",
                )}
              >
                {t("locations.appliance.interface.filters.sub-interfaces")}
              </div>
            </div>
            {interfacesFilters.length > 0 || !subInterfacesToggle ? (
              <div
                className="text-semantic-content-interactive-primary-default typography-paragraph1 cursor-pointer"
                onClick={handleReset}
                onKeyDown={handleReset}
                tabIndex={0}
                role="button"
              >
                <i
                  aria-label={t("RESET_ICON")}
                  className="fa-solid fa-rotate-left pr-rem-40"
                />
                {t("RESET")}
              </div>
            ) : (
              ""
            )}
          </div>
        </div>
        <div className="flex flex-row items-center gap-rem-160">
          <SearchBar
            onChange={handleFilteredResult}
            isOnlyExpanded={true}
            userSearchText=""
          />
        </div>
      </div>
      <div className="w-full overflow-hidden border-l border-r border-l-semantic-border-base-primary border-r-semantic-border-base-primary z-0">
        <ZDataTable
          columns={translatedTableColumnData}
          id={"appliance-interface-list"}
          nestedItems={outputData}
          noSelection={true}
          isHeaderGrey={false}
          isMultiSelect={false}
          shouldRerenderOnResize={true}
          loadingComponent={<TableLoader rows={8} />}
          showExpandCollapseAll={false}
          isExpandAll={true}
          isGrouped={true}
          sorting={{
            enabled: true,
          }}
          onSortChange={onSortChange}
          translations={{ noItems: "No Items Found", refine_filter: " " }}
        />
      </div>
      <DeleteModal
        text={
          isSubInterface
            ? "appliances.modal.subinterface-text"
            : "appliances.modal.delete-interface-text"
        }
        subText={isSubInterface ? "" : "appliances.modal.interface-subtext"}
        isSubInterface={isSubInterface}
        onClose={() => setDeleteInterface(null)}
        title="appliances.modal.confirm-delete"
        show={!!deleteInterface}
        id={deleteInterface ?? ""}
        onDelete={(id: string) => {
          deleteSelectedInterface(id);
        }}
      />
      <LANInterfaceDrawer
        {...LAN_INTERFACE_DATA}
        heading={`${isSubInterface ? t("appliances.drawer.lanInterface.sub-interface-heading") : t(LAN_INTERFACE_DATA.heading)} (${selectedData?.name})`}
        openDrawer={openLanDrawer}
        setOpenDrawer={setOpenLanDrawer}
        onSave={(formData) => onSaveLANhandler(formData, isSubInterface)}
        initFormData={initLanFormData}
        isLanSubInterfaceDrawer={isSubInterface}
      />

      <WANInterface
        {...WAN_INTERFACE_DATA}
        heading={`${isSubInterface ? t("appliances.drawer.wanInterface.sub-interface-heading") : t(WAN_INTERFACE_DATA.heading)} (${selectedData?.name})`}
        openDrawer={openWanDrawer}
        setOpenDrawer={setOpenWanDrawer}
        onSave={(formData) => onSaveWANhandler(formData, isSubInterface)}
        initFormData={initWanFormData}
        isWanSubInterfaceDrawer={isSubInterface}
      />

      <MGTDrawer
        {...MGT_DRAWER_DATA}
        heading={
          !isAddDrawer
            ? `${t(MGT_DRAWER_DATA.heading)} (${selectedData?.name})`
            : t(MGT_DRAWER_DATA.heading)
        }
        openDrawer={openMgtDrawer}
        setOpenDrawer={setOpenMgtDrawer}
        onSave={onSaveMGThandler}
        initFormData={initMGTFormData}
      />
    </div>
  );
};

export default ApplianceInterfaces;

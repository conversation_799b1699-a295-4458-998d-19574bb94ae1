import { useEffect, useState } from "react";
import { type DHCPOptionsProps } from "../../../types";
import FormInput from "../../DrawerComponents/FormInput";
import FormSelect, {
  type SelectOption,
} from "../../DrawerComponents/FormSelect";
import { DHCPOptionsPlaceholderMapping } from "@/components/pages/UnifiedLocations/constants";

const DHCPOptions = ({
  options,
  dhcpServerVal,
  setDhcpServerVal,
  dhcpServerData,
  validationError,
}: DHCPOptionsProps) => {
  const [availableOptions, setAvailableOptions] = useState<SelectOption[]>([]);

  const { id, value, selectedOption } = dhcpServerVal;
  const { defaultGatewayError } = validationError;

  useEffect(() => {
    const fliteredOptions = options?.map((item) => ({
      ...item,
      disable: dhcpServerData?.some(
        (filterItems) => filterItems.selectedOption === item.label,
      ),
    }));
    setAvailableOptions(fliteredOptions);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedOption]);

  const handleSelectChange = (value: {
    id: string | number;
    label: string;
  }) => {
    const data = dhcpServerData.map((item) =>
      item.id === id ? { ...item, selectedOption: value.label } : item,
    );
    setDhcpServerVal(data);
  };
  const handleIpAddressChange = (value: string, id: string | number) => {
    const data = dhcpServerData.map((item) =>
      item.id === Number(id) ? { ...item, value: value } : item,
    );
    setDhcpServerVal(data);
  };

  const errorMapping: Record<string, string> = {
    "Default Gateway": defaultGatewayError,
  };

  return (
    <div className="inline-flex gap-rem-80">
      <FormSelect
        options={availableOptions}
        onChange={handleSelectChange}
        value={selectedOption}
        id={id}
      />
      <div className="w-[233px]">
        <FormInput
          key={"id"}
          value={value}
          id={id}
          onChange={handleIpAddressChange}
          placeholder={DHCPOptionsPlaceholderMapping[selectedOption]}
          errorMessage={errorMapping[selectedOption]}
          formValidation={!!errorMapping[selectedOption]}
        />
      </div>
    </div>
  );
};

export default DHCPOptions;

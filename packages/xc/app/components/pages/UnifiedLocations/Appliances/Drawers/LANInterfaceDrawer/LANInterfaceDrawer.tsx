/* eslint-disable @up/unified-platform/max-file-lines */

import { useState, useRef, useEffect } from "react";
import { Drawer, ZToggleSwitch } from "@xc/legacy-components";
import { useTranslation } from "react-i18next";
import { RadioGroup } from "@zs-nimbus/core";
import { REGEX_SUBNET, REGEX_IPV4 } from "@up/std";
import DrawerFooter from "../DrawerComponents/DrawerFooter";
import {
  type LANInterfaceDrawerProps,
  type FormErrors,
  type DhcpServerDataProps,
  type DefaultGatewayValidation,
  type DHCPServerStaticIpError,
  type DHCPRangeError,
  type DhcpServerStaticIpProps,
} from "../../types";
import MTUBytes from "../DrawerComponents/MTUBytes";
import FormInput from "../DrawerComponents/FormInput";
import LocationToggleSwitch from "../../../Locations/LocationToggleSwitch/LocationToggleSwitch";
import DrawerHeader from "../DrawerComponents/DrawerHeader";
import FormTextArea from "../DrawerComponents/FormTextArea";
import {
  drawerFooterConfig,
  highAvailabilityConfig,
} from "./LANInterfaceDrawer.data";
import CustomDNS from "./Components/CustomDNS";
import HighAvailabilityInfo from "./Components/HighAvailabilityInfo";
import DHCPServer from "./Components/DHCPServer";
import { getDataTestId } from "@/utils/utils";

const LANInterfaceDrawer = ({
  heading,
  radioLabel,
  dhcpOptionsLabel,
  staticIpLabel,
  openDrawer,
  setOpenDrawer,
  customDNS,
  customDNSData,
  dhcpServer,
  dhcpServerDropDownOption,
  highAvailability,
  adminStatus,
  lanInterfaceLabels,
  onSave,
  initFormData,
  preferred,
  peerDHCP,
  defaultLeaseTimeInput,
  maxLeaseTimeInput,
  dhcpLeaseLabel,
  dhcpRangeOptions,
  isLanSubInterfaceDrawer,
  vlanIdLabel,
}: LANInterfaceDrawerProps) => {
  const drawerRef = useRef<{ hide: () => void; show: () => void }>();
  const { t } = useTranslation();

  useEffect(() => {
    if (openDrawer) {
      drawerRef?.current?.show();
    }
  }, [openDrawer]);

  const parentMtu = initFormData?.parentMtu;
  const LANInterfaceDrawerContent = () => {
    const [formData, setFormData] = useState(initFormData);
    const [errors, setErrors] = useState<boolean>(false);
    const [isFormChange, setIsFormChange] = useState<boolean>(false);
    const [saveDisabled, setSaveDisabled] = useState<boolean>(false);
    const [formErrors, setFormErrors] = useState<FormErrors>({
      ipError: "",
      dhcpServerError: "",
      defaultGatewayError: "",
      dhcpRangeError: [],
      peerDhcpIpError: "",
      dhcpServerStaticIpError: [],
    });

    const handleToggleState = (id: keyof typeof formData.toggles) => {
      setFormData({
        ...formData,
        toggles: {
          ...formData.toggles,
          [id]: !formData.toggles[id],
        },
      });
    };

    const onTextChange = (value: string, id: string) => {
      setFormData({ ...formData, [id]: value });
      setErrors(false);
    };

    const onHighAvailTextChange = (value: string, id: string) => {
      setFormData({ ...formData, [id]: value });
    };

    const onSaveClick = () => {
      formData.mtu === "" ? setErrors(true) : onSave?.(formData);
    };

    const onCancel = () => {
      drawerRef?.current?.hide();
      setOpenDrawer(false);
    };

    useEffect(() => {
      setIsFormChange(
        JSON.stringify(formData) !== JSON.stringify(initFormData),
      );
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formData]);

    drawerFooterConfig.save.onButtonClick = onSaveClick;
    drawerFooterConfig.save.disabled = !isFormChange || saveDisabled;
    drawerFooterConfig.cancel.onButtonClick = onCancel;

    const getSubnet = (ip: string): string =>
      ip.split(".").slice(0, 3).join(".");
    const validation: Record<string, RegExp> = {
      ipAddress: REGEX_IPV4,
    };

    const validatedefaultGateway = (
      ipAddress: string,
      defaultGateway: string,
    ): string => {
      const ipSubnet = getSubnet(ipAddress);
      const defaultGatewaySubnet = getSubnet(defaultGateway);

      if (ipSubnet !== defaultGatewaySubnet) {
        return t("appliances.drawer.lanSubInterface.ipSubnetError");
      }

      if (!validation.ipAddress?.test(defaultGateway)) {
        return t("appliances.drawer.lanSubInterface.ipAddressError");
      }

      return "";
    };

    function validateStaticIp(
      data: DhcpServerStaticIpProps[],
    ): DHCPServerStaticIpError[] {
      return data.map(({ id, ip }) => {
        let ipError = "";
        const ipSubnet = getSubnet(formData?.ipAddress);
        if (ipSubnet !== getSubnet(ip) && ip !== "") {
          ipError = t("appliances.drawer.lanSubInterface.ipSubnetError");
        }

        if (
          ip &&
          !validation.ipAddress?.test(ip) &&
          !(ipSubnet !== getSubnet(ip))
        ) {
          ipError = t("appliances.drawer.lanSubInterface.ipAddressError");
        }

        return { id, ipError };
      });
    }

    function validateIPs(
      data: Array<{ id: number; ipStart: string; ipEnd: string }>,
    ): DHCPRangeError[] {
      const compareIps = (ip1: string, ip2: string): number => {
        const toNumberArray = (ip: string): number[] =>
          ip.split(".").map(Number);
        const ip1Parts = toNumberArray(ip1);
        const ip2Parts = toNumberArray(ip2);

        for (let i = 0; i < 4; i++) {
          if (ip1Parts[i] < ip2Parts[i]) return -1;
          if (ip1Parts[i] > ip2Parts[i]) return 1;
        }

        return 0;
      };

      const validation: Record<string, RegExp> = {
        ipAddress: REGEX_IPV4,
      };

      return data.map(({ id, ipStart, ipEnd }) => {
        let ipStartError = "";
        let ipEndError = "";
        const ipSubnet = getSubnet(formData?.ipAddress);
        if (ipStart === "") {
          ipStartError = t("appliances.drawer.lanSubInterface.dhcpStartError");
        }
        if (ipEnd === "") {
          ipEndError = t("appliances.drawer.lanSubInterface.dhcpEndError");
        }

        if (compareIps(ipStart, ipEnd) >= 0) {
          ipEndError = t("appliances.drawer.lanSubInterface.dhcpRangeError");
        }
        if (ipSubnet !== getSubnet(ipStart)) {
          ipStartError = t("appliances.drawer.lanSubInterface.ipSubnetError");
        }
        if (ipSubnet !== getSubnet(ipEnd)) {
          ipEndError = t("appliances.drawer.lanSubInterface.ipSubnetError");
        }

        if (
          ipStart &&
          !validation.ipAddress?.test(ipStart) &&
          !(ipSubnet !== getSubnet(ipStart))
        ) {
          ipStartError = t("appliances.drawer.lanSubInterface.ipAddressError");
        }

        if (
          ipEnd &&
          !validation.ipAddress?.test(ipEnd) &&
          !(ipSubnet !== getSubnet(ipEnd))
        ) {
          ipEndError = t("appliances.drawer.lanSubInterface.ipAddressError");
        }

        return { id, ipStartError, ipEndError };
      });
    }

    const lanInterfaceFormValidation = () => {
      const {
        dhcpRangeData,
        dhcpServerData,
        ipAddress,
        toggles,
        peerDhcpIp,
        dhcpServerStaticIp,
      } = formData;
      if (ipAddress) {
        const validation: Record<string, RegExp> = {
          ipAddress: REGEX_SUBNET,
        };

        const errorMessage = !validation.ipAddress?.test(ipAddress)
          ? t("appliances.drawer.shared.cidrError", { withcidr: "with CIDR" })
          : "";
        setFormErrors((prev: FormErrors) => ({
          ...prev,
          ["ipError"]: errorMessage,
        }));
        if (!errorMessage)
          setFormErrors((prev: FormErrors) => ({ ...prev, ["ipError"]: "" }));
      }
      if (toggles.dhcpServer) {
        if (dhcpRangeData?.length) {
          const validationResults = validateIPs(dhcpRangeData);
          setFormErrors((prev: FormErrors) => ({
            ...prev,
            dhcpRangeError: validationResults,
          }));
        }

        if (dhcpServerData?.length) {
          const checkForEmptyValues = (data: DhcpServerDataProps[]) => {
            const output = data.reduce(
              (acc: DefaultGatewayValidation, item: DhcpServerDataProps) => {
                acc[item.selectedOption] = {
                  hasError: item.value === "",
                  value: item?.value,
                };

                return acc;
              },
              {} as Record<string, { hasError: boolean; value: string }>,
            );

            return output;
          };
          const hasDHCPRangeEmpty = checkForEmptyValues(dhcpServerData);
          if (hasDHCPRangeEmpty["Default Gateway"]?.hasError) {
            setFormErrors((prev: FormErrors) => ({
              ...prev,
              defaultGatewayError: t(
                "appliances.drawer.lanSubInterface.defaultGatewayReq",
              ),
            }));
          } else if (hasDHCPRangeEmpty["Default Gateway"]?.value) {
            const validationMessage = validatedefaultGateway(
              ipAddress,
              hasDHCPRangeEmpty["Default Gateway"]?.value,
            );
            setFormErrors((prev: FormErrors) => ({
              ...prev,
              defaultGatewayError: validationMessage,
            }));
          } else {
            setFormErrors((prev: FormErrors) => ({
              ...prev,
              defaultGatewayError: "",
            }));
          }
        }
        if (dhcpServerStaticIp?.length) {
          const validationResults = validateStaticIp(dhcpServerStaticIp);
          setFormErrors((prev: FormErrors) => ({
            ...prev,
            dhcpServerStaticIpError: validationResults,
          }));
        }
        if (peerDhcpIp !== "") {
          const validationMessage = validatedefaultGateway(
            ipAddress,
            peerDhcpIp,
          );
          setFormErrors((prev: FormErrors) => ({
            ...prev,
            peerDhcpIpError: validationMessage,
          }));
        } else {
          setFormErrors((prev: FormErrors) => ({
            ...prev,
            peerDhcpIpError: "",
          }));
        }
      }
    };

    const handleSaveDisable = () => {
      const {
        ipError,
        dhcpServerError,
        defaultGatewayError,
        dhcpRangeError,
        peerDhcpIpError,
        dhcpServerStaticIpError,
      } = formErrors;

      function hasDHCPError(data: DHCPRangeError[]): boolean {
        return data.some(
          (item) => item.ipStartError !== "" || item.ipEndError !== "",
        );
      }

      function hasdhcpServerStaticIpError(
        data: DHCPServerStaticIpError[],
      ): boolean {
        return data.some((item) => item.ipError !== "");
      }

      const hasDHCPErrors = hasDHCPError(dhcpRangeError);
      const hasDHCPStaticServerIPError = hasdhcpServerStaticIpError(
        dhcpServerStaticIpError,
      );

      const hasError = [
        ipError,
        dhcpServerError,
        defaultGatewayError,
        peerDhcpIpError,
      ].some((value) => value !== "");

      setSaveDisabled(hasError || hasDHCPErrors || hasDHCPStaticServerIPError);
    };

    useEffect(() => {
      lanInterfaceFormValidation();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formData]);

    useEffect(() => {
      handleSaveDisable();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formErrors]);

    return (
      <div className="flex flex-col relative">
        <div className="bg-semantic-content-inverted-base-primary h-[calc(100vh-65px)] overflow-auto">
          <DrawerHeader
            heading={heading}
            onCancel={onCancel}
            dataTestId={getDataTestId(
              "lan-interface-drawer-close-icon",
              "unified-locations",
            )}
          />
          <div className="gap-rem-160 p-rem-160 flex flex-col">
            <div className="flex justify-between">
              <div>
                <RadioGroup
                  key="adminStatus"
                  groupName="adminStatus"
                  groupLabel={t(radioLabel)}
                  onChange={(value: string) => {
                    setFormData({ ...formData, adminStatus: value });
                  }}
                  defaultValue={formData.adminStatus ?? ""}
                  itemProps={adminStatus.options?.map((item) => ({
                    value: item.value,
                    labelText: t(item.label),
                  }))}
                  orientation="horizontal"
                />
              </div>
              <MTUBytes
                header={t(lanInterfaceLabels.mtuBytes.label)}
                value={formData.mtu}
                placeholder="Enter"
                id={lanInterfaceLabels.mtuBytes.id}
                onChange={onTextChange}
                isError={errors}
                parentMtu={parentMtu}
              />
            </div>
            {isLanSubInterfaceDrawer && (
              <FormInput
                header={t(vlanIdLabel)}
                value={formData?.vlanId ?? ""}
                id="vlanId"
                placeholder=""
                onChange={(value) =>
                  setFormData({ ...formData, vlanId: value })
                }
              />
            )}
            <FormInput
              header={t(lanInterfaceLabels.ipAddress.label)}
              value={formData.ipAddress}
              id={lanInterfaceLabels.ipAddress.id}
              placeholder={lanInterfaceLabels.ipAddress.placeholder}
              suffix={lanInterfaceLabels.ipAddress.suffix}
              errorMessage={formErrors.ipError}
              formValidation={!!formErrors.ipError}
              onChange={(value: string) => {
                onTextChange(value, lanInterfaceLabels.ipAddress.id);
              }}
            />
            <FormTextArea
              header={t(lanInterfaceLabels.description.label)}
              value={formData.description ?? ""}
              id={lanInterfaceLabels.description.id}
              placeholder=""
              onChange={onTextChange}
              required={lanInterfaceLabels.description.required}
            />
            <div className="border-t border-semantic-border-interactive-primary-disabled">
              <div className="pt-rem-160">
                <CustomDNS
                  customDNS={[
                    {
                      ...customDNS,
                      handleToggle: () => handleToggleState("customDNS"),
                      isEnabled: formData.toggles.customDNS,
                    },
                  ]}
                  config={customDNSData}
                  customDNSVal={formData.customDNSInputVal}
                  setCustomDNSVal={(value) =>
                    setFormData({ ...formData, customDNSInputVal: value })
                  }
                />
              </div>
            </div>
            <div className="border-t border-semantic-border-interactive-primary-disabled">
              <div className="pt-rem-160">
                <LocationToggleSwitch
                  locationSwitch={[
                    {
                      ...dhcpServer,
                      handleToggle: () => handleToggleState("dhcpServer"),
                      isEnabled: formData.toggles.dhcpServer,
                    },
                  ]}
                />
                {formData.toggles.dhcpServer && (
                  <DHCPServer
                    dhcpServerDropDownOption={dhcpServerDropDownOption}
                    dhcpServerVal={formData.dhcpServerData}
                    setDhcpServerVal={(value) =>
                      setFormData({ ...formData, dhcpServerData: value })
                    }
                    dhcpServerStaticIpData={formData.dhcpServerStaticIp}
                    setDhcpServerStaticIpData={(value) =>
                      setFormData({ ...formData, dhcpServerStaticIp: value })
                    }
                    peerDhcpVal={formData.peerDhcpIp}
                    setPeerDhcpVal={(value) =>
                      setFormData({ ...formData, peerDhcpIp: value })
                    }
                    dhcpOptionsLabel={dhcpOptionsLabel}
                    staticIpLabel={staticIpLabel}
                    dhcpRangeDataVal={formData.dhcpRangeData}
                    setDhcpRangeDataVal={(value) =>
                      setFormData({ ...formData, dhcpRangeData: value })
                    }
                    dhcpRangeOptions={dhcpRangeOptions}
                    peerDHCP={peerDHCP}
                    defaultLeaseTimeInput={defaultLeaseTimeInput}
                    maxLeaseTimeInput={maxLeaseTimeInput}
                    defaultLeaseTime={formData.defaultLeaseTime}
                    maxLeaseTime={formData.maxLeaseTime}
                    dhcpLeaseLabel={dhcpLeaseLabel}
                    setDefaultLeaseTime={(value) =>
                      setFormData({ ...formData, defaultLeaseTime: value })
                    }
                    setMaxLeaseTime={(value) =>
                      setFormData({ ...formData, maxLeaseTime: value })
                    }
                    validationError={formErrors}
                  />
                )}
              </div>
            </div>
            <div className="border-t border-semantic-border-interactive-primary-disabled bottom-rem-200">
              <div className="pt-rem-160">
                <LocationToggleSwitch
                  locationSwitch={[
                    {
                      ...highAvailability,
                      handleToggle: () => handleToggleState("highAvailability"),
                      isEnabled: formData.toggles.highAvailability,
                    },
                  ]}
                />
              </div>
              {formData.toggles?.highAvailability && (
                <>
                  <div className="flex flex-col gap-rem-160 mt-rem-160 pb-rem-200">
                    <HighAvailabilityInfo
                      config={highAvailabilityConfig}
                      formData={{
                        id: formData.id,
                        passphrase: formData.passphrase,
                        haVirtualIp: formData.haVirtualIp,
                      }}
                      onChange={onHighAvailTextChange}
                    />
                  </div>

                  <div className="inline-flex">
                    <ZToggleSwitch
                      id={preferred.id}
                      checked={formData.toggles.preferred}
                      small
                      type="secondary"
                      onChange={(_value: boolean) => {
                        handleToggleState("preferred");
                      }}
                    />

                    <div className="flex flex-col">
                      <div
                        className="typography-paragraph1-strong text-semantic-content-base-primary pl-rem-80"
                        aria-label={t(preferred.desc)}
                      >
                        {t(preferred.desc)}
                      </div>
                      <div
                        className="typography-paragraph2 text-semantic-content-base-tertiary pl-rem-80 pt-1"
                        aria-label={t(preferred?.metaText ?? "")}
                      >
                        {t(preferred?.metaText ?? "")}
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
        <DrawerFooter drawerFooterConfig={drawerFooterConfig} />
      </div>
    );
  };

  return (
    <Drawer
      ref={drawerRef}
      onClose={() => drawerRef?.current?.show()}
      contentRenderer={() => LANInterfaceDrawerContent()}
      backdrop={true}
      customBackdrop={true}
      width={{
        max: "558px",
        min: "558px",
        default: "fit",
      }}
      id={"lan-interface"}
    />
  );
};

export default LANInterfaceDrawer;

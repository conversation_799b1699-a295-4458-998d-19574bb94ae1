import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button } from "@zs-nimbus/core";
import { faTrash } from "@fortawesome/pro-regular-svg-icons";
import { type StaticIpsOptionsProps } from "../../../types";
import FormInput from "../../DrawerComponents/FormInput";

const StaticIpsOptions = ({
  dhcpServerStaticIpData,
  setDhcpServerStaticIpData,
  deleteStaticIpDataHandler,
  dhcpServerData,
  validationError,
}: StaticIpsOptionsProps) => {
  const { id, addressType, ip } = dhcpServerStaticIpData;
  const handleChangeHandler = (value: string, id: string) => {
    const data = dhcpServerData.map((item) =>
      item.id === dhcpServerStaticIpData.id ? { ...item, [id]: value } : item,
    );
    setDhcpServerStaticIpData(data);
  };

  return (
    <div className="inline-flex gap-rem-80">
      <div className="w-[233px]">
        <FormInput
          key={id}
          value={addressType}
          id={"addressType"}
          onChange={handleChangeHandler}
          placeholder="MAC address"
        />
      </div>
      <div className="w-[233px]">
        <FormInput
          key={id}
          value={ip}
          id={"ip"}
          onChange={handleChangeHandler}
          placeholder="x.x.x.x"
          errorMessage={validationError}
          formValidation={!!validationError}
        />
      </div>
      <div className="flex items-center">
        {dhcpServerData?.length > 1 && (
          <Button
            type="button"
            onClick={() => deleteStaticIpDataHandler?.(id.toString())}
            prefixIcon={<FontAwesomeIcon icon={faTrash} />}
            variant="tertiary"
            className="text-semantic-content-interactive-primary-default"
          />
        )}
      </div>
    </div>
  );
};

export default StaticIpsOptions;

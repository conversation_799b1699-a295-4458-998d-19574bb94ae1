import { InlineFeedback } from "@zs-nimbus/core";
import { useTranslation } from "react-i18next";

type Props = {
  type?: string;
  header?: string;
  id: string | number;
  value: string | number;
  disabled?: boolean;
  direction?: "vertical" | "horizontal";
  onChange: (value: string, id: string) => void;
  placeholder?: string;
  readOnly?: boolean;
  required?: boolean;
  isKeyValuePair?: boolean;
  formValidation?: boolean;
  errorMessage?: string;
  suffix?: string;
  errorClassName?: string;
};

export default function FormInput({
  type = "text",
  header,
  id,
  value,
  disabled,
  placeholder = "Enter",
  readOnly,
  required,
  direction = "vertical",
  onChange,
  formValidation,
  errorMessage,
  suffix,
  errorClassName = "",
}: Props) {
  const { t } = useTranslation();
  const emptyInputValues = [null, "", "0.0.0.0", "0.0.0.0/0"];
  const isEmptyInputValue = emptyInputValues.includes(
    value === null ? null : value.toString(),
  );
  const inputValue = isEmptyInputValue ? "" : value;

  return (
    <div
      className={`flex ${direction === "horizontal" && readOnly ? "" : "flex-col"} gap-rem-80`}
      data-testid={`form-container-${id}`}
    >
      {header && (
        <div
          className={` ${direction === "horizontal" && "w-[128px]"} ${formValidation ? "text-semantic-content-status-danger-secondary" : "text-semantic-content-base-secondary"} typography-paragraph1 truncate`}
        >
          {t(header)}{" "}
          {required === false && (
            <span className="typography-paragraph2 text-semantic-content-base-tertiary">
              ({t("OPTIONAL")})
            </span>
          )}
          {suffix && (
            <span className="typography-paragraph2 text-semantic-content-base-tertiary">
              ({t(suffix)})
            </span>
          )}
        </div>
      )}
      <input
        type={type}
        value={inputValue}
        disabled={disabled ?? readOnly}
        className={`border ${formValidation ? "border-semantic-border-interactive-danger-default" : "border-semantic-border-base-primary"} bg-semantic-surface-fields-default rounded-40 px-rem-80 py-[0.4rem] ${disabled ? "text-semantic-content-base-secondary" : "text-semantic-content-base-primary"} ${readOnly && "!text-semantic-content-interactive-primary-disabledOnSecondary !bg-semantic-surface-base-secondary !border-semantic-border-interactive-secondary-disabled"} typography-paragraph1 typography-paragraph1 placeholder:text-semantic-content-base-tertiary`}
        onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
          onChange(
            event.target.value,
            typeof id === "string" ? id : id.toString(),
          )
        }
        placeholder={placeholder}
        data-testid={`form-input-${id}`}
      />
      {errorMessage && (
        <InlineFeedback size="sm" type="danger" className={errorClassName}>
          {errorMessage}
        </InlineFeedback>
      )}
    </div>
  );
}

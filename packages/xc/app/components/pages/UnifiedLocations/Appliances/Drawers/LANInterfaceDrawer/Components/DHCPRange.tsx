import { type DHCPRangeOptions, type DHCPRangeProps } from "../../../types";
import FormInput from "../../DrawerComponents/FormInput";

type Props = {
  dhcpRangeDataVal: DHCPRangeProps;
  setDhcpRangeDataVal: (value: DHCPRangeProps[]) => void;
  dhcpRangeData: DHCPRangeProps[];
  dhcpRangeOptions: DHCPRangeOptions;
  validationError: { ipStartError: string; ipEndError: string };
};

const DHCPRange = ({
  dhcpRangeDataVal,
  setDhcpRangeDataVal,
  dhcpRangeData,
  dhcpRangeOptions,
  validationError,
}: Props) => {
  const { ipStart, ipEnd } = dhcpRangeDataVal;
  const { ipStartError, ipEndError } = validationError;

  const handleChangeHandler = (value: string, id: string) => {
    const data = dhcpRangeData.map((item) =>
      item.id === dhcpRangeDataVal.id ? { ...item, [id]: value } : item,
    );
    setDhcpRangeDataVal(data);
  };

  return (
    <div className="inline-flex gap-rem-80">
      <div className="w-[225px]">
        <FormInput
          header={dhcpRangeOptions.ipStart.label}
          value={ipStart}
          id={dhcpRangeOptions.ipStart.id}
          onChange={handleChangeHandler}
          placeholder="x.x.x.x"
          errorMessage={ipStartError}
          formValidation={!!ipStartError}
        />
      </div>
      <span className="flex text-semantic-content-immutable-disabled items-center mt-rem-200">
        -
      </span>
      <div className="w-[225px]">
        <FormInput
          header={dhcpRangeOptions.ipEnd.label}
          value={ipEnd}
          id={dhcpRangeOptions.ipEnd.id}
          onChange={handleChangeHandler}
          placeholder="x.x.x.x"
          errorMessage={ipEndError}
          formValidation={!!ipEndError}
        />
      </div>
    </div>
  );
};

export default DHCPRange;

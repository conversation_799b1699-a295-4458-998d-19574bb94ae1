import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@zs-nimbus/core";
import { faPlus, faTrash } from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState } from "react";
import {
  type DHCPRangeProps,
  type DhcpServerDataProps,
  type DHCPServerProps,
  type DhcpServerStaticIpProps,
} from "../../../types";
import FormInput from "../../DrawerComponents/FormInput";
import DHCPOptions from "./DHCPOptions";
import StaticIpsOptions from "./StaticIpsOptions";
import DHCPRange from "./DHCPRange";
import { LEASETIME } from "@/components/pages/UnifiedLocations/constants";

const DHCPServer = ({
  dhcpServerDropDownOption,
  dhcpOptionsLabel,
  staticIpLabel,
  dhcpServerVal,
  setDhcpServerVal,
  dhcpServerStaticIpData,
  setDhcpServerStaticIpData,
  peerDhcpVal,
  dhcpRangeDataVal,
  setDhcpRangeDataVal,
  setPeerDhcpVal,
  peerDHCP,
  defaultLeaseTimeInput,
  maxLeaseTimeInput,
  defaultLeaseTime,
  maxLeaseTime,
  dhcpLeaseLabel,
  setMaxLeaseTime,
  setDefaultLeaseTime,
  dhcpRangeOptions,
  validationError,
}: DHCPServerProps) => {
  const { t } = useTranslation();
  const [errors, setErrors] = useState<Record<string, string>>({});
  const deleteHandler = (id: string) => {
    const newData = dhcpServerVal.filter(
      (item: DhcpServerDataProps) => item?.id != id,
    );
    setDhcpServerVal(newData);
  };

  const dhcpRangeDataDeleteHandler = (id: string) => {
    const newData = dhcpRangeDataVal.filter(
      (item: DHCPRangeProps) => item.id != Number(id),
    );
    setDhcpRangeDataVal(newData);
  };

  const deleteStaticIpDataHandler = (id: string) => {
    const newData = dhcpServerStaticIpData.filter(
      (item: DhcpServerStaticIpProps) => item?.id != id,
    );

    setDhcpServerStaticIpData(newData);
  };

  const handlePeerDhcpChange = (value: string) => {
    setPeerDhcpVal(value);
  };

  const onAddStaticIPOption = () =>
    setDhcpServerStaticIpData([
      ...dhcpServerStaticIpData,
      {
        id:
          Number(
            dhcpServerStaticIpData.length > 0
              ? dhcpServerStaticIpData[dhcpServerStaticIpData.length - 1].id
              : 0,
          ) + 1,
        ip: "",
        addressType: "",
      },
    ]);

  const onAddDHCPOption = () =>
    setDhcpServerVal([
      ...dhcpServerVal,
      {
        id:
          Number(
            dhcpServerVal.length > 0
              ? dhcpServerVal[dhcpServerVal.length - 1].id
              : 0,
          ) + 1,
        value: "",
        selectedOption: "",
      },
    ]);

  const onAddRangeOption = () =>
    setDhcpRangeDataVal([
      ...dhcpRangeDataVal,
      {
        id:
          Number(
            dhcpRangeDataVal.length > 0
              ? dhcpRangeDataVal[dhcpRangeDataVal.length - 1].id
              : 0,
          ) + 1,
        ipStart: "",
        ipEnd: "",
      },
    ]);
  const validateInput = (id: string, value: string) => {
    const getVal = Number(value);
    const limits: Record<string, number> = {
      defaultLeaseTime: LEASETIME.MAXLEASE,
      maxLeaseTime: LEASETIME.MAXLEASE,
    };

    const errorMessage =
      id == "maxLeaseTime"
        ? limits[id] && getVal > limits.maxLeaseTime
          ? t("appliances.drawer.lanInterface.maxLeaseTime-error", {
              limit: limits[id],
            })
          : ""
        : limits[id] && getVal > limits[id]
          ? t("appliances.drawer.lanInterface.leaseTime-error")
          : "";

    setErrors((prev) => ({ ...prev, [id]: errorMessage }));
  };

  return (
    <div className="flex flex-col gap-rem-160 mt-rem-160">
      <div className="flex flex-col gap-rem-80">
        {dhcpRangeDataVal?.map(({ id }, index) => {
          const errorEntry = validationError?.dhcpRangeError?.find(
            (error) => error.id === id,
          ) || { ipStartError: "", ipEndError: "" };

          return (
            <div className="inline-flex gap-rem-80" key={index}>
              <DHCPRange
                key={id}
                dhcpRangeDataVal={dhcpRangeDataVal[index]}
                setDhcpRangeDataVal={setDhcpRangeDataVal}
                dhcpRangeData={dhcpRangeDataVal}
                dhcpRangeOptions={dhcpRangeOptions}
                validationError={{
                  ipStartError: errorEntry.ipStartError,
                  ipEndError: errorEntry.ipEndError,
                }}
              />
              <div className="flex items-center mt-rem-280">
                <div className="flex items-center">
                  {index > 0 && (
                    <Button
                      type="button"
                      onClick={() =>
                        dhcpRangeDataDeleteHandler?.(id.toString())
                      }
                      prefixIcon={<FontAwesomeIcon icon={faTrash} />}
                      variant="tertiary"
                      className="text-semantic-content-interactive-primary-default"
                    />
                  )}
                </div>
              </div>
            </div>
          );
        })}
        {dhcpRangeDataVal?.length < 4 && (
          <div className="flex justify-between">
            <Button
              variant="tertiary"
              id={"Add DHCPRange"}
              onClick={onAddRangeOption}
              prefixIcon={<FontAwesomeIcon icon={faPlus} />}
              className="mt-rem-80"
            >
              {t("appliances.drawer.lanInterface.add-range")}
            </Button>
          </div>
        )}
      </div>

      <div className="flex flex-col gap-rem-160 mt-rem-160">
        <div className="flex flex-col gap-rem-80">
          <div className="typography-header5">{t(dhcpOptionsLabel)}</div>

          {dhcpServerVal?.map(({ id }, index) => (
            <div className="inline-flex gap-rem-80" key={index}>
              <DHCPOptions
                key={id}
                options={dhcpServerDropDownOption}
                dhcpServerVal={dhcpServerVal[index]}
                setDhcpServerVal={setDhcpServerVal}
                dhcpServerData={dhcpServerVal}
                validationError={validationError}
              />
              <div className="flex items-center">
                {index > 0 && (
                  <Button
                    type="button"
                    onClick={() => deleteHandler?.(id.toString())}
                    prefixIcon={<FontAwesomeIcon icon={faTrash} />}
                    variant="tertiary"
                    className="text-semantic-content-interactive-primary-default"
                  />
                )}
              </div>
            </div>
          ))}
          {dhcpServerVal?.length !== dhcpServerDropDownOption.length && (
            <div className="flex justify-between">
              <Button
                variant="tertiary"
                id={"Add DHCPOptions"}
                onClick={onAddDHCPOption}
                prefixIcon={<FontAwesomeIcon icon={faPlus} />}
                className="typography-paragraph1 text-semantic-content-interactive-primary-default mt-rem-80"
              >
                {t("appliances.drawer.lanInterface.add-option")}
              </Button>
            </div>
          )}
        </div>
      </div>

      <div className="flex flex-col gap-rem-80">
        <div className="typography-header5 mb-rem-80">{t(dhcpLeaseLabel)}</div>
        <div className="inline-flex gap-rem-80">
          <div className="w-[233px]">
            <FormInput
              header={defaultLeaseTimeInput.label}
              value={defaultLeaseTime}
              id={defaultLeaseTimeInput.id}
              suffix={defaultLeaseTimeInput.suffix}
              errorMessage={errors.defaultLeaseTime}
              formValidation={!!errors.defaultLeaseTime}
              onChange={(value: string) => {
                setDefaultLeaseTime(value);
                validateInput("defaultLeaseTime", value);
              }}
            />
          </div>

          <div className="w-[233px]">
            <FormInput
              header={maxLeaseTimeInput.label}
              value={maxLeaseTime}
              id={maxLeaseTimeInput.id}
              suffix={maxLeaseTimeInput.suffix}
              errorMessage={errors.maxLeaseTime}
              formValidation={!!errors.maxLeaseTime}
              onChange={(value: string) => {
                setMaxLeaseTime(value);
                validateInput("maxLeaseTime", value);
              }}
            />
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-rem-80">
        <div className="typography-header5 mb-rem-80">{t(staticIpLabel)}</div>

        {dhcpServerStaticIpData?.map(({ id }, index) => {
          const errorEntry = validationError?.dhcpServerStaticIpError?.find(
            (error) => error.id === id,
          ) || { ipError: "" };

          return (
            <StaticIpsOptions
              key={id}
              dhcpServerStaticIpData={dhcpServerStaticIpData[index]}
              setDhcpServerStaticIpData={setDhcpServerStaticIpData}
              deleteStaticIpDataHandler={deleteStaticIpDataHandler}
              dhcpServerData={dhcpServerStaticIpData}
              validationError={errorEntry?.ipError}
            />
          );
        })}
        {dhcpServerStaticIpData?.length < 3 && (
          <div className="flex justify-between">
            <Button
              variant="tertiary"
              id={"edit"}
              onClick={onAddStaticIPOption}
              prefixIcon={<FontAwesomeIcon icon={faPlus} />}
              className="typography-paragraph1 text-semantic-content-interactive-primary-default mt-rem-80"
            >
              {t("appliances.drawer.lanInterface.add-static-ip")}
            </Button>
          </div>
        )}
      </div>

      <div className="flex flex-col w-[233px] gap-rem-160">
        <div className="typography-header5">{t(peerDHCP.label)}</div>
        <FormInput
          value={peerDhcpVal}
          id={peerDHCP.id}
          onChange={handlePeerDhcpChange}
          placeholder="x.x.x.x"
          errorMessage={validationError.peerDhcpIpError}
          formValidation={!!validationError.peerDhcpIpError}
        />
      </div>
    </div>
  );
};

export default DHCPServer;

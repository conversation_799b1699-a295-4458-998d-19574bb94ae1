import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { faPen } from "@fortawesome/pro-regular-svg-icons";
import { Button } from "@zs-nimbus/core";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { convertFromMinutes } from "../utils/utils";
import CardWrapper from "../Components/CardWrapper/CardWrapper";
import {
  type OptionItem,
  type LocationConnectionOptionsProps,
  type OptionListProps,
  type UnitProps,
} from "./types";
import ConnectionOptionsDrawer from "./Drawers/ConnectionOptionsDrawer/ConnectionOptionsDrawer";
import { CONNECTION_OPTIONS_DRAWER_DATA } from "./Drawers/ConnectionOptionsDrawer/ConnectionOptionsDrawer.data";
import { type ConnectionOptionsDrawerFormDataProps } from "./Drawers/types";
import { locationConnectionOptionsPayloadTransformer } from "./LocationDetailTransformer";
import { getDataTestId } from "@/utils/utils";

export const ConnectionLists = ({ label, value, itemId }: OptionItem) => {
  const { t } = useTranslation();
  const isMbpsLabel =
    label === "locations.connectionOptions.download" ||
    label === "locations.connectionOptions.upload";

  return (
    <div className="flex flex-row gap-rem-240 mb-rem-80" data-testid={itemId}>
      <div className="typography-paragraph2 text-semantic-content-base-tertiary break-words w-rem-1280">
        {t(label)}
      </div>
      <div className="typography-paragraph2 text-semantic-content-base-primary">
        {isMbpsLabel ? Number(value) / 1000 || 0 : t(value)}
      </div>
    </div>
  );
};

const LocationConnectionOptions = ({
  pageTitle,
  handleEditConnection,
  optionList,
  connectionOptionData,
  onSave,
  locationDetailFromServer,
  readOnly,
}: LocationConnectionOptionsProps) => {
  const [openDrawer, setOpenDrawer] = useState(false);

  const handleEditConnectionDrawer = () => {
    setOpenDrawer(true);
    handleEditConnection();
    setInitFormData(drawerData);
  };
  const { t } = useTranslation();
  const drawerData = {
    authentication: connectionOptionData?.authRequired ?? false,
    basicAuthentication: connectionOptionData?.basicAuthEnabled ?? false,
    digestAuthentication: connectionOptionData?.digestAuthEnabled ?? false,
    kerberosAuthentication: connectionOptionData?.kerberosAuth ?? false,
    ipSurrogate: connectionOptionData?.surrogateIp ?? false,
    jwtAuthentication: connectionOptionData?.jwtAuthentication ?? false,
    unmapUsers: connectionOptionData?.idleTimeInMinutes
      ? convertFromMinutes(
          Number(connectionOptionData?.idleTimeInMinutes),
          connectionOptionData?.idleTimeDisplayUnit?.toLowerCase() as UnitProps,
        )?.toString()
      : "8",
    unmapTime: connectionOptionData?.idleTimeDisplayUnit
      ? connectionOptionData?.idleTimeDisplayUnit
      : "",
    useIpSurrogate:
      connectionOptionData?.enableSurrogateForKnownBrowsers ?? false,
    revalidation: connectionOptionData?.refreshTimeSurrogacy
      ? convertFromMinutes(
          Number(connectionOptionData?.refreshTimeSurrogacy),
          connectionOptionData?.refreshTimeSurrogacyDisplayUnit?.toLowerCase() as UnitProps,
        )?.toString()
      : "4",
    revalidationTime: connectionOptionData?.refreshTimeSurrogacyDisplayUnit
      ? connectionOptionData?.refreshTimeSurrogacyDisplayUnit
      : "",
    cookiesAndProxy: connectionOptionData?.cookiesAndProxy
      ? "Cookie and Proxy"
      : "Cookie",
    iotDiscovery: connectionOptionData?.iotDiscoveryEnabled ?? false,
    iotEnforcePolicySet: connectionOptionData?.iotEnforcePolicySet ?? false,
    firewallControl: connectionOptionData?.enableFirewall ?? false,
    ipsControl: connectionOptionData?.enableIps ?? false,
    xffForwarding: connectionOptionData?.xffEnabled ?? false,
    bandwidthControl: connectionOptionData?.bandwidthControl ?? false,
    downloadSpeed: connectionOptionData?.downloadBandwidth
      ? (Number(connectionOptionData?.downloadBandwidth) / 1000)?.toString()
      : "",
    uploadSpeed: connectionOptionData?.uploadBandwidth
      ? (Number(connectionOptionData?.uploadBandwidth) / 1000)?.toString()
      : "",
    cautionWarning: connectionOptionData?.cautionEnabled ?? false,
    aupWarning: connectionOptionData?.aupEnabled ?? false,
    aupFrequency: connectionOptionData?.aupTimeoutInDays
      ? connectionOptionData?.aupTimeoutInDays?.toString()
      : "30",
    internetAccess: connectionOptionData?.aupBlockInternet ?? false,
    sslInspection: connectionOptionData?.aupForceSslInspection ?? false,
  };

  const [initFormData, setInitFormData] =
    useState<ConnectionOptionsDrawerFormDataProps>(drawerData);

  return (
    <CardWrapper className="p-4  w-[340px]">
      <div data-testid="unified-locations-connection-options">
        <div className="w-[308px] max-w-[308px] ">
          <div className="inline-flex justify-start items-start gap-[130px] mb-rem-240">
            <div className="grow flex flex-col justify-start items-start gap-1">
              <div
                className="typography-header5 text-semantic-content-base-primary max-w-[382px] break-all "
                aria-label={t(pageTitle)}
              >
                {t(pageTitle)}
              </div>
            </div>
            <div className="flex justify-start items-center gap-2">
              <div className="w-5 h-5 justify-center items-center flex">
                <Button
                  variant="tertiary"
                  onClick={handleEditConnectionDrawer}
                  id={"location-connection-options-edit-button"}
                  data-testid={"location-connection-options-edit-button"}
                  disabled={readOnly}
                >
                  <FontAwesomeIcon icon={faPen} />
                </Button>
              </div>
            </div>
          </div>
        </div>
        <div
          className="w-[308px] max-w-[308px]"
          data-testid="locations-connection-options"
        >
          <div className="flex flex-col gap-rem-160">
            {optionList.map((itemList: OptionListProps, listIndex: number) => (
              <div
                className="flex flex-col gap-rem-10"
                data-testid={getDataTestId(
                  listIndex,
                  "location-connection-options-details",
                )}
                key={listIndex}
              >
                {itemList.list.map((item: OptionItem, itemIndex) => (
                  <React.Fragment key={itemIndex}>
                    <ConnectionLists
                      label={item.label}
                      value={item.value}
                      itemId={getDataTestId(
                        `${listIndex}-${itemIndex}`,
                        "location-connection-options",
                      )}
                    />
                  </React.Fragment>
                ))}
              </div>
            ))}
          </div>

          {drawerData && (
            <ConnectionOptionsDrawer
              {...CONNECTION_OPTIONS_DRAWER_DATA}
              initFormData={initFormData}
              openDrawer={openDrawer}
              setOpenDrawer={setOpenDrawer}
              onSave={(formData: ConnectionOptionsDrawerFormDataProps) => {
                locationDetailFromServer &&
                  onSave?.(
                    locationConnectionOptionsPayloadTransformer(
                      locationDetailFromServer,
                      formData,
                    ),
                  );
                setInitFormData(formData);
              }}
            />
          )}
        </div>
      </div>
    </CardWrapper>
  );
};

export default LocationConnectionOptions;

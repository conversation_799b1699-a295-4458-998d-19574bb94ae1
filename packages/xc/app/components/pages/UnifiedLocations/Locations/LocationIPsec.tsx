/* eslint-disable @up/unified-platform/max-file-lines */
import { useTranslation } from "react-i18next";
import { ZDataTable } from "@xc/legacy-components";
import { useState } from "react";
import useS<PERSON> from "swr";
import { faPen } from "@fortawesome/pro-regular-svg-icons";
import CardWrapper from "../Components/CardWrapper/CardWrapper";
import {
  type LocationIPsecProps,
  type GRETableRowItemProps,
  type GREOutputData,
  type IPGreList,
  type IdLabelProps,
  type ProxyPortResponse,
  type IPGreTunnelResponse,
  type AllIpAddressesResponse,
  type AllVirtualZensResponse,
  type ALLVpnCredentialResponse,
  type VPNCredentalProps,
  type AllVirtualZenClustersResponse,
} from "./types";
import LocationHeader from "./LocationHeader";
import EditLocationIPsecDrawer from "./Drawers/EditLocationIPsecDrawer/EditLocationIPsecDrawer";
import { EDIT_LOCATION_IPSEC_DRAWER_DATA } from "./Drawers/EditLocationIPsecDrawer/EditLocationIPsecDrawer.data";
import { UnifiedLocationQueries } from "./query";
import { type EditLocationIPsecDrawerFormDataProps } from "./Drawers/types";
import { locationIPsecPayloadTransformer } from "./LocationDetailTransformer";
import { API_ENDPOINTS, postReq } from "@/utils/apiHelper";
import { WithStates } from "@/hoc/WithStates";
import TableLoader from "@/components/TableLoader/TableLoader";
import { getDataTestId } from "@/utils/utils";

export const IPsecLists = ({
  label,
  value,
  itemId,
}: {
  label: string;
  value: string;
  itemId: number;
}) => {
  const { t } = useTranslation();

  return (
    <div
      className="flex mb-rem-80 gap-rem-240"
      data-testid={getDataTestId(itemId, "unified-locations-ipsec-gre")}
    >
      <div className="typography-paragraph2 text-semantic-content-base-tertiary min-w-rem-1280 w-rem-1280 break-all">
        {t(label)}
      </div>
      <div className="typography-paragraph2 text-semantic-content-base-primary min-w-rem-320 max-w-[600px] break-all">
        {value}
      </div>
    </div>
  );
};

export const GREMapping = ({
  title,
  meta,
}: {
  title: string;
  meta: string | null;
}) => {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col">
      <span className="text-semantic-content-base-primary">{t(title)}</span>
      <span className="text-[10px] text-semantic-content-base-tertiary">
        {t(meta ?? "---")}
      </span>
    </div>
  );
};

const LocationIPsec = ({
  pageTitle,
  IPsecList,
  columnData,
  apiData,
  onSave,
  locationDetailFromServer,
  readOnly,
}: LocationIPsecProps) => {
  const { t } = useTranslation();
  const ID = "location-ipsec";
  const [openEditLocationIPsecDrawer, setOpenEditLocationIPsecDrawer] =
    useState(false);
  const handleEditIPsec = () => {
    setOpenEditLocationIPsecDrawer(true);
  };
  const [ips] = useState(IPsecList[0].value.split(","));
  const [ipGreList, setIpGreList] = useState<GREOutputData[] | undefined[]>([]);
  const [proxyPorts, setProxyPorts] = useState<IdLabelProps[]>(
    apiData?.ports?.map((port) => ({
      id: port.toString(),
      label: port.toString(),
    })) ?? [],
  );
  const [ipGreTunnel, setIpGreTunned] = useState<IdLabelProps[]>([]);
  const [ipAddress, setIpAddress] = useState<IdLabelProps[]>(
    apiData?.ipAddresses?.map((ip) => ({ id: ip, label: ip })) ?? [],
  );
  const [allvpnCred, setVpnCred] = useState<VPNCredentalProps[]>(
    Array.isArray(apiData?.vpnCredentials)
      ? apiData?.vpnCredentials.map((item) => {
          item.label = item.name
            ? item.name
            : item.type == "IP"
              ? item.ipAddress
              : item.fqdn;

          return {
            ...item,
          };
        })
      : [],
  );
  const [allVirtualZens, setAllVirtualZens] = useState<IdLabelProps[]>(
    apiData?.virtualZens?.map((item) => ({
      id: item?.id?.toString(),
      label: item?.name,
    })) ?? [],
  );

  const [allVirtualZenClusters, setAllVirtualZenClusters] = useState<
    IdLabelProps[]
  >(
    apiData?.virtualZenClusters?.map(({ id, name }) => ({
      id: id.toString(),
      label: name,
    })) ?? [],
  );
  const updateDrawerData = {
    ...EDIT_LOCATION_IPSEC_DRAWER_DATA,
    proxyPorts: {
      ...EDIT_LOCATION_IPSEC_DRAWER_DATA.proxyPorts,
      options: proxyPorts,
    },
    staticIPAddresses: {
      ...EDIT_LOCATION_IPSEC_DRAWER_DATA.staticIPAddresses,
      options: !ipAddress
        ? Array.from(
            new Map(
              [...ipGreTunnel, ...ipAddress].map((item) => [item.label, item]),
            ).values(),
          )
        : Array.from(
            new Map([...ipAddress].map((item) => [item.label, item])).values(),
          ),
    },
    credentialsVPN: {
      ...EDIT_LOCATION_IPSEC_DRAWER_DATA.credentialsVPN,
      options: allvpnCred,
    },
    virtualZENs: {
      ...EDIT_LOCATION_IPSEC_DRAWER_DATA.virtualZENs,
      options: allVirtualZens,
    },
    virtualZENClusters: {
      ...EDIT_LOCATION_IPSEC_DRAWER_DATA.virtualZENClusters,
      options: allVirtualZenClusters,
    },
  };

  const drawerData = {
    staticIPAddresses:
      apiData?.ipAddresses?.map((ip) => ({ id: ip, label: ip })) ?? [],
    proxyPorts:
      apiData?.ports?.map((port) => ({ id: port, label: port.toString() })) ??
      [],
    credentialsVPN: Array.isArray(apiData?.vpnCredentials)
      ? apiData?.vpnCredentials.map((item) => {
          item.label = item.label = item.name
            ? item.name
            : item.type == "IP"
              ? item.ipAddress
              : item.fqdn;

          return {
            ...item,
          };
        })
      : [],
    virtualZENs:
      apiData?.virtualZens?.map(({ id, name }) => ({
        id: id,
        label: name,
      })) ?? [],
    virtualZENClusters:
      apiData?.virtualZenClusters?.map(({ id, name }) => ({
        id: id,
        label: name,
      })) ?? [],
  };

  const [initFormData, setInitFormData] =
    useState<EditLocationIPsecDrawerFormDataProps>(drawerData);

  const transformTableData = (
    tableData: GRETableRowItemProps[],
  ): GREOutputData[] =>
    tableData?.map(
      (
        {
          ipAddress,
          primaryGw,
          secondaryGw,
          greRangePrimary,
          greRangeSecondary,
        },
        index,
      ) => ({
        serialNumber: index + 1,
        sourceIP: ipAddress,
        destinationIP: (
          <GREMapping title={t(primaryGw)} meta={t(secondaryGw)} />
        ),
        destinationRange: (
          <GREMapping title={greRangePrimary} meta={greRangeSecondary} />
        ),
        isRowExapdable: false,
      }),
    );
  // To be added API to support
  // greRangePrimary,
  // greRangeSecondary
  const ipFromServer = useSWR(
    `${API_ENDPOINTS.ZUXP}/unified-locations/graphql`,
    (url: string) =>
      postReq(url, {
        arg: UnifiedLocationQueries.GET_IP_FROM_SERVER,
      }),
    {
      onSuccess: (data) => {
        const serverData = data as { data: IPGreList };
        const tunnelList = serverData.data.allIpGreTunnels;
        const filtered = tunnelList
          .filter((param) => param)
          .filter(({ ipAddress }) => ips.includes(ipAddress));

        setIpGreList(transformTableData(filtered));
      },
    },
  );

  const translatedColumnData = columnData;

  const translatedTableColumnData = translatedColumnData.map((tcd) => {
    if (typeof tcd.name === "string") {
      tcd.name = t(tcd.name);
    }

    return tcd;
  });

  const locationIPSecProps = {
    noSelection: true,
    isHeaderGrey: true,
    columns: translatedTableColumnData,
    items: ipGreList,
  };

  const locationIPSecItemsLength = locationIPSecProps.items.length;

  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "proxy-port-api"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_PROVISIONING_PORTS,
      }),
    {
      onSuccess: (data) => {
        const proxyPortData = data as { data: ProxyPortResponse };
        const transformProxyPortData =
          proxyPortData.data.allProvisioningPorts.map((port, index) => ({
            id: index.toString(),
            label: port,
          }));
        setProxyPorts([...proxyPorts, ...transformProxyPortData]);
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );

  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "vpn-credential-api"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_VPN_CREDENTIALS,
      }),
    {
      onSuccess: (data) => {
        const vpnCredsData = data as { data: ALLVpnCredentialResponse };
        const updateVpnCredsData = vpnCredsData.data.allVpnCredentials
          .filter((item) => item?.name || item?.fqdn || item?.ipAddress)
          .map((item) => ({
            id: item?.id ?? "",
            name: item?.name ?? "",
            label: item.name
              ? item.name
              : item.type == "IP"
                ? item.ipAddress
                : item.fqdn,
            type: item?.type ?? "",
            fqdn: item?.fqdn ?? "",
            ipAddress: item?.ipAddress ?? "",
          }));
        setVpnCred([...allvpnCred, ...updateVpnCredsData]);
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );

  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "ip-gre-tunnel-api"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_IP_GRE_TUNNEL,
      }),
    {
      onSuccess: (data) => {
        const greTunnel = data as { data: IPGreTunnelResponse };
        const updateGreTunnel = greTunnel.data.allIpGreTunnels.map(
          (gretunnels) => ({
            id: gretunnels.ipAddress,
            label: gretunnels.ipAddress,
          }),
        );
        setIpGreTunned(updateGreTunnel);
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );

  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "all-ip-addresses-api"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_IP_ADDRESSES,
      }),
    {
      onSuccess: (data) => {
        const allIpAddresses = data as { data: AllIpAddressesResponse };
        const updateIpAddressData = allIpAddresses.data.allIpAddresses.map(
          (ip, index) => ({
            id: index.toString(),
            label: ip,
          }),
        );
        setIpAddress((prevIpAddresses) => {
          const mergeData = [...prevIpAddresses, ...updateIpAddressData];
          const uniqData = Array.from(
            new Map(mergeData.map((item) => [item.label, item])).values(),
          );

          return uniqData;
        });
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );
  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "all-virtual-zens-api"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_VIRTUAL_ZENS,
      }),
    {
      onSuccess: (data) => {
        const virtualZensData = data as { data: AllVirtualZensResponse };
        const virtualZens = virtualZensData.data.allVirtualZens;
        const updateVirtualZenData =
          virtualZens.length > 0
            ? virtualZens?.map(({ id, name }) => ({
                id: id.toString(),
                label: name,
              }))
            : [];
        setAllVirtualZens([...allVirtualZens, ...updateVirtualZenData]);
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );

  useSWR(
    [
      `${API_ENDPOINTS.ZUXP}/unified-locations/graphql`,
      "all-virtual-zen-cluster-api",
    ],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: UnifiedLocationQueries.GET_ALL_VIRTUAL_ZEN_CLUSTER,
      }),
    {
      onSuccess: (data) => {
        const virtualZenClustersData = data as {
          data: AllVirtualZenClustersResponse;
        };
        const updateallVirtualZenClustersData =
          virtualZenClustersData.data.allVirtualZenClusters.map((item) => ({
            id: item.id.toString(),
            label: item.name,
          }));
        setAllVirtualZenClusters([
          ...allVirtualZenClusters,
          ...updateallVirtualZenClustersData,
        ]);
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );

  return (
    <>
      <CardWrapper className="p-4">
        <div
          className="flex flex-col"
          data-testid="unified-locations-ipsec-gre"
        >
          <LocationHeader
            pageTitle={pageTitle}
            btnHandleClick={handleEditIPsec}
            hasButton={true}
            iconName={faPen}
            id={getDataTestId("edit", ID)}
            readOnly={readOnly}
          />
          <EditLocationIPsecDrawer
            {...updateDrawerData}
            initFormData={initFormData}
            openDrawer={openEditLocationIPsecDrawer}
            setOpenDrawer={setOpenEditLocationIPsecDrawer}
            onSave={(formData: EditLocationIPsecDrawerFormDataProps) => {
              locationDetailFromServer &&
                onSave?.(
                  locationIPsecPayloadTransformer(
                    locationDetailFromServer,
                    formData,
                  ),
                );
              setInitFormData(formData);
            }}
          />

          {IPsecList.map(({ label, value }, itemIndex) => (
            <IPsecLists
              label={label}
              value={value}
              key={itemIndex}
              itemId={itemIndex}
            />
          ))}
        </div>
      </CardWrapper>
      <div>
        <div className="pt-rem-240 pb-rem-80 pl-rem-80">
          <div className="flex justify-between">
            <div className="typography-paragraph1 font-medium text-semantic-content-base-primary">
              {t("locations.ipsec.gre-tunnel-info")}
            </div>
            {/* Need to confirm */}
            {/* <Button
              id={getDataTestId("export", ID)}
              data-testid={getDataTestId("export", ID)}
              variant="tertiary"
              onClick={handleExport}
              icon={faFileExport}
              iconPosition="start"
            >
              {t("EXPORT")}
            </Button> */}
          </div>
        </div>
      </div>
      <div className="z-10 relative pl-rem-80">
        <WithStates
          loading={ipFromServer.isLoading || !locationIPSecItemsLength}
          loadingComponent={<TableLoader rows={2} />}
        >
          <ZDataTable
            {...locationIPSecProps}
            translations={{ noItems: "No Items Found", refine_filter: " " }}
          />
        </WithStates>
      </div>
    </>
  );
};

export default LocationIPsec;

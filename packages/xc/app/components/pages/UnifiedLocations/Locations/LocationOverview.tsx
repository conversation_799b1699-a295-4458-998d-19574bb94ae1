/* eslint-disable @up/unified-platform/max-file-lines */
"use client";

import { useTranslation } from "react-i18next";
import { type ReactElement, useEffect, useState } from "react";
import { faPen, faTrash } from "@fortawesome/pro-regular-svg-icons";
import { Button } from "@zs-nimbus/core";
import useS<PERSON> from "swr";
import useSWRMutation from "swr/mutation";
import { Alert } from "@xc/legacy-components";
import { useRouter } from "next/navigation";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import DeleteModal from "../Components/DeleteModal/DeleteModal";
import {
  COUNTRIES,
  TIMEZONES,
  trafficTypesMapping,
  trafficTypeOptions,
} from "../constants";
import CardWrapper from "../Components/CardWrapper/CardWrapper";
import {
  type LocationInfoProps,
  type LocationDetailsProps,
  type IdLabelProps,
  type ManualGroupResponse,
  type ManagedByResponse,
} from "./types";
import LocationDrawer from "./Drawers/LocationDrawer/LocationDrawer";
import { EDIT_LOCATION_DRAWER_DATA } from "./Drawers/LocationDrawer/LocationDrawer.data";
import { type LocationDrawerFormDataProps } from "./Drawers/types";
import { locationOverviewPayloadTransformer } from "./LocationDetailTransformer";
import { LOCATIONS_ENDPOINTS } from "./config/apiUtils";
import { API_ENDPOINTS, deleteByIdReq, postReq } from "@/utils/apiHelper";
import {
  type ErrorType,
  removeAlertMessage,
  showErrorAlert,
} from "@/app/onboarding/apiHelper";
import { getDataTestId } from "@/utils/utils";
// import { useProductAccessProvider } from "@/context/ProductAccessProvider";

const LocationInfo = ({
  locationInfo,
  dataTestID,
}: {
  locationInfo: LocationInfoProps[];
  dataTestID: string;
}) => {
  const { t } = useTranslation();

  return (
    <>
      {locationInfo?.map((data, index) => (
        <div
          className="flex-col justify-start items-start flex"
          key={index}
          data-testid={getDataTestId(index, dataTestID)}
        >
          <div className="typography-paragraph1 text-semantic-content-base-tertiary break-all">
            {t(data.label)}
          </div>
          <div className="typography-paragraph1 text-semantic-content-base-primary break-all">
            {data.value}
          </div>
        </div>
      ))}
    </>
  );
};

const LocationOverview = ({
  locationName,
  locationInfo,
  handleEditLocation,
  handleRemoveLocation,
  locationDetailedData,
  onSave,
  locationDetailFromServer,
  readOnly,
}: LocationDetailsProps) => {
  const ID = "location-overview";
  const router = useRouter();
  const [alertType, setAlertType] = useState("");
  const [alertMessage, setAlertMessage] = useState<ReactElement | string>();
  const [openLocationDrawer, setOpenLocationDrawer] = useState(false);
  const [deleteLocation, setDeleteLocation] = useState<boolean>(false);
  const [manualGroupList, setManualGroupList] = useState<IdLabelProps[]>([]);
  const [managedByList, setManagedByList] = useState<IdLabelProps[]>([]);
  // const { subscriptions } = useProductAccessProvider();
  // const hasZSExternalPartner = (
  //   subscriptions?.ZIA as unknown as Array<Record<string, unknown>>
  // )?.find((ziaSub) => ziaSub?.id === "ZS_EXT_PARTNERS")?.subscribed as boolean;

  const timedErrorHandler = {
    onError: (err: Error) => {
      showErrorAlert(err?.cause as ErrorType, setAlertType, setAlertMessage);
      removeAlertMessage(setAlertType, setAlertMessage);
    },
  };
  const deleteLocationHandler = {
    onSuccess: () => {
      setDeleteLocation(false);
      router.push("/locations");
    },
    ...timedErrorHandler,
  };

  const handleEditLocationDrawer = () => {
    setOpenLocationDrawer(true);
    handleEditLocation();
  };

  const delteLocationHandler = () => {
    setDeleteLocation(true);
    handleRemoveLocation();
  };

  useEffect(() => {
    if (locationDetailFromServer) {
      setOpenLocationDrawer(false);
    }
  }, [locationDetailFromServer]);

  useSWR(
    [
      `${API_ENDPOINTS.ZUXP}/unified-locations/graphql`,
      "manual-location-group",
    ],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: {
          query: `query {
            allManualLocationGroups{
              id,
              name
            }
        }`,
        },
      }),
    {
      onSuccess: (data) => {
        const manualGroupData = data as { data: ManualGroupResponse };
        const updateManualGroupList =
          manualGroupData.data.allManualLocationGroups.map(({ id, name }) => ({
            id: id.toString(),
            label: name,
          })) satisfies IdLabelProps[];

        setManualGroupList(updateManualGroupList);
      },
      onError: (err) => {
        console.log(err, "error");
      },
    },
  );

  useSWR(
    [`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, "all-managed-by"],
    () =>
      postReq(`${API_ENDPOINTS.ZUXP}/unified-locations/graphql`, {
        arg: {
          query: `query {
            allManagedBy{
              id,
              name,
              type,
              disabled
            }
          }`,
        },
      }),
    {
      onSuccess: (data) => {
        const manualGroupData = data as { data: ManagedByResponse };
        const updateManualGroupList = manualGroupData.data.allManagedBy.map(
          ({ id, name }) => ({
            id: id.toString(),
            label: name,
          }),
        ) satisfies IdLabelProps[];
        setManagedByList([...managedByList, ...updateManualGroupList]);
      },
    },
  );
  const {
    country,
    description,
    dynamicLocationGroups,
    excludeFromManualGroups,
    excludeFromDynamicGroups,
    latitude,
    longitude,
    manualLocationGroups,
    state,
    timeZone,
    managedBy,
    profile,
    ecLocation,
    geoOverride,
  } = locationDetailedData;

  // for zsExternalPartner showing full options else not showing last extranet option.
  // Update zsExternalPartner when get from backend
  // for ecLocation(edge connector location) we need to show readonly traffic type dropdown so added workload in trafficTypesMapping for read only
  const updatedTrafficTypeOptions = ecLocation
    ? trafficTypesMapping().filter((_, index) => index !== 4)
    : trafficTypeOptions().slice(0, 4);

  const drawerData = {
    name: locationName!,
    cityState: state ?? "",
    country: COUNTRIES[country as keyof typeof COUNTRIES] ?? "",
    timezone: TIMEZONES[timeZone as keyof typeof TIMEZONES] ?? "",
    latitude: latitude?.toString(),
    description: description ?? "",
    longitude: longitude?.toString(),
    excludeManualLocation: excludeFromManualGroups,
    excludeDynamicLocation: excludeFromDynamicGroups,
    manualLocation: Array.isArray(manualLocationGroups)
      ? manualLocationGroups?.map((item) => ({
          id: item.id,
          label: item.name,
        }))
      : [],
    dynamicLocation: Array.isArray(dynamicLocationGroups)
      ? dynamicLocationGroups?.map((item) => ({
          id: item.id,
          label: item.name,
        }))
      : [],
    trafficType: profile
      ? updatedTrafficTypeOptions?.filter((item) => item?.id === profile)
      : [],
    managedby: managedBy
      ? [{ id: managedBy?.id, label: managedBy?.name }]
      : EDIT_LOCATION_DRAWER_DATA.internetAccess.managedby.options,
    geoOverride,
  };

  const [initFormData, setInitFormData] =
    useState<LocationDrawerFormDataProps>(drawerData);

  const updateLocationDrawerData = {
    ...EDIT_LOCATION_DRAWER_DATA,
    name: {
      ...EDIT_LOCATION_DRAWER_DATA.name,
      readonly: ecLocation,
    },
    locationGroups: {
      ...EDIT_LOCATION_DRAWER_DATA.locationGroups,
      manualLocation: {
        ...EDIT_LOCATION_DRAWER_DATA.locationGroups.manualLocation,
        options: manualGroupList,
      },
    },
    internetAccess: {
      ...EDIT_LOCATION_DRAWER_DATA.internetAccess,
      managedby: {
        ...EDIT_LOCATION_DRAWER_DATA.internetAccess.managedby,
        options: [
          ...EDIT_LOCATION_DRAWER_DATA.internetAccess.managedby.options,
          ...managedByList,
        ],
      },
      trafficType: {
        ...EDIT_LOCATION_DRAWER_DATA.internetAccess.trafficType,
        options: updatedTrafficTypeOptions,
        readonly: ecLocation,
      },
    },
  };

  const deleteLocationbyId = useSWRMutation(
    LOCATIONS_ENDPOINTS.deleteLocation,
    deleteByIdReq,
    deleteLocationHandler,
  );
  const onDelete = (id: string) => {
    void deleteLocationbyId.trigger({ id: id.toString() });
  };

  return (
    <CardWrapper className="p-4" data-testid={ID}>
      <div className="flex flex-col gap-6">
        <div className="inline-flex justify-start items-start gap-2">
          <div className="grow flex flex-col justify-start items-start gap-1">
            <div
              className="typography-header5 text-semantic-content-base-primary max-w-[382px] break-all"
              data-testid={getDataTestId("location-name", ID)}
            >
              {locationName ?? "none"}
            </div>
            <div
              className="typography-paragraph2 text-semantic-content-base-secondary max-w-[382px] break-all"
              data-testid={getDataTestId("location-description", ID)}
            >
              {locationDetailedData.description}
            </div>
          </div>
          <div className="flex justify-start items-center gap-6">
            <div className="w-5 h-5 justify-center items-center flex">
              <Button
                id={getDataTestId("edit", ID)}
                data-testid={getDataTestId("edit", ID)}
                variant="tertiary"
                onClick={handleEditLocationDrawer}
                disabled={readOnly}
              >
                <FontAwesomeIcon icon={faPen} />
              </Button>
            </div>
            {!ecLocation && (
              <div className="w-5 h-5 justify-center items-center flex">
                <Button
                  id={getDataTestId("delete", ID)}
                  data-testid={getDataTestId("delete", ID)}
                  variant="tertiary"
                  onClick={delteLocationHandler}
                  disabled={readOnly}
                >
                  <FontAwesomeIcon icon={faTrash} />
                </Button>
              </div>
            )}
          </div>
        </div>
        <div
          className="justify-start items-center gap-2 inline-flex"
          data-testid={getDataTestId(ID, "location-info")}
        >
          <div className="w-[250px] border-r border-semantic-border-base-primary flex-col justify-start items-start gap-3 inline-flex pr-rem-40">
            <LocationInfo
              dataTestID={"left-location-info"}
              locationInfo={locationInfo.slice(0, 5)}
            />
          </div>

          <div className="flex-col justify-start items-start gap-3 inline-flex max-w-[250px]">
            <LocationInfo
              dataTestID={"right-location-info"}
              locationInfo={locationInfo.slice(5)}
            />
          </div>
        </div>
      </div>
      {alertMessage && (
        <Alert
          alert={{
            message: alertMessage,
            type: alertType,
          }}
        />
      )}
      <DeleteModal
        text="appliances.modal.location-text"
        subText="appliances.modal.location-subtext"
        isSubInterface={true}
        onClose={() => setDeleteLocation(false)}
        title="appliances.modal.confirm-delete"
        show={deleteLocation}
        id={locationDetailFromServer?.id.toString()}
        onDelete={onDelete}
        warningMessageText="appliances.modal.undo-warning"
      />
      <LocationDrawer
        {...updateLocationDrawerData}
        openDrawer={openLocationDrawer}
        initFormData={initFormData}
        setOpenDrawer={setOpenLocationDrawer}
        onSave={(formData: LocationDrawerFormDataProps) => {
          locationDetailFromServer &&
            onSave?.(
              locationOverviewPayloadTransformer(
                locationDetailFromServer,
                formData,
              ),
            );
          setInitFormData(formData);
        }}
      />
    </CardWrapper>
  );
};

export default LocationOverview;

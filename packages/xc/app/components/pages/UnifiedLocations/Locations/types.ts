/* eslint-disable @up/unified-platform/max-file-lines */
import { type ReactElement, type ReactNode } from "react";
import { type IconProp } from "@fortawesome/fontawesome-svg-core";
import { type LocationsListFromServer } from "./LocationList/LocationListTransformer";
import { type LocationRowProps } from "./LocationList/LocationTableConfig";
import {
  type LocationInfo,
  type ConnectionOptions,
  type IPSecGre,
  type LocationDetailFromServer,
} from "./LocationDetailTransformer";
import { type TableColumnItemProps } from "@/components/Analytics/DataTable/types";

export type BranchDevice = {
  id: number;
  name: string;
};

export type UnifiedLocations = UnifiedLocation[];

// TODO: When real api exists, check if these are lower or uppercase
export type LocationProfile = "CORPORATE" | "SERVER" | "GUESTWIFI";

// TODO: When real api exists, check if these are lower or uppercase
export type TimeUnit = "MINUTE" | "HOUR" | "DAY";

export type UnifiedLocation = {
  id: number;
  name: string;
  description?: string;
  profile: LocationProfile;
  parentId?: number;
  uploadBandwidth: number;
  downloadBandwidth: number;
  country?: string; // possibly uses NONE for undefined
  city: string;
  timezone?: string; // possibly NOT_SPECIFIED for undefined
  geoOverride: boolean;
  longitude?: number;
  latitude?: number;
  ipaddresses: string[];
  ports: number[];
  vpnCredentials?: number;
  authRequired: boolean;
  xffEnabled: boolean;
  surrogateIP: boolean;
  idleTimeInMinutes: number;
  idleTimeDisplayUnit: TimeUnit;
  enableSurrogateForKnownBrowsers: boolean;
  refreshTimeSurrogacy: number;
  refreshTimeSurrogacyDisplayUnit: TimeUnit;
  enableFirewall: boolean;
  enableIPS: boolean;
  aupEnabled: boolean;
  aupBlockInternet: boolean;
  aupForceSSLInspection: boolean;
  aupTimeoutInDays: boolean;
  cautionEnabled: boolean;
  iotDiscoveryEnabled: boolean;
  branchDevices: BranchDevice[];
  cookiesAndProxy: boolean;
  locationBandwidth: boolean;
};

export type LocationInfoProps = {
  label: string;
  value: string;
};

export type LocationDetailsProps = {
  locationName?: string;
  templateName?: string;
  locationInfo: LocationInfoProps[];
  handleEditLocation: () => void;
  handleRemoveLocation: () => void;
  locationDetailedData: LocationInfo;
  onSave?: (
    locationInfoData: Partial<Omit<LocationDetailFromServer, "locationInfo">>,
  ) => void;
  locationDetailFromServer?: LocationDetailFromServer;
  readOnly: boolean;
};

export type LocationToggleSwitchProps = {
  locationSwitch: ToggleSwitch[];
  id?: string;
  disabled?: boolean;
};

export type ToggleSwitch = {
  id: string;
  desc: string;
  isEnabled: boolean;
  handleToggle: (value: boolean, id: string) => void;
  metaText?: string;
};

export type OperationCardButtonProps = {
  text: string;
  onClick: () => void;
};

export type OperationCardProps = {
  icon: string;
  name: string;
  count: number;
  operationCardButton: OperationCardButtonProps;
  description: string;
};

export type OperationCardProp = {
  operationCardData: OperationCardProps[];
};

export type NoDataCardProps = {
  id?: string | number;
  icon?: ReactElement;
  name: string;
  NoDataCardButton: () => void;
  description: string;
  buttonText: string;
  readOnly: boolean;
};

export type OptionItem = {
  label: string;
  value: string;
  itemId?: string | number;
};

export type OptionListProps = {
  list: OptionItem[];
};

export type LocationConnectionOptionsProps = {
  pageTitle: string;
  handleEditConnection: () => void;
  optionList: OptionListProps[];
  connectionOptionData?: ConnectionOptions;
  onSave?: (
    locationInfoData: Partial<
      Omit<LocationDetailFromServer, "connectionOptions">
    >,
  ) => void;
  locationDetailFromServer?: LocationDetailFromServer;
  readOnly: boolean;
};

export type IPsecList = {
  label: string;
  value: string;
};

export type LocationIPsecProps = {
  pageTitle: string;
  handleEditIPsec: () => void;
  handleExport: () => void;
  IPsecList: IPsecList[];
  tableRowData: GRETableRowItemProps[];
  columnData: TableColumnItemProps[];
  apiData?: IPSecGre;
  onSave?: (
    locationInfoData: Partial<Omit<LocationDetailFromServer, "ipSecGre">>,
  ) => void;
  locationDetailFromServer?: LocationDetailFromServer;
  readOnly: boolean;
};

export type SublocationTagsProps = string[];

export type FilterOptionsProps = {
  id: string;
  name: string;
  value?: string;
};

export type AppliancesDataSetProps = {
  id: number;
  name: string;
  meta: string | null;
  type: string;
  typeMeta: string | null;
  status: string;
  ipAddress: string;
  serialNumber: string;
  applianceGroupId: number | null;
  provTemplateName: string;
  vmSize?: string;
};

export type TableRowItemProps = {
  id: number;
  name: string;
  meta: string | null;
  dataSet: AppliancesDataSetProps[];
};

type AppliancesOutputDataChild = {
  id: number;
  appliance: JSX.Element;
  type: JSX.Element;
  status: JSX.Element;
  ipAddress: string;
};

export type AppliancesOutputData = {
  id: number;
  isRowExapdable: boolean;
  children: AppliancesOutputDataChild[];
};

export type AppliancesProps = {
  id?: string;
  typeOptions: FilterOptionsProps[];
  statusOptions: FilterOptionsProps[];
  resetButtonText: string;
  addButtonText: string;
  tableRowData: TableRowItemProps[];
  columnData: TableColumnItemProps[];
  onBulkSelect: () => void;
  onBulkUnselect: () => void;
  onRowSelect: () => void;
  onRowUnSelect: () => void;
  onAddOpen: () => void;
  handleReset: () => void;
  handleAdd: () => void;
  applianceCount: number;
  locationId: number;
  readOnly: boolean;
  ztwSuperAdmin: boolean;
};

export type GRETunnelProps = {
  tableRowData: TableRowItemProps[];
  columnData: TableColumnItemProps[];
};

export type GREOutputData = {
  serialNumber?: number;
  sourceIP: string;
  destinationIP: JSX.Element;
  destinationRange: JSX.Element;
  isRowExapdable: boolean;
};

export type GRETableRowItemProps = {
  ipAddress: string;
  greEnabled: boolean;
  greTunnelIp: string;
  primaryGw: string;
  secondaryGw: string;
  greRangePrimary: string;
  greRangeSecondary: string;
};

export type TunnelEntry = Array<{
  greEnabled: boolean;
  greTunnelIp: string;
  ipAddress: string;
  primaryGw: string;
  secondaryGw: string;
  greRangePrimary: string;
  greRangeSecondary: string;
}>;

export type IPGreList = {
  allIpGreTunnels: TunnelEntry;
};

export type EmptySubLocationProps = {
  id?: string | number;
  icon: string;
  name: string;
  EmptySubLocationButton: () => void;
  description: string;
};

export type LocationHeaderProps = {
  pageTitle: string;
  btnText?: string;
  btnHandleClick?: () => void;
  hasButton?: boolean;
  iconName?: IconProp;
  hasDescription?: boolean;
  pageDesc?: string;
  id?: string | number;
  readOnly: boolean;
};

export type PaginationResponse = {
  pageNumber: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
};

export type ResponseBody = {
  pagination: PaginationResponse;
  data: LocationsListFromServer;
};

export type SubLocationResponseBody = {
  pagination: PaginationResponse;
  data: SubLocationProps | SubLocations[];
};

export type LocationGroupsProps = {
  id: number;
  name: string;
};

type SubLocationInfo = {
  otherSubLocation: boolean;
  description: string;
  profile: string;
  parentId: number;
  country: string;
  state: string;
  timeZone: string;
  geoOverride: boolean;
  longitude: number;
  latitude: number;
  excludeFromDynamicGroups: boolean;
  excludeFromManualGroups: boolean;
  manualLocationGroups: LocationGroupsProps[];
  dynamicLocationGroups: LocationGroupsProps[];
  managedBy: LocationGroupsProps | null;
  ecLocation: boolean;
};

export type ConnectionOptionsProps = {
  uploadBandwidth: number;
  downloadBandwidth: number;
  authRequired: boolean;
  xffEnabled: boolean;
  surrogateIp: boolean;
  idleTimeInMinutes: number;
  idleTimeDisplayUnit: string;
  enableSurrogateForKnownBrowsers: boolean;
  refreshTimeSurrogacy: number;
  refreshTimeSurrogacyDisplayUnit: string;
  enableFirewall: boolean;
  enableIps: boolean;
  aupEnabled: boolean;
  aupBlockInternet: boolean;
  aupForceSslInspection: boolean;
  aupTimeoutInDays: number;
  cautionEnabled: boolean;
  iotDiscoveryEnabled: boolean;
  kerberosAuth: boolean;
  digestAuthEnabled: boolean;
  basicAuthEnabled: boolean;
  basicAuthentication?: boolean;
  unmapTime?: boolean;
  iotEnforcePolicySet?: boolean;
  xffForwarding?: boolean;
  bandwidthControl?: boolean;
  cookiesAndProxy: boolean;
  locationBandwidth: boolean;
  jwtAuthentication: boolean;
};

export type SubLocations = {
  id: number;
  name: string;
  meta?: string;
  subLocationInfo: SubLocationInfo;
  ipAddresses: string[];
  connectionOptions: ConnectionOptionsProps;
  otherLocation?: boolean;
  ipSecGre: IPSecGre;
};

export type SubLocationProps = {
  id: number;
  subLocations: SubLocations[];
};

export type Appliances = {
  id: number;
  name: string;
  location: string;
  locationId: number;
  applianceGroup: string;
  applianceGroupId: number;
  status: string;
  type?: string;
  typeMeta?: string;
  ipAddress?: string;
  serialNumber?: string;
  provTemplateName?: string;
  managementIp?: string;
  vmSize?: string;
};

export type AppliancesFromServer = {
  id: number;
  name: string;
  appliances: Appliances[];
};

export type ApplianceResponseBody = {
  pagination: PaginationResponse;
  data: AppliancesFromServer[];
};

export type TableRenderItem = {
  columnIndex: number;
  rowIndex: number;
  item: LocationRowProps;
  column: TableColumnItemProps;
};

export type LocationTableColumnItemProps = {
  id: string;
  name: string | ReactNode;
  isSortable: boolean;
  isHidden: boolean;
  renderItem?: (item: TableRenderItem) => void;
  width: string;
  isPinned?: boolean;
  pinnedDirection?: string;
};

export type ManualGroupResponse = {
  allManualLocationGroups: LocationGroupsProps[];
};

export type ManagedByResponse = {
  allManagedBy: Array<{
    id: number;
    name: string;
    type: string;
    disabled: string;
  }>;
};

export type ProxyPortResponse = {
  allProvisioningPorts: string[];
};

export type IPGreTunnelResponse = {
  allIpGreTunnels: Array<{
    ipAddress: string;
    greEnabled: string;
    greTunnelIp: string;
    primaryGw: string;
    secondaryGw: string;
  }>;
};

export type AllIpAddressesResponse = {
  allIpAddresses: string[];
};

export type AllVirtualZensResponse = {
  allVirtualZens: Array<{
    id: number;
    name: string;
    gatewayId: number;
    status: string;
    inProduction: string;
    ipAddress: string;
    subnetMask: string;
    defaultGateway: string;
    type: string;
    ipSecEnabled: boolean;
    onDemandSupportTunnelEnabled: boolean;
    establishSupportTunnelEnabled: boolean;
    deploymentMode: string;
    vzenSkuType: string;
  }>;
};

export type ALLVpnCredentialResponse = {
  allVpnCredentials: VPNCredentalProps[];
};

export type AllVirtualZenClustersResponse = {
  allVirtualZenClusters: Array<{
    id: number;
    zgatewayId: number;
    name: string;
    status: string;
    inProduction: boolean;
    ipAddress: string;
    type: string;
    ipSecEnabled: boolean;
    onDemandSupportTunnelEnabled: boolean;
    establishSupportTunnelEnabled: boolean;
  }>;
};

export type VPNCredentalProps = {
  id: number | string;
  fqdn: string;
  name: string;
  label: string;
  type: string;
  ipAddress: string;
};

export type IdLabelProps = {
  id: string;
  label: string;
};

export type UnitProps = "minute" | "hour" | "day";

export type AdminAccessTypes = {
  ziaSuperAdmin: boolean;
  ztwSuperAdmin: boolean;
};

export type SimpleDropdownMenuItemData = {
  id: string;
  name: string;
  disabled?: boolean;
  icon?: string;
};

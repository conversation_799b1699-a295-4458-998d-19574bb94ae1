import { useTranslation } from "react-i18next";
import { useSearchParams, useRouter } from "next/navigation";
import useAnalyticsDateFilterSetup from "@/hooks/useAnalyticsDateFilterSetup";
import Tabs from "@/components/Analytics/Tabs/Tabs";
import { CONNECTOR_ACTIVITY } from "@/configs/constants/analytics";
import { BranchConnector } from "@/components/Analytics/Networking/ConnectorActivity/BranchConnector/BranchConnector";
import { CloudConnector } from "@/components/Analytics/Networking/ConnectorActivity/CloudConnector/CloudConnector";
import { GridContent } from "@/components/Analytics/GridUtils";

const ConnectorActivity = () => {
  const activeMenu = CONNECTOR_ACTIVITY;
  const loading = useAnalyticsDateFilterSetup(activeMenu);
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const type = searchParams.get("type");
  const router = useRouter();
  const ID = "connector-activity";

  return (
    <>
      {!loading && (
        <GridContent className="h-full">
          <Tabs
            containerClass="overflow-visible"
            value={type === "cloud" ? "CLOUD_CONNECTORS" : "BRANCH_CONNECTORS"}
            onChange={(_value) => {
              const url = `/analytics/networking/connector-activity?type=${_value === "BRANCH_CONNECTORS" ? "branch" : "cloud"}`;
              router.replace(url);
            }}
            options={[
              {
                displayLabel: t("BRANCH_CONNECTORS"),
                value: "BRANCH_CONNECTORS",
                content: <BranchConnector id={ID} />,
              },
              {
                displayLabel: t("CLOUD_CONNECTORS"),
                value: "CLOUD_CONNECTORS",
                content: <CloudConnector id={ID} />,
              },
            ]}
            id={ID}
          />
        </GridContent>
      )}
    </>
  );
};

export { ConnectorActivity };

import { useTranslation } from "react-i18next";
import Tabs from "@/components/Analytics/Tabs/Tabs";
import { GridContent } from "@/components/Analytics/GridUtils";
import { BranchConnector } from "@/components/Analytics/Networking/ConnectorActivity/BranchConnector/BranchConnector";
import { CloudConnector } from "@/components/Analytics/Networking/ConnectorActivity/CloudConnector/CloudConnector";

const ConnectorActivity = () => {
  const { t } = useTranslation();
  const ID = "connector-activity";

  return (
    <>
      <GridContent className="h-full">
        <Tabs
          value={"BRANCH_CONNECTORS"}
          onChange={() => ({})}
          options={[
            {
              displayLabel: t("BRANCH_CONNECTORS"),
              value: "BRANCH_CONNECTORS",
              content: <BranchConnector />,
            },
            {
              displayLabel: t("CLOUD_CONNECTORS"),
              value: "CLOUD_CONNECTORS",
              content: <CloudConnector />,
            },
          ]}
          id={ID}
        />
      </GridContent>
    </>
  );
};

export { ConnectorActivity };

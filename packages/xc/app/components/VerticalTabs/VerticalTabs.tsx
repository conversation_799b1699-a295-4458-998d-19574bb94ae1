import type React from "react";
import { useTranslation } from "react-i18next";
import { cn } from "@up/components";
import { getDataTestId } from "@/utils/utils";

export type TabConfig = {
  title: string;
  value: string;
  content: () => React.ReactNode;
  icon?: string;
  count?: number;
  disabled?: boolean;
};

type TabProps = {
  onClick: (tab: TabConfig) => void;
  selected: boolean;
  id?: string;
  count?: number;
};

export type VerticalTabsProps = {
  tabs: TabConfig[];
  onTabClick: (tab: TabConfig) => void;
  selectedTab: string;
  id?: string;
};

const Tab = (props: TabProps & TabConfig) => {
  const defaultButtonClass =
    "flex py-rem-80 items-center border-b border-semantic-border-interactive-primary-disabled px-rem-80 typography-paragraph1-strong";
  const defaultIconClass = "pr-[6px] fa-sm";
  const iconClass = cn(
    defaultIconClass,
    props.disabled
      ? "text-semantic-content-base-subdued"
      : "text-semantic-content-base-secondary",
    props.selected && "text-semantic-content-interactive-primary-default",
  );
  const buttonClass = cn(
    defaultButtonClass,
    props.disabled
      ? "text-semantic-content-base-subdued"
      : "text-semantic-content-base-secondary",
    props.selected &&
      "text-semantic-content-interactive-primary-default bg-semantic-surface-table-row-active  border-r-2 border-r-semantic-border-interactive-primary-active",
  );

  const { t } = useTranslation();

  return (
    <button
      className={buttonClass}
      disabled={props.disabled}
      onClick={() => props.onClick({ ...props })}
      data-testid={getDataTestId("tab", props.id)}
    >
      <i className={cn(props.icon, iconClass)} />
      {t(props.title)} {props?.count ? `(${props?.count})` : ""}
    </button>
  );
};

const VerticalTabs = ({
  tabs,
  onTabClick,
  selectedTab,
  id,
}: VerticalTabsProps) => {
  const ID = getDataTestId("vertical-tabs", id);

  return (
    <div className="flex flex-col" data-testid={ID}>
      {tabs.map((tab) => (
        <Tab
          key={tab.value}
          {...tab}
          onClick={onTabClick}
          selected={tab.value === selectedTab}
          id={ID}
        />
      ))}
    </div>
  );
};

export default VerticalTabs;

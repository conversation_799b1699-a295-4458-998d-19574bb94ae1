declare module "@ztw/pages" {
  import { type FC, type ComponentType } from "react";

  // Dashboard
  // export const DashboardPage: ComponentType<any>;

  // Admin Pages
  export const Gateways: ComponentType<any>;
  // export const AdvancedSettingsPage: ComponentType<any>;
  // export const ApiClientsPage: ComponentType<any>;
  // export const ApiResourcesPage: ComponentType<any>;
  // export const AttributesPage: ComponentType<any>;
  // export const AuditLogsPage: ComponentType<any>;
  // export const AuthenticationLevels: ComponentType<any>;
  // export const AuthenticationMethodsPage: ComponentType<any>;
  // export const BrandingPage: ComponentType<any>;
  // export const DepartmentsPage: ComponentType<any>;
  // export const DeviceTokenPage: ComponentType<any>;
  // export const DomainsPage: ComponentType<any>;
  // export const ExternalIdentitiesPage: ComponentType<any>;
  // export const IpLocationGroupsPage: ComponentType<any>;
  // export const IpLocationsPage: ComponentType<any>;
  // export const LinkedServicesPage: ComponentType<any>;
  // export const MyProfilePage: ComponentType<any>;
  // export const RolesPage: ComponentType<any>;
  // export const ServiceEntitlementsPage: ComponentType<any>;
  // export const TokenValidatorsPage: ComponentType<any>;
  // export const TokensPage: ComponentType<any>;
  // export const UserGroupsPage: ComponentType<any>;
  // export const UsersPage: ComponentType<any>;

  // // Policy Pages
  // export const AccessPolicyPage: ComponentType<any>;
  // export const PasswordPage: ComponentType<any>;
  // export const SignonPolicyPage: ComponentType<any>;

  // // Signup Pages
  // export const PseudoDomainPage: ComponentType<any>;
  // export const SignupPage: ComponentType<any>;

  // // Help Pages
  // export const RemoteAssistancePage: ComponentType<any>;

  // Layout
  export const AppProvider: FC<{
    children: React.ReactNode;
  }>;

  // Utils
  export function initializeHttpClient(
    environment: any,
    getToken: () => string | null,
  ): void;

  export function getDataTestId(
    id: string | undefined,
    parentId: string | undefined,
  ): string;
}

import { useEffect, useRef, useContext } from "react";
import useFetchETime from "./useFetchETime";
import { AnalyticsFilterContext } from "@/app/(post-onboarding)/(with-side-nav)/analytics/AnalyticsFilterContext";
import { type FilterPillList } from "@/components/Analytics/FilterModal/types";
import { getDurationValue } from "@/utils/utils";
import { type PayloadProps } from "@/components/Analytics/CyberSecurity/AdvancedThreats/AdvancedThreatsDetails/types";
import { generateCybersecurityFilterPayload } from "@/components/pages/Analytics/Dashboard/Filters/ModalPillGroupContainer/util";
import { useFlags } from "@/context/FeatureFlags";

type FetchFunctionType = (object?: object) => void;
type PayloadFunctionType = (object: PayloadProps) => object;

const useCybersecurityFilterEffect = (
  fetchFunction: FetchFunctionType,
  hasPermission: boolean,
  payloadConfig: PayloadFunctionType,
  filtersApplied: FilterPillList[],
  additionalFilters?: object,
): [boolean, Error | undefined, number] => {
  const { can } = useFlags();
  const showFilter = can("showCSFilters");

  const {
    analyticsFilter: {
      dateFilter: {
        timeRangeSelected: { value },
      },
      contextualFilters,
    },
  } = useContext(AnalyticsFilterContext);

  const [eTime, isETimeLoading, eTimeError] = useFetchETime(
    getDurationValue(value).toString(),
    hasPermission,
  );

  const prevPayload = useRef({});

  useEffect(() => {
    const currentPayload = generateCybersecurityFilterPayload(
      contextualFilters,
      filtersApplied,
      value,
      eTime,
      additionalFilters,
    );

    if (
      JSON.stringify(currentPayload) !== JSON.stringify(prevPayload.current) &&
      eTime &&
      hasPermission
    ) {
      const payload = payloadConfig({ eTime, value, showFilter });
      void fetchFunction({
        ...payload,
        ...currentPayload,
      });
      prevPayload.current = currentPayload;
    }
  }, [
    eTime,
    value,
    hasPermission,
    contextualFilters,
    fetchFunction,
    payloadConfig,
    filtersApplied,
    additionalFilters,
    showFilter,
  ]);

  return [isETimeLoading, eTimeError, eTime];
};

export default useCybersecurityFilterEffect;

/* eslint-disable @up/unified-platform/max-file-lines */
import { v4 as uuidv4 } from "uuid";
import { type KeyboardEventHandler } from "react";
import _ from "lodash";
import { t } from "i18next";
import { getHoursAgo } from "./mockHelper";
import { WEB_APPS } from "./unifiedAnalytics/webAppList";
import {
  CYBERSECURITY_INCIDENTS_MAX_FILTER_OPTION,
  MIN_GRID_DISTANCE_RANGE,
  ON,
  TOP_ENTRIES_LIMIT,
  VM_TYPES,
  ZDX_SCORE_LOOKUP,
} from "@/configs/constants/analytics";
import { type TrafficType } from "@/components/Analytics/Networking";
import { type ChartType } from "@/components/Analytics/Operational/Devices/UserDevices/types";
import { type GeoTableResponse } from "@/components/Analytics/Networking/ConnectorActivity/BranchConnector/GeoTable/types";
import {
  type ThemeTypes,
  type DarkModeTypes,
} from "@/context/UserPreferenceContext";

type VolumeUnit = { B: number; KB: number; MB: number; GB: number; TB: number };

type DateFormat = {
  day?: "numeric" | "2-digit" | undefined;
  hour?: "numeric" | "2-digit" | undefined;
  hour12?: boolean;
  minute?: "numeric" | "2-digit" | undefined;
  month?: "numeric" | "2-digit" | undefined;
  second?: "numeric" | "2-digit" | undefined;
  year?: "numeric" | "2-digit" | undefined;
  isTimestampBreak?: boolean;
};

const volumeConfig: VolumeUnit = {
  B: 1,
  KB: 1024,
  MB: 1048576,
  GB: 1073741824,
  TB: 1099511627776,
};

export const convertUnit = (
  value: number,
  prevUnit: string,
  newUnit: string,
) => {
  const prevUnitValue = volumeConfig[prevUnit as keyof VolumeUnit];
  const prevUnitInBytes = value * prevUnitValue;

  return prevUnitInBytes / volumeConfig[newUnit as keyof VolumeUnit];
};

export const convertBytes = (data: string, convertedUnit: string) => {
  const value = Number(data?.replace(/[^.0-9]/g, "") || 0);
  const unit = data?.replace(/[^A-Za-z]/g, "") ?? convertedUnit;

  if (!(value || unit)) {
    return value;
  }

  return convertUnit(value, unit, convertedUnit);
};

export const calculateEpoch = (date: number) =>
  Math.floor(new Date(date).getTime() / 1000);

export const formatTimestampToDate = (timestamp: string) => {
  const date = new Date(Number(timestamp));

  //format the date as "Jan 9, 2025"
  const formatter = new Intl.DateTimeFormat("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });

  return formatter.format(date);
};

/**
 * Calculates the date and time on the basis of given timestamp.
 *
 * @param {number} time - date and time in timestamp
 *
 */
export const parseDateAndLabel = (
  time: number,
  isTimestamp = true,
  format?: DateFormat,
): string => {
  const formatType = format ?? {};

  const formattedDate = new Date(time * 1000).toLocaleDateString("en-us", {
    day: "day" in formatType ? formatType.day : "numeric",
    month: "month" in formatType ? formatType.month : "short",
    year: "year" in formatType ? formatType.year : "numeric",
  });
  const formattedTime = new Date(time * 1000).toLocaleTimeString("en-us", {
    hour: "hour" in formatType ? formatType.hour : "numeric",
    minute: "minute" in formatType ? formatType.minute : "numeric",
    second: "second" in formatType ? formatType.second : "numeric",
    hour12: !!formatType.hour12,
  });

  return `${formattedDate}${isTimestamp ? `${!!formatType.isTimestampBreak ? "\n" : " "}${formattedTime}` : ""}`;
};

export const parseDateLabel = (time: number): string =>
  new Date(time * 1000).toLocaleDateString("en-us", {
    month: "2-digit",
    day: "2-digit",
  });

export const parseTimeLabel = (time: number, isHour24 = false): string => {
  const date = new Date(time * 1000);
  let timeString = date.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "numeric",
    hour12: isHour24,
  });

  // Handling the edge case for midnight in 24-hour format
  if (!isHour24 && timeString === "24:00") {
    timeString = "00:00";
  }

  return timeString;
};

export const parseTime = (time: number, isHour24 = false): string =>
  new Date(time * 1000).toLocaleTimeString("en-us", {
    hour: "numeric",
    minute: "numeric",
    hour12: isHour24,
  });

export const parseDate = (time: number): string => {
  const formattedDate = new Date(time * 1000).toLocaleDateString("en-us", {
    day: "numeric",
    month: "short",
  });

  return formattedDate;
};

export const parseDateValueLabel = (time: number): string => {
  const formattedDate = new Date(time).toLocaleDateString("en-us", {
    day: "numeric",
    month: "short",
  });

  return formattedDate;
};

export const parseDateAndLabelShortFormat = (
  time: number,
  isTimestamp = true,
): string => {
  const formattedDate = new Date(time * 1000).toLocaleDateString("en-us", {
    day: "numeric",
    month: "short",
  });

  const formattedTime = new Date(time * 1000).toLocaleTimeString("en-us", {
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  });

  return `${formattedDate}${isTimestamp ? ` ${formattedTime}` : ""}`;
};

export const parseDateWithYearAndLabelShortFormat = (
  time: number,
  isTimestamp = true,
  hour12 = false,
): string => {
  const formattedDate = new Date(time * 1000).toLocaleDateString("en-us", {
    day: "numeric",
    month: "short",
    year: "numeric",
  });

  const formattedTime = new Date(time * 1000).toLocaleTimeString("en-us", {
    hour: "numeric",
    minute: "numeric",
    hour12,
  });

  return `${formattedDate}${isTimestamp ? ` ${formattedTime}` : ""}`;
};

/**
 * Converts the date in the format `MMM d`.
 */
export const toShortFormat = (date: Date, isTimeStamp = false) => {
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const day = date.getDate();
  const monthIndex = date.getMonth();
  const time = `${date.getHours()}:${date.getMinutes().toString().length === 1 ? `0${date.getMinutes()}` : date.getMinutes()}`;
  const monthName = monthNames[monthIndex];

  return `${monthName} ${day}${isTimeStamp ? `, ${time}` : ""}`;
};

export const formatDuration = (val: number): string => {
  if (val === 0) return "0";
  const seconds = val / 1000;

  return `${seconds} second${seconds === 1 ? "" : "s"}`;
};

/**
 * Convert bytes to specific format
 *
 * @param {number} bytes - bytes value
 * @param {number} decimals - round to number of decimal places
 *
 */
export const formatBytes = (bytes: number, decimals = 1, unit = "") => {
  if (!+bytes) return "0 B";

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

  let i = Math.floor(Math.log(bytes) / Math.log(k));

  if (unit) {
    i = Math.max(
      sizes.findIndex((item) => item === unit),
      0,
    );
  }

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
};

/**
 * Convert number to specific format: 1k, 2k etc.
 *
 * @param {number} num - bytes value
 * @param {number} decimals - round to number of decimal places
 *
 */
export const formatNumber = (num: number, decimals = 1): string => {
  if (!num) {
    return "0";
  }

  if (num > 999999999) {
    return (num / 1000000000).toFixed(decimals) + "B";
  }

  if (num > 999999) {
    return (num / 1000000).toFixed(decimals) + "M";
  }

  if (num > 999) {
    return (num / 1000).toFixed(decimals) + "K";
  }

  return num.toString();
};

/**
 * Calculate the region color code on the basis of provided count.
 *
 * @param {number} count - count value
 *
 */
export const regionColorCode = (count: number): string => {
  if (
    count >= ZDX_SCORE_LOOKUP.Poor.range[0] &&
    count <= ZDX_SCORE_LOOKUP.Poor.range[1]
  )
    return "#DC362E";
  if (
    count >= ZDX_SCORE_LOOKUP.Okay.range[0] &&
    count <= ZDX_SCORE_LOOKUP.Okay.range[1]
  )
    return "#F19325";
  if (
    count >= ZDX_SCORE_LOOKUP.Good.range[0] &&
    count <= ZDX_SCORE_LOOKUP.Good.range[1]
  )
    return "#3DA592";

  return "#DC362E";
};

export const getGradeDetailByScore = (score: number): object => {
  const grades = [
    {
      label: "Poor",
    },
    {
      label: "Okay",
    },
    {
      label: "Good",
    },
  ];

  if (score >= 66) {
    return grades[2];
  } else if (score >= 34 && score <= 65) {
    return grades[1];
  } else {
    return grades[0];
  }
};

/**
 * Convert milliseconds to time format
 *
 * @param {number} duration - duration in ms
 *
 */
export const msToTime = (duration: number): string => {
  const milliseconds = Math.floor((duration % 1000) / 100);
  const seconds: string | number = Math.floor((duration / 1000) % 60);
  const minutes: string | number = Math.floor((duration / (1000 * 60)) % 60);
  const hours: string | number = Math.floor((duration / (1000 * 60 * 60)) % 24);

  if (hours || minutes || seconds) {
    return `${hours ? `${hours} hrs` : ""}${minutes ? `${hours ? ", " : ""}${minutes} mins` : ""}${seconds ? `${hours || minutes ? ", " : ""}${seconds} secs` : ""}`;
  }

  return `${milliseconds} ms`;
};

export const secToMs = (sec: number): number => sec * 1000;

export const snakeCaseToTitleCase = (str: string): string => {
  if (str === undefined || str === null) return "";

  return str
    .split("_")
    .map((w) => w.charAt(0).concat(w.slice(1).toLowerCase()))
    .join(" ");
};

export const capitalizeFirstLetterOnly = (str: string): string => {
  if (str === undefined || str === null) return "";

  return str.charAt(0).toUpperCase().concat(str.slice(1).toLowerCase());
};

export const capitalizeWords = (str: string): string => {
  if (str === undefined || str === null) return "";

  return str
    .split(" ")
    .map((w) => w.charAt(0).toUpperCase().concat(w.slice(1)))
    .join(" ");
};

export const formatTimeAsDuration = (ended_on: number, started_on: number) => {
  if (ended_on === 0) {
    return `In Progress`;
  }
  const time = ended_on - started_on;
  const hours = Math.floor(time / (60 * 60))
    .toString()
    .padStart(2, "0");
  const minutes = Math.floor((time % (60 * 60)) / 60)
    .toString()
    .padStart(2, "0");
  const seconds = Math.ceil((time % (60 * 60)) % 60)
    .toString()
    .padStart(2, "0");

  return `${hours}:${minutes}:${seconds}`;
};

export const formatTimeInTwelveHourFormat = (time: number) => {
  const period = time < 12 || time === 24 ? "AM" : "PM";
  const formattedTime = time % 12 || 12;

  return `${formattedTime}:00${period}`;
};

export const formatScoreValue = (score: number) =>
  score < 0 ? "N/A" : Math.round(score);

export const getProgressBarColor = (score: number) => {
  if (score >= ZDX_SCORE_LOOKUP.Good.range[0])
    return "bg-dataviz-severity-primary-lowest";
  else if (score >= ZDX_SCORE_LOOKUP.Okay.range[0])
    return "bg-dataviz-severity-primary-medium";
  else if (score >= ZDX_SCORE_LOOKUP.Poor.range[0])
    return "bg-dataviz-severity-primary-high";
  else return "";
};

export const formatDurationMinutes = (
  endTimestamp: number,
  startTimestamp: number,
): string => {
  const durationInMinutes = Math.round(
    (endTimestamp * 1000 - startTimestamp * 1000) / (1000 * 60),
  );

  return `${durationInMinutes} min`;
};

export const getDaysAgo = (days: number) =>
  new Date(getHoursAgo(24 * days, "ago"));

export const validateEmail = (email: string) => {
  const pattern =
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

  return pattern.test(email);
};

export const filterTopEntries = (
  entriesList: Array<{
    name: string;
    total: number;
  }>,
  limit = TOP_ENTRIES_LIMIT,
) =>
  entriesList
    ?.sort((a, b) => b.total - a.total)
    ?.filter((_, i) => i < limit)
    .reverse();

export const formatDonutResponse = (
  data: Array<{
    name: string;
    total: number;
  }>,
  config: unknown[],
  threshold = 5, // threshold for considering "Other" category
) => {
  const sortedData = data?.sort((a, b) => b.total - a.total);
  const totalLength = sortedData?.length;

  if (totalLength <= threshold + 1) {
    return sortedData?.map((val, i) => ({
      category: WEB_APPS[val.name] ?? val.name,
      count: val.total,
      ...(config[i] || {}),
    }));
  } else {
    const topEntries = sortedData?.slice(0, threshold);
    const otherTotal = sortedData
      ?.slice(threshold)
      ?.reduce((acc, cur) => acc + cur.total, 0);

    const topEntriesMapped = topEntries?.map((val, i) => ({
      category: WEB_APPS[val.name] ?? val.name,
      count: val.total,
      ...(config[i] || {}),
    }));

    const otherEntry = {
      category: "OTHERS",
      count: otherTotal,
      ...(config[threshold] || {}),
    };

    return topEntriesMapped ? [...topEntriesMapped, otherEntry] : [];
  }
};

export const formatLineChartDate = (timestamp: number): string => {
  const date = new Date(timestamp * 1000);
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const amPm = hours >= 12 ? "pm" : "am";
  const formattedHours = hours % 12 || 12;
  const formattedDate = `${formattedHours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")} ${amPm}\n ${date.getMonth() + 1}/${date.getDate()}`;

  return formattedDate;
};

export const parseJwt = (token: string | null) => {
  if (!token) {
    return;
  }
  // The JWT has a header.payload.signature
  const base64Url = token.split(".")[1];
  const base64 = base64Url?.replace("-", "+").replace("_", "/");
  try {
    // If the input string is not in a parseable format, we expect an exception. catch and return null
    return JSON.parse(window?.atob(base64)) as Record<string, unknown>;
  } catch (exception) {
    console.log(exception);

    return null;
  }
};

export const formatXAxisDateTime = (timestamp: number) =>
  timestamp ? parseTimeLabel(timestamp) + "\n" + parseDateLabel(timestamp) : "";

export const calculateSystemTimeZoneIdentifier = () =>
  Intl.DateTimeFormat().resolvedOptions().timeZone;

export const getGridConfigByDataLength = (
  dataPoints: unknown[],
): { minGridDistance: number; width: number } => {
  const len: number = dataPoints?.length ?? 0;

  // Get the sorted keys of MIN_GRID_DISTANCE_RANGE in ascending order
  const sortedKeys = Object.keys(MIN_GRID_DISTANCE_RANGE)
    .map(Number)
    .sort((a, b) => a - b);

  // Find the smallest key that is greater than or equal to the length of dataPoints
  const closestKey =
    sortedKeys.find((key) => len <= key) ?? sortedKeys[sortedKeys.length - 1];

  // Retrieve the minGridDistance and width for the closest key
  const { minGridDistance, width } = MIN_GRID_DISTANCE_RANGE[closestKey] || {
    minGridDistance: 10,
    width: 30,
  };

  return { minGridDistance, width };
};

export const generateSessionId = () => `ONEUI_DASHBOARD${uuidv4()}`;

export const formatDeltaValue = (value: number): number => {
  const roundedValue = Math.round(value);

  return Math.abs(roundedValue);
};

export const getDurationFilter = (
  tab: TrafficType | "hadoop",
  dateFilter: {
    startDate: number;
    endDate: number;
  },
  selectedDay: string,
) => {
  const isPrivateOrHadoop = ["private", "hadoop"].includes(tab);

  const { startDate, endDate } = dateFilter;

  const numberOfDays = +selectedDay;

  const calculatedStartDate =
    numberOfDays > 14 && isPrivateOrHadoop
      ? endDate - 14 * 24 * 60 * 60
      : startDate;

  const value = numberOfDays > 14 && isPrivateOrHadoop ? 14 : numberOfDays;

  return { startDate: calculatedStartDate, endDate, value };
};

export const getDuration = (sTime: number, eTime: number) => {
  const startTime = new Date(sTime * 1000);
  const endTime = new Date(eTime * 1000);
  const time_diff = endTime.getTime() - startTime.getTime();

  const days = Math.floor(time_diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (time_diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
  );
  const minutes = Math.floor((time_diff % (1000 * 60 * 60)) / (1000 * 60));

  const duration = `${days > 0 ? `${days} days,` : ""} ${hours > 0 ? `${hours} hours,` : ""} ${minutes} minutes`;

  return sTime && eTime ? duration : "-";
};

export const getDurationValue = (value: string) =>
  +value > CYBERSECURITY_INCIDENTS_MAX_FILTER_OPTION
    ? CYBERSECURITY_INCIDENTS_MAX_FILTER_OPTION
    : +value;

export const handleKeyboardDown =
  (callback: KeyboardEventHandler) => (event: React.KeyboardEvent) => {
    if (event?.key === "Enter") {
      event.preventDefault?.();
      callback(event);
    }
  };

export const upperCaseFirst = (str = "") =>
  str && typeof str === "string" ? str[0].toUpperCase() + str.slice(1) : "";

export const getDataTestId = (
  id: number | string = "",
  parentId: number | string = "",
): string | undefined => {
  const parent = typeof parentId == "number" ? parentId.toString() : parentId;
  const child = typeof id == "number" ? id.toString() : id;
  // if (process.env.NEXT_PUBLIC_INCLUDE_TEST_IDS !== "true") return undefined;

  return parent && child
    ? `${parent}-${child}`
    : child
      ? `${child}`
      : parent
        ? `${parent}`
        : "";
};

// TODO : This is a temporary check as for some connectors VM type is missing, once BCC BE changes are available in prod, we can revert these changes
export const getVMtype = (
  showSASEPhase2: boolean,
  deviceType: string,
  vm?: string,
): string => {
  const isBranchType = showSASEPhase2
    ? vm && VM_TYPES.branch.includes(vm)
    : deviceType === "PHYSICAL" || (vm && VM_TYPES.branch.includes(vm));
  const isCloudType = vm && VM_TYPES.cloud.includes(vm);

  return isBranchType ? "branch" : isCloudType ? "cloud" : "";
};

export const getFilteredVMs = (
  showSASEPhase2: boolean,
  responseData: GeoTableResponse,
  VMType: string,
) =>
  responseData?.filter(
    (res) =>
      getVMtype(showSASEPhase2, res.deviceType, res?.deploymentType) === VMType,
  );

export const getNormalizedLatLong = (lat: number, long: number) => ({
  latitude: lat / 1e7,
  longitude: long / 1e7,
});

export const roundDownEndDate = (endDate: number): number => {
  const minutes = new Date(endDate * 1000).getMinutes() % 5;

  const updatedEndDate =
    (new Date(endDate * 1000).getTime() + (5 - minutes) * 60000) / 1000;

  return updatedEndDate;
};

export const roundDownStartDate = (startDate: number): number => {
  const minutes = new Date(startDate * 1000).getMinutes() % 5;

  const updatedStartDate =
    (new Date(startDate * 1000).getTime() - minutes * 60000) / 1000;

  return updatedStartDate;
};

const osMapping: Record<keyof ChartType, RegExp[]> = {
  windows: [/windows/i],
  macos: [/version.*x86/i, /version.*arm/i],
  ios: [/version/i],
  android: [/android/i],
  linux: [
    /ubuntu/i,
    /centos/i,
    /rhel/i,
    /linux/i,
    /fedora/i,
    /opensuse/i,
    /debian/i,
  ],
};

export const getOsType = (os: string): keyof ChartType => {
  for (const [osType, regexList] of Object.entries(osMapping)) {
    if (regexList.some((regex) => regex.test(os))) {
      return osType;
    }
  }

  return "other";
};

export const isPlural = (count: number, text: string): string =>
  count > 1 ? text + "S" : text;

export const getTotal = <T>(arr: T[], key: keyof T): number =>
  arr.reduce((all, ele) => all + Number(ele[key] ?? 0), 0);

export const getThemeFromDarkMode = (darkMode?: DarkModeTypes): ThemeTypes =>
  darkMode === ON ? "dark" : "light";

export function chunkArray<T>(arr: T[], n: number): T[][] {
  return Array.from({ length: Math.ceil(arr.length / n) }, (_, i) =>
    arr.slice(i * n, i * n + n),
  );
}

export const tWithPlural = (key: string, value: number) =>
  t(key, { value, plural: value === 1 ? "" : "s" });

export const formatTimestamp = (epochTimestamp: number) => {
  const now = Date.now();
  const diffInSeconds = Math.floor((now - epochTimestamp) / 1000);

  if (diffInSeconds < 60) {
    return t("timestamp.justNow");
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return tWithPlural("timestamp.minute", diffInMinutes);
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return tWithPlural("timestamp.hour", diffInHours);
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return tWithPlural("timestamp.day", diffInDays);
  }

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return tWithPlural("timestamp.week", diffInWeeks);
  }

  const diffInMonths = Math.floor(diffInDays / 30);

  return tWithPlural("timestamp.month", diffInMonths);
};

export const getTopEntries = (
  entriesList: Array<{
    category: string;
    categoryLabel: string;
    value: number;
  }>,
  limit = TOP_ENTRIES_LIMIT,
) =>
  entriesList
    ?.sort((a, b) => b.value - a.value)
    .slice(0, limit)
    .reverse();

import { expect, describe, it, vi } from "vitest";
import { getTotal, getDataTestId, formatTimestamp, tWithPlural, getTopEntries } from "./utils";
import { getThemeFromDarkMode } from '../utils/utils';
import { ON } from "@/configs/constants/analytics";

// getDataTestId.test.js

describe('getDataTestId function', () => {
  it('returns an empty string when both id and parentId are empty', () => {
    expect(getDataTestId()).toBe('');
  });

  it('returns the id as a string when parentId is empty', () => {
    expect(getDataTestId('test-id')).toBe('test-id');
    expect(getDataTestId(123)).toBe('123');
  });

  it('returns the parentId as a string when id is empty', () => {
    expect(getDataTestId('', 'parent-id')).toBe('parent-id');
    expect(getDataTestId('', 456)).toBe('456');
  });

  it('returns the concatenated id and parentId when both are provided', () => {
    expect(getDataTestId('test-id', 'parent-id')).toBe('parent-id-test-id');
    expect(getDataTestId(123, 456)).toBe('456-123');
    expect(getDataTestId('test-id', 456)).toBe('456-test-id');
    expect(getDataTestId(123, 'parent-id')).toBe('parent-id-123');
  });

});

describe("getTopEntries", () => {
  it("returns sorted and top limited entries in reverse order", () => {
    const entriesList = [
      { category: 'A', categoryLabel: 'Label A', value: 100 },
      { category: 'B', categoryLabel: 'Label B', value: 300 },
      { category: 'C', categoryLabel: 'Label C', value: 200 },
    ];

    const result = getTopEntries(entriesList, 2); // Limit = 2

    expect(result).toEqual([
      { category: 'B', categoryLabel: 'Label B', value: 300 },
      { category: 'C', categoryLabel: 'Label C', value: 200 },
    ].reverse());
  });
});

describe('getTotal function', () => {
  it('should return the total sum of a specific key in an array of objects', () => {
    const arr = [
      { id: 1, value: 10 },
      { id: 2, value: 20 },
      { id: 3, value: 30 },
    ];

    const result = getTotal(arr, 'value');
    expect(result).toBe(60);
  });

  it('should return 0 when the array is empty', () => {
    const result = getTotal([], 'value');
    expect(result).toBe(0);
  });

  it('should return the total sum of a specific key in an array of objects when the key is not a number', () => {
    const arr = [
      { id: 1, value: '10' },
      { id: 2, value: '20' },
      { id: 3, value: '30' },
    ];

    expect(getTotal(arr, 'value')).toBe(60);
  });

});

describe('getThemeFromDarkMode function', () => {
  it('should return "dark" when darkMode is ON', () => {
    const darkMode = ON;
    const result = getThemeFromDarkMode(darkMode);
    expect(result).toBe('dark');
  });

  it('should return "light" when darkMode is not ON', () => {
    const darkMode = 'off';
    const result = getThemeFromDarkMode(darkMode);
    expect(result).toBe('light');
  });

  it('should return "light" when darkMode is undefined', () => {
    const result = getThemeFromDarkMode();
    expect(result).toBe('light');
  });

  it('should not throw an error when darkMode is an invalid value', () => {
    const darkMode = 'auto';
    const result = getThemeFromDarkMode(darkMode);
    expect(result).toBe('light');
  });

});


vi.mock("i18next", () => ({
  t: (key: string) => {
    const translations: Record<string, any> = {
      "timestamp.justNow": "Just Now",
    };
    return translations[key] || key;
  },
}));

describe("formatTimestamp", () => {
  it("returns 'Just Now' for timestamps within the last 60 seconds", () => {
    const now = Date.now();
    const timestamp = now - 10 * 1000; // 10 seconds ago
    expect(formatTimestamp(timestamp)).toEqual("Just Now");
  });

  it("returns '<x> mins ago' for timestamps within the last hour", () => {
    const now = Date.now();
    const timestamp = now - 15 * 60 * 1000; // 15 minutes ago
    expect(formatTimestamp(timestamp)).toEqual(tWithPlural("timestamp.minute",  15 ));
  });

  it("returns '<x> hours ago' for timestamps within the last 24 hours", () => {
    const now = Date.now();
    const timestamp = now - 8 * 60 * 60 * 1000; // 8 hours ago
    expect(formatTimestamp(timestamp)).toEqual(tWithPlural("timestamp.hour", 8 ));
  });

  it("returns '<x> days ago' for timestamps within the last 7 days", () => {
    const now = Date.now();
    const timestamp = now - 5 * 24 * 60 * 60 * 1000; // 5 days ago
    expect(formatTimestamp(timestamp)).toEqual(tWithPlural("timestamp.day", 5 ));
  });

  it("returns '<x> weeks ago' for timestamps within the last 4 weeks", () => {
    const now = Date.now();
    const timestamp = now - 2 * 7 * 24 * 60 * 60 * 1000; // 2 weeks ago
    expect(formatTimestamp(timestamp)).toEqual(tWithPlural("timestamp.week",  2 ));
  });

  it("returns '<x> months ago' for timestamps older than 4 weeks", () => {
    const now = Date.now();
    const timestamp = now - 3 * 30 * 24 * 60 * 60 * 1000; // 3 months ago
    expect(formatTimestamp(timestamp)).toEqual(tWithPlural("timestamp.month", 3 ));
  });
});
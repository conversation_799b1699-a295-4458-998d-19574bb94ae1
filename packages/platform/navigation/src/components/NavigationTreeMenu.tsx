import { useEffect, useRef, useState } from "react";
import { cn, Toggle } from "@up/components";
import { restProps, testIdFrom } from "@up/std";
import {
  inferAlternateIndex,
  useNavigationContext,
  useNavigationStore,
} from "../context";
import {
  filterHiddenNodes,
  Layer,
  resolveRoute,
  type RouteFn,
  type TreeNode,
} from "../core";
import { type BaseNavigationProps } from "./types";
import { NavigationTreeNode } from "./NavigationTreeNode";
import { NavigationTreeSelect } from "./NavigationTreeSelect";
import {
  currentRelativePath,
  findFirstRoute,
  findFirstRouteNode,
} from "./helpers";

type Props<T extends object> = {
  onItemClicked?: (n?: TreeNode<T>) => void;
  onClose?: () => void;
  currentNode?: TreeNode<T>;
  disabledText?: string;
  onCollape?: (value: boolean) => void;
  onHovering?: (value: boolean) => void;
  collapsed?: boolean;
  alternateDisabled?: boolean;
} & BaseNavigationProps<T>;

// NavigationTreeMenu renders a vertical tree menu with expanding/collapsing nodes. It is automatically
// configured on the fly based on the selected primary node (top navigation pill). If it can not determine
// the active Primary layer, it searches the tree for the current route and renders from that.
export const NavigationTreeMenu = <T extends object>({
  entitlements,
  disabledText,
  onCollape,
  onHovering,
  collapsed = false,
  alternateDisabled = false,
  ...rest
}: Props<T>) => {
  const {
    store,
    selectedIds,
    selected,
    current,
    setActive,
    setAlternateIndex,
    getAlternateIndex,
    inferCorrectNodeAlt,
    // toggleVeritcalCollapsed,
    // setVeritcalCollapsed,
  } = useNavigationStore();

  const { t, navigator } = useNavigationContext();

  const [isCollapsed, setIsCollapsed] = useState<boolean>(collapsed);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    // setVeritcalCollapsed(collapsed);
    setIsCollapsed(collapsed);
  }, [collapsed]);

  const [isHovering, setIsHovering] = useState<boolean>(false);
  let currentlyActive = current();

  // We ar using the ref to avoid some force render issues (needing to) with useState/Effect
  const childrenRef = useRef<Array<TreeNode<T>>>([]);

  const routeToResolve = currentRelativePath();

  // let routeToResolve = currentlyActive?.option.route ?? "";
  // const currentRoute = resolveRoute(routeToResolve);

  // if (currentRoute === "" || currentRoute !== currentLocation) {
  //   // && currentlyActive) {
  //   routeToResolve = currentLocation;
  // }

  // // since the currently active can be undefined or could be a primary node (if routeable), we need to determine
  // the correct starting node (Secondary Layer) for the tree menu.
  if (
    !currentlyActive ||
    currentlyActive?.option.layer === Layer.Primary ||
    routeToResolve !== currentlyActive?.option.route
  ) {
    // use the current location to start, important iuf currentlyActive is undefined
    let matchLocation = routeToResolve;

    // if currently active is a primary node with a route, resolve it
    if (routeToResolve) {
      matchLocation = resolveRoute(routeToResolve, entitlements);
    }

    // find first node for route on secondary layer
    const match = store.tree.search(matchLocation, Layer.Secondary);

    if (match) {
      // if we are coming back from another route, even if we were on the alternate list, go back to initial
      // replicates current nav
      setAlternateIndex(0);
      currentlyActive = match.node;
      setActive(match?.node);
    }
  }

  const selectionPath = selectedIds();
  const navigationPath = selected();
  const focuedNode = current();

  // determing siblings for use in dropdown picker
  let siblings = store.tree.siblings(
    navigationPath[Layer.Secondary] ?? "",
    true,
  );

  // if we have entitlements, filter the siblings based on visibility state
  // TODO: Make this a param on tree:siblings to clean up code
  if (entitlements) {
    siblings = siblings.filter((n: TreeNode<T>) => {
      return !filterHiddenNodes(n, entitlements);
    });
  }

  const selectedNode = store.tree.get(navigationPath[Layer.Secondary] ?? "");

  const resolveNode = selectedNode ?? navigationPath[Layer.Secondary];

  const inferNode = inferCorrectNodeAlt(
    resolveNode ?? "",
    Layer.Secondary,
    entitlements,
  );

  const [altIndex, setAltIndex] = useState<number>(
    inferAlternateIndex(resolveNode ?? ""),
  );

  const hasAlternates = useRef<boolean>(
    store.tree.hasAlternates(inferNode!, entitlements),
  );

  // set the children to render. Done as state since the list will be different based on the picker choice
  // Also used when we collapse siblings (if configured) when expanding a leaf
  let clist = store.tree.resolveChildren(inferNode!, true, getAlternateIndex());
  if (entitlements) {
    clist = clist.filter((n: TreeNode<T>) => {
      return !filterHiddenNodes(n, entitlements);
    });
  }
  // if (childrenRef.current === undefined) {
  //   childrenRef.current = clist;
  // }

  // this is a sticky point, but if we remove it, renders wont happen as expected
  const [children, setChildren] = useState<Array<TreeNode<T>>>(clist);

  const handleOnNavigate = (
    fn: string | RouteFn<T>,
    external?: boolean,
    data?: T,
  ) => {
    if (navigator) {
      const href = resolveRoute(fn, data);
      navigator.push(href, { external });
      navigator.refresh();
    }
  };

  useEffect(() => {
    hasAlternates.current = store.tree.hasAlternates(inferNode!, entitlements);

    const node =
      (selectedNode?.option.layer ?? 0 > 0) ? selectedNode : currentlyActive;

    // All this handles the "switch to existing reports" nonsense
    const alternates = store.tree.hasAlternates(node ?? "", entitlements);

    // if (alternates && altIndex !== getAlternateIndex()) {
    if (alternates && altIndex !== getAlternateIndex()) {
      const parent = store.tree.get(node?.parentId ?? "");

      // determine if the resolved node is in an "alternte" menu, ie: switch to existing reports
      const nodeIdx = store.tree.indexForNode(node!);

      // set the quick check for alternates and be sure to update alt index state (for render) and in the store
      // for other menus like top nav
      hasAlternates.current = store.tree.hasAlternates(parent!, entitlements);
      setAlternateIndex(nodeIdx);
      setAltIndex(nodeIdx);

      // fetch the parents children (ie: left side tree)
      let tc = store.tree.resolveChildren(
        parent?.children[nodeIdx] as TreeNode<object>,
        true,
        nodeIdx,
      );

      // filter nodes based on entitlements
      if (entitlements) {
        tc = tc.filter((n: TreeNode<T>) => {
          return !filterHiddenNodes(n, entitlements);
        });
      }

      childrenRef.current = tc;
      setChildren(() => {
        return tc;
      });
    } else {
      let tc = store.tree.resolveChildren(node!, true, getAlternateIndex());
      if (entitlements) {
        tc = tc.filter((n: TreeNode<T>) => {
          return !filterHiddenNodes(n, entitlements);
        });
      }

      childrenRef.current = tc;
      setChildren(() => {
        return tc;
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentlyActive, altIndex, entitlements]);

  const collpaseSiblingLayer = Layer.Tertiary;

  const navigateToFirstRouteable = () => {
    // lets find the parent at the primary layer
    const pnode = store.tree.traverseToLayer(
      currentlyActive ?? selectedNode!,
      Layer.Primary,
    );
    // grab the right index (analytics or switch to existing)
    const indexRoot = pnode?.children[altIndex === 1 ? 0 : 1];
    // find first routeable child
    const frn = findFirstRouteNode(
      indexRoot as TreeNode<object>,
      entitlements,
    ) as TreeNode<object>;

    // go there
    if (navigator) {
      const href = resolveRoute(frn.option.route!, entitlements);
      navigator.push(href);
    }
  };

  const handleNodeExpansion = (node: TreeNode<T>) => {
    // if (collapseOthers.current && node.option.layer! <= Layer.Tertiary) {
    if (node.option.layer === collpaseSiblingLayer) {
      children
        .filter((n) => n.option.layer === collpaseSiblingLayer)
        .forEach((n) => {
          if (n.id !== node.id) {
            n.expanded = false;
          } else {
            n.expanded = !n.expanded;
          }
        });
    } else {
      node.expanded = !node.expanded;
    }

    childrenRef.current = [...children];
    setChildren(() => {
      return [...children];
    });
  };

  return (
    <div {...restProps(rest)} className="flex flex-col h-full">
      <div
        className={cn(
          "flex flex-col items-start gap-y-0 overflow-y-scroll overflow-x-hidden flex-grow",
          siblings.length === 0 ? "pt-m" : "pt-xs",
          "[&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]",
        )}
        data-testid="layout-collapsible-side-panel"
        onMouseEnter={() => {
          if (onHovering) {
            onHovering(isCollapsed);
          }
          if (isHovering !== isCollapsed) {
            setIsHovering(isCollapsed);
          }
        }}
        onMouseLeave={() => {
          if (onHovering) {
            onHovering(false);
          }
          if (isHovering) {
            setIsHovering(false);
          }
        }}
      >
        {siblings.length > 0 && (
          <NavigationTreeSelect<T>
            items={siblings}
            value={selectedNode!}
            onSelected={(n: TreeNode<T>) => {
              const first = findFirstRouteNode(n, entitlements);
              if (first) {
                setActive(first as TreeNode<object>);
              }

              const nc = store.tree.resolveChildren(
                (n as TreeNode<object>) ?? navigationPath[Layer.Secondary],
                true,
              );

              setChildren(() => {
                return [...nc];
              });

              handleOnNavigate(findFirstRoute(n, entitlements));
            }}
            collapsed={isCollapsed && !isHovering}
          />
        )}

        {childrenRef.current.map((n: string | TreeNode<T>) => {
          const nid = typeof n === "string" ? n : n.id;

          const resnode = store.tree.get(nid);

          if (
            !resnode ||
            resnode.option.visibility?.(entitlements!) === "hidden"
          ) {
            return null;
          }

          return (
            <NavigationTreeNode<T>
              key={resnode.id}
              node={resnode}
              onNavigate={handleOnNavigate}
              entitlements={entitlements}
              onExpanded={handleNodeExpansion}
              active={focuedNode?.id ?? ""}
              selection={selectionPath}
              disabledText={disabledText}
              primaryLayer={resnode.option.layer}
              collapsed={(collapsed || isCollapsed) && !isHovering}
              testId={testIdFrom(rest, ["nav", "item"])}
            />
          );
        })}
      </div>
      <div className="pt-m">
        {hasAlternates.current && !isCollapsed && (
          <div className="flex gap-x-2 items-center justify-center mb-[20px] w-full">
            <div
              className="typography-paragraph2-strong  text-semantic-content-base-secondary"
              data-testid={"switch-to-existing-reports-title"}
            >
              {t(
                store.tree.getParent(selectedNode ?? "")?.option.options
                  ?.alternateLabel ?? "Switch to Existing Reports",
              )}
            </div>
            <Toggle
              disabled={alternateDisabled}
              onChange={navigateToFirstRouteable}
              enabled={altIndex === 1}
              id={"switch-to-existing-reports"}
            />
          </div>
        )}
        <div className="flex w-full items-end mb-rem-40 justify-end py-default pr-2">
          <button
            className="flex text-semantic-content-base-tertiary rounded-xxxl bg-semantic-surface-base-tertiary w-[28px] h-[28px] p-s items-center justify-center cursor-pointer"
            type="button"
            data-testid="layout-side-nav-footer-toggle"
            onClick={() => {
              if (onCollape) {
                onCollape(!isCollapsed);
              }
              setIsCollapsed(!isCollapsed);
              // toggleVeritcalCollapsed();
            }}
          >
            <i
              aria-label="Left icon"
              className={`fa-regular ${isCollapsed ? "fa-chevron-right" : isHovering ? "fa-chevron-right" : "fa-chevron-left"}`}
            ></i>
          </button>
        </div>
      </div>
    </div>
  );
};
